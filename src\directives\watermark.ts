import type { Directive, DirectiveBinding } from 'vue'
import { useEnhancedWatermark, type WatermarkOptions } from '/@/hooks/web/useEnhancedWatermark'
import { ref } from 'vue'

interface WatermarkDirectiveBinding extends DirectiveBinding {
  value?: WatermarkOptions | string | boolean
}

const watermarkInstances = new WeakMap()

const watermarkDirective: Directive = {
  mounted(el: HTMLElement, binding: WatermarkDirectiveBinding) {
    const elementRef = ref(el)
    const { setWatermark, clear } = useEnhancedWatermark(elementRef)

    // 存储实例，用于后续清理
    watermarkInstances.set(el, { setWatermark, clear })

    // 解析指令值
    let options: WatermarkOptions = {
      showUser: true,
      showDate: true
    }

    if (typeof binding.value === 'string') {
      options.text = binding.value
    } else if (typeof binding.value === 'object' && binding.value !== null) {
      options = { ...options, ...binding.value }
    } else if (binding.value === false) {
      // 如果值为 false，不显示水印
      return
    }

    // 设置水印
    setWatermark(options)
  },

  updated(el: HTMLElement, binding: WatermarkDirectiveBinding) {
    const instance = watermarkInstances.get(el)
    if (!instance) return

    const { setWatermark, clear } = instance

    // 如果值为 false，清除水印
    if (binding.value === false) {
      clear()
      return
    }

    // 解析新的指令值
    let options: WatermarkOptions = {
      showUser: true,
      showDate: true
    }

    if (typeof binding.value === 'string') {
      options.text = binding.value
    } else if (typeof binding.value === 'object' && binding.value !== null) {
      options = { ...options, ...binding.value }
    }

    // 重新设置水印
    setWatermark(options)
  },

  unmounted(el: HTMLElement) {
    const instance = watermarkInstances.get(el)
    if (instance) {
      instance.clear()
      watermarkInstances.delete(el)
    }
  }
}

export default watermarkDirective
