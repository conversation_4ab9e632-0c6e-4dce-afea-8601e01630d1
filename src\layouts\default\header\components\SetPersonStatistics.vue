<template>
  <BasicModal @register="registerModal" v-bind="$attrs" title="设置个人业绩" width="30%" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { editSalesTargetPerformance, getSalesPerformance } from '/@/api/Performance/statistics'
import { useMessage } from '/@/hooks/web/useMessage'

const { createMessage } = useMessage()
const [registerModal, { closeModal }] = useModalInner((data) => {
  console.log(data)
  initData()
})

const [registerForm, { setFieldsValue, validate }] = useForm({
  labelWidth: 120,
  baseColProps: { span: 24 },
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'id',
      label: 'id',
      component: 'Input',
      show: false
    },
    {
      field: 'year',
      label: '年',
      component: 'Input',
      dynamicDisabled: true
    },
    {
      field: 'inCharge_name',
      label: '姓名',
      component: 'Input',
      dynamicDisabled: true
    },
    {
      field: 'month',
      label: '月',
      component: 'Input',
      dynamicDisabled: true
    },
    {
      field: 'dept_name',
      label: '所在部门',
      component: 'Input',
      dynamicDisabled: true
    },
    {
      field: 'target_amount',
      label: '目标业绩金额',
      component: 'InputNumber',
      required: true
    }
  ]
})

async function initData() {
  try {
    const {
      items: [data]
    } = await getSalesPerformance()
    console.log(data)
    await setFieldsValue(data)
  } catch (e) {
    createMessage.error(e?.message || '加载失败')
  }
}

async function handleSubmit() {
  try {
    const data = await validate()
    handleEditCell(data)
  } catch (err) {
    createMessage.error(err?.message || '设置失败')
  }
}

async function handleEditCell(record) {
  console.log(record)
  try {
    const { msg } = await editSalesTargetPerformance({
      id: record.id,
      month: record.month,
      year: record.year,
      target_amount: record.target_amount
    })
    if (msg === 'success') {
      closeModal()
      createMessage.success('设置成功')
    }
  } catch (err) {
    createMessage.error('设置失败')
  }
}
</script>
