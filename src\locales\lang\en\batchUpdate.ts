export default {
  index: {
    file: 'File',
    BatchUpdateQuotes: 'Batch update quotes',
    template: 'Template',
    selecttheseparatorbetweenvalues: 'Select the separator between values',
    pleaseSelect: 'Please select',
    selectLineBreaks: 'Select line breaks:',
    preview: 'Preview',
    textAreaPlaceholder:
      'After entering the content, please select the corresponding separator, click on the preview button to preview the table data.',
    submit: 'Submit',
    previewTable: 'Preview table data',
    validatePass2: 'Please enter parameters',
    segmentError1: 'Please select the parameter name option', //
    segmentError2: 'Please check for extra spaces or incorrect number of attribute names', //
    segmentError3: 'Quantity and status must be a number',
    onPreviewTableError: 'Please select the separator and preview after that!', //
    parameterName: 'Parameter name',
    parameterValue: 'Parameter value',
    parameterValueHelpMessage: 'Enter content and select the corresponding separator!',
    excelSpace: 'Excel space',
    normalSpace: 'Normal space',
    equalSign: 'Equal sign(=)',
    doubleObSign: 'Double oblique bar()',
    chineseComma: 'Chinese comma(，)',
    englishComma: 'English comma(,)',
    chineseSemicolon: 'Chinese semicolon(；)',
    englishSemicolon: 'English semicolon(;)',
    chineseColon: 'Chinese colon(：)',
    englishColon: 'English colon(:)',
    lineBreak: 'Line break(Enter)',
    statusName: 'Status name',
    statusValue: 'Status value',
    statusHelpMessage: 'Please fill in the status value to update the status'
  }
}
