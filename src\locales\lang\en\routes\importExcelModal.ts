export default {
  modalTitle: 'excel data import',
  step1Title: 'Selecting the uploaded file',
  step2Title: 'Select the data to be uploaded',
  step3Title: 'Finish',
  alertMessage: 'Currently only xlsx, xls files are supported.',
  selectFileBtn: 'Select File',
  alertMessage2: 'Please specify the data table and fill in the corresponding parameters, then parse the data.',
  span1: 'Please select the table where the data is located',
  span2: 'Please provide the number of rows in which the header of the data is located',
  span3: 'Please provide the region where the data is located',
  example: 'example',
  reUploadBtn: 'Re-upload the file',
  tableHelpMsg: 'The data in the table is only available for preview',
  uploadBtn: 'Upload data',
  rusultTitle: 'Result',
  resultSubTitle: 'The upload is successful when the OK message appears.',
  continueBtn: 'Continue working on the file you just created.',
  uploadAgainBtn: ' Upload another one.'
}
