export default {
  index: {
    file: '文件',
    BatchUpdateQuotes: '批量更新报价', //Batch update quotes',
    template: '模板',
    selecttheseparatorbetweenvalues: '选择值与值之间的分割符:', //Select the separator between values
    pleaseSelect: '请选择', //Please select
    selectLineBreaks: '选择换行符:',
    preview: '预览',
    textAreaPlaceholder: '输入内容后请选择对应的分隔符，点击预览按钮可以预览表格数据。', //'After entering the content, please select the corresponding separator, click on the preview button to preview the table data.'
    submit: '提交',
    previewTable: '预览表格数据', //Preview table data
    validatePass2: '请输入参数', //Please enter parameters
    segmentError1: '请选择参数名选项', //Please select the parameter name option
    segmentError2: '请检查是否有多余空格或属性名数量不对', //Please check for extra spaces or incorrect number of attribute names
    segmentError3: 'Quantity and status must be a number',
    onPreviewTableError: '请选择分割符后在预览！', //Please select the separator and preview after that!
    parameterName: '参数名', //Parameter name
    parameterValue: '参数值', //Parameter value
    parameterValueHelpMessage: '输入内容后请选择对应的分隔符！', //Enter content and select the corresponding separator!
    excelSpace: 'excel空格', //excel space
    normalSpace: '普通空格', //normal space
    equalSign: '等于号(=)', //equal sign(=)
    doubleObSign: '双斜杆(//)', //double oblique bar(//)
    chineseComma: '中文逗号(，)', //chinese comma(，)',
    englishComma: '英文逗号(,)', //english comma(,)',
    chineseSemicolon: '中文分号(；)', //chinese semicolon(；)',
    englishSemicolon: '英文分号(;)', //english semicolon(;)',
    chineseColon: '中文冒号(：)', //chinese colon(：)',
    englishColon: '英文冒号(:)', //english colon(:)',
    lineBreak: '换行(Enter)', //line break(Enter),
    statusName: '状态名称',
    statusValue: '状态值',
    statusHelpMessage: '更新状态请填状态值'
  }
}
