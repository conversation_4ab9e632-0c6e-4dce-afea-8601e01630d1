export default {
  tag: {
    1: '是',
    0: '否'
  },
  tagColor: {
    1: { label: '是', color: '#f50' },
    0: { label: '否', color: '#87d068' }
  },
  colorTag: {
    2: { label: '待质检', color: '#2db7f5' },
    1: { label: '已质检', color: '#87d068' },
    0: { label: '未质检', color: '#f50' }
  },
  qcStatusTag: {
    2: { label: '待质检', color: '#2db7f5' },
    1: { label: '已质检', color: '#87d068' },
    0: { label: '未质检', color: '#f50' },
    3: { label: '质检审核中', color: '#0f0' },
    4: { label: '质检中', color: '#00f' }
  },
  qcStockTag: {
    2: { label: '质检审核中', color: '#2db7f5' },
    1: { label: '已质检', color: '#87d068' },
    0: { label: '未质检', color: '#f50' }
  },
  approveStatus: {
    1: { label: '审批通过', color: 'success' },
    0: { label: '待审批', color: 'error' }
  },
  status: {
    1: '启用',
    0: '禁用'
  },
  colors: {
    1: 'success',
    0: 'error'
  },
  reverseStatus: {
    0: '启用',
    1: '禁用'
  },
  reverseColors: {
    0: 'success',
    1: 'error'
  },
  overviewTag: {
    0: { label: '未开始', color: 'default' },
    1: { label: '进行中', color: 'processing' },
    2: { label: '已结束', color: 'success' }
  },
  salesStatus: {
    0: { label: '未审批', color: 'gray' },
    1: { label: '已执行', color: 'warning' },
    2: { label: '可备货', color: 'processing' },
    3: { label: '备货中', color: 'processing' },
    4: { label: '已入库', color: 'processing' },
    5: { label: '出库中', color: 'processing' },
    15: { label: '已结束', color: 'success' },
    16: { label: '取消', color: 'error' }
  },
  payDetermineStatus: {
    0: { text: '未付款', color: '' },
    1: { text: '已付款', color: 'warning' },
    2: { text: '已结束', color: 'success' }
  },
  financeExamine: {
    0: { text: '待审批', color: '' },
    1: { text: '已审批', color: 'success' },
    2: { text: '驳回', color: 'red' }
  },
  collectDetermineStatus: {
    0: { text: '未收款', color: '' },
    1: { text: '已收款', color: 'success' }
  },
  otherIncomeStatus: {
    0: { label: '待审核', color: 'error' },
    1: { label: '生效', color: 'warning' },
    3: { label: '待执行', color: 'processing' },
    4: { label: '执行中', color: 'processing' },
    5: { label: '备货中', color: 'processing' },
    6: { label: '已在库', color: 'processing' },
    15: { label: '已结束', color: 'success' }
  },
  otherExpendStatus: {
    0: { color: '', text: '待执行' },
    1: { color: 'green', text: '执行中' },
    15: { color: 'red', text: '已结束' }
  },
  otherExpendCheckTag: {
    0: { color: '', text: '未审核' },
    1: { color: 'green', text: '通过' },
    2: { color: 'red', text: '驳回' }
  },
  otherExpendTypeTag: {
    1: { text: '个人报销', color: 'default' },
    2: { text: '款项支出', color: 'blue' },
    3: { text: '财务费用', color: 'green' }
  },
  refundStatus: {
    0: { label: '待审核', color: 'processing' },
    1: { label: '已审核', color: 'success' }
  },
  refundType: {
    1: { label: '退款', color: '#f90' },
    2: { label: '不退款', color: '#2db7f5' }
  },
  refundOrderType: {
    1: { label: '销售退款', color: 'pink' },
    2: { label: '采购退款', color: 'orange' }
  },
  bookingStatus: {
    0: { label: '预约中', color: 'default' },
    1: { label: '已确认', color: 'processing' },
    2: { label: '出库中', color: 'warning' },
    15: { label: '已出库', color: 'warning' },
    16: { label: '已作废', color: 'error' }
  },
  bookingStatusList: [
    { label: '预约中', value: 0 },
    { label: '已确认', value: 1 },
    { label: '出库中', value: 2 },
    { label: '已出库', value: 15 },
    { label: '已作废', value: 16 }
  ],

  outWarehouse: {
    shipmentType: {
      1: '送出装柜',
      2: '送货',
      3: '装柜',
      4: '自提',
      5: '工厂出货'
    },
    shipmentUrgency: {
      1: { label: '一般', color: '#0f0' },
      2: { label: '紧急', color: '#00f' },
      3: { label: '非常紧急', color: '#f00' }
    }
  },
  mapUrgentLevel: {
    1: { label: '低', color: 'default', alias: '一般' },
    2: { label: '中', color: '#ffaa00', alias: '紧急' },
    3: { label: '高', color: '#ff0000', alias: '非常紧急' }
  },
  urgentLevelList: [
    { label: '低', value: 1, alias: '一般' },
    { label: '中', value: 2, alias: '紧急' },
    { label: '高', value: 3, alias: '非常紧急' }
  ],
  isOutOpt: [
    { label: '未出库', value: 0 },
    { label: '出库中', value: 1 },
    { label: '已出库', value: 2 }
  ]
}
