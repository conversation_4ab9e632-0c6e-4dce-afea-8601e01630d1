import type { AppRouteModule } from '/@/router/types'
// import { RoleEnum } from '/@/enums/roleEnum'
import { LAYOUT } from '/@/router/constant'

const extrasPage: AppRouteModule = {
  path: '/extrasPage',
  name: 'extrasPage',
  component: LAYOUT,
  meta: {
    order_no: 9,
    icon: 'ant-design:unordered-list-outlined',
    title: '额外页面'
  },
  children: [
    {
      path: 'createSaleOrder',
      name: '/extrasPage/createSaleOrder',
      component: () => import('/@/views/extrasPage/createSaleOrder/index.vue'),
      meta: {
        title: '创建销售订单'
      }
    }
  ]
}

export default extrasPage
