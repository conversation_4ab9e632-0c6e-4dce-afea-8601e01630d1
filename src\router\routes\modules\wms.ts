import type { AppRouteModule } from '/@/router/types'
import { LAYOUT } from '/@/router/constant'

const wms: AppRouteModule = {
  path: '/wms',
  name: 'wms',
  component: LAYOUT,
  meta: {
    order_no: 10,
    icon: 'ant-design:unordered-list-outlined',
    title: '库存WMS'
  },
  children: [
    {
      path: 'purchaseReceipt',
      name: '/wms/purchaseReceipt',
      component: () => import('/@/views/wms/purchaseReceipt/index.vue'),
      meta: {
        title: '外购入库'
      }
    },
    {
      path: 'salesOutbound',
      name: '/wms/salesOutbound',
      component: () => import('/@/views/wms/salesOutbound/index.vue'),
      meta: {
        title: '销售出库'
      }
    },
    {
      path: 'realTimeInventory',
      name: '/wms/realTimeInventory',
      component: () => import('/@/views/wms/realTimeInventory/index.vue'),
      meta: {
        title: '即时库存'
      }
    }
  ]
}
export default wms
