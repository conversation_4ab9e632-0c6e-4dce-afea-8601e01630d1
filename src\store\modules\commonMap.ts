import { defineStore } from 'pinia'
import { store } from '/@/store'
import {
  getAccountList,
  getClientList,
  getDeptList,
  getErpSupplier,
  getItemRequest,
  getItemStocking,
  getStoreList,
  getWorkList
} from '/@/api/commonUtils'

import { WorkItem, CommonItems, SupplierItem, ItemStockingItem, ItemRequestItem } from '/@/types/store'

type MapStoreState = {
  workList: WorkItem[]
  deptList: CommonItems[]
  storeList: CommonItems[]
  supplierList: SupplierItem[]
  personList: CommonItems[]
  itemStockingList: ItemStockingItem[]
  clientList: CommonItems[]
  salesWorkList: WorkItem[]
  purchaseWorkList: WorkItem[]
  itemRequestList: ItemRequestItem[]
  isFirstLoad: boolean
}

export const useMapStore = defineStore('map-store', {
  state: (): MapStoreState => ({
    workList: [],
    salesWorkList: [],
    purchaseWorkList: [],
    deptList: [],
    storeList: [],
    supplierList: [],
    personList: [],
    itemStockingList: [],
    clientList: [],
    itemRequestList: [],
    isFirstLoad: false
  }),
  getters: {
    getMapSalesWork() {
      const mapWork = {}
      for (const item of this.salesWorkList) {
        mapWork[item.id] = item
      }
      return mapWork
    },
    getMapPurchaseWork() {
      const mapWork = {}
      for (const item of this.purchaseWorkList) {
        mapWork[item.id] = item
      }
      return mapWork
    },
    getMapDept() {
      const mapDept = {}
      for (const item of this.deptList) {
        mapDept[item.id] = item.name
      }
      return mapDept
    },
    getMapSupplier() {
      const mapSupplier = {}
      for (const item of this.supplierList) {
        mapSupplier[item.id] = item
      }
      return mapSupplier
    },
    getMapPerson() {
      const mapPerson = {}
      for (const item of this.personList) {
        mapPerson[item.id] = item.name
      }
      return mapPerson
    },
    getMapStore() {
      const mapStore = {}
      for (const item of this.storeList) {
        mapStore[item.id] = item.name
      }
      return mapStore
    },
    getMapItemRequest() {
      const mapItemRequest = {}
      for (const item of this.itemRequestList) {
        mapItemRequest[item.id] = item
      }
      return mapItemRequest
    }
  },
  actions: {
    // setWorkList(data: WorkItem[]) {
    //   this.workList = data
    //   // this.salesWorkList = data.filter((item: WorkItem) => {
    //   //   return +item.type === 1 && +item.receivable > 0
    //   // })
    //   // this.purchaseWorkList = data.filter((item: WorkItem) => {
    //   //   return +item.type === 1 && +item.cost > 0
    //   // })
    // },
    setSalesWorkList(data: WorkItem[]) {
      this.salesWorkList = data
    },
    setPurchaseWorkList(data: WorkItem[]) {
      this.purchaseWorkList = data
    },
    setDeptList(data: CommonItems[]) {
      this.deptList = data
    },
    setStoreList(data: CommonItems[]) {
      this.storeList = data
    },
    setSupplierList(data: SupplierItem[]) {
      this.supplierList = data
    },
    setPersonList(data: CommonItems[]) {
      this.personList = data
    },
    setItemStockingList(data: ItemStockingItem[]) {
      this.itemStockingList = data
    },
    setClientList(data: CommonItems[]) {
      this.clientList = data
    },
    setItemRequestList(data: ItemRequestItem[]) {
      this.itemRequestList = data
    },
    setFirstLoad(flag: boolean) {
      this.isFirstLoad = flag
    },
    async getAllSaleWorkList() {
      try {
        const { items } = await getWorkList({ pageSize: 999999, type: 3 })

        if (items && items.length > 0) this.setSalesWorkList(items)
      } catch (e) {
        console.log(e)
      }
    },
    async getAllPurchaseWorkList() {
      try {
        const { items } = await getWorkList({ pageSize: 999999, type: 4 })
        if (items && items.length > 0) this.setPurchaseWorkList(items)
      } catch (e) {
        console.log(e)
      }
    },
    // async getAllWorkList() {
    //   const { items } = await getWorkList()
    //   if (items && items.length > 0) this.setWorkList(items)
    // },
    async getAllDeptList() {
      try {
        const { items } = await getDeptList()
        if (items && items.length > 0) this.setDeptList(items)
      } catch (e) {
        console.log(e)
      }
    },
    async getAllStoreList() {
      try {
        const { items } = await getStoreList()
        if (items && items.length > 0) this.setStoreList(items)
      } catch (e) {
        console.log(e)
      }
    },
    async getAllSupplierList() {
      try {
        const { items } = await getErpSupplier()
        if (items && items.length > 0) this.setSupplierList(items)
      } catch (e) {
        console.log(e)
      }
    },
    async getAllPersonList() {
      try {
        const { items } = await getAccountList({})
        if (items && items.length > 0) this.setPersonList(items)
      } catch (e) {
        console.log(e)
      }
    },
    async getAllItemStockingList() {
      try {
        const { items } = await getItemStocking()
        if (items && items.length > 0) this.setItemStockingList(items)
      } catch (e) {
        console.log(e)
      }
    },
    async getAllClientList() {
      try {
        const { items } = await getClientList()
        if (items && items.length > 0) this.setClientList(items)
      } catch (e) {
        console.log(e)
      }
    },
    async getAllItemRequest() {
      try {
        const { items } = await getItemRequest()
        if (items && items.length > 0) this.setItemRequestList(items)
      } catch (e) {
        console.log(e)
      }
    },
    async initTools() {
      try {
        if (this.isFirstLoad) return
        await Promise.allSettled([
          // getAllWorkList(),
          // this.getAllSaleWorkList(),
          // this.getAllPurchaseWorkList(),
          this.getAllDeptList(),
          this.getAllStoreList(),
          this.getAllSupplierList(),
          this.getAllPersonList(),
          // this.getAllItemStockingList(),
          this.getAllClientList()
          // this.getAllItemRequest()
        ])
        this.setFirstLoad(true)
      } catch (e) {
        console.log(e)
        this.setFirstLoad(false)
      }
    }
  }
})

export function useMapStoreWithOut() {
  return useMapStore(store)
}
