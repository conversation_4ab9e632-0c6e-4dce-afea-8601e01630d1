/**
 * 格式化价格, 保留两位小数,人民币
 * 例子: formatter.format(value)
 */
export const formatter = new Intl.NumberFormat('zh-CN', {
  style: 'currency',
  currency: 'CNY',
  currencyDisplay: 'symbol'
})

/**不带货币标识 */
// export const formateerNotCurrency = new Intl.NumberFormat('zn-CN', {
//   style: undefined,
//   currency: undefined,
//   currencyDisplay: 'symbol',
//   minimumFractionDigits: 2,
//   maximumFractionDigits: 6
// })

// console.log()

// /**
//  * @param num 需要截断的数值
//  * @param digits 保留几位小数
//  */
// function formateerNotCurrency(num, digits = 4) {
//   var numStr = num.toString() // 将数字转换为字符串
//   var dotIndex = numStr.indexOf('.') // 找到小数点的位置
//   if (dotIndex >= 0) {
//     return parseFloat(numStr.slice(0, dotIndex + digits + 1)) // 截取小数点后两位并转换回数字类型
//   } else {
//     return num // 如果是整数，直接返回原数
//   }
// }

export const formateerNotCurrency = {
  /**
   * @param num 需要截断的数值
   * @param digits 保留几位小数
   */
  format: function (num, digits = 4) {
    if (num) {
      const numStr = num.toString() // 将数字转换为字符串
      const dotIndex = numStr.indexOf('.') // 找到小数点的位置
      if (dotIndex >= 0) {
        return parseFloat(numStr.slice(0, dotIndex + digits + 1)).toFixed(digits) // 截取小数点后两位并转换回数字类型
      } else {
        return num.toFixed(digits) // 如果是整数，直接返回原数
      }
    } else {
      return 0
    }
  }
}
