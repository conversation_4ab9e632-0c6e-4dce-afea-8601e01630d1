import * as XLSX from 'xlsx'
// import { isArray } from './is'

//导出模版 方案一:可以传入一个参数, 也可以传多个数组,就可以多行了
// export function onExpExcelTemplate(...args) {
//   const data = Array.from(args)
//   const workbook = XLSX.utils.book_new()
//   const worksheet = XLSX.utils.aoa_to_sheet(data)

//   XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

//   const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
//   const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
//   const url = URL.createObjectURL(blob)
//   const link = document.createElement('a')

//   link.href = url
//   link.download = 'template.xlsx'
//   link.click()

//   URL.revokeObjectURL(url)
// }

//方案二
export function onExpExcelTemplate(excelHeader, isUsePrompt = true, isNamed?) {
  const data = isUsePrompt
    ? [
        [
          '请按照以下格式填写数据： 除金额,外汇金额,手续费,手续费 ,外汇金额,与部门ID外;皆为文本格式上传，金额一类为数字格式上传,部门ID为部门id,数字格式上传,请勿以模版上传'
        ],
        excelHeader
      ]
    : [excelHeader]

  const workbook = XLSX.utils.book_new()
  const worksheet = XLSX.utils.aoa_to_sheet(data)

  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
  const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')

  link.href = url
  link.download = !isNamed ? 'template.xlsx' : isNamed + '.xlsx'
  link.click()

  URL.revokeObjectURL(url)
}
