import { FormSchema } from '/@/components/Form'
import { isArray } from 'lodash-es'
/**
 * 将传入的formSchemas，全部转换成disabled
 * 一般用于form的updateSchema方法中
 * 因为componentProps有可能会是一个函数，所以做成update的形式
 * @param formSchemaList 要进行转换的源数据
 * @param whiteFieldList 不需要进行转换的白名单
 * @param whiteField 指定进行判断白名单的字段
 */
export const transformFormSchemaDisabled = (formSchemaList: FormSchema[], whiteFieldList?: string[], whiteField?: string) => {
  return formSchemaList.map((item) => {
    if (isArray(whiteFieldList) && whiteFieldList.includes(item[whiteField ?? 'field'])) return item

    return {
      field: item.field,
      label: item.label,
      dynamicDisabled: true,
      componentName: item.component,
      componentProps: {
        disabled: true,
        treeSelectProps: {
          disabled: true
        },
        selectProps: {
          disabled: true
        }
      }
    }
  })
}

/**
 * 将传入的formSchemas，全部字段自动转成status传入的状态
 * 一般用于form的updateSchema方法中
 * 因为componentProps有可能会是一个函数，所以做成update的形式
 * @param formSchemaList 要进行转换的源数据
 * @param whiteFieldList 不需要进行转换的白名单
 * @param whiteField 指定进行判断白名单的字段
 * @param status 转换的状态
 */
export const transformFieldDisabledStatus = (
  formSchemaList: FormSchema[],
  status: boolean,
  whiteFieldList?: string[],
  whiteField?: string
) => {
  return formSchemaList.map((item) => {
    if (isArray(whiteFieldList) && whiteFieldList.includes(item[whiteField ?? 'field'])) return item

    return {
      field: item.field,
      label: item.label,
      dynamicDisabled: status,
      componentName: item.component,
      componentProps: {
        disabled: true,
        treeSelectProps: {
          disabled: status
        },
        selectProps: {
          disabled: status
        }
      }
    }
  })
}
