/**
 * 格式化价格和货币
 * @param price 价格
 * @param currency 货币名,如USD
 * @returns '¥' + price
 */

export function formateCurrencySign(price, currency = '人民币') {
  price = formateThousandthSymbol(price, 2)
  switch (currency) {
    case '美元':
      return `${currency} $${price}`
      break
    case '人民币':
      return `${currency} ¥${price}`
      break
  }
}

/**
 * 价格格式化 '-10000.222' => '-10000.22'
 * @param {*} s 价格
 * @param {*} n 保留几位小数 最长5为,默认2位 n>0 and n<6
 * @param {*} isRound  是否四舍五入
 */
function formatPrice(s, n, isRound = false) {
  s = s.toString().trim()
  n = n > 0 && n <= 5 ? n : 2
  let _index = s.indexOf('.')
  // 如果是整数,需要补零,最长5位所以补5个0
  if (_index === -1) {
    s += '.00000'
  }
  _index = s.indexOf('.')
  const reg = new RegExp('^-?[0-9]+.{0,1}[0-9]{0,10}$')
  const isNumberString = reg.test(s)
  if (isNumberString || (typeof s === 'number' && !isNaN(s))) {
    if (isRound) {
      s = parseFloat(s.replace(/[^\d\\.-]/g, '')).toFixed(n) + ''
    } else {
      s = s.replace(/[^\d\\.-]/g, '').substring(0, n + _index + 1) + ''
    }
    return s
  } else {
    return '0.00000'.substring(0, n + 2)
  }
}

/**
 * '-10000.222' => '-10,000.22'
 * 格式化成千分位数字,如果格式不对返回0.00
 * @param {*} s 要格式化的数字
 * @param {*} n 保留几位小数 最长5为,默认2位
 * @param {*} isRound 是否四舍五入,默认是
 */
function formateThousandthSymbol(s, n = 2, isRound = false) {
  s = formatPrice(s, n, isRound)
  const l = s.split('.')[0].split('').reverse()
  const r = s.split('.')[1]
  let t = ''
  for (let i = 0; i < l.length; i++) {
    t += l[i] + ((i + 1) % 3 == 0 && i + 1 != l.length ? ',' : '')
  }
  return t.split('').reverse().join('') + '.' + r
}

/**
 * 保留小数位数
 *value是为要保留的数字
 * places是保留的小数位数
 */
export function retainDecimalPlaces(value: number, places: number) {
  const multiplier = Math.pow(10, places)
  return Math.floor(value * multiplier) / multiplier
}
