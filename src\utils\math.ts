import { isNumber } from 'lodash-es'
import Decimal from 'decimal.js'

/**
 * add 加法运算
 * @param {number} num1 - 加法第一位
 * @param {number} num2 - 加法第二位
 * @param {number} acc - 保留小数位数
 * returns: 加法结果
 * example: add(0.1, 0.2, 2)
 */
export function add(num1: number, num2: number, acc?: number): number {
  const x = new Decimal(num1 || 0)
  const y = new Decimal(num2 || 0)
  const result = x.plus(y)

  return isNumber(acc) ? result.toDecimalPlaces(acc).toNumber() : result.toNumber()
}

/**
 * sub 减法运算
 * @param {number} num1 - 减法第一位
 * @param {number} num2 - 减法第二位
 * @param {number} acc - 保留小数位数
 * returns: 减法结果
 * example: sub(0.1, 0.2, 2)
 */
export function sub(num1: number, num2: number, acc?: number): number {
  const x = new Decimal(num1 || 0)
  const y = new Decimal(num2 || 0)
  const result = x.minus(y)

  return isNumber(acc) ? result.toDecimalPlaces(acc).toNumber() : result.toNumber()
}

/**
 * mul 乘法运算
 * @param {number} num1 - 乘法第一位
 * @param {number} num2 - 乘法第二位
 * @param {number} acc - 保留小数位数
 * returns: 乘法结果
 * example: mul(0.1, 0.2, 2)
 */
export function mul(num1: number, num2: number, acc?: number): number {
  const x = new Decimal(num1 || 0)
  const y = new Decimal(num2 || 0)
  const result = x.times(y)

  return isNumber(acc) ? result.toDecimalPlaces(acc).toNumber() : result.toNumber()
}

/**
 * div 除法运算
 * @param {number} num1 - 除法第一位
 * @param {number} num2 - 除法第二位
 * @param {number} acc - 保留小数位数
 * returns: 除法结果
 * example: div(0.1, 0.2, 2)
 */
export function div(num1: number, num2: number, acc?: number): number {
  const x = new Decimal(num1 || 0)
  const y = new Decimal(num2 || 0)

  // 避免除以零的错误
  if (y.isZero()) {
    console.error('除数不能为零')
    return 0
  }

  const result = x.dividedBy(y)

  return isNumber(acc) ? result.toDecimalPlaces(acc).toNumber() : result.toNumber()
}

/**
 * 将数字保留指定位数的小数
 * @param {number} num - 要保留小数的数字
 * @param {number} acc - 要保留的小数位数
 * @returns {number} 被保留指定位数小数的数字
 */
export function keepDecimal(num: number, acc: number): number {
  const decimal = new Decimal(num || 0)
  return decimal.toDecimalPlaces(acc).toNumber()
}
