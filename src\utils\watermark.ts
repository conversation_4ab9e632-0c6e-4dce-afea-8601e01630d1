import { useUserStore } from '/@/store/modules/user'
import { formatToDate } from '/@/utils/dateUtil'

// 全局水印功能
export function initGlobalWatermark() {
  const userStore = useUserStore()

  function createWatermarkText(): string {
    const parts: string[] = []

    // 添加用户信息
    const userInfo = userStore.getUserInfo
    if (userInfo?.realName) {
      parts.push(userInfo.realName)
    }

    // 添加当前日期时间
    const currentDate = formatToDate(new Date(), 'YYYY-MM-DD HH:mm')
    parts.push(currentDate)

    return parts.join(' ')
  }

  function createWatermarkCanvas(): string {
    const watermarkText = createWatermarkText()

    const canvas = document.createElement('canvas')
    const width = 300
    const height = 240
    canvas.width = width
    canvas.height = height

    const ctx = canvas.getContext('2d')
    if (ctx) {
      ctx.rotate((-20 * Math.PI) / 180)
      ctx.font = '16px Arial, sans-serif'
      ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'
      ctx.textAlign = 'left'
      ctx.textBaseline = 'middle'
      ctx.fillText(watermarkText, width / 20, height / 2)
    }
    return canvas.toDataURL('image/png')
  }

  function addWatermark() {
    // 移除已存在的水印
    const existingWatermark = document.getElementById('global-watermark')
    if (existingWatermark) {
      existingWatermark.remove()
    }

    // 创建水印元素
    const watermarkDiv = document.createElement('div')
    watermarkDiv.id = 'global-watermark'
    watermarkDiv.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 100000;
      pointer-events: none;
      background-image: url(${createWatermarkCanvas()});
      background-repeat: repeat;
    `

    document.body.appendChild(watermarkDiv)
  }

  // 初始化水印
  const userInfo = userStore.getUserInfo
  if (userInfo?.realName) {
    addWatermark()
  }

  return {
    refresh: addWatermark,
    remove: () => {
      const watermark = document.getElementById('global-watermark')
      if (watermark) {
        watermark.remove()
      }
    }
  }
}
