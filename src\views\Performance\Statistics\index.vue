<template>
  <div class="m-4 p-4 bg-white">
    <Tabs type="card" v-model:activeKey="activeKey" tabPosition="top">
      <template v-for="tab in tabConfigList.filter((item) => item.ifShow)" :key="tab.key">
        <TabPane :tab="tab.tabName">
          <BasicTable
            :data-cachekey="`/performance/statistics/${tab.value}`"
            :ref="(el) => (mapTableAction[tab.value] = el)"
            v-bind="tab.config"
            @edit-end="(data) => handleEditCell({ ...data, tabKey: tab.key, tabType: tab.value })"
            @columns-change="(col) => handleColumnsChange(col, tab.value)"
          >
            <template #toolbar>
              <template v-if="tab.value !== 'operationCenterDeptTable'">
                <a-button
                  v-if="tab.value === 'salesDetailTable'"
                  :loading="downloading"
                  :disabled="downloading"
                  type="primary"
                  @click="
                    handleDownload({
                      url: 'https://img.gbuilderchina.com/erp/purchase/20241205/173409054967514b8be007e500173187.docx',
                      fileName: '业绩统计核算标准.docx'
                    })
                  "
                  >下载业绩核算标准文件</a-button
                >
                <a-button
                  v-if="tab.value === 'salesDetailTable'"
                  :loading="downloading"
                  :disabled="downloading"
                  type="primary"
                  @click="
                    handleDownload({
                      url: 'https://img.gbuilderchina.com/erp/purchase/20240828/172563604166ceebb0eea49013896916.pdf',
                      fileName: '业绩明细疑难解答.pdf'
                    })
                  "
                >
                  下载疑问解答文件
                </a-button>
                <template v-if="hasPermission(425)">
                  <Popover
                    v-if="tab.value === 'salesDetailTable'"
                    v-model:visible="popoverVisible"
                    title="选择重新计算的月份"
                    :disabled="calcBtnLoading"
                    trigger="click"
                  >
                    <template #content>
                      <date-picker v-model:value="yearMonth" picker="month" size="small" />
                      <a-button size="small" :loading="calcBtnLoading" type="primary" @click="handleCalc({ tabType: tab.value })"
                        >确定</a-button
                      >
                    </template>
                    <a-button :loading="calcBtnLoading" type="primary">重新计算</a-button>
                  </Popover>
                  <Tooltip v-else title="点击后请2分钟后再次执行，请勿频繁点击" placement="top">
                    <a-button :loading="calcBtnLoading" type="primary" @click="handleCalc({ tabType: tab.value })">重新计算</a-button>
                  </Tooltip>
                </template>
              </template>

              <a-button
                v-if="hasPermission(427) && tab.value !== 'operationCenterDeptTable'"
                type="primary"
                @click="handleExport({ tabType: tab.value, tabName: tab.tabName, searchInfo: tab.searchInfo })"
                >导出</a-button
              >
            </template>
            <template #footer="currentPageData">
              <div class="footer" v-if="['salesDetailTable'].includes(tab.value)">
                <Popover title="当前页业绩合计" trigger="click" v-model:open="showAchiPopover" placement="right">
                  <template #content>
                    <div class="achi-popover w-[70vw]">
                      <span>当前页过去业绩加减单合计：{{ handleFormatPrice(currentPageData, 'over_amount') }}</span>
                      <span>当前页当月新增核定业绩合计：{{ handleFormatPrice(currentPageData, 'now_amount') }}</span>
                      <span>当前页2025业绩核定合计：{{ handleFormatPrice(currentPageData, 'reality_amount') }}</span>
                      <span>当前页开单金额合计：{{ handleFormatPrice(currentPageData, 'bill_amount') }}</span>
                      <span>当前页2025业绩核定值（产品部）：{{ handleFormatPrice(currentPageData, 'product_amount') }}</span>
                      <span>当前页2025业绩核定值（业务部）：{{ handleFormatPrice(currentPageData, 'business_amount') }}</span>
                    </div>
                  </template>
                  <Button type="primary" size="small" class="mr-4">查看当前页统计数据</Button>
                </Popover>
                <div class="flex flex-wrap">
                  <span>过去业绩加减单合计：{{ dataSumGather?.[tab.value]?.over_amount ?? 0 }}</span>
                  <span>当月新增核定业绩合计：{{ dataSumGather?.[tab.value]?.now_amount ?? 0 }}</span>
                  <span>2025业绩核定合计：{{ dataSumGather?.[tab.value]?.reality_amount ?? 0 }}</span>
                  <span>开单金额合计：{{ dataSumGather?.[tab.value]?.sum_bill_amount ?? 0 }}</span>
                  <span>2025业绩核定值（产品部）：{{ dataSumGather?.[tab.value]?.product_amount ?? 0 }}</span>
                  <span>2025业绩核定值（业务部）：{{ dataSumGather?.[tab.value]?.business_amount ?? 0 }}</span>
                </div>
              </div>
              <div class="footer" v-else-if="hasPermission(421)">
                <Popover title="当前页业绩合计" trigger="click" v-model:open="showAchiPopover" placement="right">
                  <template #content>
                    <div class="achi-popover w-[70vw]">
                      <template
                        v-if="tab.value === 'onlinePersonalTable' || tab.value === 'offlinePersonalTable' || tab.value === 'guideTable'"
                      >
                        <span>当前页目标业绩合计：{{ handleFormatPrice(currentPageData, 'target_sum_amount') }}</span>
                        <span>当前页已完成业绩合计：{{ handleFormatPrice(currentPageData, 'finish_amount') }}</span>
                        <span
                          >当前页业绩完成率合计：{{
                            handleFormatPrice(currentPageData, 'target_sum_amount') > 0
                              ? Math.floor(
                                  div(
                                    handleFormatPrice(currentPageData, 'finish_amount'),
                                    handleFormatPrice(currentPageData, 'target_sum_amount')
                                  ) * 10000
                                ) / 100
                              : 0
                          }}%</span
                        >
                      </template>
                      <template v-if="tab.value === 'onlineDeptTable'">
                        <span>当前页目标业绩合计：{{ handleFormatPrice(currentPageData, 'target_amount') }}</span>
                      </template>
                      <template v-if="tab.value === 'offlineDeptTable'">
                        <span>当前页目标业绩合计：{{ handleFormatPrice(currentPageData, 'target_amount') }}</span>
                        <span>当前页已完成业绩合计：{{ handleFormatPrice(currentPageData, 'finish_amount') }}</span>
                        <span
                          >当前页业绩完成率合计：{{
                            handleFormatPrice(currentPageData, 'target_amount') > 0
                              ? Math.floor(
                                  div(
                                    handleFormatPrice(currentPageData, 'finish_amount'),
                                    handleFormatPrice(currentPageData, 'target_amount')
                                  ) * 10000
                                ) / 100
                              : 0
                          }}%</span
                        >
                      </template>
                      <template v-if="tab.value === 'operationCenterTable'">
                        <span>当前页目标业绩合计：{{ handleFormatPrice(currentPageData, 'target_sum_amount') }}</span>
                        <span>当前页已完成业绩合计：{{ handleFormatPrice(currentPageData, 'finish_amount') }}</span>
                      </template>
                      <template v-if="tab.value === 'operationCenterDeptTable'">
                        <span>当前页完成业绩合计：{{ handleFormatPrice(currentPageData, 'finish_amount') }}</span>
                      </template>
                    </div>
                  </template>
                  <Button type="primary" size="small" class="mr-4">查看当前页统计数据</Button>
                </Popover>

                <!-- 这里是后端统计所有数据 -->
                <div class="flex flex-wrap">
                  <template v-if="['onlineDeptTable', 'offlineDeptTable', 'operationCenterDeptTable'].includes(tab.value)">
                    <span>目标业绩合计：{{ dataSumGather?.[tab.value]?.sum_target_amount ?? 0 }}</span>
                    <span>已完成业绩合计：{{ dataSumGather?.[tab.value]?.sum_finish_amount ?? 0 }}</span>
                    <span>已完成业绩完成率：{{ dataSumGather?.[tab.value]?.sum_finish_score ?? 0 }}%</span>
                  </template>
                  <template
                    v-if="['onlinePersonalTable', 'offlinePersonalTable', 'guideTable', 'operationCenterTable'].includes(tab.value)"
                  >
                    <span>总完成业绩：{{ dataSumGather?.[tab.value]?.finish_amount ?? 0 }}</span>
                  </template>
                </div>
                <!-- 这里是后端统计所有数据 -->
              </div>
            </template>
          </BasicTable>
        </TabPane>
      </template>
    </Tabs>
  </div>
</template>

<script setup lang="ts">
import { BasicTable } from '/@/components/Table'
import { editSalesTargetPerformance, calcSalesDetail, exportPersonal, exportDept, exportSalesDetail } from '/@/api/Performance/statistics'
import {
  tabConfigList,
  mapTableAction,
  handleColumnsChange,
  // salesTotal,
  // onlineDeptTotal,
  // operateDeptTotal,
  dataSumGather
} from '/@/views/Performance/Statistics/datas/datas'
import { useMessage } from '/@/hooks/web/useMessage'
import { Tabs, TabPane, Tooltip, Popover, DatePicker, Button } from 'ant-design-vue'
import { ref, computed } from 'vue'
import { isNumber } from 'lodash-es'
import { add, div } from '/@/utils/math'
import { usePermission } from '/@/hooks/web/usePermission'
import dayjs from 'dayjs'
import axios from 'axios'

const yearMonth = ref(null)

const popoverVisible = ref(false)

const exporting = ref<boolean>(false)

const downloading = ref<boolean>(false)

const calcBtnLoading = ref<boolean>(false)

const showAchiPopover = ref<boolean>(false)

const { hasPermission } = usePermission()

const compTabs = computed(() => tabConfigList.filter((item) => item.ifShow))

const activeKey = ref(compTabs.value[0]?.key ?? 6)

if (window.history.state?.searchParams) {
  activeKey.value = window.history.state.searchParams.tabVal
}

const { createMessage } = useMessage()

async function handleEditCell({ record, key, value, tabType }) {
  try {
    const { msg } = await editSalesTargetPerformance(getParams({ record, key, value }, tabType))
    if (msg === 'success') {
      createMessage.success('设置成功')
    }
  } catch (err) {
    createMessage.error('设置失败')
  } finally {
    mapTableAction.value?.[tabType]?.tableAction?.reload()
  }
}

function formatPersonAmountParams({ record, key, value }) {
  if (['target_sum_amount'].includes(key)) {
    return {
      id: record.target_sum_id,
      yearMonth: record.yearMonth,
      dept_id: record.dept_id,
      // dept_id: 999,
      target_amount: value,
      inCharge: record.inCharge
    }
  }

  const { target } = record
  const curTarget = target.find((item) => item.dept_id === key)
  if (!curTarget) return createMessage.error('没有找到对应的运营中心！')
  return {
    id: curTarget.target_id,
    yearMonth: record.yearMonth,
    dept_id: curTarget.dept_id,
    target_amount: value,
    inCharge: record.inCharge,
    type: 1
  }
}

function formatDeptAmount({ record, value }) {
  return {
    dept_id: record.dept_id,
    type: 2,
    yearMonth: record.yearMonth,
    target_amount: value,
    id: record.id
  }
}

function formatOperationCenterParams({ record, value }) {
  return {
    inCharge: record.inCharge,
    target_amount: value,
    dept_id: record.operation,
    type: 1,
    yearMonth: record.yearMonth
  }
}

function getParams({ record, key, value }, tabType) {
  const mapHandle = {
    onlinePersonalTable: formatPersonAmountParams,
    onlineDeptTable: formatDeptAmount,
    offlinePersonalTable: formatPersonAmountParams,
    offlineDeptTable: formatDeptAmount,
    guideTable: formatPersonAmountParams,
    operationCenterTable: formatOperationCenterParams,
    operationCenterDeptTable: formatDeptAmount
  }

  return mapHandle[tabType]?.({ record, key, value })
}

function handleFormatPrice(data, field) {
  return data.reduce((acc, cur) => {
    return !isNaN(+cur[field]) && isNumber(+cur[field]) ? add(acc, +cur[field]) : acc
  }, 0)
}

async function handleCalc({ tabType }) {
  try {
    calcBtnLoading.value = true
    const formData = mapTableAction.value?.[tabType]?.formActions?.getFieldsValue()
    if (tabType === 'salesDetailTable' && !yearMonth.value) return createMessage.error('请在弹窗中选择重新计算的年月！')
    const date = tabType !== 'salesDetailTable' ? formData?.yearMonth : yearMonth.value
    const startDate = dayjs(date).startOf('month').format('YYYY-MM-DD')
    const endDate = dayjs(date).endOf('month').format('YYYY-MM-DD')
    const mapType = {
      salesDetailTable: 'sale',
      operationCenterTable: 'product'
    }
    const { message } = await calcSalesDetail({ startDate, endDate, operate: mapType[tabType] ?? 'account' })
    if (message === 'ok') {
      calcBtnLoading.value = false
      createMessage.success('执行完成')
      mapTableAction.value?.[tabType]?.tableAction?.reload()
      yearMonth.value = null
    }
  } catch (err) {
    calcBtnLoading.value = false
    createMessage.error('执行失败')
  } finally {
    popoverVisible.value = false
    calcBtnLoading.value = false
  }
}

async function handleExport({ tabType, tabName, searchInfo }) {
  try {
    exporting.value = true
    const mapFn = {
      onlinePersonalTable: exportPersonal,
      onlineDeptTable: exportDept,
      offlinePersonalTable: exportPersonal,
      offlineDeptTable: exportDept,
      guideTable: exportPersonal,
      salesDetailTable: exportSalesDetail,
      operationCenterTable: exportPersonal
    }
    const params = mapTableAction.value?.[tabType]?.formActions?.getFieldsValue()
    const response = await mapFn[tabType]({ ...params, ...searchInfo })
    // const blob = new Blob([response.data], { type: response.headers['content-type'] })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    downloadLink.download = `${tabName}-${+new Date()}.xlsx`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    createMessage.success('导出成功')
  } catch (err) {
    createMessage.error('导出失败')
    throw new Error(err)
  } finally {
    exporting.value = false
  }
}

async function handleDownload({ url, fileName }: { url: string; fileName: string }) {
  try {
    downloading.value = true
    const response = await axios.get(url, {
      responseType: 'blob'
    })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response.data)
    downloadLink.download = fileName

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
  } catch (e) {
    createMessage.error('下载失败')
  } finally {
    downloading.value = false
  }
}
</script>
<style lang="less">
.achi-popover {
  display: flex;
  flex-wrap: wrap;
  span {
    margin-right: 4%;
    font-size: 15px;
    font-weight: bold;
  }
}
</style>
<style scoped lang="less">
.footer {
  font-size: 15px;
  font-weight: bold;

  span {
    margin-right: 4%;
  }
}
</style>
