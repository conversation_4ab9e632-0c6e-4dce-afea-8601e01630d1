import dayjs from 'dayjs'
import { BasicColumn, FormSchema } from '/@/components/Table'

const typeoption = [
  {
    label: '产品部方案经理',
    value: 1
  },
  {
    label: '运营中心项目方案经理',
    value: 2
  },
  {
    label: '.导购员业绩',
    value: 3
  },
  {
    label: '带单业绩（个人）',
    value: 4
  },
  {
    label: '社媒运营',
    value: 5
  },
  {
    label: '2D设计师',
    value: 6
  },
  {
    label: '3D设计师',
    value: 7
  },
  {
    label: '设计师',
    value: 8
  },
  {
    label: '翻译',
    value: 9
  },
  {
    label: '销售跟单',
    value: 10
  },
  {
    label: '产品部业绩',
    value: 11
  },
  {
    label: '运营中心业绩',
    value: 12
  },
  {
    label: '带单部门业绩',
    value: 13
  }
]

export const columns: BasicColumn[] = [
  {
    dataIndex: 'accountName',
    title: '账户名称',
    width: 100,
    resizable: true
  },
  {
    dataIndex: 'moonNowAmount',
    title: '当月新增核定业绩',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text + '元'
    }
  },
  {
    dataIndex: 'moonOldAmount',
    title: '实际业绩',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text + '元'
    }
  },
  {
    dataIndex: 'completionRate',
    title: '完成率',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text + '%'
    }
  },
  {
    dataIndex: 'departName',
    title: '部门',
    width: 100,
    resizable: true
  }
]
export const schemas: FormSchema[] = [
  {
    field: 'type',
    label: '类型',
    component: 'Select',
    defaultValue: 1,
    componentProps: {
      options: typeoption
    }
  },
  {
    field: 'Date',
    label: '开单日期',
    component: 'SingleRangeDate',
    defaultValue: [dayjs().startOf('month').format('YYYY-MM-DD 00:00:00'), dayjs().endOf('month').format('YYYY-MM-DD 23:59:59')],
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  }
]
