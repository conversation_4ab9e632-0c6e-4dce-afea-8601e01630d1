<template>
  <div> <BasicTable @register="registerTable" :data-cachekey="routePath" /></div>
</template>
<script setup lang="ts" name="/credential/log">
import { useTable, BasicTable } from '/@/components/Table'
import { columns, schemas } from './datas/datas'
import { getlist } from '/@/api/Performance'
import { useRoute } from 'vue-router'

const route = useRoute()
const { path: routePath } = route

const [registerTable] = useTable({
  title: '业绩核算',
  api: getlist,
  columns,
  showTableSetting: true,
  tableSetting: { redo: false },
  rowKey: 'id',
  showIndexColumn: false,
  pagination: {
    pageSize: 10,
    pageSizeOptions: ['10', '50', '100'],
    position: ['bottomRight']
  },
  useSearchForm: true,
  formConfig: {
    schemas,
    baseColProps: { span: 6 },
    labelCol: { span: 4 },
    fieldMapToTime: [['Date', ['startDate', 'endDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  }
})
</script>
