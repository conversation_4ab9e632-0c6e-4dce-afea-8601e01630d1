import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { Tag } from 'ant-design-vue'
import { isNullOrUnDef } from '/@/utils/is'

export const columns: BasicColumn[] = [
  {
    title: '名称',
    dataIndex: 'name',
    width: 200,
    resizable: true
  },
  {
    title: '内容',
    dataIndex: 'value',
    width: 100,
    resizable: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '是否禁用',
    dataIndex: 'is_disabled',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: text == 1 ? 'error' : 'success' }, text == 1 ? '是' : '否')
    }
  }
]

export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '名称',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'is_disabled',
    label: '是否禁用',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    },
    colProps: { span: 8 }
  }
]
