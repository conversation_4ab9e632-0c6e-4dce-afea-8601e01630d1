<template>
  <div>
    <BasicTable @register="registerTable" />
  </div>
</template>
<script setup lang="ts">
import { BasicTable, useTable } from '/@/components/Table'
import { columns, searchFormSchema } from './datass/datas'
import { messageconfiggetList } from '/@/api/admin/systemconfigurationlist'
const [registerTable] = useTable({
  api: messageconfiggetList,
  columns,
  showTableSetting: false,
  showIndexColumn: false,
  useSearchForm: true,
  formConfig: {
    name: 'searchForm',
    labelWidth: 120,
    schemas: searchFormSchema,
    alwaysShowLines: 1
  }
})
</script>
