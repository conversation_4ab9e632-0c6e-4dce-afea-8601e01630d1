import { BasicColumn, FormSchema } from '/@/components/Table'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { isNullOrUnDef } from '/@/utils/is'
import { getStaffList } from '/@/api/erp/systemInfo'

const saleStore = useSaleOrderStore()

export const columns: BasicColumn[] = [
  {
    title: '订单类型',
    dataIndex: 'type',
    width: 200,
    resizable: true,
    customRender: ({ value }) => (isNullOrUnDef(value) ? '-' : saleStore.saleType[value])
  },
  {
    title: '单据单号',
    dataIndex: 'order_strid',
    width: 200,
    resizable: true
  },
  {
    title: '操作类型',
    dataIndex: 'deal_type',
    width: 200,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : value
    }
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    resizable: true
  },
  {
    title: '操作人',
    dataIndex: 'deal_inCharge_name',
    width: 200,
    resizable: true
  },
  {
    title: '单据金额',
    dataIndex: 'order_amount',
    width: 200,
    resizable: true
  },
  {
    title: '操作时间',
    dataIndex: 'created_at',
    width: 200,
    resizable: true
  },
  {
    title: '单据时间',
    dataIndex: 'order_at',
    width: 200,
    resizable: true
  }
]

export const searchFormSchema: FormSchema[] = [
  // {
  //   field: 'sort',
  //   label: '排序方式',
  //   component: 'Select',
  //   colProps: {
  //     span: 8
  //   },
  //   componentProps: {
  //     options: [
  //       {
  //         label: '顺序',
  //         value: 'asc'
  //       },
  //       {
  //         label: '倒序',
  //         value: 'desc'
  //       }
  //     ]
  //   }
  // },
  {
    field: 'type',
    label: '单据类型',
    component: 'Select',
    colProps: {
      span: 8
    },
    componentProps: {
      options: Object.keys(saleStore.saleType).map((key) => {
        return {
          label: saleStore.saleType[key],
          value: key
        }
      })
    }
  },
  {
    field: 'order_strid',
    label: '单据单号',
    component: 'Input',
    colProps: {
      span: 8
    }
  },
  {
    field: 'deal_type',
    label: '操作类型',
    component: 'Input',
    colProps: {
      span: 8
    }
  },
  {
    field: 'deal_inCharge',
    component: 'ApiSelect',
    label: '员工',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    colProps: {
      span: 8
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  }
]

//子表格colmuns删减不需字段方法
function filterColumns(columns, excludeDataIndexes) {
  return columns.filter((item) => !excludeDataIndexes.includes(item.dataIndex))
}

const excludeDataIndexes = ['deal_type', 'deal_inCharge_name', 'order_at', 'remark']
export const childRenColumns = filterColumns(columns, excludeDataIndexes)
