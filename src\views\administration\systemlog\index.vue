<template>
  <div>
    <BasicTable @register="registerTable">
      <template #expandedRowRender="{ record }">
        <BasicTable
          :dataSource="record.item"
          :columns="childRenColumns"
          :pagination="false"
          :showIndexColumn="false"
          :bordered="true"
          :ref="(el) => (expandedRowRefs[record.id] = el)"
        />
      </template>
    </BasicTable>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { columns, childRenColumns, searchFormSchema } from './datas/datas'
import { operateloggetList } from '/@/api/admin/systemlog'
import { BasicTable, useTable, TableActionType } from '/@/components/Table'

const expandedRowRefs = ref<{ [key: string]: TableActionType | Nullable<any> }>({})

const [registerTable] = useTable({
  title: '系统日志',
  api: operateloggetList,
  columns,
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema
  },
  useSearchForm: true,
  showTableSetting: true,
  bordered: true,
  showIndexColumn: false,
  pagination: {
    pageSize: 10,
    position: ['bottomRight']
  }
})
</script>
