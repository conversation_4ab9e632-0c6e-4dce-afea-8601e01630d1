import { BasicColumn, FormSchema } from '/@/components/Table'
import { getStaffList } from '/@/api/erp/systemInfo'
import { h } from 'vue'

import { JsonPreview } from '/@/components/CodeEditor'
import { message } from 'ant-design-vue'

export const columns: BasicColumn[] = [
  {
    title: '日志编号',
    dataIndex: 'id',
    width: 100,
    resizable: true
  },
  {
    title: 'ip地址',
    dataIndex: 'ip',
    width: 200,
    resizable: true
  },
  {
    title: '请求地址',
    dataIndex: 'url',
    width: 200,
    resizable: true
  },
  {
    title: '请求方法',
    dataIndex: 'method',
    width: 100,
    resizable: true
  },
  {
    title: '用户名',
    dataIndex: 'user_name',
    width: 200,
    resizable: true
  },
  {
    title: '用户id',
    dataIndex: 'user_id',
    width: 100,
    resizable: true
  },
  {
    title: '操作日期',
    dataIndex: 'created_at',
    width: 200,
    resizable: true
  },
  {
    title: '操作内容',
    dataIndex: 'request_data',
    width: 200,
    resizable: true,
    customRender: ({ value }) => {
      return h('div', { onClick: () => copyText(value) }, value ? renderJsonPreview(value) : '')
    }
  }
]

export const searchFormSchema: FormSchema[] = [
  // {
  //   field: 'sort',
  //   label: '排序方式',
  //   component: 'Select',
  //   colProps: {
  //     span: 8
  //   },
  //   componentProps: {
  //     options: [
  //       {
  //         label: '顺序',
  //         value: 'asc'
  //       },
  //       {
  //         label: '倒序',
  //         value: 'desc'
  //       }
  //     ]
  //   }
  // },
  {
    field: 'user_id',
    label: '用户',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 8
    }
  },
  {
    field: 'ip',
    label: 'ip地址',
    component: 'Input',
    colProps: {
      span: 8
    }
  },
  {
    field: 'method',
    label: '请求方法',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: 'GET',
          value: 'GET'
        },
        {
          label: 'POST',
          value: 'POST'
        }
        // {
        //   label: 'PUT',
        //   value: 'PUT'
        // },
        // {
        //   label: 'DELETE',
        //   value: 'DELETE'
        // }
      ]
    },
    colProps: {
      span: 8
    }
  },
  {
    field: 'url',
    label: '请求地址',
    component: 'Input',
    colProps: {
      span: 8
    }
  },
  {
    field: 'created_at',
    label: '操作日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      startPickerProps: {
        showTime: true
      },
      endPickerProps: {
        showTime: true
      }
    }
  }
]

/**
 * 使用JsonPreview组件  方便预览JSON
 * @param json json字符串/obj
 * @returns 能转为json返回JsonPreview 否则返回自身
 */
function renderJsonPreview(json: any) {
  if (!json) return ''
  if (typeof json === 'object') return h(JsonPreview, { data: json })

  if (typeof json === 'string') {
    try {
      const data = JSON.parse(json)
      return h(JsonPreview, { data })
    } catch (e) {
      return json
    }
  }
}

//复制文本到剪切板
export function copyText(text: string, prompt: string | null = '已成功复制到剪切板!') {
  if (navigator.clipboard) {
    return navigator.clipboard
      .writeText(text)
      .then(() => {
        prompt && message.success(prompt)
      })
      .catch((error) => {
        message.error('复制失败!' + error.message)
        return error
      })
  }
  if (Reflect.has(document, 'execCommand')) {
    return new Promise<void>((resolve, reject) => {
      try {
        const textArea = document.createElement('textarea')
        textArea.value = text
        // 在手机 Safari 浏览器中，点击复制按钮，整个页面会跳动一下
        textArea.style.width = '0'
        textArea.style.position = 'fixed'
        textArea.style.left = '-999px'
        textArea.style.top = '10px'
        textArea.setAttribute('readonly', 'readonly')
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)

        prompt && message.success(prompt)
        resolve()
      } catch (error) {
        message.error('复制失败!' + error.message)
        reject(error)
      }
    })
  }
  return Promise.reject(`"navigator.clipboard" 或 "document.execCommand" 中存在API错误, 拷贝失败!`)
}
