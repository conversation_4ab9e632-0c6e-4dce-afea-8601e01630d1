<template>
  <div>
    <BasicTable @register="registerTable" />
  </div>
</template>
<script setup lang="ts">
import { columns, searchFormSchema } from './datas/datas'
import { operateloggetListByUser } from '/@/api/admin/useroperationlog'
import { BasicTable, useTable } from '/@/components/Table'

const [registerTable] = useTable({
  title: '用户操作日志',
  api: operateloggetListByUser,
  columns,
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema,
    baseColProps: {
      span: 7
    },
    actionColOptions: {
      span: 24
    },
    fieldMapToTime: [['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm:ss']]]
  },
  useSearchForm: true,
  showTableSetting: true,
  bordered: true,
  showIndexColumn: false,
  pagination: {
    pageSize: 10,
    position: ['bottomRight']
  }
})
</script>
