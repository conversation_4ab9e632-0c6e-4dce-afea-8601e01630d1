<template>
  <BasicDrawer destroyOnClose @register="registerDrawer" v-bind="$attrs" showFooter width="30%" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { schemas } from '/@/views/baseData/ChanneL/datas/drawer.data'
import { getChanneLcreate, getChanneLupdate } from '/@/api/baseData/ChanneL'

const emits = defineEmits(['success', 'register', 'reloadSub'])

//type
const type = ref('')
//更新id
const updatedid = ref()
const [registerDrawer, { closeDrawer, changeLoading, changeOkLoading }] = useDrawerInner(async (data) => {
  try {
    console.log(data)
    await changeLoading(true)
    setFieldsValue(data.record)
    type.value = data.type
    if (data.record) {
      updatedid.value = data.record.id
    }
    console.log(type.value)
  } catch (e) {
    console.error(e)
  } finally {
    changeLoading(false)
  }
})

const [registerForm, { validate, setFieldsValue }] = useForm({
  actionColOptions: { span: 24 },
  baseColProps: { span: 24 },
  labelWidth: 120,
  colon: true,
  showActionButtonGroup: false,
  schemas
})
async function handleSubmit() {
  try {
    await changeOkLoading(true)
    const fromdata = await validate()
    const res = type.value == 'add' ? await getChanneLcreate(fromdata) : await getChanneLupdate({ ...fromdata, id: updatedid.value })
    console.log(res)
    if (res.msg == 'success') {
      emits('success')
      closeDrawer()
      changeOkLoading(false)
    }
  } catch (e) {
    changeOkLoading(false)
    throw new Error(e)
  }
}
</script>
