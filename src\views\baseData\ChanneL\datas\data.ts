import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { Tag } from 'ant-design-vue'
export const mapStatus = {
  0: { label: '启用', color: 'green' },
  1: { label: '禁用', color: 'red' }
}

export const columns: BasicColumn[] = [
  {
    title: '渠道来源名称',
    dataIndex: 'name',
    width: 180
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 180
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ value }) => {
      return h(Tag, { color: mapStatus[value].color }, () => mapStatus[value].label)
    }
  }
]

export const searchFormSchemas: FormSchema[] = [
  {
    field: 'name',
    label: '渠道来源名称',
    component: 'Input'
  },
  {
    field: 'remark',
    label: '备注',
    component: 'Input'
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 }
      ]
    }
  }
]
