import { FormSchema } from '/@/components/Form'

export const schemas: FormSchema[] = [
  {
    field: 'name',
    label: '渠道来源名称',
    component: 'Input',
    required: true
  },

  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 }
      ]
    },
    defaultValue: 1,
    required: true
  },
  {
    field: 'remark',
    label: '备注',
    component: 'Input'
  }
]
