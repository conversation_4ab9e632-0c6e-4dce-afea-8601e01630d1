<template>
  <div>
    <BasicTable class="p-4" bordered @register="registerTable">
      <template #toolbar>
        <a-button v-if="hasPermission([217])" type="primary" @click="handleCreate({ type: 'add' })">新增</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <SubjectDrawer @register="registerDrawer" @success="reload" />
  </div>
</template>

<script setup lang="ts">
import { ActionItem, BasicTable, TableAction, useTable } from '/@/components/Table'
import { columns, searchFormSchemas } from './datas/data'
import { useDrawer } from '/@/components/Drawer'
import SubjectDrawer from './components/SubjectDrawer.vue'
import { DataRolesList } from '/@/api/dataArchive/model/types'
import { usePermission } from '/@/hooks/web/usePermission'
import { getChanneLgetList } from '/@/api/baseData/ChanneL'

const { hasPermission } = usePermission()
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
//注册表格
const [registerTable, { reload }] = useTable({
  title: '渠道来源列表',
  columns,
  useSearchForm: true,
  formConfig: {
    colon: true,
    labelWidth: 150,
    autoAdvancedLine: 1,
    showAdvancedButton: true,
    schemas: searchFormSchemas,
    submitButtonOptions: { text: '搜索' },
    resetButtonOptions: { text: '重置' },
    baseColProps: { span: 6 }
  },
  rowKey: 'id',
  api: getChanneLgetList,
  showTableSetting: true,
  showIndexColumn: false,
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  }
})

function handleCreate(data) {
  openDrawer(true, data)
  setDrawerProps({ title: '创建渠道来源' })
}

function handleEdit(data) {
  openDrawer(true, data)
  setDrawerProps({ title: '编辑渠道来源' })
}

function createActions(record: DataRolesList) {
  return [
    {
      label: '编辑',
      onClick: handleEdit.bind(null, { type: 'edit', record }),
      ifShow: hasPermission([218])
    }
  ] as ActionItem[]
}
</script>
