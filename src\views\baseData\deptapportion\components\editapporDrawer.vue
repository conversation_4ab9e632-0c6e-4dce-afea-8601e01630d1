<template>
  <BasicDrawer @register="registerDrawer" title="新增" width="30%" show-footer @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { schemas } from '../datas/edit.data'
import { deptappronAdd } from '/@/api/baseData/deptapporton'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
const emit = defineEmits(['registerDrawer', 'reload'])

const [registerDrawer, { changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  console.log(data)
  resetFields()
})

const [registerForm, { validate, resetFields }] = useForm({
  schemas,
  labelWidth: 100,
  actionColOptions: { span: 24 },
  baseColProps: { span: 24 },
  colon: true,
  showActionButtonGroup: false
})

async function handleSubmit() {
  try {
    changeOkLoading(true)
    const formdata = await validate()
    deptappronAdd(formdata)
    console.log(formdata)
    setTimeout(() => {
      changeOkLoading(false)
      closeDrawer()
      emit('reload')
    }, 1000)
  } catch (e) {
    changeOkLoading(false)
    console.log(e)
  }
}
</script>
