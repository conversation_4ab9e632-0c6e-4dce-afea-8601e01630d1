import type { BasicColumn } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'

export const columns: BasicColumn[] = [
  {
    title: '分摊部门',
    dataIndex: 'department',
    width: 120,
    resizable: true
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 120,
    resizable: true
  },

  {
    title: '分摊年月',
    dataIndex: 'pay_year_month',
    width: 120,
    resizable: true
  },
  {
    title: '分摊模式',
    dataIndex: 'share_setting_name',
    width: 120,
    resizable: true
  },

  {
    title: '是否启用',
    dataIndex: 'is_disabled',
    width: 120,
    resizable: true,
    customRender(opt) {
      return useRender.renderTag(opt.record.is_disabled ? '禁用' : '启用', opt.record.is_disabled ? 'red' : 'green')
    }
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 120,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 120,
    resizable: true
  }
]
