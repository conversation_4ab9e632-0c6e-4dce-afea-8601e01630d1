import { getDeptTree } from '/@/api/admin/dept'
import { getShareManageList } from '/@/api/baseData/shareManage'
import { FormSchema } from '/@/components/Form'

export const schemas: FormSchema[] = [
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    required: true,
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },

  {
    field: 'share_setting_id',
    required: true,
    component: 'PagingApiSelect',
    label: '分摊模式',
    componentProps: () => {
      return {
        api: getShareManageList,
        resultField: 'items',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'id',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'account_name',
          // itEmpty: true,
          allowClear: true
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'is_disabled',
    label: '是否启用',
    component: 'RadioButtonGroup',
    required: true,
    defaultValue: 0,
    componentProps: {
      options: [
        {
          label: '是',
          value: 0
        },
        {
          label: '否',
          value: 1
        }
      ]
    }
  },
  {
    field: 'pay_year_month',
    label: '支付年月',
    component: 'MonthPicker',
    componentProps: {
      valueFormat: 'YYYYMM'
    },
    required: true
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    required: true,
    componentProps: {
      placeholder: '请输入备注'
    }
  }
]
