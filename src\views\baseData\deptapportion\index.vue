<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button v-if="hasPermission([492])" type="primary" @click="handleAdd">新增</a-button>
      </template>
    </BasicTable>
    <EditapporDrawer @register="registerDrawer" @reload="reload" />
  </div>
</template>
<script setup lang="ts">
import { columns } from './datas/datas'
import { deptappronList } from '/@/api/baseData/deptapporton'
import { BasicTable, useTable } from '/@/components/Table'
import EditapporDrawer from './components/editapporDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { usePermission } from '/@/hooks/web/usePermission'
const { hasPermission } = usePermission()

const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()

const [registerTable, { reload }] = useTable({
  title: '部门分摊关系列表',
  api: deptappronList,
  showIndexColumn: false,
  columns,
  showTableSetting: true,
  useSearchForm: true,
  rowKey: 'id',
  formConfig: {
    labelWidth: 120,
    actionColOptions: {
      span: 24
    },
    baseColProps: {
      span: 6
    }
    // schemas
  }
  //   actionColumn: {
  //     width: 130,
  //     title: '操作',
  //     dataIndex: 'action'
  //   }
})

function handleAdd() {
  console.log('新增')
  openDrawer(true, {
    type: 'add'
  })
  setDrawerProps({
    title: '新增'
  })
}
</script>
