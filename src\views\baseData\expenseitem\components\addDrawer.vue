<template>
  <BasicDrawer @register="registerDrawer" width="30%" show-footer @ok="handleOk">
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { schemas } from '../datas/form.data'
import { expenpostupdate } from '/@/api/baseData/expenseitem'

const emit = defineEmits(['register', 'reload'])

const [registerDrawer, { closeDrawer, changeOkLoading, changeLoading }] = useDrawerInner(() => {
  resetFields()
})
const [registerForm, { validate, resetFields }] = useForm({
  schemas,
  labelWidth: 100,
  actionColOptions: { span: 24 },
  baseColProps: { span: 24 },
  colon: true,
  showActionButtonGroup: false
})

async function handleOk() {
  try {
    changeLoading(true)
    changeOkLoading(true)
    const formdata = await validate()
    expenpostupdate(formdata)
    setTimeout(() => {
      closeDrawer()
      emit('reload')
      changeOkLoading(false)
      changeLoading(false)
    }, 1000)
  } catch (e) {
    changeOkLoading(false)
    changeLoading(false)
    console.log(e)
  }
}
</script>
