import { isNull } from 'lodash-es'
import { BasicColumn, FormSchema } from '/@/components/Table'

// import { useUserStoreWithOut } from '/@/store/modules/user'
import { expenmentType } from '/@/views/financialDocuments/common'
import { getCategory } from '/@/api/financialDocuments/otherIncome'
import { GET_STATUS_SCHEMA } from '/@/const/status'

// const userStore = useUserStoreWithOut()

export const columns = (reload?): BasicColumn[] => [
  {
    title: '费用项目名称',
    dataIndex: 'name',
    width: 120,
    resizable: true
  },
  {
    title: '状态',
    dataIndex: 'is_disabled',
    width: 120,
    resizable: true,
    customRender: ({ text, record }) => {
      // 当前人角色
      //   const roleValue = userStore.getUserInfo?.roleValue
      // 可以修改的角色
      //   const changeRoles = ['treasurer', 'financia_review', 'finance', 'super_admin', 'developer', 'cashier']
      // 判断本人的角色是财务相关、开发者、超管其中之一才能改
      // const isChange = changeRoles.includes(roleValue) ? record.clause == 2 : record.clause == 2 && record.is_check == 0
      return !isNull(text) ? expenmentType(record.id, text, true, reload) : '-'
    }
  },
  {
    title: '科目名称',
    dataIndex: 'account_name',
    width: 120,
    resizable: true
  },
  {
    title: '科目代码',
    dataIndex: 'account_code',
    width: 120,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 120
  }
]

const status_schema = GET_STATUS_SCHEMA(
  [
    { label: '启用', value: 0 },
    { label: '禁用', value: 1 }
  ],
  {
    field: 'is_disabled'
  }
)

export const tabchemas: FormSchema[] = [
  status_schema,
  {
    field: 'name',
    label: '费用项目名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入费用项目名称'
    }
  },
  {
    field: 'account_name',
    label: '科目名称',
    component: 'ApiSelect',
    componentProps: {
      api: getCategory,
      resultField: 'items',
      selectProps: {
        showSearch: true,
        allowClear: true,
        placeholder: '请选择科目名称',
        fieldNames: {
          account_code: 'account_code',
          value: 'account_name',
          label: 'account_name'
        },
        optionFilterProp: 'account_name'
      }
    }
  },
  {
    field: 'remark',
    label: '备注',
    component: 'Input',
    componentProps: {
      placeholder: '请输入备注'
    }
  }
  // {
  //   field: 'is_disabled',
  //   label: '状态',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       {
  //         label: '禁用',
  //         value: 1
  //       },
  //       {
  //         label: '启用',
  //         value: 0
  //       }
  //     ]
  //   }
  // }
]
