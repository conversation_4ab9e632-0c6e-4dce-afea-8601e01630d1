import { getCategory } from '/@/api/financialDocuments/otherIncome'
import { FormSchema } from '/@/components/Form'

export const schemas: FormSchema[] = [
  {
    field: 'name',
    label: '费用项目名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入费用项目名称'
    },
    rules: [{ required: true, message: '请输入费用项目名称' }]
  },
  {
    field: 'is_disabled',
    label: '状态',
    component: 'RadioButtonGroup',
    required: true,
    defaultValue: 0,
    componentProps: {
      options: [
        {
          label: '启用',
          value: 0
        },
        {
          label: '禁用',
          value: 1
        }
      ]
    }
  },
  {
    field: 'account_name',
    label: '科目名称',
    component: 'PagingApiSelect',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        api: getCategory,
        resultField: 'items',
        labelField: 'account_name',
        valueField: 'account_name',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'account_name',
            label: 'account_name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'account_name',
          allowClear: true
        },
        params: {
          status: 1
        },
        onChange: (val: number, shall) => {
          if (!shall) return
          if (shall) {
            formModel.account_code = shall.account_code
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'account_code',
    label: '科目代码',
    component: 'InputNumber',
    required: true,
    componentProps: {
      disabled: true
    }
  },
  {
    field: 'remark',
    label: '备注',
    component: 'Input',
    componentProps: {
      placeholder: '请输入备注'
    },
    rules: [{ required: true, message: '请输入备注' }]
  }
]
