<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleAdd" v-if="hasPermission([472])">新增</a-button>
      </template>
    </BasicTable>
    <addDrawer @register="registerDrawer" @reload="reload" />
  </div>
</template>
<script setup lang="ts">
import { BasicTable, useTable } from '/@/components/Table'
import addDrawer from './components/addDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { columns, tabchemas } from './datas/data'
import { expengetList } from '/@/api/baseData/expenseitem'
import { nextTick, onMounted } from 'vue'
import { usePermission } from '/@/hooks/web/usePermission'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'

const { hasPermission } = usePermission()
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()

onMounted(() => {
  nextTick(() => {
    setColumns(columns(reload))
  })
})

const [registerTable, { reload, setColumns }] = useTable({
  api: expengetList,
  columns: columns(),
  title: '费用项目',
  showTableSetting: true,
  showIndexColumn: false,
  useSearchForm: true,
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas: tabchemas
  }
})

function handleAdd() {
  openDrawer(true, { type: 'add' })
  setDrawerProps({ title: '新增费用项目' })
}
</script>
