<template>
  <ScrollContainer>
    <div class="m-4 mr-0 bg-white">
      <BasicTree
        title="档案名称"
        toolbar
        search
        treeWrapperClassName="h-[calc(100%-35px)] overflow-auto"
        :clickRowToExpand="false"
        :treeData="treeData"
        :fieldNames="{ key: 'a_id', title: 'name' }"
        @select="handleSelect"
        :style="{ fontSize: '14px' }"
      />
    </div>
  </ScrollContainer>
</template>
<script lang="ts" setup>
import { getFundsLevel } from '/@/api/baseData/fundsArchive'

import { ScrollContainer } from '/@/components/Container/index'
import { onMounted, ref } from 'vue'
import { BasicTree, TreeItem } from '/@/components/Tree'
import { handleTree } from '/@/utils/tree'

const emit = defineEmits(['select'])
const treeData = ref<TreeItem[]>([])

async function fetch(noCache = 0) {
  try {
    const res: any = await getFundsLevel({ noCache })

    treeData.value = handleTree(res, 'a_id')
  } catch (err) {
    console.log(err)
  }
}

function handleSelect(keys) {
  emit('select', keys[0])
}

onMounted(() => {
  fetch()
})

defineExpose({ fetch })
</script>
