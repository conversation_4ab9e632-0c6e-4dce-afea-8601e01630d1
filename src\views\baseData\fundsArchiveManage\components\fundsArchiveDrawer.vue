<template>
  <BasicDrawer @register="registerDrawer" v-bind="$attrs" width="40%" destroyOnClose show-footer @ok="handleOk">
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { updateFunds } from '/@/api/baseData/fundsArchive'
import { ref } from 'vue'
import { getSchemas } from '../datas/drawer.datas'
import { message } from 'ant-design-vue'

const emits = defineEmits(['success', 'register'])
const propsData = ref({})
const [registerDrawer, { closeDrawer }] = useDrawerInner(async (data) => {
  console.log(data)
  resetFields()
  resetSchema(getSchemas)
  propsData.value = data
  if (data.type === 'edit') {
    setFieldsValue(data.record)
  }
})

const [registerForm, { setFieldsValue, validate, resetSchema, resetFields }] = useForm({
  // schemas: getSchemas('add'),
  labelWidth: 100,
  actionColOptions: { span: 24 },
  baseColProps: { span: 24 },
  colon: true,
  showActionButtonGroup: false
})

async function handleOk() {
  try {
    const values = await validate()
    console.log(values)

    const { msg } = await updateFunds(values)
    if (msg === 'success') {
      message.success('修改成功')
      emits('success')
      closeDrawer()
    }
  } catch (e) {
    throw new Error(JSON.stringify(e))
  }
}
</script>
