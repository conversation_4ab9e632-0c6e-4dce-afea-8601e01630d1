import { h } from 'vue'
import { BasicColumn } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { useI18n } from '/@/hooks/web/useI18n'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'
const { t } = useI18n()

export const typeSelect = {
  1: { label: '现金', value: 1 },
  2: { label: '微信支付宝', value: 2 },
  3: { label: '银行', value: 3 },
  4: { label: '阿里平台资金', value: 4 },
  5: { label: '银行公户', value: 5 }
}

export const isOutFundSelect = {
  1: { label: '是', value: 1 },
  0: { label: '否', value: 0 }
}

export const columns: BasicColumn[] = [
  {
    title: '名称',
    dataIndex: 'name'
  },
  {
    title: '资金账号',
    dataIndex: 'account',
    customRender({ value }) {
      return isNullOrUnDef(value) ? '-' : value.replace(/\B(?=(\d{4})+(?!\d))/g, ' ')
    }
  },
  {
    title: '部门',
    dataIndex: 'department'
  },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ value }) =>
      isNullOrUnDef(value) ? '' : useRender.renderTag(t(`tag.reverseStatus.${value}`), t(`tag.reverseColors.${value}`))
  },
  {
    title: '是否对外收款',
    dataIndex: 'is_out_fund',
    customRender: ({ value }) => {
      return h(Tag, { color: value === 1 ? 'green' : 'red' }, value === 1 ? '是' : '否')
    }
  },
  {
    title: '是否公户',
    dataIndex: 'is_public_account',
    customRender: ({ value }) => {
      return h(Tag, { color: value === 1 ? 'green' : 'red' }, value === 1 ? '是' : '否')
    }
  },
  {
    title: '资金类型',
    dataIndex: 'type',
    customRender({ value }) {
      return isNullOrUnDef(value) ? '' : h('span', {}, typeSelect[value]?.label)
    }
  },

  {
    title: '银行名称',
    dataIndex: 'bank_name'
  },
  {
    title: '银行账户',
    dataIndex: 'bank'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at'
  }
]
