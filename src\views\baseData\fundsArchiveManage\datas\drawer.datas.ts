import { typeSelect, isOutFundSelect } from './data'
import { getDeptSelectTree } from '/@/api/admin/dept'
import { FormSchema } from '/@/components/Form'

export const getSchemas: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false
  },
  {
    field: 'name',
    label: '资金名称',
    component: 'Input',
    required: true
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    required: true,
    componentProps: {
      api: getDeptSelectTree,
      immediate: false,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        placeholder: '请选择',
        showSearch: true,
        optionFilterProp: 'name',
        treeDefaultExpandAll: true,
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  {
    field: 'account',
    label: '资金账号',
    component: 'Input'
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 }
      ]
    },
    defaultValue: 1,
    required: true
  },
  // {
  //   field: 'amount_start',
  //   label: '期初数据',
  //   component: 'InputNumber',
  //   show: type === 'add',
  //   defaultValue: 0,
  //   required: type === 'add'
  // },
  {
    field: 'is_out_fund',
    label: '对外收款',
    component: 'Select',
    required: true,
    componentProps: {
      options: Object.values(isOutFundSelect)
    }
  },
  {
    field: 'is_public_account',
    label: '是否公户',
    component: 'Select',
    required: true,
    componentProps: {
      options: Object.values(isOutFundSelect)
    }
  },
  {
    field: 'type',
    label: '资金类型',
    component: 'Select',
    componentProps: {
      options: Object.values(typeSelect)
    },
    defaultValue: 1
    // required: true
  },
  {
    field: 'bank_name',
    label: '银行账号',
    component: 'Input'
  },
  {
    field: 'bank',
    label: '银行账户',
    component: 'Input'
  }
]
