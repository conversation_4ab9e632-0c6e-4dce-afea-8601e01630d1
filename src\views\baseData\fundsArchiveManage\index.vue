<!-- 资金档案 -->
<template>
  <div>
    <BasicTable class="p-4" @register="registerTable">
      <template #toolbar>
        <a-button v-if="hasPermission([215])" type="primary" @click="handleCreate">新增</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <FundsArchive @register="registerDrawer" @success="reload" />
  </div>
</template>

<script setup lang="ts" name="fundsArchive">
import { getFundsList } from '/@/api/baseData/fundsArchive'
import resizeableColumns from '/@/utils/erp/resizeableColumns'
import { ActionItem, BasicTable, EditRecordRow, TableAction, useTable } from '/@/components/Table'
import FundsArchive from './components/fundsArchiveDrawer.vue'
import { columns } from './datas/data'
import { useDrawer } from '/@/components/Drawer'
import { usePermission } from '/@/hooks/web/usePermission'
import { getDeptSelectTree } from '/@/api/admin/dept'

const { hasPermission } = usePermission()
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()

//注册表格
const [registerTable, { reload }] = useTable({
  title: '资金档案',
  columns: resizeableColumns(columns),
  api: getFundsList,
  showTableSetting: true,
  showIndexColumn: false,
  useSearchForm: true,

  formConfig: {
    baseColProps: { span: 8 },
    labelWidth: 100,
    schemas: [
      {
        field: 'name',
        label: '资金名称',
        component: 'Input'
      },
      {
        field: 'dept_id',
        label: '部门',
        component: 'ApiTreeSelect',
        componentProps: {
          api: getDeptSelectTree,
          immediate: false,
          lazyLoad: true,
          treeSelectProps: {
            fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
            placeholder: '请选择',
            showSearch: true,
            optionFilterProp: 'name',
            treeDefaultExpandAll: true,
            filterTreeNode: (search, item) => {
              if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
              return false
            }
          }
        }
      }
    ]
  },
  actionColumn: {
    width: 80,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  }
})

function createActions(record: EditRecordRow): ActionItem[] {
  return [
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      ifShow: hasPermission([216])
    }
  ]
}

function handleCreate() {
  setDrawerProps({ title: '创建' })
  openDrawer(true, { type: 'add' })
}

function handleEdit(record) {
  setDrawerProps({ title: '编辑' })
  openDrawer(true, { type: 'edit', record })
}

// function onRedo() {
//   nextTick(() => {
//     LevelTreeRef.value.fetch(1)
//     reload()
//   })
// }
</script>
