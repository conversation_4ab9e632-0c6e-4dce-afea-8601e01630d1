<template>
  <BasicDrawer @register="registerDrawer" width="30%" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { schemas } from '../datas/edit.data'
import { pointsupdate } from '/@/api/baseData/pointmanagement'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'

const init_id = ref(null)
const type = ref()
const emit = defineEmits(['success', 'registerDrawer'])
const [registerDrawer, { closeDrawer, changeOkLoading }] = useDrawerInner(async (data) => {
  console.log(data)
  resetFields()
  setFieldsValue(data.record)
  type.value = data.type
  init_id.value = data.record.id
})

const [registerForm, { validate, resetFields, setFieldsValue }] = useForm({
  schemas,
  labelWidth: 140,
  baseColProps: { span: 24 },
  showActionButtonGroup: false
})

async function handleSubmit() {
  try {
    changeOkLoading(true)
    const formdata = await validate()
    const params = {
      ...formdata,
      id: type.value === 'edit' ? init_id.value : undefined
    }
    const { msg } = await pointsupdate(params)
    console.log(msg)
    emit('success')
    closeDrawer()
    changeOkLoading(false)
  } catch (e) {
    console.log(e)
    changeOkLoading(false)
  }
}
</script>
