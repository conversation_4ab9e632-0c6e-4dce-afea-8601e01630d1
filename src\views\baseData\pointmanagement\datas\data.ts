import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'
import { GET_STATUS_SCHEMA } from '/@/const/status'
export const types = {
  1: { label: '交付', color: 'blue' }
}

export const columns: BasicColumn[] = [
  {
    title: '类型',
    dataIndex: 'type',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : h(Tag, { color: types[value].color }, () => types[value]?.label)
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    resizable: true,

    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : h(Tag, { color: value == 1 ? 'green' : 'red' }, () => (value == 1 ? '已审核' : '未审核'))
    }
  },
  {
    title: '是否禁用',
    dataIndex: 'is_disabled',
    width: 120,
    resizable: true,

    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : h(Tag, { color: value == 1 ? 'red' : 'green' }, () => (value == 1 ? '禁用' : '启用'))
    }
  },
  {
    title: '工作指标',
    dataIndex: 'target',
    width: 120,
    resizable: true
  },
  {
    title: '得分权重',
    dataIndex: 'points',
    width: 120,
    resizable: true
  },
  {
    title: '交付标准描述',
    dataIndex: 'desc',
    width: 120,
    resizable: true
  },
  {
    title: '是否填写系统单号',
    dataIndex: 'is_strid',
    width: 120,
    customRender: ({ value }) => {
      return value == 1 ? '是' : '否'
    },
    resizable: true
  },
  {
    title: '是否需要上传附件',
    dataIndex: 'is_file',
    width: 120,
    customRender: ({ value }) => {
      return value == 1 ? '是' : '否'
    },
    resizable: true
  },
  {
    title: '是否项目通知推送',
    dataIndex: 'is_send',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return value == 1 ? '是' : '否'
    }
  },
  {
    title: '是否填写描述',
    dataIndex: 'is_content',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return value == 1 ? '是' : '否'
    }
  }
]

const status_schema = GET_STATUS_SCHEMA([
  { label: '已审核', value: 1 },
  { label: '未审核', value: 0 }
])

export const schemas: FormSchema[] = [
  status_schema,
  {
    field: 'type',
    label: '类型',
    component: 'Select',
    componentProps: {
      options: [{ label: '交付', value: 1 }]
    }
  },
  // {
  //   field: 'status',
  //   label: '状态',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: '已审核', value: 1 },
  //       { label: '未审核', value: 0 }
  //     ]
  //   }
  // },
  {
    field: 'target',
    label: '工作指标',
    component: 'Input'
  },
  {
    field: 'points',
    label: '得分权重',
    component: 'InputNumber'
  },
  {
    field: 'is_disabled',
    label: '是否禁用',
    component: 'Select',
    componentProps: {
      options: [
        { label: '禁用', value: 1 },
        { label: '启用', value: 0 }
      ]
    }
  },
  {
    field: 'is_strid',
    label: '是否填写系统单号',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'is_file',
    label: '是否需要上传附件',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'is_send',
    label: '是否项目通知推送',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'is_content',
    label: '是否填写描述',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  }
]
