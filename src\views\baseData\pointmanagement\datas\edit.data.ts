import { types } from './data'
import { FormSchema } from '/@/components/Form'

export const schemas: FormSchema[] = [
  {
    field: 'type',
    label: '类型',
    component: 'Select',
    required: true,
    componentProps: {
      options: Object.keys(types).map((key) => {
        return { label: types[key].label, value: parseInt(key, 10) }
      })
    }
  },
  {
    field: 'target',
    label: '工作指标',
    required: true,
    component: 'Input'
  },
  {
    field: 'points',
    label: '得分权重',
    required: true,
    component: 'InputNumber',
    componentProps: {
      min: 0,
      max: 100
    }
  },
  {
    field: 'is_strid',
    label: '是否填写系统单号',
    required: true,
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  },
  {
    field: 'is_file',
    label: '是否需要上传附件',
    required: true,
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  },
  {
    field: 'is_send',
    label: '是否项目通知推送',
    required: true,
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  },
  {
    field: 'is_content',
    label: '是否填写描述',
    required: true,
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  },
  {
    field: 'desc',
    label: '交付描述',
    required: true,
    component: 'InputTextArea'
  }
]
