<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleStatus" v-if="hasPermission([563])"> 批量审核 </a-button>
        <a-button type="primary" @click="handleDisable('enable')" v-if="hasPermission([564])"> 批量启用 </a-button>
        <a-button type="primary" @click="handleDisable('disable')" v-if="hasPermission([565])"> 批量禁用 </a-button>
        <a-button type="primary" @click="handleAdd" v-if="hasPermission([562])"> 新增 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <editDrawer @register="registereditDrawer" @success="reload" />
  </div>
</template>
<script setup lang="ts">
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import { usePermission } from '/@/hooks/web/usePermission'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { pointsgetList, pointssetIsDisabled, pointssetStatus } from '/@/api/baseData/pointmanagement'
import { columns, schemas } from './datas/data'
import { message } from 'ant-design-vue'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'

const { hasPermission } = usePermission()
const [registereditDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerTable, { reload, getSelectRows }] = useTable({
  title: '积分管理',
  api: pointsgetList,
  showIndexColumn: false,
  columns,
  showTableSetting: true,
  useSearchForm: true,
  rowKey: 'id',
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas
  },
  rowSelection: {
    // type: 'checkbox',
  }
})

function createActions(record: EditRecordRow): ActionItem[] {
  return [
    {
      label: '详情',
      onClick: handleDetail.bind(null, record)
    },
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      disabled: record.status == 1 || record.is_disabled == 1,
      ifShow: hasPermission([566])
    }
  ]
}

function handleAdd() {
  openDrawer(true, { type: 'add' })
  setDrawerProps({ title: '新增积分规则', showFooter: true })
}
function handleDetail(record) {
  openDrawer(true, { record, type: 'detail' })
  setDrawerProps({ title: '积分规则详情', showFooter: false })
}
function handleEdit(record) {
  openDrawer(true, { record, type: 'edit' })
  setDrawerProps({ title: '编辑积分规则', showFooter: true })
}
async function handleStatus() {
  try {
    const row = await getSelectRows()
    if (row.length == 0) {
      message.error('请选择积分规则')
      return
    }
    for (const item of row) {
      if (item.status == 1) {
        message.error('选中积分规则中存在已审核规则,不得再次审核')
        return
      }
    }
    const ids = row.map((items) => {
      return {
        id: items.id,
        status: 1
      }
    })
    await pointssetStatus({ ids: ids })
    await reload()
  } catch (e) {
    console.log(e)
  }
}
async function handleDisable(type) {
  try {
    const row = await getSelectRows()
    console.log(row)

    if (row.length == 0) {
      message.error('请选择积分规则')
      return
    }
    for (const item of getSelectRows()) {
      console.log(item)

      if (item.is_disabled == 1 && type == 'disable') {
        message.error('选中积分规则中存在已禁用规则,不得再次禁用规则')
        return
      }
      if (item.is_disabled == 0 && type == 'enable') {
        message.error('选中积分规则中存在已启用规则,不得再次启用规则')
        return
      }
    }
    const ids = row.map((items) => {
      return {
        id: items.id,
        is_disabled: type === 'enable' ? 0 : 1
      }
    })
    await pointssetIsDisabled({ ids: ids })
    await reload()
  } catch (e) {
    console.log(e)
  }
}
</script>
