<template>
  <BasicDrawer @register="registerDrawer" width="60%" @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg2Stocking"
          :custom-request="handleFileRequest"
          :multiple="true"
          :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
        >
          <a-button type="primary">
            <upload-outlined />
            Upload
          </a-button>
        </Upload>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { schemas } from '../datas/edit.data'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { Upload, UploadFile } from 'ant-design-vue'
import { UploadOutlined } from '@ant-design/icons-vue'
import { ref, watch } from 'vue'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { createRevisitLog } from '/@/api/revisit'

//附件
const filesList = ref<UploadFile[]>([])
const emit = defineEmits(['success'])

const [registerDrawer, { closeDrawer, changeOkLoading }] = useDrawerInner((data) => {
  console.log(data)
  filesList.value = []
})

const [registerForm, { setFieldsValue, validate }] = useForm({
  baseColProps: { span: 12 },
  colon: true,
  showActionButtonGroup: false,
  labelWidth: 140,
  schemas
})

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val?.map((item) => item.url) ?? [] })
  }
)
//附件上传
async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  filesList.value = filesList.value!.map((item) => {
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: item.name
    }
  })
  await setFieldsValue({
    files: filesList.value.map((item) => item.url)
  })
}

async function handleSubmit() {
  try {
    changeOkLoading(true)
    const formdata = await validate()
    console.log(formdata)
    const { project_number, project_name, creator, cc_recipient, content, points_manage_id, matter_strid, files, client_id } = formdata
    await createRevisitLog({
      project_number: project_number.toString(),
      project_name,
      client_id,
      creator,
      cc_recipient,
      content,
      points_manage_id,
      matter_strid,
      files
    })
    closeDrawer()
    emit('success')
    changeOkLoading(false)
  } catch (e) {
    console.log(e)
    changeOkLoading(false)
  }
}
</script>
