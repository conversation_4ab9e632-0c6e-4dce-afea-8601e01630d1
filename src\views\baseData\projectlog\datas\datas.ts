import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'
import { getStaffList } from '/@/api/baseData/staff'

export const columns: BasicColumn[] = [
  {
    title: '项目ID',
    dataIndex: 'project_number',
    width: 150,
    resizable: true
  },
  {
    title: '项目名称',
    dataIndex: 'project_name',
    width: 150,
    resizable: true
  },
  {
    title: '负责人',
    dataIndex: 'creator_name',
    width: 150,
    resizable: true
  },
  {
    title: '抄送人',
    dataIndex: 'cc_recipient_name',
    width: 150,
    resizable: true
  },
  {
    title: '工作指标',
    dataIndex: 'target',
    width: 150,
    resizable: true
  },
  {
    title: '得分权重',
    dataIndex: 'points',
    width: 150,
    resizable: true
  },
  {
    title: '交付标准描述',
    dataIndex: 'desc',
    width: 150,
    resizable: true
  },
  {
    title: '是否作废',
    dataIndex: 'is_cancel',
    width: 150,
    customRender: ({ value }) => {
      return !isNullOrUnDef(value) ? h(Tag, { color: value == 1 ? 'red' : 'green' }, () => (value == 1 ? '是' : '否')) : ''
    },
    resizable: true
  },
  {
    title: '作废人',
    dataIndex: 'cancel_inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '事项单号',
    dataIndex: 'matter_strid',
    width: 150,
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 150,
    resizable: true
  },
  {
    title: '作废时间',
    dataIndex: 'cancel_at',
    width: 150,
    resizable: true
  },
  {
    title: '回访描述',
    dataIndex: 'content',
    width: 150,
    resizable: true
  },
  {
    title: '作废备注',
    dataIndex: 'cancel_remark',
    width: 150,
    resizable: true
  },
  {
    title: '附件',
    dataIndex: 'files',
    width: 150,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'project_number',
    label: '项目ID',
    component: 'Input'
  },
  {
    field: 'creator',
    label: '负责人',
    component: 'ApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        allowClear: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      },
      params: {
        pageSize: 9999
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'cc_recipient',
    label: '抄送人',
    component: 'ApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        allowClear: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      },
      params: {
        pageSize: 9999
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'target',
    label: '工作指标',
    component: 'Input'
  },
  {
    field: 'desc',
    label: '工作指标解释',
    component: 'Input'
  },
  {
    field: 'is_cancel',
    label: '是否作废',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  },
  {
    field: 'matter_strid',
    label: '事项单号',
    component: 'Input'
  },
  {
    field: 'content',
    label: '回访描述',
    component: 'Input'
  },
  {
    field: 'created_at',
    label: '创建时间',
    component: 'SingleRangeDate',
    componentProps: {
      allowEmpty: [true, true],
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  },
  {
    field: 'cancel_at',
    label: '作废时间',
    component: 'SingleRangeDate',
    componentProps: {
      allowEmpty: [true, true],
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  }
]
