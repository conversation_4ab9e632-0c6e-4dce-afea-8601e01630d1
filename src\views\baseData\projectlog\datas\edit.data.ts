import { types } from '../../pointmanagement/datas/data'
import { pointsgetList } from '/@/api/baseData/pointmanagement'
import { crmgetProjectByCrm } from '/@/api/extrasPage/createSaleOrder'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { FormSchema } from '/@/components/Form'
import { isNullOrUnDef } from '/@/utils/is'

const general_options = [
  {
    label: '是',
    value: 1
  },
  {
    label: '否',
    value: 0
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'project_number',
    label: '项目ID',
    required: true,
    component: 'PagingApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: crmgetProjectByCrm,
        resultField: 'items',
        labelField: 'title',
        valueField: 'id',
        searchMode: true,
        pagingMode: true,
        searchParamField: 'project_number',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'id',
            label: 'id'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'id',
          allowClear: true,
          onChange(_, shall) {
            console.log(shall)
            formModel.project_name = shall?.title
            formModel.customer_name = shall?.customer_name
            formModel.client_id = shall?.customer_id
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.type !== 2 ? true : false
    }
  },
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input',
    dynamicDisabled: true,
    ifShow(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.project_number)
    }
  },
  {
    field: 'customer_name',
    label: '客户名称',
    component: 'Input',
    dynamicDisabled: true,
    ifShow(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.project_number)
    }
  },
  {
    field: 'client_id',
    label: '客户',
    component: 'Input',
    dynamicDisabled: true,
    show: false
  },
  {
    field: 'creator',
    label: '负责人',
    component: 'PagingApiSelect',
    required: true,
    componentProps: {
      api: getCreatorList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'cc_recipient',
    label: '抄送人',
    component: 'PagingApiSelect',
    required: true,
    componentProps: {
      api: getCreatorList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'points_manage_id',
    label: '工作指标',
    component: 'PagingApiSelect',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        api: pointsgetList,
        resultField: 'items',
        searchMode: true,
        params: {
          status: 1,
          is_disabled: 0
        },
        pagingMode: true,
        returnParamsField: 'id',
        selectProps: {
          fieldNames: { value: 'id', label: 'target' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          onChange(_, shall) {
            if (!shall) return
            // points_manage_shall.value = shall
            formModel.type = shall.type
            formModel.points = shall.points
            formModel.is_strid = shall.is_strid
            formModel.is_file = shall.is_file
            formModel.is_send = shall.is_send
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'type',
    label: '积分类型',
    component: 'Select',
    componentProps: {
      options: Object.keys(types).map((item) => ({
        label: types[item].label,
        value: parseInt(item, 10)
      }))
    },
    ifShow(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    },
    dynamicDisabled(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    }
  },
  {
    field: 'points',
    label: '积分权重',
    component: 'Input',
    ifShow(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    },
    dynamicDisabled(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    }
  },
  {
    field: 'is_strid',
    label: '是否填写系统单号',
    component: 'Select',
    componentProps: {
      options: general_options
    },
    ifShow(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    },
    dynamicDisabled(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    }
  },
  {
    field: 'is_file',
    label: '是否需要上传附件',
    component: 'Select',
    componentProps: {
      options: general_options
    },
    ifShow(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    },
    dynamicDisabled(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    }
  },
  {
    field: 'is_send',
    label: '是否项目通知推送',
    component: 'Select',
    componentProps: {
      options: general_options
    },
    ifShow(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    },
    dynamicDisabled(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    }
  },
  {
    field: 'matter_strid',
    label: '事项单号',
    component: 'Input',
    required(renderCallbackParams) {
      return renderCallbackParams.values.is_strid === 1
    }
  },
  {
    field: 'content',
    label: '回访描述',
    component: 'InputTextArea',
    required: true
  },
  {
    field: 'files',
    label: '附件',
    component: 'Upload',
    slot: 'Files',
    required(renderCallbackParams) {
      return renderCallbackParams.values.is_file === 1
    }
  }
]
