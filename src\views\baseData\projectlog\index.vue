<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" v-if="hasPermission([572])" @click="handleCreate">新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <editDrawer @register="registereditDrawer" @success="reload" />
  </div>
</template>
<script setup lang="tsx">
import { ActionItem, BasicTable, EditRecordRow, useTable, TableAction } from '/@/components/Table'
import { getRevisitLog } from '/@/api/revisit'
import { columns, schemas } from './datas/datas'
import { projectpflsetIsCancel } from '/@/api/baseData/projectlog'
import { Textarea, Form, message, Button } from 'ant-design-vue'
import { ref } from 'vue'
import { usePermission } from '/@/hooks/web/usePermission'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
const FormItem = Form.Item

const { hasPermission } = usePermission()
const [registereditDrawer, { openDrawer, setDrawerProps }] = useDrawer()

const [registerTable, { reload }] = useTable({
  title: '项目日志',
  api: getRevisitLog,
  showIndexColumn: false,
  columns,
  showTableSetting: true,
  useSearchForm: true,
  rowKey: 'id',
  formConfig: {
    labelWidth: 120,
    actionColOptions: {
      span: 24
    },
    fieldMapToTime: [
      ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['cancel_at', ['cancel_at_start', 'cancel_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ],
    baseColProps: {
      span: 6
    },
    schemas
  },
  actionColumn: {
    width: 130,
    title: '操作',
    dataIndex: 'action'
  }
})
function createActions(record: EditRecordRow): ActionItem[] {
  return [
    {
      label: '作废',
      popConfirm: {
        title: (
          <div class="w-100">
            <Form>
              <FormItem label={'作废备注'} required>
                <Textarea v-model:value={cancel_remark.value} />
              </FormItem>
            </Form>
          </div>
        ),
        placement: 'left',
        confirm: handleChangeStatus.bind(null, record),
        disabled: record.is_cancel === 1
      },
      ifShow: hasPermission([571])
    }
  ]
}
const cancel_remark = ref('')
async function handleChangeStatus(record) {
  if (cancel_remark.value === '') return message.error('请输入作废备注')
  await projectpflsetIsCancel({ id: record.id, cancel_remark: cancel_remark.value })
  await reload()
  cancel_remark.value = ''
}

function handleCreate() {
  openDrawer(true, {})
  setDrawerProps({ title: '新增', showFooter: true })
}
</script>
