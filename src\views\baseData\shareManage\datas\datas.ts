import { Switch } from 'ant-design-vue'
import type { BasicColumn, FormSchema } from '/@/components/Table'
import { h } from 'vue'
import { useMessage } from '/@/hooks/web/useMessage'
import { updateShareStatus } from '/@/api/baseData/shareManage'

type CheckedType = boolean | string | number
const statusMap = {
  1: {
    color: 'green',
    text: '启用'
  },
  0: {
    color: 'red',
    text: '禁用'
  }
}

export const columns: BasicColumn[] = [
  {
    title: '分摊模式名称',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 100,
    resizable: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    resizable: true,
    customRender: ({ record }) => {
      if (!Reflect.has(record, 'pendingStatus')) {
        record.pendingStatus = false
      }
      return h(Switch, {
        checked: record.status === 1,
        checkedChildren: '点击停用',
        unCheckedChildren: '点击启用',
        loading: record.pendingStatus,
        onChange(checked: CheckedType) {
          record.pendingStatus = true
          const newStatus = checked ? 1 : 0
          const { createMessage } = useMessage()
          updateShareStatus({ id: record.id, status: newStatus })
            .then(() => {
              record.status = newStatus
              createMessage.success(`已成功修改状态`)
            })
            .catch(() => {
              createMessage.error('修改状态失败')
            })
            .finally(() => {
              record.pendingStatus = false
            })
        }
      })
    }
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 100,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps() {
      const options = Object.keys(statusMap).map((key) => {
        return {
          label: statusMap[key].text,
          value: key
        }
      })
      return {
        options
      }
    }
  },
  {
    field: 'created_at',
    label: '创建时间',
    component: 'SingleRangeDate',
    componentProps: {
      allowEmpty: [true, true],
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  }
]
