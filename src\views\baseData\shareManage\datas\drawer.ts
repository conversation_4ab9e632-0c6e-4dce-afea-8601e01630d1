import type { BasicColumn, FormSchema } from '/@/components/Table'

export type TType = 'create' | 'detail'

export const getSchemasList: (type: TType) => FormSchema[] = (type) => [
  {
    field: 'name',
    label: '分摊模式名称',
    component: 'Input',
    dynamicDisabled: type !== 'create',
    required: true
  },
  {
    field: 'status',
    label: '状态',
    component: 'RadioButtonGroup',
    defaultValue: 1,
    dynamicDisabled: type !== 'create',
    required: true,

    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '停用', value: 0 }
      ]
    }
  },
  {
    field: 'remark',
    label: '备注',
    dynamicDisabled: type !== 'create',
    component: 'InputTextArea'
  }
]

export const columns: BasicColumn[] = [
  {
    title: 'id',
    dataIndex: 'id',
    width: 100,
    ifShow: false
  },
  {
    title: '部门名称',
    dataIndex: 'name',
    width: 300,
    resizable: true,
    align: 'left'
  },
  {
    title: '比例',
    dataIndex: 'proportion',
    width: 50
  }
]

export const detailsColumns: BasicColumn[] = [
  {
    title: 'id',
    dataIndex: 'id',
    width: 100,
    ifShow: false
  },
  {
    title: '部门名称',
    dataIndex: 'department',
    width: 300,
    resizable: true,
    align: 'left'
  },
  {
    title: '比例',
    dataIndex: 'proportion',
    customRender: ({ value }) => {
      return `${value}%`
    },
    width: 400
  }
]
