<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="onOpenCreate" v-if="hasPermission([234])"> 新增 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <ShareDrawer @register="registerDrawer" @success="reload" />
  </div>
</template>

<script setup lang="ts" name="shareManage">
import { columns, schemas } from './datas/datas'
import { getShareManageList } from '/@/api/baseData/shareManage'
import { useDrawer } from '/@/components/Drawer'
import { ActionItem, BasicTable, EditRecordRow, useTable, TableAction } from '/@/components/Table'
import ShareDrawer from './components/ShareDrawer.vue'
import { usePermission } from '/@/hooks/web/usePermission'
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()

const { hasPermission } = usePermission()

const [registerTable, { reload }] = useTable({
  title: '分摊管理',
  api: getShareManageList,
  showIndexColumn: false,
  columns,
  showTableSetting: true,
  useSearchForm: true,
  rowKey: 'id',
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  formConfig: {
    labelWidth: 100,
    baseColProps: { span: 8 },
    fieldMapToTime: [['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]],
    schemas
  }
})

function createActions(record: EditRecordRow): ActionItem[] {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission([235])
    }
  ]
}

function onOpenCreate() {
  setDrawerProps({ title: '新增分摊管理' })
  openDrawer(true, {
    type: 'create'
  })
}

function handleDetail(record: EditRecordRow) {
  setDrawerProps({ title: '详情' })
  openDrawer(true, {
    type: 'detail',
    record
  })
}
</script>
