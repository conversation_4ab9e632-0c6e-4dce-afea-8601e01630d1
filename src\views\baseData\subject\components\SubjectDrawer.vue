<template>
  <BasicDrawer destroyOnClose @register="registerDrawer" v-bind="$attrs" showFooter width="30%" @ok="handleSubmit">
    <BasicForm @register="registerForm" @field-value-change="handleFiledValueChange" />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { schemas } from '/@/views/baseData/subject/datas/drawer.data'
import { updateSubject } from '/@/api/baseData/subjectManage'

const emits = defineEmits(['success', 'register', 'reloadSub'])

const propsData = ref<Recordable>({})

const [registerDrawer, { closeDrawer, changeLoading, changeOkLoading }] = useDrawerInner(async (data) => {
  try {
    await changeOkLoading(false)
    await changeLoading(true)
    propsData.value = data
    const { type, record } = data
    const schemasList = schemas({ type })
    resetSchema(schemasList)
    if (type === 'edit') {
      setFieldsValue(record)
    } else if (type === 'addSub') {
      setFieldsValue({
        level: record.level + 1,
        p_id: record.id,
        index: record.id,
        type: record.type,
        balance_direction: record.balance_direction,
        account_desc: record.account_desc
      })
    }
  } catch (e) {
    console.error(e)
  } finally {
    changeLoading(false)
  }
})

function handleFiledValueChange(key, value) {
  if (key == 'account_name' && propsData.value.type == 'addSub') {
    setFieldsValue({ account_desc: `${propsData.value.record.account_desc}-${value}` })
  } else if (key == 'account_name' && propsData.value.type == 'add') {
    setFieldsValue({ account_desc: value })
  } else if (key == 'account_name' && propsData.value.type == 'edit') {
    if (propsData.value.record.account_desc.includes('-')) {
      const lastDashIndex = propsData.value.record.account_desc.lastIndexOf('-')
      // 获取破折号前面的部分
      const prefix = propsData.value.record.account_desc.slice(0, lastDashIndex)
      // 重新构建整个字符串
      const updatedString = `${prefix}-${value}`
      setFieldsValue({ account_desc: updatedString })
    } else {
      setFieldsValue({ account_desc: value })
    }
  }
}

const [registerForm, { resetSchema, validate, setFieldsValue }] = useForm({
  actionColOptions: { span: 24 },
  baseColProps: { span: 24 },
  labelWidth: 120,
  colon: true,
  showActionButtonGroup: false
})
async function handleSubmit() {
  try {
    await changeOkLoading(true)
    const values = await validate()
    const { msg } = await updateSubject(values)
    if (msg === 'success') {
      // 创建：刷新首层
      // 创建子科目： 刷新当前层
      // 编辑：刷新上层
      if (propsData.value.type === 'addSub') {
        emits('reloadSub', propsData.value.record)
      } else if (propsData.value.type === 'edit' && propsData.value?.record?.level !== 1) {
        emits(
          'reloadSub',
          {
            level: propsData.value?.record?.level - 1,
            id: propsData.value.record.p_id,
            children: propsData.value.record.children
          },
          propsData.value.record
        )
      } else {
        emits('success')
      }
      await closeDrawer()
    } else {
      message.error(msg)
      throw new Error(msg)
      changeOkLoading(false)
    }
  } catch (e) {
    changeOkLoading(false)
    throw new Error(e)
  }
}
</script>
