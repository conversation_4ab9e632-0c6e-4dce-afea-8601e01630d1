import { BasicColumn, FormSchema } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { useI18n } from '/@/hooks/web/useI18n'
const { t } = useI18n()

export const typeMap = {
  1: '资产类',
  2: '负债类',
  3: '权益类',
  4: '成本类',
  5: '损益类'
}

export const directMap = {
  1: '借方',
  2: '贷方'
}
export const columns: BasicColumn[] = [
  {
    title: '科目代号',
    dataIndex: 'account_code',
    width: 120,
    resizable: true
  },
  {
    title: '科目名称',
    dataIndex: 'account_name',
    width: 180,
    resizable: true
  },
  {
    title: '科目类别',
    dataIndex: 'type',
    width: 180,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(typeMap[value])
    }
  },
  {
    title: '余额方向',
    dataIndex: 'balance_direction',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(directMap[value])
    }
  },
  {
    title: '科目描述',
    dataIndex: 'account_desc',
    width: 180,
    resizable: true
  },
  {
    title: '是否系统受控',
    dataIndex: 'is_control',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(t(`tag.tag.${value}`), t(`tag.colors.${value}`))
      // return useRender.renderTag(text)
    }
  },
  {
    title: '是否属于费用',
    dataIndex: 'is_cost',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(t(`tag.tag.${value}`), t(`tag.colors.${value}`))
    }
  },
  {
    title: '填写往来单位',
    dataIndex: 'is_need_corres',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(t(`tag.tag.${value}`), t(`tag.colors.${value}`))
    }
  },
  {
    title: '填写分摊人员',
    dataIndex: 'is_need_share_person',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(t(`tag.tag.${value}`), t(`tag.colors.${value}`))
    }
  },
  {
    title: '填写分摊渠道',
    dataIndex: 'is_need_share_channel',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(t(`tag.tag.${value}`), t(`tag.colors.${value}`))
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(t(`tag.status.${value}`), t(`tag.colors.${value}`))
    }
  },
  {
    title: '生成现金流报表',
    dataIndex: 'is_cash',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(t(`tag.tag.${value}`), t(`tag.colors.${value}`))
    }
  },
  {
    title: 'AGR科目代码',
    dataIndex: 'account_code_agr',
    width: 120,
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 180,
    resizable: true
  },
  {
    title: '更新时间',
    dataIndex: 'updated_at',
    width: 180,
    resizable: true
  }
]

export const searchFormSchemas: FormSchema[] = [
  {
    field: 'account_name',
    label: '科目名称',
    component: 'Input'
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    }
  },
  {
    field: 'is_control',
    label: '是否系统受控科目',
    component: 'Select',
    componentProps: {
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ]
    }
  },
  {
    field: 'is_cost',
    label: '是否属于费用',
    component: 'Select',
    componentProps: {
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ]
    }
  },
  {
    field: 'type',
    label: '科目类别',
    component: 'Select',
    componentProps: () => ({ options: Object.keys(typeMap).map((key) => ({ label: typeMap[key], value: Number(key) })) })
  },
  {
    field: 'balance_direction',
    label: '余额方向',
    component: 'Select',
    componentProps: () => ({ options: Object.keys(directMap).map((key) => ({ label: directMap[key], value: Number(key) })) })
  }
]
