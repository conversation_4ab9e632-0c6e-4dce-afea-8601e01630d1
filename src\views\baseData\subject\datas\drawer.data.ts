import { directMap, typeMap } from './data'
import { FormSchema } from '/@/components/Form'

export const schemas = ({ type }: { type: 'edit' | 'add' | 'addSub' }): FormSchema[] => [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    dynamicDisabled: true,
    ifShow: type === 'edit'
  },
  {
    field: 'level',
    label: '层级',
    component: 'Input',
    defaultValue: '1',
    dynamicDisabled: true,
    required: true
  },
  // {
  //   field: 'a_id',
  //   label: 'a_id',
  //   component: 'Input',
  //   defaultValue: random(********, *********).toString()
  // },
  {
    field: 'p_id',
    label: '父级ID',
    component: 'Input',
    defaultValue: '0',
    show: type === 'addSub',
    dynamicDisabled: true
  },
  // {
  //   field: 'index',
  //   label: '科目父ID',
  //   component: 'Input',
  //   defaultValue: '0',
  //   show: type === 'addSub'
  // },
  {
    field: 'account_name',
    label: '科目名称',
    component: 'Input',
    required: true
  },
  // {
  //   field: 'account_code',
  //   label: '科目代码',
  //   component: 'Input',
  //   defaultValue: random(1, *********).toString()
  // },
  {
    field: 'type',
    label: '科目类别',
    component: 'Select',
    required: true,
    dynamicDisabled: type === 'addSub',
    componentProps: () => ({ options: Object.keys(typeMap).map((key) => ({ label: typeMap[key], value: Number(key) })) })
  },
  {
    field: 'balance_direction',
    label: '余额方向',
    component: 'Select',
    required: true,
    dynamicDisabled: type === 'addSub',
    componentProps: () => ({ options: Object.keys(directMap).map((key) => ({ label: directMap[key], value: Number(key) })) })
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    },
    defaultValue: 1,
    required: true
  },
  {
    field: 'is_control',
    label: '系统受控科目',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    },
    defaultValue: 1,
    required: true
  },
  {
    field: 'is_cost',
    label: '属于费用',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    },
    defaultValue: 1,
    required: true
  },
  {
    field: 'account_desc',
    label: '科目描述',
    component: 'InputTextArea',
    required: true,
    componentProps: {
      disabled: true
    }
    // required: true
  },
  {
    field: 'is_need_corres',
    label: '是否填写往来单位',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    },
    defaultValue: 0,
    labelWidth: 130,
    required: true
  },
  {
    field: 'is_need_share_person',
    label: '是否填写分摊人员',
    component: 'Select',
    labelWidth: 130,
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    },
    defaultValue: 0,
    required: true
  },
  {
    field: 'is_need_share_channel',
    label: '是否填写分摊渠道',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    },
    defaultValue: 0,
    required: true,
    labelWidth: 130
  },
  {
    field: 'account_code_agr',
    label: 'AGR科目代码',
    component: 'Input'
  },
  {
    field: 'is_cash',
    label: '生成现金流报表',
    component: 'Select',
    componentProps: {
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ]
    }
  }
]
