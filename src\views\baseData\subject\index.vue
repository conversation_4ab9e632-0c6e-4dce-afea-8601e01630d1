<template>
  <div>
    <BasicTable class="p-4" bordered @register="registerTable" @expand="handleExpand">
      <template #toolbar>
        <a-button v-if="hasPermission([217])" type="primary" @click="handleCreate({ type: 'add' })">新增</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <SubjectDrawer @register="registerDrawer" @success="reload" @reload-sub="reloadSub" />
  </div>
</template>

<script setup lang="ts">
import { getSubjectList } from '/@/api/baseData/subjectManage'
import resizeableColumns from '/@/utils/erp/resizeableColumns'
import { ActionItem, BasicTable, TableAction, useTable } from '/@/components/Table'
import { columns, searchFormSchemas } from './datas/data'
import { useDrawer } from '/@/components/Drawer'
import SubjectDrawer from './components/SubjectDrawer.vue'
import { DataRolesList } from '/@/api/dataArchive/model/types'
import { usePermission } from '/@/hooks/web/usePermission'

const { hasPermission } = usePermission()
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
//注册表格
const [registerTable, { reload }] = useTable({
  title: '科目列表',
  columns: resizeableColumns(columns),
  useSearchForm: true,
  formConfig: {
    colon: true,
    labelWidth: 150,
    autoAdvancedLine: 1,
    showAdvancedButton: true,
    schemas: searchFormSchemas,
    submitButtonOptions: { text: '搜索' },
    resetButtonOptions: { text: '重置' },
    baseColProps: { span: 6 }
  },
  rowKey: 'id',
  api: getSubjectList,
  showTableSetting: true,
  showIndexColumn: false,
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  afterFetch: handleAfterFetch,
  beforeFetch: handleBeforeFetch
})

function handleAfterFetch(result) {
  return result.map((item) => ({ ...item, children: [] }))
}

function handleBeforeFetch(params) {
  // params.level = params.level ? params.level : 1
  return params
}

async function handleExpand(expanded: boolean, record) {
  try {
    if (expanded && record.children?.length === 0) {
      const { items } = await getSubjectList({ level: record.level + 1, id: record.id, pageSize: 100 })
      record.children = record.children.concat(items.map((item) => ({ ...item, children: [] })))
    }
  } catch (e) {
    console.log(e)
  }
}

function handleCreate(data) {
  openDrawer(true, data)
  setDrawerProps({ title: '创建科目' })
}

function handleEdit(data) {
  openDrawer(true, data)
  setDrawerProps({ title: '编辑科目' })
}

function createActions(record: DataRolesList) {
  return [
    {
      label: '创建子科目',
      onClick: handleCreate.bind(null, { type: 'addSub', record }),
      ifShow: hasPermission([217])
    },
    {
      label: '编辑',
      onClick: handleEdit.bind(null, { type: 'edit', record }),
      ifShow: hasPermission([218])
    }
  ] as ActionItem[]
}

async function reloadSub(record, editRow) {
  try {
    const { items } = await getSubjectList({ level: record.level + 1, id: record.id, pageSize: 100 })
    const updateItem = items.find((item) => item.id === editRow?.id)
    if (updateItem) {
      for (const key of Object.keys(editRow)) {
        Reflect.set(editRow, key, key === 'children' ? editRow[key] : updateItem[key])
      }
    } else {
      Reflect.set(record, 'children', items)
    }
  } catch (e) {
    throw new Error(e)
  }
}
</script>
