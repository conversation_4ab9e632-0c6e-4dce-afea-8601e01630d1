<template>
  <BasicDrawer @register="registerDrawer" width="50%" title="供应商详情" :showOkBtn="false">
    <Descriptions title="其他支出单详情" :column="2">
      <DescriptionsItem label="供应商名称">
        {{ records?.name }}
      </DescriptionsItem>
      <DescriptionsItem label="联系方式">{{ records?.contact }}</DescriptionsItem>
      <DescriptionsItem label="负责人">{{ records?.inCharge_name }}</DescriptionsItem>
      <DescriptionsItem label="Gbuilder供应商">{{ records?.is_gbuilder ? '是' : '否' }}</DescriptionsItem>
      <DescriptionsItem label="Gbuilder部门">{{ records?.gbuilder_department }}</DescriptionsItem>
      <DescriptionsItem label="gbuilder负责人">{{ records?.gbuilder_incharge_name }}</DescriptionsItem>
      <DescriptionsItem label="供应商等级">{{ records?.level }}</DescriptionsItem>
      <DescriptionsItem label="是否公司">{{ records?.is_company ? '是' : '否' }}</DescriptionsItem>
      <DescriptionsItem label="关联部门">{{ records?.department }}</DescriptionsItem>
      <DescriptionsItem label="母公司名称">{{ records?.parent_company }}</DescriptionsItem>
      <DescriptionsItem label="是否公户">{{ records?.is_public_account ? '是' : '否' }}</DescriptionsItem>
      <DescriptionsItem label="状态">{{ records?.is_disabled ? '禁用' : '启用' }}</DescriptionsItem>
      <DescriptionsItem label="统一社会信用代码">{{ records?.credit_code }}</DescriptionsItem>
      <DescriptionsItem label="法定代表人姓名">{{ records?.oper_name }}</DescriptionsItem>
      <DescriptionsItem label="公司状态">{{ records?.company_status }}</DescriptionsItem>
      <DescriptionsItem label="公司注册地址">{{ records?.address }}</DescriptionsItem>
      <DescriptionsItem label="增值税票收的税点">{{ records?.tax_point }}</DescriptionsItem>
      <DescriptionsItem label="开票收的税点">{{ records?.ticket_point }}</DescriptionsItem>
      <DescriptionsItem label="开增值税票">{{ records?.is_open_tax ? '是' : '否' }}</DescriptionsItem>
      <DescriptionsItem label="开普票">{{ records?.is_open_ticket ? '是' : '否' }}</DescriptionsItem>
      <DescriptionsItem label="开票主体">{{ records?.Invoicing_is_self ? '自己' : '其他' }}</DescriptionsItem>
      <DescriptionsItem label="是否定金通知">{{ records?.deposit_is_send ? '是' : '否' }}</DescriptionsItem>
      <DescriptionsItem label="定金通知人员">
        <div v-for="(item, index) in records?.deposit_send_person_name" :key="index">{{ item }},</div>
      </DescriptionsItem>
      <DescriptionsItem label="非定金通知">{{ records?.no_deposit_is_send ? '是' : '否' }}</DescriptionsItem>
      <DescriptionsItem label="非否定金通知人员">
        <div v-for="(item, index) in records?.no_deposit_send_person_name" :key="index">{{ item }},</div>
      </DescriptionsItem>
      <DescriptionsItem label="营业执照">
        <ul>
          <li v-for="(item, index) in records?.business_license" :key="item">
            <a :href="item" target="_blank" @click="handlePreview(item, $event)">{{ `附件${index + 1}` }}</a>
          </li>
        </ul>
      </DescriptionsItem>
      <DescriptionsItem label="合同">
        <ul>
          <li v-for="(item, index) in records?.contract" :key="item">
            <a :href="item" target="_blank" @click="handlePreview(item, $event)">{{ `附件${index + 1}` }}</a>
          </li>
        </ul></DescriptionsItem
      >
      <DescriptionsItem label="备注">{{ records?.remark }}</DescriptionsItem>

      <!-- <DescriptionsItem label="附件">
        <ul>
          <li v-for="(item, index) in records.files" :key="item">
            <a :href="item" target="_blank" @click="handlePreview(item, $event)">{{ `附件${index + 1}` }}</a>
          </li>
        </ul>
      </DescriptionsItem> -->
    </Descriptions>
    <PreviewFile @register="registerModal" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { Descriptions, DescriptionsItem } from 'ant-design-vue'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { useMessage } from '/@/hooks/web/useMessage'
import { useModal } from '/@/components/Modal'
import { createImgPreview } from '/@/components/Preview'

const records = ref()
const [registerModal, { openModal }] = useModal()
const { createMessage } = useMessage()

const [registerDrawer] = useDrawerInner((data) => {
  records.value = data.record
})

async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  console.log(prefix)
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>
