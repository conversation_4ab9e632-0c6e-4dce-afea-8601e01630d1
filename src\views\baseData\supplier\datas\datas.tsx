import { Tag, Menu, MenuItem, Dropdown } from 'ant-design-vue'
import { h, ref } from 'vue'
import type { BasicColumn, FormSchema } from '/@/components/Table'
import { getAccountList } from '/@/api/commonUtils'
import { isUnDef, isNull } from '/@/utils/is'
import { isUndefined } from 'lodash-es'
import { usePermission } from '/@/hooks/web/usePermission'
import { setIsGbuilder } from '/@/api/baseData/supplier'
import { useMessage } from '/@/hooks/web/useMessage'
import { getDeptTree } from '/@/api/admin/dept'
import { getDept } from '/@/api/erp/systemInfo'
import { GET_STATUS_SCHEMA } from '/@/const/status'
import { useRender } from '/@/components/Table/src/hooks/useRender'

const { createMessage } = useMessage()
const { hasPermission } = usePermission()
export const customRenderIs = (value, status = ['是', '否']) => (isNull(value) || isUnDef(value) ? '' : value ? status[0] : status[1])

export const columns: BasicColumn[] = [
  {
    title: '供应商名称',
    dataIndex: 'name',
    width: 200,
    resizable: true
  },
  {
    title: '联系方式',
    dataIndex: 'contact',
    width: 100,
    resizable: true
  },
  // {
  //   title: '账号',
  //   dataIndex: 'account',
  //   width: 100
  // },
  // {
  //   title: '账号名称',
  //   dataIndex: 'account_name',
  //   width: 100
  // },
  {
    title: '负责人',
    dataIndex: 'inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: 'Gbuilder供应商',
    dataIndex: 'is_gbuilder',
    width: 150,
    resizable: true,
    customRender({ text, record }) {
      if (!isUndefined(text) && !isNull(text)) {
        return (
          <Dropdown disabled={!hasPermission(351)} placement="bottom">
            {{
              default: () => <Tag color={text ? 'success' : 'error'}>{text ? '是' : '否'}</Tag>,
              overlay: () => (
                <Menu onClick={(v) => handleSetIsGbuilder(v, record)}>
                  <MenuItem class="text-center" key={1}>
                    设为是
                  </MenuItem>
                  <MenuItem class="text-center" key={0}>
                    设为否
                  </MenuItem>
                </Menu>
              )
            }}
          </Dropdown>
        )
      } else {
        return '-'
      }
    }
  },
  {
    title: 'Gbuilder部门',
    dataIndex: 'gbuilder_department',
    width: 200,
    resizable: true
  },
  {
    title: 'gbuilder负责人',
    dataIndex: 'gbuilder_incharge_name',
    width: 200,
    resizable: true
  },
  {
    title: '供应商等级',
    dataIndex: 'level',
    width: 200,
    resizable: true
  },
  {
    title: '是否公司',
    dataIndex: 'is_company',
    width: 200,
    resizable: true,
    customRender({ record }) {
      return h(Tag, { color: record.is_public_account ? 'pink' : 'orange' }, () => (record.is_public_account ? '个人' : '公司'))
    }
  },
  {
    title: '关联部门',
    dataIndex: 'department',
    width: 250,
    resizable: true,
    customRender: ({ text }) => {
      return text ? useRender.renderTags(text) : '-'
    }
  },
  // {
  //   title: '母公司名称',
  //   dataIndex: 'parent_company',
  //   width: 250,
  //   resizable: true
  // },
  {
    title: '是否公户',
    dataIndex: 'is_public_account',
    width: 250,
    resizable: true,
    customRender({ record }) {
      return h(Tag, { color: record.is_public_account ? 'success' : 'error' }, () => (record.is_public_account ? '是' : '否'))
    }
  },
  {
    title: '状态',
    dataIndex: 'is_disabled',
    width: 180,
    resizable: true,
    customRender({ record }) {
      if (record.is_disabled === 0) {
        return h(Tag, { color: 'success' }, () => '启用')
      } else if (record.is_disabled === 1) {
        return h(Tag, { color: 'error' }, () => '禁用')
      }
    }
  },
  // {
  //   title: '统一社会信用代码',
  //   dataIndex: 'credit_code',
  //   width: 180,
  //   resizable: true
  // },
  // {
  //   title: '法定代表人姓名',
  //   dataIndex: 'oper_name',
  //   width: 180,
  //   resizable: true
  // },
  // {
  //   title: '公司状态',
  //   dataIndex: 'company_status',
  //   width: 180,
  //   resizable: true
  // },
  // {
  //   title: '公司注册地址',
  //   dataIndex: 'address',
  //   width: 180,
  //   resizable: true
  // },
  // {
  //   title: '增值税票收的税点',
  //   dataIndex: 'tax_point',
  //   width: 100,
  //   resizable: true
  // },
  // {
  //   title: '开票收的税点',
  //   dataIndex: 'ticket_point',
  //   width: 120,
  //   resizable: true
  // },
  {
    title: '开增值税票',
    dataIndex: 'is_open_tax',
    width: 80,
    resizable: true,
    customRender: ({ value }) => customRenderIs(value)
  },
  {
    title: '开普票',
    dataIndex: 'is_open_ticket',
    width: 80,
    resizable: true,
    customRender: ({ value }) => customRenderIs(value)
  },
  // {
  //   title: '开票主体',
  //   dataIndex: 'Invoicing_is_self',
  //   width: 100,
  //   resizable: true,
  //   customRender: ({ value }) => customRenderIs(value, ['自己', '其他'])
  // },
  // {
  //   title: '开户行',
  //   dataIndex: 'bank',
  //   width: 120,
  //   resizable: true
  // },
  // {
  //   title: '营业执照',
  //   dataIndex: 'business_license',
  //   width: 120,
  //   resizable: true
  // },
  // {
  //   title: '合同',
  //   dataIndex: 'contract',
  //   width: 120,
  //   resizable: true
  // },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 150,
    resizable: true
  }
]

const status_schema = GET_STATUS_SCHEMA(
  [
    { label: '启用', value: 0 },
    { label: '禁用', value: 1 }
  ],
  {
    field: 'is_disabled'
  }
)

export const schemas: FormSchema[] = [
  status_schema,
  {
    field: 'name',
    label: '供应商名称',
    component: 'Input'
  },
  {
    field: 'contact',
    label: '联系方式',
    component: 'Input'
  },
  // {
  //   field: 'account',
  //   label: '账号',
  //   component: 'Input'
  // },
  // {
  //   field: 'account_name',
  //   label: '账号名称',
  //   component: 'Input'
  // },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  // {
  //   field: 'is_disabled',
  //   label: '状态',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: '启用', value: 0 },
  //       { label: '禁用', value: 1 }
  //     ]
  //   }
  // },
  {
    field: 'dept_ids',
    label: '关联部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      maxTagCount: 3,
      treeCheckable: true,
      multiple: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        treeCheckable: true,
        showCheckedStrategy: 'SHOW_ALL',
        treeDefaultExpandAll: true,
        treeLine: {
          showLeafIcon: false
        },
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  {
    field: 'gbuilder_dept_id',
    label: 'Gbuilder部门',
    component: 'PagingApiSelect',
    componentProps: {
      api: getDept,
      params: { isgbuilder: 1, is_audit: 1 },
      resultField: 'items',
      labelField: 'name',
      valueField: 'id',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    }
  }
]

export const tableRef = ref<ElRef>(null)

async function handleSetIsGbuilder(target, record) {
  console.log(tableRef.value)
  try {
    const { code } = await setIsGbuilder({ id: +record.id, is_gbuilder: +target.key })
    if (code === 200) {
      createMessage.success('操作成功')
      tableRef.value?.tableAction?.reload?.()
      return
    }
    createMessage.error('操作失败')
  } catch (err) {
    console.log(err)
    createMessage.error('操作失败')
  }
}
