<template>
  <BasicDrawer destroyOnClose @register="registerDrawer" v-bind="$attrs" width="90%">
    <BasicTable @register="registerTable" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable } from '/@/components/Table'
import { columns } from '../datas/drawer'
import { getPackageList } from '/@/api/baseData/warehouse'

const [registerDrawer, { changeLoading }] = useDrawerInner(async (data) => {
  try {
    await changeLoading(true)
    await setProps({
      searchInfo: data.params
    })
    reload()
  } catch (e) {
    console.error(e)
  } finally {
    changeLoading(false)
  }
})

const [registerTable, { reload, setProps }] = useTable({
  title: '包裹列表',
  api: getPackageList,
  showIndexColumn: false,
  columns,
  showTableSetting: false,
  useSearchForm: false,
  rowKey: 'id',
  immediate: false,
  canResize: false
})
</script>
