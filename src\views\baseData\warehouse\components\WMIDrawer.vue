<template>
  <BasicDrawer destroyOnClose @register="registerDrawer" v-bind="$attrs" showFooter width="30%" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { schemas } from '../datas/drawer'
import { updateWMI } from '/@/api/baseData/warehouse'

const emits = defineEmits(['success', 'register'])
const propsData = ref()

const [registerDrawer, { closeDrawer, changeLoading, changeOkLoading }] = useDrawerInner(async (data) => {
  try {
    await changeOkLoading(false)
    await changeLoading(true)
    await resetFields()

    propsData.value = data
    if (data.type === 'edit') {
      setFieldsValue({ ...data.record, strid: data.record.strid.slice(2) })
    }
  } catch (e) {
    console.error(e)
  } finally {
    changeLoading(false)
  }
})

const [registerForm, { resetFields, validate, setFieldsValue }] = useForm({
  actionColOptions: { span: 24 },
  schemas,
  baseColProps: { span: 24 },
  labelWidth: 120,
  colon: true,
  showActionButtonGroup: false
})

async function handleSubmit() {
  try {
    await changeOkLoading(true)
    const formData = await validate()
    console.log(formData)

    let params
    let warehouse_id = propsData.value.type === 'create' ? propsData.value.warehouse_id : propsData.value.record.warehouse_id
    if (propsData.value.type === 'create') {
      params = {
        ...formData,
        warehouse_id
      }
    } else {
      params = {
        ...formData,
        strid: `CW${formData.strid}`,
        id: propsData.value.record.id,
        warehouse_id
      }
    }

    await updateWMI(params)
    emits('success', warehouse_id)
    await closeDrawer()
  } catch (err) {
    console.error(err)
    changeOkLoading(false)
  }
}
</script>
