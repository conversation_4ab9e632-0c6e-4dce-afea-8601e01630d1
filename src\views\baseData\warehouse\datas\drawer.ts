import type { BasicColumn, FormSchema } from '/@/components/Table'

export const schemas: FormSchema[] = [
  {
    field: 'is_disabled',
    label: '状态',
    component: 'RadioButtonGroup',
    defaultValue: 0,
    required: true,
    componentProps: {
      options: [
        {
          label: '启用',
          value: 0
        },
        {
          label: '禁用',
          value: 1
        }
      ]
    }
  },
  {
    field: 'name',
    label: '仓位名称',
    component: 'Input',
    required: true
  },
  {
    field: 'strid',
    label: '仓位编号',
    component: 'Input',
    componentProps: {
      addonBefore: 'CW'
    },
    rules: [
      {
        required: true,
        validator: (_, value) => {
          if (!value) {
            return Promise.reject('请输入仓库编号')
          }
          // 检查自定义内容中是否有空格
          if (value.substring(1).includes(' ')) {
            return Promise.reject('自定义内容不能有空格')
          }

          return Promise.resolve()
        },
        trigger: 'blur'
      }
    ]
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    required: true
  }
]

export const columns: BasicColumn[] = [
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 100,
    resizable: true
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name',
    width: 100,
    resizable: true
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    width: 100,
    resizable: true
  },
  {
    title: '打包方式',
    dataIndex: 'method',
    width: 100,
    resizable: true
  },
  {
    title: '重量(KG)',
    dataIndex: 'weight',
    width: 100,
    resizable: true
  },
  {
    title: '包裹体积(m³)',
    dataIndex: 'volume',
    width: 100,
    resizable: true
  },
  {
    title: '包裹长(cm)',
    dataIndex: 'length',
    width: 100,
    resizable: true
  },
  {
    title: '包裹宽(cm)',
    dataIndex: 'width',
    width: 100,
    resizable: true
  },
  {
    title: '包裹高(cm)',
    dataIndex: 'height',
    width: 100,
    resizable: true
  }
]
