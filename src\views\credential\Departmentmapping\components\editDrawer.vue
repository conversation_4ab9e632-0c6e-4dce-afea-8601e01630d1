<template>
  <BasicDrawer @register="registerDrawer" width="30%" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { schemas } from '../datas/edit.data'
import { certpacreateOrUpdateDeptMap } from '/@/api/credential/Departmentmapping'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'

const recordID = ref()
const recordtype = ref()
const emit = defineEmits(['success'])
const [registerDrawer, { closeDrawer, changeOkLoading }] = useDrawerInner(async (data) => {
  console.log(data)
  recordtype.value = data.type
  if (data.type !== 'add') {
    recordID.value = data.record.id
  }
  resetFields()
  resetSchema(schemas)
  setFieldsValue(data.record)
  console.log(recordtype.value)
})
const [registerForm, { validate, resetFields, setFieldsValue, resetSchema }] = useForm({
  baseColProps: { span: 24 },
  showActionButtonGroup: false,
  colon: true,
  labelCol: { style: { width: '120px' } }
})

async function handleSubmit() {
  try {
    changeOkLoading(true)
    const fromdata = await validate()
    await certpacreateOrUpdateDeptMap({ ...fromdata, id: recordtype.value === 'edit' ? recordID.value : null })
    setTimeout(() => {
      closeDrawer()
      changeOkLoading(false)
      emit('success')
    }, 1000)
  } catch (e) {
    changeOkLoading(false)
    console.log(e)
  }
}
</script>
