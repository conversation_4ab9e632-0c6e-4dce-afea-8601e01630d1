import { BasicColumn } from '/@/components/Table'
import { getDept } from '/@/api/erp/systemInfo'
import { FormSchema } from '/@/components/Form'

export const columns: BasicColumn[] = [
  {
    title: '部门名称',
    dataIndex: 'department',
    width: '150px'
  },
  {
    title: '对应Gbuilder部门名称',
    dataIndex: 'gb_department',
    width: '150px'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: '150px'
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'dept_id',
    label: '部门',
    component: 'PagingApiSelect',
    componentProps: {
      api: getDept,
      params: { is_production: 1, is_audit: 1 },
      resultField: 'items',
      labelField: 'name',
      valueField: 'id',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    }
  },
  {
    field: 'gb_dept_id',
    label: 'Gbuir部门',
    component: 'PagingApiSelect',
    componentProps: {
      api: getDept,
      params: { isgbuilder: 1, is_audit: 1 },
      resultField: 'items',
      labelField: 'name',
      valueField: 'id',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    }
  }
]
