import { getDept } from '/@/api/erp/systemInfo'
import { FormSchema } from '/@/components/Form'

export const schemas: FormSchema[] = [
  {
    field: 'dept_id',
    label: '部门',
    required: true,
    component: 'PagingApiSelect',
    componentProps: {
      api: getDept,
      params: { is_production: 1, is_audit: 1 },
      resultField: 'items',
      labelField: 'name',
      valueField: 'id',
      searchMode: true,
      pagingMode: true,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    }
  },
  {
    field: 'gb_dept_id',
    label: 'Gbuir部门',
    required: true,
    component: 'PagingApiSelect',
    componentProps: {
      api: getDept,
      params: { isgbuilder: 1, is_audit: 1 },
      resultField: 'items',
      returnParamsField: 'id',
      labelField: 'name',
      valueField: 'id',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    }
  }
]
