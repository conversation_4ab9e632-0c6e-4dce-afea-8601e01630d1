<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleAdd" v-if="hasPermission(578)">新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <editDrawer @register="registereditDrawer" @success="reload" />
  </div>
</template>
<script setup lang="ts">
import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table'
import { Button } from 'ant-design-vue'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { columns, schemas } from './datas/data'
import { certpagetDeptMapList } from '/@/api/credential/Departmentmapping'
import { usePermission } from '/@/hooks/web/usePermission'

const { hasPermission } = usePermission()
const [registereditDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerTable, { reload }] = useTable({
  title: '部门映射',
  api: certpagetDeptMapList,
  columns,
  showTableSetting: true,
  rowKey: 'id',
  showIndexColumn: false,
  useSearchForm: true,
  formConfig: {
    labelWidth: 120,
    schemas,
    autoSubmitOnEnter: true,
    baseColProps: { span: 8 },
    fieldMapToTime: [['date', ['date1', 'date2'], ['YYYY-MM-DD', 'YYYY-MM-DD']]]
  },
  //   rowSelection: {
  //     type: 'checkbox'
  //   }
  actionColumn: {
    width: 100,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  }
})
function createActions(record): ActionItem[] {
  return [
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      ifShow: hasPermission([579])
    }
  ]
}
function handleAdd() {
  openDrawer(true, { type: 'add' })
  setDrawerProps({ title: '新增', showFooter: true })
}

function handleEdit(record) {
  openDrawer(true, { record, type: 'edit' })
  setDrawerProps({ title: '编辑', showFooter: true })
}
</script>
