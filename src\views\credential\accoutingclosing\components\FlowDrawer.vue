<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="90%" show-footer @ok="handleSubmit">
    <template #footer>
      <Button type="default" @click="handleBack" v-if="current > 0">上一步</Button>
      <Button type="primary" @click="handleNext" v-if="current < 4" :disabled="!Buttonshow">下一步</Button>
      <Button type="primary" @click="handleSubmit" v-if="current == 4" :loading="buttonloading">提交</Button>
    </template>
    <div class="mb-10">
      <Steps :current="current">
        <Step title="试算平衡" />
        <Step title="结转Gbuilder利润" />
        <Step title="部门内部利润调拨" />
        <Step title="结转损益" />
        <Step title="锁定日期" />
      </Steps>
    </div>
    <Trialbalancing v-if="current == 0" @success="currentnumber" @fixed="fixedDateFun" />
    <!-- <Carryforward v-if="current == 1" @success="currentnumber" :fixedDate="FixedDate" /> -->
    <Gbuilder v-if="current == 1" @success="currentnumber" :fixedDate="FixedDate" />
    <deprnament v-if="current == 2" @success="currentnumber" @error="currentnumber" :fixedDate="FixedDate" />
    <lossandgain v-if="current == 3" @success="currentnumber" :fixedDate="FixedDate" />
    <Card v-if="current == 4" :title="FixedDate.year + '年' + FixedDate.issue + '月' + '锁定日期'">
      <BasicForm @register="registerform" />
    </Card>
  </BasicDrawer>
</template>
<script lang="ts" setup>
import { nextTick, ref, watch } from 'vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { Steps, Step, Button, Card } from 'ant-design-vue'
import Trialbalancing from './trialbalancing.vue'
// import Carryforward from './carryforward.vue'
import lossandgain from './lossandgain.vue'
import Gbuilder from './Gbuilder.vue'
import deprnament from './deptnament.vue'
import { schemas } from '../datas/drawer'
import { createEndDate } from '/@/api/credential/credential'
const emit = defineEmits(['success', 'register'])
const buttonloading = ref(false)
//步骤时间
const current = ref(0)
//button显影
const Buttonshow = ref(false)
const FixedDate = ref<any>({})

const [registerDrawer, { closeDrawer }] = useDrawerInner(async () => {
  // 需要初始化值
  current.value = 0
  Buttonshow.value = false
  FixedDate.value = {}
})

function handleNext() {
  current.value++
  Buttonshow.value = false
}
function handleBack() {
  current.value--
}
async function handleSubmit() {
  try {
    buttonloading.value = true
    const formdata = await validate()
    await createEndDate(formdata)
    setTimeout(() => {
      buttonloading.value = false
      closeDrawer()
      emit('success')
    }, 1000)
  } catch (e) {
    console.log(e)
  }
}
function fixedDateFun(e) {
  console.log(e)
  FixedDate.value = e
  console.log(FixedDate.value)
}
function currentnumber(e) {
  Buttonshow.value = e
}

const [registerform, { setFieldsValue, validate }] = useForm({
  schemas,
  showActionButtonGroup: false,
  baseColProps: { span: 12 },
  labelCol: { span: 5 }
})

watch(
  () => current.value,
  (val) => {
    if (val == 4) {
      nextTick(() => {
        setFieldsValue(FixedDate.value)
      })
    }
  }
)
</script>
<style></style>
