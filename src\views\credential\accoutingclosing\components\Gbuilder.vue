<template>
  <Card :bordered="false">
    <template #title>
      <div>凭证年份:{{ props.fixedDate.year }}年</div>
      <div>凭证月份:{{ props.fixedDate.issue }}月</div>
    </template>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <Tag :color="statustype[record.status]?.color"> {{ statustype[record.status]?.label }}</Tag>
        </template>
        <template v-if="column.key === 'type'">
          <Tag :color="mapType[record.type]?.color"> {{ mapType[record.type]?.label }}</Tag>
        </template>
      </template>
    </BasicTable>
  </Card>
</template>
<script setup lang="ts">
import { BasicTable, useTable } from '/@/components/Table'
import { getCredentialList } from '/@/api/credential/credential'
import { columns, statustype } from '../../credential/datas/datas'
import { Tag, Card } from 'ant-design-vue'
import { onMounted, ref } from 'vue'
import { mapType } from '../../credential/datas/modal'
//月份第一天
const firstDayFormatted = ref('')
//月份最后一天
const lastDayFormatted = ref('')
const props = defineProps({
  fixedDate: {
    type: Object as PropType<any>,
    dfault: () => {}
  }
})

onMounted(() => {
  console.log(props.fixedDate)
  const firstDay = new Date(props.fixedDate.year, props.fixedDate.issue - 1, 2)
  // 获取传入月份的最后一天
  const lastDay = new Date(props.fixedDate.year, props.fixedDate.issue, 1)

  // 格式化日期为"YYYY-MM-DD"形式
  firstDayFormatted.value = firstDay.toISOString().split('T')[0]
  lastDayFormatted.value = lastDay.toISOString().split('T')[0]
  console.log(firstDayFormatted.value, lastDayFormatted.value)
})

const emit = defineEmits(['success'])

const [registerTable] = useTable({
  title: '结算Gbuilder信息',
  api: getCredentialList,
  searchInfo: { type: 18 },
  columns,
  showTableSetting: false,
  rowKey: 'id',
  beforeFetch: (params) => {
    return {
      ...params,
      date1: firstDayFormatted.value,
      date2: lastDayFormatted.value
    }
  },
  showIndexColumn: false,
  useSearchForm: false
})

setTimeout(() => {
  emit('success', true)
}, 3000)
</script>
