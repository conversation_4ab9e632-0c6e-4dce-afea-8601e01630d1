<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="35%" show-footer @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup>
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { schemas } from '../datas/drawer'
import { createEndDate } from '/@/api/credential/credential'
const emit = defineEmits(['success', 'register'])

const [registerForm, { validate, resetFields }] = useForm({
  schemas,
  baseColProps: { span: 12 },
  showActionButtonGroup: false,
  colon: true
})

const [registerDrawer, { closeDrawer }] = useDrawerInner(async () => {
  await resetFields()
  // 需要初始化值
})

async function handleSubmit() {
  const valid = await validate()
  console.log(valid)
  await createEndDate(valid)
  emit('success')
  closeDrawer()
}
</script>
<style></style>
