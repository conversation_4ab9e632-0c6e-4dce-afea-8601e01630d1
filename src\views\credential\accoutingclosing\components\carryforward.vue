<template>
  <Card :bordered="false" title="试算信息">
    <BasicForm @register="registertriaForm" />
  </Card>
  <BasicTable @register="registerTable">
    <template #toolbar> </template>
  </BasicTable>
</template>
<script setup lang="ts">
import { BasicForm, useForm } from '/@/components/Form'
import { BasicTable, useTable } from '/@/components/Table'
import { Card } from 'ant-design-vue'
import { trialSchemas } from '../../credential/datas/modal'
import { getCredentialList } from '/@/api/credential/credential'
import { columns } from '../../credential/datas/datas'

// const emit = defineEmits(['success'])

// const props = defineProps({
//   fixedDate: {
//     type: Object,
//     dfault: () => {}
//   }
// })

const [registertriaForm, {}] = useForm({
  schemas: trialSchemas,
  baseColProps: { span: 8 },
  labelWidth: 100,
  showResetButton: false,
  // showActionButtonGroup: false,
  colon: true,
  actionColOptions: {
    span: 24
  }
})

const [registerTable] = useTable({
  title: '凭证信息',
  api: getCredentialList,
  columns,
  showTableSetting: false,
  rowKey: 'id',
  showIndexColumn: false,
  useSearchForm: false
})
</script>
