<template>
  <Card :bordered="false">
    <template #title>
      <div>凭证年份:{{ props.fixedDate.year }}年</div>
      <div>凭证月份:{{ props.fixedDate.issue }}月</div>
    </template>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Tooltip title="如若数据不对,可删除后再次新增列表数据">
          <Button @click="handleDelete" type="primary" :loading="buttonlodaing">删除</Button>
        </Tooltip>
        <Tooltip title="审核结转损益的月份信息为试算平衡月份">
          <Button @click="handleEdit" type="primary" :loading="buttonlodaing">审核</Button>
        </Tooltip>
        <Tooltip title="生成结转损益的月份信息为试算平衡月份">
          <Button @click="handleadd" type="primary" :loading="buttonlodaing">新增</Button>
        </Tooltip>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <Tag :color="statustype[record.status]?.color"> {{ statustype[record.status]?.label }}</Tag>
        </template>
        <template v-if="column.key === 'type'">
          <Tag :color="mapType[record.type]?.color"> {{ mapType[record.type]?.label }}</Tag>
        </template>
      </template>
    </BasicTable>
  </Card>
</template>
<script setup lang="ts">
import { BasicTable, useTable } from '/@/components/Table'
import { deptcheckOutCreate, deptdelete, deptsetStatus, getCredentialList } from '/@/api/credential/credential'
import { columns, statustype } from '../../credential/datas/datas'
import { Tag, Button, message, Card } from 'ant-design-vue'
import { onMounted, ref } from 'vue'
import { mapType } from '../../credential/datas/modal'
//月份第一天
const firstDayFormatted = ref('')
//月份最后一天
const lastDayFormatted = ref('')
//lodaing
const buttonlodaing = ref(false)

const props = defineProps({
  fixedDate: {
    type: Object as PropType<any>,
    dfault: () => {}
  }
})

onMounted(() => {
  console.log(props.fixedDate)
  const firstDay = new Date(props.fixedDate.year, props.fixedDate.issue - 1, 2)
  // 获取传入月份的最后一天
  const lastDay = new Date(props.fixedDate.year, props.fixedDate.issue, 1)

  // 格式化日期为"YYYY-MM-DD"形式
  firstDayFormatted.value = firstDay.toISOString().split('T')[0]
  lastDayFormatted.value = lastDay.toISOString().split('T')[0]
  console.log(firstDayFormatted.value, lastDayFormatted.value)
})

const emit = defineEmits(['success'])

const [registerTable, { reload }] = useTable({
  title: '部门内部调转信息',
  api: getCredentialList,
  searchInfo: { type: 19 },
  columns,
  showTableSetting: false,
  rowKey: 'id',
  beforeFetch: (params) => {
    return {
      ...params,
      date1: firstDayFormatted.value,
      date2: lastDayFormatted.value
    }
  },
  afterFetch: (res) => {
    console.log(res)
    if (res.length == 0) {
      setTimeout(() => {
        emit('success', true)
      }, 3000)
    } else if (res.some((item: any) => item.status == 1)) {
      emit('success', true)
    } else if (res.some((item: any) => item.status == 0)) {
      emit('success', false)
    }
    return res
  },
  showIndexColumn: false,
  useSearchForm: false
})

async function handleDelete() {
  try {
    buttonlodaing.value = true
    const res = await deptdelete(props.fixedDate)
    if (res.news == 'success') {
      message.success('删除成功')
      reload()
      buttonlodaing.value = false
    }
  } catch (error) {
    buttonlodaing.value = false
  }
}
async function handleEdit() {
  try {
    buttonlodaing.value = true
    const res = await deptsetStatus(props.fixedDate)
    if (res.news == 'success') {
      message.success('审核成功')
      emit('success', true)
      reload()
      buttonlodaing.value = false
    }
  } catch (error) {
    buttonlodaing.value = false
  }
}

async function handleadd() {
  try {
    buttonlodaing.value = true
    const res = await deptcheckOutCreate(props.fixedDate)
    if (res.news == 'success') {
      message.success('新增成功')
      reload()
      buttonlodaing.value = false
    }
  } catch (error) {
    buttonlodaing.value = false
  }
}
</script>
