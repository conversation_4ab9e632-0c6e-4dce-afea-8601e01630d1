<template>
  <Card :bordered="false">
    <template #title>
      <div>凭证年份:{{ props.fixedDate.year }}年</div>
      <div>凭证月份:{{ props.fixedDate.issue }}月</div>
    </template>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Tooltip title="如若数据不对,可删除后再次新增列表数据">
          <Button @click="handleDelete" type="primary" :loading="buttonlodaing">删除</Button>
        </Tooltip>
        <Tooltip title="审核结转损益的月份信息为试算平衡月份">
          <Button @click="handleEdit" type="primary" :loading="buttonlodaing">审核</Button>
        </Tooltip>
        <Tooltip title="生成结转损益的月份信息为试算平衡月份">
          <Button @click="handleadd" type="primary" :loading="buttonlodaing">新增</Button>
        </Tooltip>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <Tag :color="statustype[record.status]?.color"> {{ statustype[record.status]?.label }}</Tag>
        </template>
      </template>
    </BasicTable>
  </Card>
</template>
<script setup lang="ts">
import { BasicTable, useTable } from '/@/components/Table'
import { Card, message, Tooltip, Tag, Button } from 'ant-design-vue'
import { columns, statustype } from '../../lossandgainbroughtforward/datas/datas'
import { checkOutpl, getplList, setdelete, setStatuspl } from '/@/api/credential/lossa'
import { ref } from 'vue'

const emit = defineEmits(['success', 'changeLoadingFn'])

const buttonlodaing = ref(false)

const props = defineProps({
  fixedDate: {
    type: Object as PropType<any>,
    dfault: () => {}
  }
})

const [registerTable, { reload }] = useTable({
  title: '结转损益凭证信息',
  api: getplList,
  columns,
  showTableSetting: false,
  beforeFetch: (params) => {
    return {
      ...params,
      year: props.fixedDate.year,
      issue: props.fixedDate.issue
    }
  },
  afterFetch: (res) => {
    console.log(res)
    if (res.length == 0) {
      setTimeout(() => {
        emit('success', true)
      }, 3000)
    } else if (res.some((item: any) => item.status == 1)) {
      emit('success', true)
    } else if (res.some((item: any) => item.status == 0)) {
      emit('success', false)
    }
    return res
  },
  rowKey: 'id',
  showIndexColumn: false,
  useSearchForm: false
})

async function handleDelete() {
  try {
    buttonlodaing.value = true
    const res = await setdelete(props.fixedDate)
    if (res.news == 'success') {
      message.success('删除成功')
      reload()
      buttonlodaing.value = false
    }
  } catch (error) {
    buttonlodaing.value = false
  }
}
async function handleEdit() {
  try {
    buttonlodaing.value = true
    const res = await setStatuspl(props.fixedDate)
    if (res.news == 'success') {
      message.success('审核成功')
      emit('success', true)
      reload()
    }
  } catch (error) {
    buttonlodaing.value = false
  }
}
async function handleadd() {
  try {
    buttonlodaing.value = true
    const res = await checkOutpl(props.fixedDate)
    if (res.news == 'success') {
      message.success('新增成功')
      reload()
      buttonlodaing.value = false
    }
  } catch (error) {
    buttonlodaing.value = false
  }
}
</script>
