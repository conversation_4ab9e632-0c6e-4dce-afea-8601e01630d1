<template>
  <Card :bordered="false" title="试算信息">
    <BasicForm @register="registertriaForm" />
  </Card>
  <BasicTable @register="registerTable">
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'status'">
        <Tag :color="statustype[record.status]?.color"> {{ statustype[record.status]?.label }}</Tag>
      </template>
      <template v-if="column.key === 'type'">
        <Tag :color="mapType[record.type]?.color"> {{ mapType[record.type]?.label }}</Tag>
      </template>
    </template>
  </BasicTable>
</template>
<script setup lang="ts">
import { BasicForm, useForm } from '/@/components/Form'
import { BasicTable, useTable } from '/@/components/Table'
import { Card, message, Tag } from 'ant-design-vue'
import { trialSchemas } from '../../credential/datas/modal'
import { getCredentialList, trialBalancing } from '/@/api/credential/credential'
import { columns, statustype } from '../../credential/datas/datas'
import { mapType } from '../../credential/datas/modal'

const emit = defineEmits(['success', 'fixed'])

const [registertriaForm, { validate }] = useForm({
  schemas: trialSchemas,
  baseColProps: { span: 8 },
  labelWidth: 100,
  showResetButton: false,
  colon: true,
  actionColOptions: {
    span: 24
  },
  submitFunc: trialsubmit
})

const [registerTable, { setLoading, setProps, reload }] = useTable({
  title: '凭证信息',
  api: getCredentialList,
  columns,
  showTableSetting: false,
  rowKey: 'id',
  showIndexColumn: false,
  useSearchForm: false
})

async function trialsubmit() {
  const formdata = await validate()
  const res = await trialBalancing(formdata)

  const firstDay = new Date(formdata.year, formdata.issue - 1, 2)
  // 获取传入月份的最后一天
  const lastDay = new Date(formdata.year, formdata.issue, 1)

  // 格式化日期为"YYYY-MM-DD"形式
  const firstDayFormatted = firstDay.toISOString().split('T')[0]
  const lastDayFormatted = lastDay.toISOString().split('T')[0]

  setLoading(true)
  setProps({
    api: getCredentialList,
    searchInfo: {
      date1: firstDayFormatted,
      date2: lastDayFormatted
    }
  })
  reload()
  setTimeout(() => {
    setLoading(false)
  }, 500)

  if (res.news == 'success') {
    emit('success', true)
    emit('fixed', { year: formdata.year, issue: formdata.issue })
    message.success('试算成功')
  } else {
    message.error('试算失败,请前往凭证管理页面进行凭证信息的核对')
  }
}
</script>
