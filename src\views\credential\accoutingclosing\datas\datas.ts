import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { Tag } from 'ant-design-vue'
const lock = {
  2: { label: '最新锁定', color: 'green' },
  1: { label: '已锁定', color: 'orange' },
  0: { label: '未锁定', color: 'red' }
}

export const columns: BasicColumn[] = [
  {
    dataIndex: 'date',
    title: '结算日期',
    width: 100,
    resizable: true
  },
  {
    dataIndex: 'is_lock',
    title: '锁定状态',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return h(Tag, { color: lock[text].color }, lock[text].label)
    }
  },
  {
    dataIndex: 'creator_name',
    title: '创建人',
    width: 100,
    resizable: true
  },

  {
    dataIndex: 'year',
    title: '选择年份',
    width: 100,
    resizable: true
  },
  {
    dataIndex: 'issue',
    title: '选择月份',
    width: 100,
    resizable: true
  },

  {
    dataIndex: 'created_at',
    title: '创建日期',
    width: 100,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'date',
    label: '结算日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  }
]
