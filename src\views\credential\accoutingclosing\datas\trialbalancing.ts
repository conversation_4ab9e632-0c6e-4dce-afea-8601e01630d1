import { FormSchema } from '/@/components/Form'

export const deptschemas: FormSchema[] = [
  {
    field: 'year',
    label: '结账年份',
    component: 'DatePicker',
    required: true,
    componentProps: {
      picker: 'year',
      valueFormat: 'YYYY',
      style: {
        width: '100%'
      }
    },
    dynamicDisabled: true
  },
  {
    field: 'issue',
    label: '结账月份',
    component: 'Select',
    required: true,
    componentProps: {
      options: [
        {
          label: '1月',
          value: 1
        },
        {
          label: '2月',
          value: 2
        },
        {
          label: '3月',
          value: 3
        },
        {
          label: '4月',
          value: 4
        },
        {
          label: '5月',
          value: 5
        },
        {
          label: '6月',
          value: 6
        },
        {
          label: '7月',
          value: 7
        },
        {
          label: '8月',
          value: 8
        },
        {
          label: '9月',
          value: 9
        },
        {
          label: '10月',
          value: 10
        },
        {
          label: '11月',
          value: 11
        },
        {
          label: '12月',
          value: 12
        }
      ]
    },
    dynamicDisabled: true
  }
]
