<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button v-if="hasPermission([333])" @click="handadd" type="primary">新增</Button>
        <Button v-if="hasPermission([333])" @click="handaddflow" type="primary">流程新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <addclosingDrawer @register="registerDrawer" @success="reload" />
    <FlowDrawer @register="registerFlowDrawer" @success="reload" />
  </div>
</template>
<script setup lang="ts">
import { useTable, BasicTable, ActionItem, TableAction } from '/@/components/Table'
import { columns, schemas } from './datas/datas'
import { Button } from 'ant-design-vue'
import addclosingDrawer from './components/addclosingDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { getEndDateList, getdelEndDate } from '/@/api/credential/credential'
import { usePermission } from '/@/hooks/web/usePermission'
import FlowDrawer from './components/FlowDrawer.vue'

const { hasPermission } = usePermission()

const [registerTable, { reload }] = useTable({
  title: '财务结算',
  api: getEndDateList,
  columns,
  showTableSetting: true,
  tableSetting: { redo: false },
  rowKey: 'id',
  showIndexColumn: false,
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  pagination: {
    pageSize: 10,
    pageSizeOptions: ['10', '50', '100'],
    position: ['bottomRight']
  },
  useSearchForm: true,
  formConfig: {
    schemas,
    baseColProps: { span: 6 },
    labelCol: { span: 4 },
    fieldMapToTime: [['date', ['date1', 'date2'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  }
})
const [registerDrawer, { openDrawer }] = useDrawer()

function createActions(record): ActionItem[] {
  return [
    {
      icon: 'clarity:note-edit-line',
      label: '删除',
      onClick: handledelete.bind(null, record),
      disabled: record.is_lock !== 2,
      ifShow: hasPermission([410])
    }
  ]
}
function handledelete(record) {
  getdelEndDate({ id: record.id }).then(() => {
    reload()
  })
}
function handadd() {
  openDrawer(true, {})
}

//流程
const [registerFlowDrawer, { openDrawer: openFlowDrawer }] = useDrawer()
function handaddflow() {
  openFlowDrawer(true, {})
}
</script>
