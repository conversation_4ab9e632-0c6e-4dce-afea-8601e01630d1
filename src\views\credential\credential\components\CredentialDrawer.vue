<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="新增" show-footer @ok="handleSubmit" width="50%">
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { createCredential } from '/@/api/credential/credential'
import { schemas } from '../datas/drawer'

const emit = defineEmits(['success', 'register'])

const [registerDrawer, { changeLoading, changeOkLoading, closeDrawer }] = useDrawerInner(async () => {
  try {
    changeLoading(true)
    resetFields()
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
})

const [registerForm, { resetFields, validate }] = useForm({
  schemas,
  baseColProps: { span: 12 },
  showActionButtonGroup: false,
  colon: true,
  labelCol: { style: { width: '70px' } }
})

async function handleSubmit() {
  try {
    await changeOkLoading(true)
    const formData = await validate()
    await createCredential({
      ...formData,
      date: undefined,
      date1: formData.date ? formData.date[0] : undefined,
      date2: formData.date ? formData.date[1] : undefined
    })
    setTimeout(async () => {
      await closeDrawer()
      changeOkLoading(false)
      emit('success')
    }, 1000)
    // changeOkLoading(false)
  } catch (err) {
    changeOkLoading(false)
    throw new Error(`${err}`)
  }
}
</script>
