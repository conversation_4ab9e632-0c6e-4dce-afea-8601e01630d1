<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="编辑" show-footer @ok="handleSubmit" width="35%">
    <BasicForm @register="registerForm" @field-value-change="handvaluechange">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg2Stocking"
          :custom-request="handleFileRequest"
          :multiple="true"
          :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
        >
          <a-button type="primary">
            <upload-outlined />
            Upload
          </a-button>
        </Upload>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { editschema } from '../datas/drawer'
import { postupdate } from '/@/api/credential/credential'
import { ref, watch } from 'vue'
import { UploadFile, Upload, message } from 'ant-design-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'

const emit = defineEmits(['success', 'register'])
const propsData = ref()
//附件
const filesList = ref<UploadFile[]>([])

const [registerForm, { resetFields, validate, setFieldsValue, resetSchema, updateSchema }] = useForm({
  baseColProps: { span: 24 },
  showActionButtonGroup: false,
  colon: true,
  labelCol: { style: { width: '120px' } }
})

const [registerDrawer, { changeLoading, changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  try {
    console.log(data)

    changeLoading(true)
    resetFields()
    await resetSchema(editschema(data.sales_strid ? true : false))
    setFieldsValue({ ...data, corres_type: data.corres_type == 0 ? undefined : data.corres_type })
    filesList.value = data?.files?.map((item) => ({ url: item, name: item, uid: item }))
    propsData.value = data
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
})

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val?.map((item) => item.url) ?? [] })
  }
)
async function handleSubmit() {
  try {
    await changeOkLoading(true)
    const formData = await validate()
    console.log(formData)
    delete formData.department
    delete formData.clear_department
    const params = {
      ...formData,
      category_id: formData.category_id.toString(),
      id: propsData.value.id
    }
    await postupdate(params)

    emit('success')
    await closeDrawer()
    changeOkLoading(false)
  } catch (err) {
    changeOkLoading(false)
    throw new Error(`${err}`)
  }
}

//修改借贷款输入
function handvaluechange(key, value) {
  if (key == 'amount0') {
    setFieldsValue({ amount1: value ? 0 : undefined })
    updateSchema({
      field: 'amount1',
      componentProps: {
        disabled: value ? true : false
      }
    })
  } else if (key == 'amount1') {
    setFieldsValue({ amount0: value ? 0 : undefined })
    updateSchema({
      field: 'amount0',
      componentProps: {
        disabled: value ? true : false
      }
    })
  } else if (key == 'corres_type') {
    updateSchema({
      field: 'corres_pondent',
      required: true
    })
  }
}

//附件上传
async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    changeOkLoading(true)
    const result = await commonFileUpload(file, 'purchase')
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return {
        url: (item.url as string) || (item.response as string),
        uid: item.uid,
        name: item.name
      }
    })
    await setFieldsValue({
      files: filesList.value.map((item) => item.url)
    })
    const isAllDone = filesList.value.every((item) => item.url)
    if (isAllDone) {
      changeOkLoading(false)
    }
  } catch (e) {
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}
</script>
