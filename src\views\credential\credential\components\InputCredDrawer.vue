<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="录入凭证" show-footer @ok="handleSubmit" width="100%">
    <BasicForm @register="registerForm">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg2Stocking"
          :custom-request="handleFileRequest"
          :multiple="true"
          :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
        >
          <a-button type="primary">
            <upload-outlined />
            Upload
          </a-button>
        </Upload>
      </template>
    </BasicForm>
    <Alert message="手动凭证生成时,明细必须为一借一贷" show-icon />
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button @click="handleAddDetail" type="primary">新增</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :stop-button-propagation="true" />
        </template>
      </template>
      <template #footer>
        <div class="footer">
          <span style="color: green" class="mr-8 font-weight-bold"
            >借方金额合计：{{ formateerNotCurrency.format(allTotalPriceArr[0]) }}</span
          >
          <span style="color: orange" class="mr-8 font-weight-bold"
            >贷方外汇金额合计：{{ formateerNotCurrency.format(allTotalPriceArr[1]) }}</span
          >
        </div>
      </template>
    </BasicTable>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit('sole')" :loading="buttonLoading">确认</a-button>
      <a-button type="primary" @click="handleSubmit('batch')" :loading="buttonLoading">确认并录入下一条</a-button>
    </template>
    <DetailModal @register="registerModal" @add-success="handleAddSuccess" @update-success="handleUpdate" />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { message, Alert, Upload, UploadFile } from 'ant-design-vue'
import { cloneDeep, throttle } from 'lodash-es'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import type { ActionItem } from '/@/components/Table'
import type { EditRecordRow } from '/@/components/Table'
import { useModal } from '/@/components/Modal'
import { batchImportERP, getOrderNumber } from '/@/api/credential/credential'
import { add } from '/@/utils/math'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { UploadOutlined } from '@ant-design/icons-vue'

import { batchInputSchemas, categoryRef, childrenColumns, currentEditKeyRef, getDetailColumns, propsToKeep } from '../datas/drawer'
import DetailModal from './DetailModal.vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'

const emit = defineEmits(['registerTable', 'registerForm', 'registerDrawer', 'success'])

const beforeRecord: any = ref()
//部门列表
const deptlist = ref<any>([])
//借方金额
const allTotalPriceArr = ref([0, 0])
const buttonLoading = ref(false)
//附件
const filesList = ref<UploadFile[]>([])
//生成方式
const Isenter = ref(1)
const [registerModal, { openModal }] = useModal()

const [registerDrawer, { changeLoading, changeOkLoading, closeDrawer }] = useDrawerInner(async () => {
  try {
    currentEditKeyRef.value = ''
    await changeLoading(true)
    setTableData([])
    resetFields()
    Isenter.value = 1
    filesList.value = []
    await setColumns(await getDetailColumns(handList, { updateTableDataRecord }))
    const { items } = await getOrderNumber()
    filesList.value = items?.files?.map((item) => ({ url: item, name: item, uid: item }))
    setFieldsValue({ order_number: items.order_number })
  } catch (err) {
    console.error(err)
  } finally {
    changeLoading(false)
  }
})

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val?.map((item) => item.url) ?? [] })
  }
)

const [registerTable, { getDataSource, setTableData, insertTableDataRecord, deleteTableDataRecord, updateTableDataRecord, setColumns }] =
  useTable({
    showIndexColumn: false,
    columns: getDetailColumns(handList),
    dataSource: [],
    pagination: false,
    rowKey: 'key',
    bordered: true,
    title: '凭证明细',
    canResize: false,
    actionColumn: {
      width: 150,
      title: '操作',
      dataIndex: 'action'
    }
  })

const [registerForm, { resetFields, validate, setFieldsValue }] = useForm({
  schemas: batchInputSchemas,
  baseColProps: { span: 6 },
  showActionButtonGroup: false,
  colon: true,
  labelCol: { style: { width: '80px' } }
})

function handleAddDetail() {
  openModal(true, { isUpdate: false })
}

//写入表格
async function handleAddSuccess(params) {
  deptlist.value = params.deptlist
  params.deptlist.forEach((item) => {
    if (params.params.dept_id == item.id) {
      params.params.dept_name = item.name
    }
    if (params.params.clear_dept_id == item.id) {
      params.params.clear_dept_name = item.name
    }
  })
  await insertTableDataRecord(params.params)
  const datas = await getDataSource()
  allTotalPriceArr.value[0] = datas.reduce((pre, cur) => add(pre, cur.amount0), 0)
  allTotalPriceArr.value[1] = datas.reduce((pre, cur) => add(pre, cur.amount1), 0)
}

function createActions(record: EditRecordRow): ActionItem[] {
  if (!record.editable) {
    return [
      {
        // label: '编辑',
        icon: 'clarity:note-edit-line',
        tooltip: '编辑',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        onClick: handleEdit.bind(null, record)
      },
      {
        // label: '复制',
        icon: 'ant-design:copy-outlined',
        tooltip: '复制',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        onClick: throttleHandleCopy.bind(null, record)
      },
      {
        // label: '删除',
        icon: 'ant-design:delete-outlined',
        color: 'error',
        tooltip: '删除',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        popConfirm: {
          title: '是否确认删除',
          placement: 'left',
          confirm: handleDelete.bind(null, record)
        }
      }
    ]
  }
  return [
    {
      // label: '保存',
      icon: 'ant-design:check-outlined',
      tooltip: '保存',

      onClick: handleSave.bind(null, record)
    },
    {
      // label: '取消',
      icon: 'ant-design:close-outlined',
      color: 'error',
      tooltip: '取消',
      popConfirm: {
        title: '是否取消编辑',
        placement: 'left',
        confirm: handleCancelClick.bind(null, record)
      }
    }
  ]
}

function handleEdit(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  beforeRecord.value = cloneDeep(record)
  categoryRef.value = record.category
  if (record.corres_type === 5) {
    setColumns(childrenColumns(handList))
  } else {
    setColumns(getDetailColumns(handList))
  }
  record.onEdit?.(true, false)
}

async function handleCopy(record: EditRecordRow) {
  await insertTableDataRecord({ ...record, key: Date.now() })
}

//使用节流
const throttleHandleCopy = throttle(handleCopy, 300)
async function handleDelete(record: EditRecordRow) {
  await deleteTableDataRecord(record.key)
}

async function handleSave(record: EditRecordRow) {
  // 校验
  message.loading({ content: '正在保存...', duration: 0, key: 'saving' })
  const valid = await record.onValid?.()
  if (valid) {
    try {
      const data = cloneDeep(record.editValueRefs)
      console.log(data)
      //TODO 此处将数据提交给服务器保存
      // ...
      // 保存之后提交编辑状态
      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
      }
      categoryRef.value = undefined
      message.success({ content: '数据已保存', key: 'saving' })
    } catch (error) {
      message.error({ content: '保存失败', key: 'saving' })
    }
  } else {
    message.error({ content: '请填写正确的数据', key: 'saving' })
  }
}

function handleCancelClick(record: EditRecordRow) {
  currentEditKeyRef.value = ''
  record.onEdit?.(false, false)
  updateTableDataRecord(record.key, beforeRecord.value)
}

async function handleUpdate(params) {
  await updateTableDataRecord(params.key, params)
}

function handleCancel() {
  //还要清空表格
  setTableData([])
  resetFields()
  closeDrawer()
}

async function handleSubmit(type: 'sole' | 'batch') {
  try {
    changeOkLoading(true)
    buttonLoading.value = true
    const { date, order_number, files } = await validate()

    const details = getDataSource()
    if (details.length === 0) return message.error('请添加明细')
    const amount0Total = details.reduce((pre, cur) => add(pre, cur.amount0), 0)
    const amount1Total = details.reduce((pre, cur) => add(pre, cur.amount1), 0)

    if (amount0Total !== amount1Total) {
      return message.error('贷方金额总额与借方金额总额不等')
    }
    if (details.length < 2) {
      return message.error('明细数据不足2条,不能只为借方或贷方')
    }
    details.forEach((item: any) => {
      deptlist.value.forEach((val: any) => {
        if (item.dept_name == val.name) {
          item.dept_id = val.id
        }
        if (item.clear_dept_name == val.name) {
          item.clear_dept_id = val.id
        }
      })
    })

    const items = details.map((item) => {
      //当有借方金额就将资金来源给到收款资金资料//当有贷方金额就将资金来源给到付款资金资料
      const data = { ...item, date, files, order_number, category: item.category, is_enter: Isenter.value }

      const filteredItem = Object.fromEntries(Object.entries(data).filter(([key]) => propsToKeep[key]))
      return filteredItem
    })

    await batchImportERP({ items })
    switch (type) {
      case 'sole':
        closeDrawer()
        break
    }

    setTimeout(() => {
      resetFields()
      setTableData([])
      emit('success')
      buttonLoading.value = false
    })
  } catch (err) {
    console.error('提交日志出错', err)
    changeOkLoading(false)
    buttonLoading.value = false
    throw new Error(`${err}`)
  } finally {
    changeOkLoading(false)
    buttonLoading.value = false
  }
}
//获取部门
function handList(e) {
  console.log(e)
  if (e.value === 5) {
    setColumns(childrenColumns(handList))
  } else {
    setColumns(getDetailColumns(handList))
  }
  if (Object.keys(e).length > 0) {
    deptlist.value.push(e)
  }
}

//附件上传
async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    buttonLoading.value = true
    const result = await commonFileUpload(file, 'purchase')
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      buttonLoading.value = false
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return {
        url: (item.url as string) || (item.response as string),
        uid: item.uid,
        name: item.name
      }
    })
    await setFieldsValue({
      files: filesList.value.map((item) => item.url)
    })
    const isAllDone = filesList.value.every((item) => item.url)
    if (isAllDone) {
      buttonLoading.value = false
    }
  } catch (e) {
    buttonLoading.value = false
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}
</script>

<style scoped lang="less">
:deep(.vben-basic-table) {
  font-size: 12px;
}
</style>
