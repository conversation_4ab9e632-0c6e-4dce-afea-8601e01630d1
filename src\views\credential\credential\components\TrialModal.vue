<template>
  <BasicModal @register="registerModal" v-bind="$attrs" @ok="handleSubmit" :minHeight="540" :width="600">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'

import { trialSchemas } from '../datas/modal'
import { trialBalancing } from '/@/api/credential/credential'
import { ref } from 'vue'

const emit = defineEmits(['success', 'register'])
const show = ref(false)

const [registerForm, { resetFields, validate }] = useForm({
  showActionButtonGroup: false,
  schemas: trialSchemas,
  layout: 'vertical',
  baseColProps: { span: 24 }
  //   colon: true
})

const [registerModal, { closeModal }] = useModalInner(() => {
  resetFields()
  show.value = false
})

async function handleSubmit() {
  const vaildata = await validate()
  console.log(vaildata)

  await trialBalancing(vaildata)

  emit('success')
  closeModal()
}
</script>
