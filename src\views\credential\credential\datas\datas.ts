// import dayjs from 'dayjs'
import type { BasicColumn, FormSchema } from '/@/components/Table'

import { mapType, mapWorkListFn } from './modal'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import dayjs from 'dayjs'
import { getDeptSelectTree } from '/@/api/admin/dept'
import { add } from '/@/utils/math'
import { message } from 'ant-design-vue'
import { isNullOrUnDef } from '/@/utils/is'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { getDept } from '/@/api/erp/systemInfo'
import { isNumber } from 'lodash-es'
import { GET_STATUS_SCHEMA } from '/@/const/status'
import { getCategory } from '/@/api/financialDocuments/otherIncome'

const srctype = {
  1: { label: '已审核' },
  2: { label: '可备货' },
  3: { label: '结算' },
  4: { label: '清点完成' },
  5: { label: '取消作废' }
}
export const statustype = {
  0: { label: '未审核', color: 'red' },
  1: { label: '已审核', color: 'green' }
}
const correstype = {
  5: { label: '其他' },
  4: { label: '供应商' },
  3: { label: '客户' },
  2: { label: '部门' },
  1: { label: '员工' }
}

const accounttype = {
  1: { label: '资产类' },
  2: { label: '负债类' },
  3: { label: '权益类' },
  4: { label: '成本类' },
  5: { label: '损益类' }
}
const balancedirection = {
  1: { label: '借方', color: 'red' },
  2: { label: '贷方', color: 'green' }
}

const isreport = {
  0: { label: '待处理' },
  1: { label: '否' },
  2: { label: '是' }
}
const iscash = {
  0: { label: '否' },
  1: { label: '是' },
  2: { label: '待处理' }
}

export const columns: BasicColumn[] = [
  {
    dataIndex: 'order_no',
    title: '系统单号',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'strid',
    title: '单据单号',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'order_number',
    title: '凭证单号',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'type',
    title: '单据类型',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'src',
    title: '单据来源',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return value ? useRender.renderTag(srctype[value].label) : '-'
    }
  },
  {
    dataIndex: 'creator_name',
    title: '创建人',
    width: 150,
    resizable: true
  },
  {
    dataIndex: 'status',
    title: '状态',
    width: 150,
    resizable: true
  },
  {
    dataIndex: 'sales_strid',
    title: '关联的销售订单号',
    width: 150,
    resizable: true
  },
  {
    dataIndex: 'par_strid',
    title: '单据上级单号',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'account_type',
    title: '科目类别',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return value ? useRender.renderTag(accounttype[value].label) : '-'
    }
  },
  {
    dataIndex: 'balance_direction',
    title: '余额方向',
    width: 120,
    resizable: true,
    customRender({ value }) {
      return value ? useRender.renderTag(balancedirection[value].label) : '-'
    }
  },
  {
    dataIndex: 'is_cash',
    title: '是否计算现金流报表',
    width: 150,
    resizable: true,
    customRender({ value }) {
      return isNumber(value) ? useRender.renderTag(iscash[value].label) : '-'
    }
  },
  {
    dataIndex: 'is_report',
    title: '是否对公司报表有影响',
    width: 150,
    resizable: true,
    customRender({ value }) {
      return isNumber(value) ? useRender.renderTag(isreport[value].label) : '-'
    }
  },
  {
    dataIndex: 'is_dept_report',
    title: '是否对部门报表有影响',
    width: 150,
    resizable: true,
    customRender({ value }) {
      return isNumber(value) ? useRender.renderTag(isreport[value].label) : '-'
    }
  },
  {
    dataIndex: 'department',
    title: '部门',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'clear_department',
    title: '结算部门',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'amount0',
    title: '借方金额(元)',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'amount1',
    title: '贷方金额(元)',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'category',
    title: '科目名称',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'corres_type',
    title: '往来单位类型',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return value ? useRender.renderTag(correstype[value].label) : '-'
    }
  },
  {
    dataIndex: 'corres_pondent',
    title: '往来单位',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return value ? value : '-'
    }
  },
  {
    dataIndex: 'business',
    title: '业务对象',
    width: 150,
    resizable: true
  },
  {
    dataIndex: 'capital',
    title: '资金资料',
    width: 150,
    resizable: true
  },
  {
    dataIndex: 'share_inCharge_name',
    title: '分摊人员',
    width: 150,
    resizable: true
  },

  {
    dataIndex: 'year',
    title: '会计年度',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'issue',
    title: '会计期间',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'day',
    title: '天',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'date',
    title: '凭证日期',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'remark',
    title: '备注',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'files',
    title: '附件',
    width: 120,
    resizable: true
  }
]

const status_schema = GET_STATUS_SCHEMA([
  { label: '未审核', value: 0 },
  { label: '已审核', value: 1 }
])

export const schemas: FormSchema[] = [
  status_schema,
  {
    field: 'date',
    label: '凭证日期',
    component: 'SingleRangeDate',
    defaultValue: [dayjs().add(-7, 'day').format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')],
    componentProps: {
      allowEmpty: [true, true],
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  },
  // {
  //   field: 'status',
  //   label: '状态',
  //   component: 'Select',
  //
  //   componentProps: {
  //     options: [
  //       { label: '未审核', value: 0 },
  //       { label: '已审核', value: 1 }
  //     ]
  //   }
  // },
  {
    field: 'strid',
    label: '单据单号',
    component: 'Input'
  },
  {
    field: 'order_no',
    label: '系统单号',
    component: 'Input'
  },
  {
    field: 'order_number',
    label: '凭证单号',
    component: 'Input'
  },
  {
    field: 'business',
    label: '业务对象',
    component: 'Input'
  },
  {
    field: 'sales_strid',
    label: '关联的销售订单号',
    component: 'Input'
  },

  {
    field: 'src',
    label: '单据来源',
    component: 'Select',

    componentProps: {
      options: [
        { label: '已审核', value: 1 },
        { label: '可备货(销售单)', value: 2 },
        { label: '结算(销售单，采购单)', value: 3 },
        { label: '清点完成(入库单，出库单，退货单)', value: 4 },
        { label: '取消作废(流水单,其他支出单,其他收入单)', value: '5' }
      ]
    }
  },
  {
    field: 'type',
    label: '单据类型',
    component: 'Select',

    componentProps: () => {
      const options = Object.keys(mapType).map((item) => ({ label: mapType[item].label, value: item }))
      return {
        options
      }
    }
  },
  {
    field: 'par_strid',
    label: '单据上级单号',
    component: 'Input'
  },
  {
    field: 'category',
    label: '科目名称',
    component: 'Input'
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptSelectTree,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        treeDefaultExpandAll: true,
        showSearch: true,
        treeLine: {
          showLeafIcon: false
        },
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  {
    field: 'clear_dept_id',
    label: '结算部门',
    component: 'PagingApiSelect',
    componentProps: {
      api: getDept,
      params: { is_production: 1, is_audit: 1 },
      resultField: 'items',
      labelField: 'name',
      valueField: 'id',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        style: {
          width: '100%'
        }
      }
    }
  },
  {
    field: 'corres_type',
    label: '往来类型',
    component: 'Select',
    componentProps: {
      options: [
        { label: '供应商', value: 4 },
        { label: '客户', value: 3 },
        { label: '部门', value: 2 },
        { label: '员工', value: 1 }
      ]
    }
  },
  {
    field: 'corres_pondent',
    label: '往来单位',
    component: 'PagingApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: isNullOrUnDef(formModel.corres_type) ? getCreatorList : mapWorkListFn[formModel.corres_type],
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        pagingSize: 10,
        params: {
          type: 3,
          status: [1, 3, 4, 5, 15]
        },
        selectProps: {
          fieldNames: {
            value: 'id',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          style: {
            width: '100%'
          }
        }
      }
    }
  },
  {
    field: 'no_amount',
    label: '金额是否为0',
    component: 'Select',
    componentProps: {
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ]
    }
  },
  {
    field: 'remark',
    label: '摘要',
    component: 'InputTextArea'
  },
  {
    field: 'amount0',
    label: '借方金额',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      precision: 2
    }
  },
  {
    field: 'amount1',
    label: '贷方金额',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      precision: 2
    }
  },
  {
    field: 'amount',
    label: '借贷金额',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      precision: 2
    }
  },
  {
    field: 'category_arr',
    label: '科目名称',
    component: 'PagingApiSelect',
    componentProps: {
      api: getCategory,
      resultField: 'items',
      labelField: 'account_name',
      valueField: 'account_name',
      selectProps: {
        mode: 'multiple',
        maxTagCount: 3,
        fieldNames: {
          key: 'key',
          value: 'account_name',
          label: 'account_name'
        },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'account_name',
        allowClear: true
      },

      params: {
        status: 1
      }
    }
  }
]

//判断借方金额总额和贷方金额总额是否相等
export function checkAmount(selectData) {
  const totalAmount0 = selectData.reduce((total, item) => {
    return add(total, item.amount0)
  }, 0)
  const totalAmount1 = selectData.reduce((total, item) => {
    return add(total, item.amount1)
  }, 0)
  if (totalAmount0 !== totalAmount1) {
    message.error('贷方金额总额与借方金额总额不等')
    return false
  }
  return true
}

export const excelHeader = [
  '日期',
  '销售订单号',
  '单据类型',
  '科目名称',
  '科目代码',
  '借方金额',
  '贷方金额',
  '部门',
  '结算部门',
  '资金来源',
  '往来单位类型',
  '往来单位',
  '原单号',
  '备注',
  '凭证单号',
  '版本号0110'
]
