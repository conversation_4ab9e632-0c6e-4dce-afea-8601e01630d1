import { message } from 'ant-design-vue'

export const IMP_EXCEL_END = 20000
export function transformData2Import(data: Recordable[]): any[] {
  console.log(data)

  const fieldMap = {
    type: '单据类型',
    source_uniqid: '销售订单号',
    category: '科目名称',
    category_id: '科目代码',
    // business: '业务对象',
    amount0: '借方金额',
    amount1: '贷方金额',
    dept_id: '部门',
    clear_dept_id: '结算部门',
    capital: '资金来源',
    corres_type: '往来单位类型',
    corres_pondent: '往来单位',
    strid: '原单号',
    remark: '备注',
    date: '日期',
    order_number: '凭证单号',
    key: '版本号0110'
  }
  if (Object.keys(data[0]).indexOf('版本号0110') === -1) {
    message.error('请上传最新版本模板')
  } else {
    return data.map((obj) => {
      const cookedData: any = {
        type: undefined,
        category: '',
        source_uniqid: '',
        strid: '',
        category_id: '',
        // business: '',
        amount0: undefined,
        amount1: undefined,
        dept_id: undefined,
        clear_dept_id: undefined,
        capital: '',
        corres_type: undefined,
        corres_pondent: '',
        remark: '',
        date: '',
        order_number: '',
        key: ''
      }
      for (const key in cookedData) {
        if (fieldMap[key]) {
          cookedData[key] = obj[fieldMap[key]]
        }
      }
      // 去除空格
      cookedData.type = cookedData.type ? cookedData.type : 0
      cookedData.source_uniqid = String(cookedData.source_uniqid ? cookedData.source_uniqid : '').replace(/\s/g, '')
      cookedData.strid = cookedData.strid ? cookedData.strid : ''
      cookedData.category = String(cookedData.category ? cookedData.category : '').replace(/\s/g, '')
      cookedData.category_id = String(cookedData.category_id ? cookedData.category_id : '').replace(/\s/g, '')
      // cookedData.business = String(cookedData.business ? cookedData.business : '').replace(/\s/g, '')
      cookedData.amount0 = cookedData.amount0 ? cookedData.amount0 : 0
      cookedData.amount1 = cookedData.amount1 ? cookedData.amount1 : 0
      cookedData.dept_id = cookedData.dept_id ? cookedData.dept_id : 0
      cookedData.clear_dept_id = cookedData.clear_dept_id ? cookedData.clear_dept_id : ''
      cookedData.capital = String(cookedData.capital ? cookedData.capital : '').replace(/\s/g, '')
      cookedData.corres_type = cookedData.corres_type ? cookedData.corres_type : ''
      cookedData.corres_pondent = String(cookedData.corres_pondent ? cookedData.corres_pondent : '').replace(/\s/g, '')
      cookedData.remark = String(cookedData.remark ? cookedData.remark : '').replace(/\s/g, '')
      cookedData.date = String(cookedData.date ? cookedData.date : '').replace(/\s/g, '')
      cookedData.order_number = String(cookedData.order_number ? cookedData.order_number : '').replace(/\s/g, '')
      Reflect.set(cookedData, 'is_enter', 2)
      // console.log(cookedData, 'cookedData')
      return cookedData
    })
  }
}
