import { FormSchema } from '/@/components/Form'

import { getFinancialInformation } from '/@/api/financialDocuments/capitalFlow'
import { getDept } from '/@/api/erp/systemInfo'
import { corres_type_options } from '/@/views/financialDocuments/otherExpend/datas/drawer'
import { isNullOrUnDef } from '/@/utils/is'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { getCategory, getcustomerList } from '/@/api/financialDocuments/otherIncome'
import { getErpSupplier, getWorkList } from '/@/api/commonUtils'
import dayjs from 'dayjs'
import { getDeptSelectTree } from '/@/api/admin/dept'

export const mapWorkListFn = {
  1: getCreatorList,
  2: getDept,
  3: getcustomerList,
  4: getErpSupplier
}
export const ifArr = ['银行存款']

export const mapType = {
  100: {
    label: '全部'
  },
  0: {
    label: '手动录入'
  },
  1: {
    label: '销售'
  },
  2: {
    label: '采购'
  },
  3: {
    label: '入库'
  },
  4: {
    label: '库存转换'
  },
  5: {
    label: '盘点'
  },
  6: {
    label: '出库'
  },
  7: {
    label: '退货'
  },
  8: {
    label: '其他收入单'
  },
  9: {
    label: '其他支出单'
  },
  10: {
    label: '收款单'
  },
  11: {
    label: '付款单'
  },
  12: {
    label: '流水单'
  },
  13: {
    label: '退款单'
  },
  14: {
    label: '冲销单'
  },
  15: {
    label: '收款单流水'
  },
  16: {
    label: '付款单流水'
  },
  17: {
    label: '支出分摊'
  },
  18: {
    label: '结转Gbuilder'
  },
  19: {
    label: '内部部门调转'
  },
  20: {
    label: '消费单'
  },
  21: {
    label: '结转利润'
  },
  22: {
    label: '摊销费用'
  }
}

export const getSchemas: (handleFn?: any, hande?: Function) => FormSchema[] = (handleFn, hande) => [
  {
    field: 'type',
    label: '单据类型',
    component: 'Select',
    componentProps: {
      disabled: true,
      options: Object.keys(mapType).map((item) => ({ label: mapType[item].label, value: item }))
    }
  },
  {
    field: 'sales_strid',
    label: '关联销售订单',
    component: 'PagingApiSelect',
    componentProps: ({ formModel }) => {
      return {
        resultField: 'items',
        api: getWorkList,
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        searchParamField: 'source_uniqid',
        selectProps: {
          allowClear: true,
          fieldNames: { value: 'source_uniqid', label: 'source_uniqid' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'source_uniqid'
        },
        params: {
          types: [3, 27]
        },
        onChange: async (val: number, shall) => {
          hande && hande(shall)
          // await handleFn.setFieldsValue({ dept_id: shall.dept_id })
          formModel.dept_id = shall.dept_id
          formModel.dept_name = shall.department_name
          formModel.business = shall.creator_name
          formModel.clear_dept_id = shall.clear_dept_id || ''
          formModel.clear_department = shall.clear_department || ''
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },

  {
    field: 'category',
    label: '科目名称',
    component: 'ApiSelect',
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: ({}) => {
      return {
        api: getCategory,
        resultField: 'items',
        selectProps: {
          showSearch: true,
          allowClear: true,
          placeholder: '请选择',
          // labelInValue: true,
          fieldNames: { account_code: 'account_code', value: 'account_name', label: 'account_name' },
          optionFilterProp: 'account_name'
        },
        onChange: (value, shall) => {
          const isContained = ifArr.some((item) => value.includes(item))
          handleFn &&
            handleFn.updateSchema([
              {
                field: 'capital',
                required: isContained
              }
            ])
          if (shall?.account_code && handleFn) handleFn.setFieldsValue({ category_id: shall.account_code })
        }
      }
    }
  },
  {
    field: 'category_id',
    label: '科目代码',
    component: 'Input',
    required: true,
    dynamicDisabled: true
  },
  // {
  //   field: 'business',
  //   label: '业务对象',
  //   component: 'Input'
  // },
  {
    field: 'amount0',
    label: '借方金额(元)',
    component: 'InputNumber',
    required: true,
    helpMessage: '贷方金额和借方金额必须有一个为0',
    componentProps: {
      precision: 2
    }
  },
  {
    field: 'amount1',
    label: '贷方金额(元)',
    component: 'InputNumber',
    required: true,
    helpMessage: '贷方金额和借方金额必须有一个为0',
    componentProps: {
      precision: 2
    }
  },
  {
    field: 'dept_name',
    label: '部门',
    required: true,
    component: 'PagingApiSelect',
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: {
      api: getDept,
      params: { status: 1 },
      resultField: 'items',
      labelField: 'name',
      valueField: 'id',
      searchMode: true,
      pagingMode: true,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        style: {
          width: '100%'
        }
      },
      onChange: (_, shall) => {
        hande && hande(shall)
        handleFn.setFieldsValue({ dept_id: shall?.id })
      }
    },
    dynamicDisabled(renderCallbackParams) {
      return renderCallbackParams.model.sales_strid ? true : false
    }
  },
  {
    field: 'clear_department',
    label: '结算部门',
    component: 'PagingApiSelect',
    componentProps: {
      api: getDept,
      params: { is_audit: 1 },
      resultField: 'items',
      labelField: 'name',
      valueField: 'id',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        style: {
          width: '100%'
        },
        onChange: (_, shall) => {
          if (shall == undefined) {
            handleFn.setFieldsValue({ clear_dept_id: undefined })
          }
          hande && hande(shall)
          handleFn.setFieldsValue({ clear_dept_id: shall?.id })
        }
      },
      onChange: (_, shall) => {
        hande && hande(shall)
      }
    },
    dynamicDisabled(renderCallbackParams) {
      return renderCallbackParams.model.sales_strid ? true : false
    }
  },
  {
    field: 'corres_type',
    label: '往来单位类型',
    component: 'Select',
    componentProps: {
      allowClear: true,
      options: corres_type_options,
      style: {
        width: '100%'
      },
      onChange: (value) => {
        //逆天,设置为undefined就会让pagingApiSelect失效
        handleFn.setFieldsValue({ corres_pondent: null })
        if (value !== 5) {
          handleFn.updateSchema({
            field: 'corres_pondent',
            ifShow: !isNullOrUnDef(value),
            component: 'PagingApiSelect',
            required: !isNullOrUnDef(value),
            itemProps: {
              validateTrigger: 'blur'
            },
            componentProps: {
              api: isNullOrUnDef(value) ? getCreatorList : mapWorkListFn[value],
              resultField: 'items',
              searchMode: true,
              pagingMode: true,
              pagingSize: 20,
              params: {
                type: 3,
                status: [1, 3, 4, 5, 15]
              },
              selectProps: {
                fieldNames: {
                  value: 'name',
                  label: 'name'
                },
                showSearch: true,
                placeholder: '请选择',
                optionFilterProp: 'name',
                allowClear: true,
                style: {
                  width: '100%'
                }
              }
            }
          })
        } else {
          handleFn.updateSchema({
            field: 'corres_pondent',
            ifShow: !isNullOrUnDef(value),
            component: 'Input',
            required: !isNullOrUnDef(value),
            componentProps: {
              placeholder: '请输入'
            }
          })
        }
      }
    }
  },
  {
    field: 'corres_pondent',
    label: '往来单位',
    ifShow: false,
    component: 'PagingApiSelect'
  },
  {
    field: 'capital',
    label: '资金资料',
    component: 'ApiSelect',
    componentProps: {
      api: getFinancialInformation,
      resultField: 'items',
      selectProps: {
        showSearch: true,
        placeholder: '请选择',
        fieldNames: { value: 'name', label: 'name' },
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'submit'
    }
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    defaultValue: ''
  },
  {
    field: 'dept_id',
    label: '不显示的部门id',
    component: 'Input',
    ifShow: false
  },
  {
    field: 'clear_dept_id',
    label: '不显示的结算部门id',
    component: 'Input',
    ifShow: false
  }
]

export const exportSchemas: FormSchema[] = [
  {
    field: 'type',
    label: '单据类型',
    component: 'Select',
    componentProps: () => {
      const options = Object.keys(mapType).map((item) => ({ label: mapType[item].label, value: item }))
      return {
        options
      }
    }
  },
  {
    field: 'date',
    label: '凭证日期',
    component: 'SingleRangeDate',
    required: true,
    defaultValue: [dayjs().add(-7, 'day').format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')],
    componentProps: {
      allowEmpty: [true, true],
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  },
  {
    field: 'category',
    label: '科目名称',
    component: 'ApiSelect',
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: ({}) => {
      return {
        api: getCategory,
        resultField: 'items',
        selectProps: {
          showSearch: true,
          allowClear: true,
          placeholder: '请选择',
          // labelInValue: true,
          fieldNames: { account_code: 'account_code', value: 'account_name', label: 'account_name' },
          optionFilterProp: 'account_name'
        }
      }
    }
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptSelectTree,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        treeDefaultExpandAll: true,
        showSearch: true,
        treeLine: {
          showLeafIcon: false
        },
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  }
]

export const trialSchemas: FormSchema[] = [
  {
    field: 'type',
    label: '类型',
    component: 'Select',
    componentProps: {
      options: Object.keys(mapType).map((item) => ({ label: mapType[item].label, value: Number(item) }))
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'year',
    label: '结账年份',
    component: 'DatePicker',
    required: true,
    componentProps: {
      picker: 'year',
      valueFormat: 'YYYY',
      style: {
        width: '100%'
      }
    }
  },
  {
    field: 'issue',
    label: '结账月份',
    component: 'Select',
    required: true,
    componentProps: {
      options: [
        {
          label: '1月',
          value: 1
        },
        {
          label: '2月',
          value: 2
        },
        {
          label: '3月',
          value: 3
        },
        {
          label: '4月',
          value: 4
        },
        {
          label: '5月',
          value: 5
        },
        {
          label: '6月',
          value: 6
        },
        {
          label: '7月',
          value: 7
        },
        {
          label: '8月',
          value: 8
        },
        {
          label: '9月',
          value: 9
        },
        {
          label: '10月',
          value: 10
        },
        {
          label: '11月',
          value: 11
        },
        {
          label: '12月',
          value: 12
        }
      ]
    }
  }
]
