<template>
  <div>
    <BasicTable @register="registerTable">
      <template #form-advanceBefore>
        <a-button type="primary" class="mr-8px" :loading="buttonlodaing" @click="handleBeforeExport('condition')">条件导出凭证 </a-button>
      </template>
      <template #toolbar>
        <!-- <a-button class="mr-8px" @click="handleCreate">生成凭证</a-button> -->
        <a-button class="mr-8px" @click="handleunBatchAudit" v-if="hasPermission([447])"><form-outlined />批量反审核 </a-button>
        <a-button class="mr-8px" @click="handletrial" v-if="hasPermission([312])"><form-outlined />试算平衡 </a-button>
        <a-button class="mr-8px" @click="handleInputCreate" v-if="hasPermission([289])"><form-outlined />录入凭证 </a-button>
        <a-button @click="handleBatchDelete" type="danger" v-if="hasPermission([290])"><rest-outlined />批量删除</a-button>
        <a-button type="primary" @click="handleBatchAudit" v-if="hasPermission([291])"><file-search-outlined />批量审核</a-button>
        <a-button type="primary" class="mr-8px" @click="handleBeforeExport('page')">本页导出凭证 </a-button>
        <Dropdown>
          <a class="ant-dropdown-link" @click.prevent>
            <a-button type="primary" class="mr-8px"> 导出 <DownOutlined /></a-button>
          </a>
          <template #overlay>
            <Menu @click="handleMenuimportClick">
              <MenuItem key="archives"><DownloadOutlined /> 档案导出 <BasicHelp text="导出部门档案与科目档案" /></MenuItem>
              <MenuItem key="personnel"><DownloadOutlined /> 人员导出 <BasicHelp text="公司员工档案" /></MenuItem>
            </Menu>
          </template>
        </Dropdown>
        <!-- <Tooltip>
          <template #title></template>
          <a-button type="primary" class="mr-8px" @click="handleBeforeExport('record')"> </a-button>
        </Tooltip> -->
        <Dropdown>
          <a class="ant-dropdown-link" @click.prevent>
            <a-button type="primary" class="mr-8px"> 文件 <DownOutlined /></a-button>
          </a>
          <template #overlay>
            <Menu @click="handleMenuClick">
              <MenuItem key="upload"><UploadOutlined /> 导入</MenuItem>
              <MenuItem key="export"><DownloadOutlined /> 导出模板</MenuItem>
            </Menu>
          </template>
        </Dropdown>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <Tag :color="statustype[record.status]?.color"> {{ statustype[record.status]?.label }}</Tag>
        </template>
        <template v-if="column.key === 'type'">
          <Tag :color="mapType[record.type]?.color"> {{ mapType[record.type]?.label }}</Tag>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
        <template v-if="column.key === 'files'">
          <div v-for="(newVal, index) in record.files" :key="index">
            <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
          >
        </template>
      </template>
      <template #footer>
        <div class="footer">
          <span> 借方金额(元)合计: {{ formateerNotCurrency.format(totalAmount0, 2) }} </span>
          <span>贷方金额(元)合计：{{ formateerNotCurrency.format(totalAmount1, 2) }}</span>
        </div>
      </template>
    </BasicTable>
    <!-- <CredentialDrawer @register="registerCredentialDrawer" @success="reload" /> -->
    <ImpExcelModal @register="registerUploadModal" :dataCallBackFn="handleUploadData" ref="ImpExcelModalRef" />
    <InputCredDrawer @register="registerInputCredDrawer" @success="reload" />
    <EditDrawer @register="registereditDrawer" @success="reload" />
    <TrialModal @register="registerTrialModal" @success="reload" />
    <PreviewFile @register="registerModal" />
  </div>
</template>
<script setup lang="ts" name="/credential/credential">
import { message, Tag, Dropdown, Menu, MenuItem } from 'ant-design-vue'
import { FormOutlined, RestOutlined, FileSearchOutlined, DownOutlined, DownloadOutlined, UploadOutlined } from '@ant-design/icons-vue'
import { useTable, BasicTable, TableAction } from '/@/components/Table'
import { useDrawer } from '/@/components/Drawer'
import { usePermission } from '/@/hooks/web/usePermission'
import {
  getCredentialList,
  batchDeleteERP,
  batchUpdateStatus,
  exportPackage,
  exportimport,
  checkImportAmount,
  exportAccount,
  exportDept,
  certrecordexportStaff
} from '/@/api/credential/credential'
import { checkAmount, columns, excelHeader, schemas, statustype } from './datas/datas'
// import CredentialDrawer from './components/CredentialDrawer.vue'
import InputCredDrawer from './components/InputCredDrawer.vue'
import { mapType } from './datas/modal'
import { debounce } from 'lodash-es'
import { useModal } from '/@/components/Modal'
import { ref } from 'vue'
import { IMP_EXCEL_END, transformData2Import } from './datas/importModal'
import { ImpExcelModal } from '/@/components/Excel'
import type { ActionItem } from '/@/components/Table'
import EditDrawer from './components/EditDrawer.vue'
// const [registerCredentialDrawer, { openDrawer }] = useDrawer()
import TrialModal from './components/TrialModal.vue'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'
import { onExpExcelTemplate } from '/@/utils/exportTemplate'
import { BasicHelp } from '/@/components/Basic/index'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { add } from '/@/utils/math'
import { createImgPreview } from '/@/components/Preview/index'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'

let totalAmount0 = ref(0)
let totalAmount1 = ref(0)

const [registerInputCredDrawer, { openDrawer: openInputDrawer }] = useDrawer()

const { hasPermission } = usePermission()
const buttonlodaing = ref(false)

const [registerTable, { reload, getSelectRows, setLoading, clearSelectedRowKeys, getSelectRowKeys, getForm }] = useTable({
  title: '凭证信息',
  api: getCredentialList,
  columns,
  showTableSetting: true,
  rowKey: 'id',
  showIndexColumn: false,
  useSearchForm: true,
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas,
    fieldMapToTime: [['date', ['date1', 'date2'], ['YYYY-MM-DD', 'YYYY-MM-DD']]]
  },
  pagination: {
    pageSize: 100,
    pageSizeOptions: ['100', '500', '1000'],
    position: ['bottomRight']
  },
  beforeFetch: handleBeforeFetch,
  rowSelection: {
    type: 'checkbox'
  },
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  afterFetch: (tableData) => {
    totalAmount0.value = 0
    totalAmount1.value = 0

    tableData.forEach((item) => {
      totalAmount0.value = add(totalAmount0.value, Number(item.amount0), 4)
      totalAmount1.value = add(totalAmount1.value, Number(item.amount1), 4)
    })
  }
})

function createActions(record): ActionItem[] {
  return [
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      disabled: ![0].includes(record.status) || record.type !== 0,
      ifShow: hasPermission([411])
    }
  ]
}

//除了这样处理还有别的方式吗?好像可以直接在useTableL里处理
function handleBeforeFetch(params) {
  if (Array.isArray(params.date)) {
    return { ...params, date: undefined, date1: params?.date[0], date2: params?.date[1] }
  } else {
    return params
  }
}

// function handleCreate() {
//   openDrawer(true, {})
// }
function handleInputCreate() {
  openInputDrawer(true, {})
}
//编辑
const [registereditDrawer, { openDrawer: openEditDrawer }] = useDrawer()
function handleEdit(record: any) {
  openEditDrawer(true, record)
}
//审核
const handleBatchAudit = debounce(_handleBatchAudit, 200)
async function _handleBatchAudit() {
  // setLoading(true)
  try {
    const selectData = await getSelectRows()
    if (selectData.length < 1) return message.warning('请选择至少一行审核的数据!')
    if (selectData.some((item) => item.status === 1)) return message.warning('已审核的不能继续审核!')
    if (!checkAmount(selectData)) return
    await batchUpdateStatus({ ids: selectData.map((item) => item.id), status: 1 })
    await reload()
    await clearSelectedRowKeys()
  } catch (error) {
    // setLoading(false)
    console.error(error)
  }
}

//批量删除
const handleBatchDelete = debounce(_handleBatchDelete, 200)
async function _handleBatchDelete() {
  try {
    setLoading(true)
    const selectData = await getSelectRows()

    //判断selectData每个元素的capital属性是否为空
    if (selectData.some((item) => item.capital)) {
      return message.warning('有资金来源的不能删除!')
    }
    if (selectData.some((item) => item.status == 1)) {
      return message.warning('已审批的不能删除!')
    }

    if (selectData.length < 2) return message.warning('请选择至少两行删除的数据!')

    if (!checkAmount(selectData)) return

    await batchDeleteERP({ ids: selectData.map((item) => item.id) })
    reload()
  } catch (err) {
    console.error(err)
  } finally {
    setLoading(false)
  }
}

async function handleBeforeExport(type: string) {
  if (type === 'condition') {
    const fordata = await getForm().getFieldsValue()
    handexport(fordata)
  } else if (type === 'page') {
    const tabledata = await getSelectRowKeys()
    if (tabledata.length > 0) {
      handexport({ ids: tabledata })
    } else {
      message.error('请选择凭证')
    }
  } else if (type === 'record') {
    handexportArchives()
    handexportAccount()
  } else if (type === 'personnel') {
    handpersonnelexport()
  }
}
async function handexport(params) {
  try {
    buttonlodaing.value = true
    setLoading(true)
    try {
      await exportPackage(true, params)
    } catch (err: any) {
      if (err.message === 'Reflect.has called on non-object') {
        const response = await exportPackage(false, params)
        // const blob = new Blob([response.data], { type: response.headers['content-type'] })
        const downloadLink = document.createElement('a')
        downloadLink.href = URL.createObjectURL(response)
        downloadLink.download = `凭证-${+new Date()}.xlsx`

        // 模拟点击下载链接
        downloadLink.click()

        // 清理临时资源
        URL.revokeObjectURL(downloadLink.href)
        message.success('导出成功')
      }
      throw new Error(`${e}`)
    }
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    setLoading(false)
    buttonlodaing.value = false
  }
}
async function handexportArchives() {
  try {
    buttonlodaing.value = true
    setLoading(true)
    try {
      await exportDept(true)
    } catch (err: any) {
      if (err.message === 'Reflect.has called on non-object') {
        const response = await exportDept(false)
        // const blob = new Blob([response.data], { type: response.headers['content-type'] })
        const downloadLink = document.createElement('a')
        downloadLink.href = URL.createObjectURL(response)
        downloadLink.download = `部门-${+new Date()}.xlsx`

        // 模拟点击下载链接
        downloadLink.click()

        // 清理临时资源
        URL.revokeObjectURL(downloadLink.href)
        message.success('导出成功')
      }
      throw new Error(`${e}`)
    }
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    buttonlodaing.value = false
    setLoading(false)
  }
}
async function handexportAccount() {
  try {
    buttonlodaing.value = true
    setLoading(true)
    try {
      await exportAccount(true)
    } catch (err: any) {
      if (err.message === 'Reflect.has called on non-object') {
        const response = await exportAccount(false)
        // const blob = new Blob([response.data], { type: response.headers['content-type'] })
        const downloadLink = document.createElement('a')
        downloadLink.href = URL.createObjectURL(response)
        downloadLink.download = `科目-${+new Date()}.xlsx`

        // 模拟点击下载链接
        downloadLink.click()

        // 清理临时资源
        URL.revokeObjectURL(downloadLink.href)
        message.success('导出成功')
      }
      throw new Error(`${e}`)
    }
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    buttonlodaing.value = false
    setLoading(false)
  }
}

//人员信息
async function handpersonnelexport() {
  try {
    buttonlodaing.value = true
    setLoading(true)
    try {
      await certrecordexportStaff(true)
    } catch (err: any) {
      if (err.message === 'Reflect.has called on non-object') {
        const response = await certrecordexportStaff(false)
        // const blob = new Blob([response.data], { type: response.headers['content-type'] })
        const downloadLink = document.createElement('a')
        downloadLink.href = URL.createObjectURL(response)
        downloadLink.download = `人员信息-${+new Date()}.xlsx`

        // 模拟点击下载链接
        downloadLink.click()

        // 清理临时资源
        URL.revokeObjectURL(downloadLink.href)
        message.success('导出成功')
      }
      throw new Error(`${e}`)
    }
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    setLoading(false)
    buttonlodaing.value = false
  }
}

//导入数据
//execl上传
const [registerUploadModal, { openModal: openModalexcel }] = useModal()
//导入
async function handletoleadDetail() {
  openModalexcel(true, {
    sheetName: 'Sheet1',
    headerRow: 1,
    startCell: 'A2',
    endCell: `P${IMP_EXCEL_END}`
  })
}
const ImpExcelModalRef = ref<InstanceType<typeof ImpExcelModal>>()
async function handleUploadData(data) {
  ImpExcelModalRef.value?.changeLoading(true)
  const hide = message.loading('正在导入数据，请稍后...', 0)
  const code = `${Date.now()}${Math.floor(Math.random() * 9000) + 1000}`

  console.log(code)

  try {
    let cookedData = transformData2Import(data)
    if (cookedData == undefined) {
      hide()
      ImpExcelModalRef.value?.changeLoading(false)
      return Promise.reject('Fail')
    }

    // //每次最多5000条数据
    const chunkSize = 1000
    const totalChunks = Math.ceil(cookedData.length / chunkSize)
    for (let i = 0; i < totalChunks; i++) {
      const start = i * chunkSize
      const end = Math.min(start + chunkSize, cookedData.length)
      const chunk = cookedData.slice(start, end)
      //发请求
      await exportimport({ items: chunk, code })
    }
    await checkImportAmount({ code })

    hide()
    ImpExcelModalRef.value?.changeLoading(false)
    return Promise.resolve('OK')
  } catch (err) {
    hide()
    ImpExcelModalRef.value?.changeLoading(false)
    return Promise.reject('Fail')
  }
}
//试算平衡
const [registerTrialModal, { openModal: openModalTrial }] = useModal()
function handletrial() {
  openModalTrial(true)
}
//反审核
const handleunBatchAudit = debounce(_handleunBatchAudit, 200)
async function _handleunBatchAudit() {
  try {
    const selectData = await getSelectRows()
    if (selectData.length < 1) return message.warning('请选择至少一行审核的数据!')
    if (selectData.some((item) => item.status === 0)) return message.warning('未审核的不能进行反审核!')
    if (selectData.some((item) => item.type !== 0)) return message.warning('不是手工录入的凭证的不能进行反审核!')
    if (!checkAmount(selectData)) return
    await batchUpdateStatus({ ids: selectData.map((item) => item.id), status: 0 })
    await reload()
    await clearSelectedRowKeys()
  } catch (error) {
    // setLoading(false)
    console.error(error)
  }
}

//文件操作
function handleMenuClick({ key }) {
  if (key === 'export') {
    onExpExcelTemplate(excelHeader, true, '凭证导入模板')
  } else {
    handletoleadDetail()
  }
}
//导出
function handleMenuimportClick({ key }) {
  if (key === 'archives') {
    handleBeforeExport('record')
  } else if (key === 'personnel') {
    handleBeforeExport('personnel')
  }
}

const [registerModal, { openModal }] = useModal()
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return message.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>

<style lang="less" scoped>
.footer {
  font-size: 15px;
  font-weight: bold;
  span:nth-of-type(2) {
    margin-left: 7%;
  }
  span:nth-of-type(3) {
    margin-left: 7%;
  }
}
</style>
