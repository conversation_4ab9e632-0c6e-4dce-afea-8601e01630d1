<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="新增" show-footer @ok="handleSubmit" width="50%">
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { createLog } from '/@/api/credential/log'
import { schemas } from '../datas/drawer'

const emit = defineEmits(['success', 'register'])

const [registerDrawer, { changeLoading, changeOkLoading, closeDrawer }] = useDrawerInner(async () => {
  try {
    changeLoading(true)
    resetFields()
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
})

const [registerForm, { resetFields, validate }] = useForm({
  schemas,
  baseColProps: { span: 12 },
  showActionButtonGroup: false,
  colon: true,
  labelAlign: 'left',
  labelCol: { style: { width: '70px' } }
})

async function handleSubmit() {
  try {
    await changeOkLoading(true)
    const formData = await validate()
    await createLog(formData)
    emit('success')
    await closeDrawer()
    changeOkLoading(false)
  } catch (err) {
    changeOkLoading(false)
    throw new Error(`${err}`)
  }
}
</script>
