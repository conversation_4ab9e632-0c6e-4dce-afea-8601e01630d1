import type { BasicColumn, FormSchema } from '/@/components/Table'
import { mapType } from './drawer'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { isNull, isUnDef } from '/@/utils/is'

export const mapOrder = {
  1: '销售',
  2: '采购',
  3: '入库'
}

const mapSrc = {
  1: '已审核',
  2: '可备货',
  3: '结算',
  4: '清点完成',
  5: '取消作废'
}

const mapFundType = {
  1: '收入',
  2: '支出',
  3: '收入调拨'
}
const isloan = {
  0: '可生产借贷',
  1: '可生成借',
  2: '可生成贷'
}
const isstock = {
  0: '否',
  1: '是'
}
const iscost = {
  0: '否',
  1: '是'
}
const isorg = {
  0: '无',
  1: '被转'
}

const mapClause = {
  1: '销售',
  2: '采购',
  3: '其他收入',
  4: '其他支出',
  5: '销售退款',
  6: '采购退款'
}

const mapIsLoan = {
  0: '可生产借贷',
  1: '可生成借',
  2: '可生成贷'
}

export const columns: BasicColumn[] = [
  {
    dataIndex: 'type',
    title: '类型',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(mapType[value].label)
    }
  },
  {
    dataIndex: 'order',
    title: '来源单据',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(mapOrder[value])
    }
  },
  {
    dataIndex: 'src',
    title: '单据状态',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(mapSrc[value])
    }
  },
  {
    dataIndex: 'strid',
    title: '单据单号',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'par_strid',
    title: '上级单据',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'work_strid',
    title: '单据任务单号',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'sales_strid',
    title: '关联的销售订单号',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'is_loan',
    title: '生成借贷凭证类型',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(isloan[value])
    }
  },
  {
    dataIndex: 'is_stock',
    title: ' 是否备货',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(isstock[value])
    }
  },
  {
    dataIndex: 'is_org',
    title: ' 是否是被转',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(isorg[value])
    }
  },
  {
    dataIndex: 'is_cost',
    title: '是否是成本',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(iscost[value])
    }
  },
  {
    dataIndex: 'amount',
    title: '金额',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'status_at',
    title: '审核时间',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'audit_at',
    title: '结算时间',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'stock_at',
    title: '可备货时间',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'occurrence_at',
    title: '流水生成时间',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'finish_at',
    title: '完成时间',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'department',
    title: '部门',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'is_cert',
    title: '已生成凭证',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      if (isNull(value)) return '-'
      return value ? '是' : '否'
    }
  },
  {
    dataIndex: 'fund_type',
    title: '流水类型',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(mapFundType[value])
    }
  },
  {
    dataIndex: 'clause',
    title: '款单类型单据',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(mapClause[value])
    }
  },
  {
    dataIndex: 'date',
    title: '日期',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'is_loan',
    title: '生成借贷凭证类型',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      if (isNull(value) || isUnDef(value)) return '-'
      return useRender.renderTag(mapIsLoan[value])
    }
  },
  {
    dataIndex: 'business',
    title: '业务对象',
    width: 120,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'strid',
    label: '单据单号',
    component: 'Input'
  },
  {
    field: 'type',
    label: '类型',
    component: 'Select',
    componentProps: {
      options: Object.keys(mapType).map((key) => ({ label: mapType[key].label, value: Number(key) }))
    }
  }
]
