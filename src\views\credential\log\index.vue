<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button class="mr-8px" @click="handleCreateLog" type="primary">生成日志 </a-button>
        <a-button class="mr-8px" @click="handleReolad"><redo-outlined />重新生成日志 </a-button>
      </template>
      <!-- <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template> -->
    </BasicTable>
    <LogDrawer @register="registerLogDrawer" @success="reload" />
  </div>
</template>
<script setup lang="ts" name="/credential/log">
import { RedoOutlined } from '@ant-design/icons-vue'
import { useTable, BasicTable } from '/@/components/Table'
import { useDrawer } from '/@/components/Drawer'
import { getLogList } from '/@/api/credential/log'
import { columns, schemas } from './datas/datas'
import LogDrawer from './components/LogDrawer.vue'

const [registerLogDrawer, { openDrawer }] = useDrawer()

const [registerTable, { reload, setProps }] = useTable({
  title: '凭证日志',
  api: getLogList,
  columns,
  showTableSetting: true,
  tableSetting: { redo: false },
  rowKey: 'id',
  showIndexColumn: false,
  pagination: {
    pageSize: 10,
    pageSizeOptions: ['10', '50', '100'],
    position: ['bottomRight']
  },
  useSearchForm: true,
  formConfig: {
    schemas,
    baseColProps: { span: 6 },
    labelCol: { span: 4 }
  }
})

function handleCreateLog() {
  openDrawer(true, {})
}

async function handleReolad() {
  await setProps({
    searchInfo: { is_refresh: 1 }
  })
  await reload()
  setProps({
    searchInfo: { is_refresh: 0 }
  })
}
</script>
