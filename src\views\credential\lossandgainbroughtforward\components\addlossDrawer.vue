<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="新增" show-footer @ok="handleSubmit" width="50%">
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { schemas } from '../datas/drawer'
import { checkOutpl } from '/@/api/credential/lossa'
// checkOutpl
const emit = defineEmits(['success', 'register'])

const [registerDrawer, { closeDrawer }] = useDrawerInner(async () => {
  resetFields()
})

const [registerForm, { resetFields, validate }] = useForm({
  schemas,
  baseColProps: { span: 12 },
  showActionButtonGroup: false,
  colon: true,
  labelCol: { style: { width: '70px' } }
})

async function handleSubmit() {
  const valid = await validate()
  console.log(valid)
  await checkOutpl(valid)
  emit('success')
  closeDrawer()
}
</script>
