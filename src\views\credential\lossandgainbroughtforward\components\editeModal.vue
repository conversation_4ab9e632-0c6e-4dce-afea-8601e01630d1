<template>
  <BasicModal @register="register" @ok="handleOk" :min-height="350">
    <BasicForm @register="registerform" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { schemas } from '../datas/drawer'
import { ref } from 'vue'
import { setStatuspl, setdelete } from '/@/api/credential/lossa'

const type = ref('')
//id
const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
  type.value = data
  resetFields()
})
const [registerform, { validate, resetFields }] = useForm({
  schemas,
  showActionButtonGroup: false,
  baseColProps: { span: 12 },
  labelCol: { span: 5 }
})

const emit = defineEmits(['success', 'register'])
// 提交
async function handleOk() {
  try {
    const formdata = await validate()
    console.log(formdata)

    type.value == 'status' ? setStatuspl(formdata) : setdelete(formdata)
    emit('success')
    closeModal()
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  } finally {
    changeOkLoading(false)
  }
}
</script>
