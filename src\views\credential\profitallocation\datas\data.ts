import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { Tag } from 'ant-design-vue'

export const columns: BasicColumn[] = [
  {
    title: '订单号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 200,
    resizable: true,
    customRender: ({ text }) => {
      const typeMap = {
        1: { text: 'Gbuilder利润调拨', colos: 'red' },
        2: { text: '工单', colos: 'blue' }
      }
      return h(Tag, { color: typeMap[text].colos }, typeMap[text].text)
    }
  },
  {
    title: '调出销售订单号',
    dataIndex: 't_source_uniqid',
    width: 200,
    resizable: true
  },
  {
    title: '调入销售订单号',
    dataIndex: 'f_source_uniqid',
    width: 200,
    resizable: true
  },
  {
    title: '凭证单号',
    dataIndex: 'order_number',
    width: 200,
    resizable: true
  },
  {
    title: '是否已生成凭证',
    dataIndex: 'is_cert',
    width: 200,
    resizable: true,
    customRender: ({ text }) => {
      return h(Tag, { color: text ? 'green' : 'red' }, text ? '是' : '否')
    }
  },
  {
    title: '调入科目',
    dataIndex: 'f_category',
    width: 200,
    resizable: true
  },
  {
    title: '调出科目',
    dataIndex: 't_category',
    width: 200,
    resizable: true
  },
  {
    title: '费用名称',
    dataIndex: 'fee_name',
    width: 200,
    resizable: true
  },
  {
    title: '调入部门',
    dataIndex: 'f_department',
    width: 200,
    resizable: true
  },
  {
    title: '调出部门',
    dataIndex: 't_department',
    width: 200,
    resizable: true
  },
  {
    title: '调入结算部门',
    dataIndex: 'f_clear_department',
    width: 200,
    resizable: true
  },
  {
    title: '调出结算部门',
    dataIndex: 't_clear_department',
    width: 200,
    resizable: true
  },
  {
    title: '调拨日期',
    dataIndex: 'date',
    width: 200,
    resizable: true
  }
]
export const schemas: FormSchema[] = [
  {
    field: 'type',
    label: '类型',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: 'Gbuilder利润调拨',
          value: 1
        },
        {
          label: '工单',
          value: 2
        }
      ]
    }
  },
  {
    field: 'strid',
    label: '订单号',
    component: 'Input'
  },
  {
    field: 'order_number',
    label: '凭证单号',
    component: 'Input'
  },
  {
    field: 'date',
    label: '调拨日期',
    component: 'SingleRangeDate',
    componentProps: {
      type: 'daterange',
      format: 'YYYY-MM-DD' // 日期格式
    }
  },
  {
    field: 'f_source_uniqid',
    label: '调入销售单',
    component: 'Input'
  },
  {
    field: 't_source_uniqid',
    label: '调出销售单号',
    component: 'Input'
  }
]
