<template>
  <div> <BasicTable @register="registerTable" /></div>
</template>
<script setup lang="ts">
import { columns, schemas } from './datas/data'
import { certpagetList } from '/@/api/credential/profitallocation'
import { BasicTable, useTable } from '/@/components/Table'

const [registerTable] = useTable({
  api: certpagetList,
  columns,
  showTableSetting: true,
  rowKey: 'id',
  showIndexColumn: false,
  useSearchForm: true,
  formConfig: {
    labelWidth: 120,
    schemas,
    autoSubmitOnEnter: true,
    baseColProps: { span: 8 },
    fieldMapToTime: [['date', ['date1', 'date2'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  },
  pagination: {
    pageSize: 100,
    pageSizeOptions: ['100', '500', '1000'],
    position: ['bottomRight']
  }
})
</script>
