<template>
  <div
    v-loading="loading"
    loading-icon="ant-design:loading-outlined"
    loading-icon-size="35"
    class="w-full h-380px bg-white border-box p-16px flex flex-col relative"
  >
    <div class="comp-title">{{ props.compTitle }}</div>
    <div ref="chartRef" class="h-[93%]"></div>
    <!--    <div class="h-[93%] flex items-center justify-center" >-->
    <div
      v-show="!isArray(chatData) || chatData.length === 0"
      class="h-[84%] w-full flex items-center justify-center absolute bottom-0 left-0 bg-white"
    >
      <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, Ref, ref, watch } from 'vue'
import { useECharts } from '/@/hooks/web/useECharts'
import type { EChartsOption } from 'echarts'
import { Empty } from 'ant-design-vue'
import { isArray, isFunction } from 'lodash-es'

const loading = ref(false)

const props = withDefaults(
  defineProps<{
    dataSource?: any[]
    chatOpt: EChartsOption
    compTitle: string
    afterFetch?: Function | null
    api?: Function | null
  }>(),
  {
    afterFetch: null,
    api: null,
    // 请求回来的数据
    dataSource: () => [],
    // 表单组件每一列的名称和配置
    chatOpt: () => ({}),
    // 组件的名称
    compTitle: ''
  }
)
const chartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>, 'light')
const chatData = ref([])

onMounted(() => {
  setOptions(props.chatOpt)
})
watch(
  () => props.api,
  (val) => {
    if (isFunction(val)) {
      fetch()
    }
  },
  { deep: true, immediate: true }
)

watch(
  () => props.dataSource,
  (val) => {
    if (val?.length > 0) {
      chatData.value = val
    }
  },
  { deep: true, immediate: true }
)

watch(
  () => chatData.value,
  (val) => {
    if (val.length > 0) {
      setChatData(val)
    }
  },
  { deep: true, immediate: true }
)

async function fetch() {
  try {
    loading.value = true
    const { items } = await props.api?.()
    chatData.value = props.afterFetch?.(items)
    // setChatData(data)
  } catch (err) {
    console.log(err)
  } finally {
    loading.value = false
  }
}

function setChatData(val: any[]) {
  setTimeout(() => {
    setOptions(
      {
        series: {
          data: val
        },
        yAxis: {
          data: val.map((item) => item.groupId)
        }
      },
      false
    )
  }, 200)
}
</script>

<style lang="less" scoped>
.comp-title {
  font-weight: bold;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
}
</style>
