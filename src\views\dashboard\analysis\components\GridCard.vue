<template>
  <Row justify="start" :wrap="false">
    <Col :span="24 / props.columns.length" v-for="(item, idx) in props.columns" :key="idx" flex="1">
      <div v-if="props.columns.length > 0" class="w-full bg-white" v-bind="item">
        <div :ref="(el) => (ItemsRef[idx] = el)" class="grid-items p-16px" :class="{ 'divider-line': idx !== props.columns.length - 1 }">
          <Tooltip v-bind="item?.tipsTooltipProps || {}" :getPopupContainer="() => ItemsRef?.[idx]">
            <div ref="LabelRef" class="overflow-ellipsis whitespace-nowrap overflow-hidden">{{ item.label }}</div>
          </Tooltip>
          <div class="flex items-end">
            <div class="flex-1">
              <CountTo
                class="grid-count"
                color="#000000"
                :startVal="0"
                :endVal="dataSource[item.dataIndex] || 0"
                :duration="2000"
                :decimals="getDecimalPlaces(dataSource[item.dataIndex])"
                v-bind="item.countProps"
              />
              <div v-if="false" class="flex items-center">
                <span class="grid-sub-title mr-8px">较上月</span>
                <CountTo
                  class="grid-compare"
                  color="#f53f3f"
                  :startVal="0"
                  :endVal="dataSource[item.dataIndex] || 0"
                  :duration="2000"
                  :decimals="2"
                />
                <!--                        <span >{{ item.compare?.toLocaleString() }}</span>-->
                <!--            <span class="grid-compare">0</span>-->
                <div class="ml-5px">
                  <Icon icon="icon-park-outline:trending-up" size="15" color="#F53F3F" />
                </div>
              </div>
            </div>
            <div class="img-container">
              <img class="object-cover w-full h-full" :src="item.icon" />
            </div>
          </div>
        </div>
      </div>
    </Col>
  </Row>
</template>

<script setup lang="ts">
import { Icon } from '/@/components/Icon'
import { ref } from 'vue'
import { CountTo } from '/@/components/CountTo'
import { Row, Col, Tooltip } from 'ant-design-vue'
import { isNumber } from 'lodash-es'

const ItemsRef = ref([])

const props = withDefaults(
  defineProps<{
    dataSource?: object
    columns: Array<{ label: string; icon: string; dataIndex: string; countProps?: {} }>
  }>(),
  {
    // 请求回来的数据
    dataSource: () => ({}),
    /**
     * columns 组件每一列的名称和配置
     * columns.label 显示的名称
     * columns.icon 显示的icon
     * columns.dataIndex 对应dataSource的属性，会自动获取值渲染
     */
    columns: () => []
  }
)

function getDecimalPlaces(num) {
  if (typeof num !== 'number' && !(typeof num === 'string' && isNumber(+num) && !isNaN(+num))) {
    // throw new TypeError('Input must be a number');
    return 0
  }

  const numStr = num.toString()
  if (numStr.indexOf('.') === -1) {
    return 0
  }

  const decimalPart = numStr.split('.')[1]
  return decimalPart.length > 2 ? 2 : decimalPart.length
}
</script>

<style scoped lang="less">
.grid-items {
  font-weight: 400;
  font-size: 14px;
  color: #000000;

  &.divider-line {
    position: relative;

    &:after {
      content: '';
      display: block;
      position: absolute;
      right: 0;
      top: 16px;
      bottom: 16px;
      width: 1px;
      background: #eeeeee;
    }
  }

  .grid-sub-title {
    font-weight: 400;
    font-size: 12px;
    color: rgba(78, 89, 105, 0.62);
  }

  .grid-compare {
    font-weight: 500;
    font-size: 14px;
    //color: #f53f3f;
  }

  .grid-count {
    font-weight: 700;
    font-size: 24px;
    color: #000000;
  }
}

.img-container {
  width: 50px;
  height: 50px;
}
</style>
