<template>
  <div class="bg-white p-16px">
    <Row :gutter="16">
      <Col :span="12" class="relative">
        <div class="comp-title">客户满意度</div>
        <div ref="chartRef" class="h-[400px]"></div>
        <div v-if="props.chatDataSource.length === 0" class="absolute w-full h-[400px] left-0 bottom-0 bg-white">
          <Empty class="p-[135px]" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
        </div>
      </Col>
      <Col :span="12">
        <div class="comp-title">客户满意度详情</div>
        <BasicTable
          class="empty-padding"
          v-bind="props.tableConfig"
          :scroll="{ y: '34vh' }"
          :canResize="false"
          rowKey="id"
          size="small"
          :showIndexColumn="false"
        />
      </Col>
    </Row>
  </div>
</template>

<script setup lang="ts">
import { Row, Col, Empty } from 'ant-design-vue'
import { useECharts } from '/@/hooks/web/useECharts'
import type { EChartsOption } from 'echarts'
import { onMounted, Ref, ref, watch } from 'vue'
// import { BasicColumn, BasicTable, BasicTableProps, useTable } from '/@/components/Table'
import { BasicTable, BasicTableProps } from '/@/components/Table'

const chartRef = ref<HTMLDivElement | null>(null)
const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>, 'light')

const props = withDefaults(
  defineProps<{
    chatDataSource?: any[]
    chatTitleTotal?: number | string
    chatOpt: EChartsOption
    // tableDataSource?: any[]
    // tableColumns: BasicColumn[]
    chatTitle?: string
    tableConfig: Partial<BasicTableProps>
    // compTitle: string
  }>(),
  {
    // 请求回来的数据
    chatDataSource: () => [],
    // 总人数
    chatTitleTotal: 0,
    // 表单组件每一列的名称和配置
    chatOpt: () => ({}),
    // tableDataSource: () => [],
    // tableColumns: () => [],
    chatTitle: '',
    tableConfig: () => ({})
  }
)

// const [registerTable, { setTableData }] = useTable({
//   size: 'small',
//   showIndexColumn: false,
//   dataSource: [],
//   columns: props.tableColumns,
//   rowKey: 'id',
//   pagination: { pageSize: 10, position: ['bottomRight'] },
//   canResize: false,
//   scroll: { y: '34vh' }
// })

onMounted(() => {
  setOptions(props.chatOpt)
})

watch(
  () => props.chatDataSource,
  (val) => {
    if (val.length > 0) {
      setTimeout(() => {
        setOptions(
          {
            series: {
              data: val
            },
            title: {
              text: props.chatTitle ? `{label|${props.chatTitle}}\n{total|${props.chatTitleTotal}}` : `{total|${props.chatTitleTotal}}`
            }
          },
          false
        )
      }, 200)
    }
  },
  { deep: true, immediate: true }
)

// watch(
//   () => props.tableDataSource,
//   (val) => {
//     if (val.length > 0) {
//       setTimeout(() => {
//         setTableData(val)
//       }, 200)
//     }
//   },
//   { deep: true, immediate: true }
// )
</script>

<style lang="less" scoped>
.comp-title {
  font-weight: bold;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
}

.empty-padding {
  :deep(.ant-table-body) {
    max-height: none !important;

    .ant-empty {
      padding: 75px 0;
    }
  }
  //:deep(.ant-empty) {
  //  padding: 80px 0;
  //}
}
</style>
