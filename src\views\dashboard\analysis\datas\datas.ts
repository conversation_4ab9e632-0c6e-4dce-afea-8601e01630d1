import GridCard from '/@/views/dashboard/analysis/components/GridCard.vue'
import GridBarChat from '../components/GridBarChat.vue'
import GridPieChat from '/@/views/dashboard/analysis/components/GridPieChat.vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { markRaw, ref } from 'vue'
import { add } from '/@/utils/math'
import { getStarTop } from '/@/api/revisit'
import { getOnQcTimeRateTop, getOnTimeRatTop, getRoles } from '/@/api/dashboard/analysis'
import { getDeptTree } from '/@/api/admin/dept'
// import { getAccountList } from '/@/api/commonUtils'
import { usePermission } from '/@/hooks/web/usePermission'
import { TreeSelect } from 'ant-design-vue'
import { useGo } from '/@/hooks/web/usePage'
import { router } from '/@/router'
import { useUserStore } from '/@/store/modules/user'
import dayjs from 'dayjs'

export const BasicFormRef = ref(null)

const { hasPermission } = usePermission()

const commonBarChatConfig = {
  grid: {
    left: '15%',
    right: '5%',
    top: '5%',
    bottom: '7%'
  },
  xAxis: {
    show: false
  },
  yAxis: {
    type: 'category',
    data: [],
    inverse: true,
    max: 4,
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    }
  },
  legend: {
    show: false
  },
  series: {
    data: [],
    type: 'bar',
    realtimeSort: true,
    label: {
      show: true,
      position: 'insideLeft',
      color: '#333',
      valueAnimation: true
    },
    showBackground: true,
    backgroundStyle: {
      color: '#fafafa'
    },
    itemStyle: {
      color: '#6395fa'
    }
  },
  title: {
    show: true,
    textStyle: {
      color: 'rgba(0,0,0,0.6)',
      fontSize: 14,
      fontWeight: 400
    },
    left: 'center',
    bottom: '2%'
  }
}

const commonPieChatConfig = {
  tooltip: {
    trigger: 'item',
    formatter: (params) => {
      return `<div style="width: 150px">
        <div style="display: flex; justify-content: space-between; margin-bottom: 5px">
          <span>${params.marker}${params.name}：</span>
          <span>${params.value}</span>
        </div>
        <div style="display: flex; justify-content: space-between">
          <span>人数占比：</span>
          <span>${params.percent}%</span>
        </div>
      </div>`
    }
  },
  legend: {
    // top: '5%',
    right: '15%',
    top: 'center',
    orient: 'vertical'
  },
  title: {
    top: 'middle',
    left: 'center',
    text: `{label|总人数}\n{total|${0}}`,
    textStyle: {
      rich: {
        label: {
          fontSize: 12,
          color: 'rgba(0,0,0,0.45)',
          // fontWeight: 'bold'
          padding: [0, 0, 10, 0]
        },
        total: {
          color: 'rgba(0,0,0,0.85)',
          fontSize: 32
        }
      }
    }
  },
  series: {
    type: 'pie',
    radius: ['40%', '70%'],
    avoidLabelOverlap: false,
    label: {
      show: true,
      position: 'inside',
      formatter: '{d}%',
      color: '#fff'
    },
    labelLine: {
      show: false
    },
    data: []
  }
}

const tableColumns: BasicColumn[] = [
  // {
  //   title: '排名',
  //   dataIndex: 'top',
  //   width: 100,
  //   resizable: true
  // },
  {
    title: '姓名',
    dataIndex: 'client_name'
    // resizable: true
  },
  {
    title: '一星',
    dataIndex: 'oneStarSum',
    width: 100
    // resizable: true
  },
  {
    title: '二星',
    dataIndex: 'twoStarSum',
    width: 100
    // resizable: true
  },
  {
    title: '三星',
    dataIndex: 'threeStarSum',
    width: 100
    // resizable: true
  },
  {
    title: '四星',
    dataIndex: 'fourStarSum',
    width: 100
    // resizable: true
  },
  {
    title: '五星',
    dataIndex: 'fiveStarSum',
    width: 100
    // resizable: true
  }
]

const commonFinance = {
  comps: [
    {
      key: 'GridCard-1',
      component: markRaw(GridCard),
      layoutSpan: 24,
      componentProps: {
        columns: [
          {
            label: '当前未处理的收款单',
            icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1719161073666ffeeeb1b0c887134653.png',
            dataIndex: 'noReceipt'
          },
          {
            label: '当前待审核的其他支出单',
            icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1719269659666ffefa396de684203782.png',
            dataIndex: 'waitOtherDisburse'
          },
          {
            label: '当前待审核的付款单',
            icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1719009811666fff0406bc1257058985.png',
            dataIndex: 'waitCheckFund'
          },
          {
            label: '当前待付款的付款单',
            icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1719063077666fff8748575713599596.png',
            dataIndex: 'waitPayFund'
          },
          {
            label: '当前未分配收款的流水数',
            icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1718978795666fff98732b0678821885.png',
            dataIndex: 'nowNoFund'
          }
        ]
      }
    }
  ]
}

/**
 * rolesDashboardConfig: 角色的默认配置项
 * rolesDashboardConfig.{role角色id}: 角色的所有配置项，目前只有一个组件的配置项
 * rolesDashboardConfig.{role角色id}.comps: 决定角色渲染的组件
 * rolesDashboardConfig.{role角色id}.comps[].key: 组件的key值，mapDataSource需要用到这个值去匹配对应的取值字段
 * rolesDashboardConfig.{role角色id}.comps[].component: 渲染的组件，必须加上markRaw，否则会报警告
 * rolesDashboardConfig.{role角色id}.comps[].layoutSpan: 组件占了col数，参考ant-design-vue的col-span
 * rolesDashboardConfig.{role角色id}.comps[].componentProps: 里面的设置会传入到组件的props中
 */
export const rolesDashboardConfig = {
  // 客服
  5: {
    comps: [
      {
        key: 'GridCard-1',
        component: markRaw(GridCard),
        layoutSpan: 24,
        componentProps: {
          columns: [
            {
              label: '当前出库数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171876927866700ccc81216105561192.png',
              dataIndex: 'noOutWarehouse'
            },
            {
              label: '本月已完成出货数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171905399466700cd6924b8169721922.png',
              dataIndex: 'yesOutWarehouse'
            },
            {
              label: '本月已完成出库的订单数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171888071666700ce8e2cd1921954524.png',
              dataIndex: 'yesWorks'
            }
          ]
        }
      }
      // {
      //   key: 'GridCard-2',
      //   component: markRaw(GridCard),
      //   layoutSpan: 12,
      //   componentProps: {
      //     columns: [
      //       {
      //         label: '本月出货单数',
      //         icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171919790666700d3a508fe511544664.png',
      //         dataIndex: 'overdueSum'
      //       },
      //       {
      //         label: '本月客户回访次数',
      //         icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171941577566700d428d0a8953162043.png',
      //         dataIndex: 'overdueSum'
      //       },
      //       {
      //         label: '本月新建客户群数',
      //         icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171895327766700d4aef12f574161270.png',
      //         dataIndex: 'overdueSum'
      //       }
      //     ]
      //   }
      // },
      // {
      //   key: 'GridPieChat-1',
      //   component: markRaw(GridPieChat),
      //   layoutSpan: 24,
      //   componentProps: {
      //     chatOpt: {
      //       ...commonPieChatConfig
      //     },
      //     tableColumns: tableColumns
      //   }
      // }
    ]
  },
  // 采购
  6: {
    comps: [
      {
        key: 'GridCard-1',
        component: markRaw(GridCard),
        layoutSpan: 7,
        componentProps: {
          columns: [
            {
              label: '未完成备货订单数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/20240614/1718479199666c175bbf36c435994195.png',
              dataIndex: 'noReserveSum',
              onClick: () => {
                const go = useGo(router)
                go({ path: '/erpFlow/unpurchaseOrder' })
              }
            },
            {
              label: '当前生产订单数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/20240615/1719315458666cfb0e41948210479471.png',
              dataIndex: 'produceNowSum',
              onClick: () => {
                const go = useGo(router)
                go({ path: '/erpFlow/purchaseTracking' })
              }
            }
          ]
        }
      },
      {
        key: 'GridCard-2',
        component: markRaw(GridCard),
        layoutSpan: 7,
        componentProps: {
          columns: [
            {
              label: '采购订单生产逾期数',
              dataIndex: 'overdueSum',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/20240615/1718607142666cfb33195cd012004482.png',
              onClick: () => {
                const go = useGo(router)
                go({ name: '/erpFlow/purchaseTracking', state: { searchParams: { is_overdue: 1, status: void 0 } } })
              }
            },
            {
              label: '月备货订单数',
              dataIndex: 'stockUpMon',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/20240615/1718799257666cfb4f4c5c1172073692.png'
            }
            // {
            //   label: '当前生产中订单数',
            //   dataIndex: 'produceNowSum',
            //   icon: '//img.gbuilderchina.com/erp/dashboard/analysis/20240615/1719275629666cfb6641440073747931.png'
            // }
          ]
        }
      },
      {
        key: 'GridCard-3',
        component: markRaw(GridCard),
        layoutSpan: 10,
        componentProps: {
          columns: [
            {
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/20240615/1719275629666cfb6641440073747931.png',
              label: '均单值',
              dataIndex: 'age'
            },
            {
              label: '备货准时率',
              dataIndex: 'reserveTimeSum',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/20240615/1718799257666cfb4f4c5c1172073692.png',
              countProps: {
                suffix: '%'
              },
              onClick: () => {
                const go = useGo(router)
                go({ name: '/erp/purchaseOrder', state: { searchParams: { is_overdue: 1 } } })
              }
            },
            {
              label: '均入库时长',
              dataIndex: 'agediffday',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/20240614/1718479199666c175bbf36c435994195.png',
              onClick: () => {
                const go = useGo(router)
                go({ path: '/erp/inWarehouseNotice' })
              }
            }
          ]
        }
      },
      {
        key: 'GridBarChat-1',
        component: markRaw(GridBarChat),
        layoutSpan: 8,
        componentProps: {
          compTitle: '备货准时率TOP5',
          chatOpt: {
            ...commonBarChatConfig,
            title: {
              ...commonBarChatConfig.title,
              text: '备货准时率'
            }
          }
        }
      },
      {
        key: 'GridBarChat-2',
        component: markRaw(GridBarChat),
        layoutSpan: 8,
        componentProps: {
          compTitle: '装箱准时率TOP5',
          chatOpt: {
            ...commonBarChatConfig,
            title: {
              ...commonBarChatConfig.title,
              text: '装箱准时率'
            }
          }
        }
      },
      {
        key: 'GridBarChat-3',
        component: markRaw(GridBarChat),
        layoutSpan: 8,
        componentProps: {
          compTitle: '均入库时长TOP5',
          chatOpt: {
            ...commonBarChatConfig,
            title: {
              ...commonBarChatConfig.title,
              text: '均入库时长'
            }
          }
        }
      }
      // {
      //   key: 'GridPieChat-1',
      //   component: markRaw(GridPieChat),
      //   layoutSpan: 24,
      //   componentProps: {
      //     chatOpt: {
      //       ...commonPieChatConfig
      //     },
      //     tableColumns: tableColumns
      //   }
      // }
    ]
  },
  // 财务
  9: commonFinance,
  // 仓库
  8: {
    comps: [
      {
        key: 'GridCard-1',
        component: markRaw(GridCard),
        layoutSpan: 12,
        componentProps: {
          columns: [
            {
              label: '当前出库进行数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1718741375666ffc660a851771688073.png',
              dataIndex: 'nowOutWarehouse'
            },
            {
              label: '本月出货单数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1718747888666ffc9de3fcf783160362.png',
              dataIndex: 'monOutWarehouse'
            },
            {
              label: '本月出货订单数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1718747888666ffc9de3fcf783160362.png',
              dataIndex: 'monOrderOutWarehouse'
            },
            {
              label: '本月出货件数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1719387197666ffcb07474d497935403.png',
              dataIndex: 'monOutPiece'
            }
          ]
        }
      },
      {
        key: 'GridCard-2',
        component: markRaw(GridCard),
        layoutSpan: 12,
        componentProps: {
          columns: [
            {
              label: '本月收货单数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1719384100666ffcf600b13588450361.png',
              dataIndex: 'monInWarehouse'
            },
            {
              label: '本月收货件数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1719272698666ffcfea27c6499052369.png',
              dataIndex: 'monInWarehouse'
            },
            {
              label: '本月退货/换包件数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1719078483666ffd09da221580871048.png',
              dataIndex: 'monReturn'
            }
          ]
        }
      }
      // {
      //   key: 'GridPieChat-1',
      //   component: markRaw(GridPieChat),
      //   layoutSpan: 24,
      //   componentProps: {
      //     chatOpt: {
      //       ...commonPieChatConfig
      //     },
      //     tableColumns: tableColumns
      //   }
      // }
    ]
  },
  // 质检
  7: {
    comps: [
      {
        key: 'GridCard-1',
        component: markRaw(GridCard),
        layoutSpan: 7,
        componentProps: {
          columns: [
            {
              label: '质检中订单数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1718912298666ff2d73dda2924962030.png',
              dataIndex: 'waitQuality',
              onClick: () => {
                const go = useGo(router)
                go({ name: '/erpFlow/qualityDetection' })
              }
            },
            {
              label: '质检完成未发货订单数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1718931746666ff300ac946541796243.png',
              dataIndex: 'finshNoSendQuality',
              onClick: () => {
                const go = useGo(router)
                go({
                  name: '/erpFlow/qualityDetection',
                  state: { searchParams: { qc_status: 1 } }
                })
              }
            }
          ]
        }
      },
      {
        key: 'GridCard-2',
        component: markRaw(GridCard),
        layoutSpan: 10,
        componentProps: {
          columns: [
            {
              label: '质检报告数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1718799401666ff35df1695297535669.png',
              dataIndex: 'qaReport'
            },
            {
              label: '工厂质检数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1719403409666ff7757c861285688672.png',
              dataIndex: 'factoryQaReport'
            },
            {
              label: '仓库质检数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1719283740666ff7bb8c697776593562.png',
              dataIndex: 'stockQaReport'
            }
          ]
        }
      },
      {
        key: 'GridCard-3',
        component: markRaw(GridCard),
        layoutSpan: 7,
        componentProps: {
          columns: [
            {
              label: '质检准时率',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1719403409666ff7757c861285688672.png',
              dataIndex: 'onTimeRat',
              countProps: {
                suffix: '%'
              },
              onClick: () => {
                const go = useGo(router)
                const { month } = BasicFormRef.value?.formActionType?.getFieldsValue()
                go({
                  name: '/erpFlow/qualityDetection',
                  state: {
                    searchParams: {
                      status: [15],
                      qc_status: void 0,
                      is_overdue: 1,
                      out_at: [
                        dayjs(month).startOf('month').format('YYYY-MM-DD 00:00:00'),
                        dayjs(month).endOf('month').format('YYYY-MM-DD 23:59:59')
                      ],
                      out_at_start: dayjs(month).startOf('month').format('YYYY-MM-DD 00:00:00'),
                      out_at_end: dayjs(month).endOf('month').format('YYYY-MM-DD 23:59:59')
                    }
                  }
                })
              }
            },
            {
              label: '发货质检率',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/1719283740666ff7bb8c697776593562.png',
              dataIndex: 'deliveryRat',
              countProps: {
                suffix: '%'
              },
              onClick: () => {
                const go = useGo(router)
                const { month } = BasicFormRef.value?.formActionType?.getFieldsValue()
                go({
                  name: '/erpFlow/qualityDetection',
                  state: {
                    searchParams: {
                      status: [15],
                      out_at: [
                        dayjs(month).startOf('month').format('YYYY-MM-DD 00:00:00'),
                        dayjs(month).endOf('month').format('YYYY-MM-DD 23:59:59')
                      ],
                      out_at_start: dayjs(month).startOf('month').format('YYYY-MM-DD 00:00:00'),
                      out_at_end: dayjs(month).endOf('month').format('YYYY-MM-DD 23:59:59')
                    }
                  }
                })
              }
            }
          ]
        }
      },
      {
        key: 'GridBarChat-1',
        component: markRaw(GridBarChat),
        layoutSpan: 12,
        componentProps: {
          afterFetch: transformRatGroups,
          api: getOnTimeRatTop,
          compTitle: '质检准时率TOP5',
          chatOpt: {
            ...commonBarChatConfig,
            title: {
              ...commonBarChatConfig.title,
              text: '质检准时率'
            }
          }
        }
      },
      {
        key: 'GridBarChat-2',
        component: markRaw(GridBarChat),
        layoutSpan: 12,
        componentProps: {
          afterFetch: transformQcRateGroups,
          api: getOnQcTimeRateTop,
          compTitle: '发货准时率TOP5',
          chatOpt: {
            ...commonBarChatConfig,
            title: {
              ...commonBarChatConfig.title,
              text: '发货准时率'
            }
          }
        }
      }
      // {
      //   key: 'GridPieChat-1',
      //   component: markRaw(GridPieChat),
      //   layoutSpan: 24,
      //   componentProps: {
      //     chatOpt: {
      //       ...commonPieChatConfig
      //     },
      //     tableColumns: tableColumns
      //   }
      // }
    ]
  },
  // 销售
  13: {
    comps: [
      {
        key: 'GridCard-1',
        component: markRaw(GridCard),
        layoutSpan: 12,
        componentProps: {
          columns: [
            {
              label: '本月开单额',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171914892466700b315139a507507436.png',
              dataIndex: 'moSale',
              onClick: () => {
                const go = useGo(router)
                go({
                  name: '/Performance/Statistics',
                  state: { searchParams: { tabVal: 6 } }
                })
              }
            },
            {
              label: '本月核定业绩',
              tipsTooltipProps: { visible: true, placement: 'topLeft', title: '凤凰计划按开单额 × 0.85计算业绩!' },
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171959299666700b3f19d93348136582.png',
              dataIndex: 'daySale',
              onClick: () => {
                const go = useGo(router)
                go({
                  name: '/Performance/Statistics',
                  state: { searchParams: { tabVal: 6 } }
                })
              }
            }
          ]
        }
      },
      {
        key: 'GridCard-2',
        component: markRaw(GridCard),
        layoutSpan: 12,
        componentProps: {
          columns: [
            {
              label: '本月出货金额(元)',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171961104466700b49d616a487446520.png',
              dataIndex: 'moOutWarehouse'
            },
            // {
            //   label: '本月回款金额(元)',
            //   icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171888521866700b572a566333336281.png',
            //   dataIndex: 'moReturn'
            // },
            {
              label: '总已发货应收金额(元)',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171882166766700b62bc595051057943.png',
              dataIndex: 'saleoutSum',
              onClick: () => {
                const userStore = useUserStore()
                console.log(userStore.getToken)
                // window.open('https://fims.gbuilderchina.com/s/#/reportForm/accountsReceivable?token=' + userStore.getToken, '_blank')
                window.open('https://fims.gbuilderchina.com/s/#/reportForm/accountsReceivable?token=' + userStore.getCommonToken, '_blank')
              }
            }
          ]
        }
      },
      {
        key: 'GridPieChat-1',
        component: markRaw(GridPieChat),
        layoutSpan: 24,
        componentProps: {
          chatOpt: {
            ...commonPieChatConfig
          },
          // tableColumns: tableColumns,
          tableConfig: {
            columns: tableColumns,
            api: getStarTop
          }
        }
      }
    ]
  },
  // 交付经理
  66: {
    comps: [
      {
        key: 'GridCard-1',
        component: markRaw(GridCard),
        layoutSpan: 24,
        componentProps: {
          columns: [
            {
              label: '当前待建群客户数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171959299666700b3f19d93348136582.png',
              dataIndex: 'nowWaitGroup',
              onClick: () => {
                const go = useGo(router)
                const userStore = useUserStore()
                go({
                  name: '/erpFlow/revisitLog',
                  state: { searchParams: { group_name_is_null: 0, delivery_incharge: userStore.getUserInfo.userId, status: 1 } }
                })
              }
            },
            {
              label: '未对接交付经理的项目数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171959299666700b3f19d93348136582.png',
              dataIndex: 'noDelivery',
              onClick: () => {
                const go = useGo(router)
                go({
                  name: '/erpFlow/revisitLog',
                  state: { searchParams: { delivery_incharge_is_null: 1, status: 1 } }
                })
              }
            },
            {
              label: '当前对接中的项目数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171959299666700b3f19d93348136582.png',
              dataIndex: 'nowDelivery',
              onClick: () => {
                const go = useGo(router)
                const userStore = useUserStore()
                go({
                  name: '/erpFlow/revisitLog',
                  state: { searchParams: { delivery_incharge: userStore.getUserInfo.userId, status: 1 } }
                })
              }
            },
            {
              label: '当前待回访客户数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171914892466700b315139a507507436.png',
              dataIndex: 'nowWaitFollow',
              onClick: () => {
                const go = useGo(router)
                go({
                  name: '/erpFlow/revisitLog',
                  state: { searchParams: { is_overdue: 1, status: 1 } }
                })
              }
            }
          ]
        }
      },
      {
        key: 'GridCard-2',
        component: markRaw(GridCard),
        layoutSpan: 8,
        componentProps: {
          columns: [
            {
              label: '跟进次数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171959299666700b3f19d93348136582.png',
              dataIndex: 'monWaitFollow'
            },
            {
              label: '本月新建客户群数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171959299666700b3f19d93348136582.png',
              dataIndex: 'monGroup'
            }
          ]
        }
      },
      {
        key: 'GridCard-3',
        component: markRaw(GridCard),
        layoutSpan: 16,
        componentProps: {
          columns: [
            {
              label: '当前出库数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171876927866700ccc81216105561192.png',
              dataIndex: 'noOutWarehouse'
            },
            {
              label: '本月已完成出货数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171905399466700cd6924b8169721922.png',
              dataIndex: 'yesOutWarehouse'
            },
            {
              label: '本月已完成出库的订单数',
              icon: '//img.gbuilderchina.com/erp/dashboard/analysis/********/171888071666700ce8e2cd1921954524.png',
              dataIndex: 'yesWorks'
            }
          ]
        }
      },
      {
        key: 'GridPieChat-1',
        component: markRaw(GridPieChat),
        layoutSpan: 24,
        componentProps: {
          chatOpt: {
            ...commonPieChatConfig
          },
          tableConfig: {
            columns: tableColumns,
            api: getStarTop,
            fetchSetting: {
              listField: 'result.item'
            }
          }
        }
      }
    ]
  }
}

/**
 * mapDataSource: 映射各个角色的组件取值字段和字段处理函数，除了GridCard组件
 *
 * mapDataSource.{role角色id}: 角色里面对应需要处理的组件名，对应rolesDashboardConfig.{role角色id}.comps.key
 *
 * mapDataSource.{role角色id}.{componentKey}: 组件中props到渲染组件的数据
 *
 * mapDataSource.{role角色id}.{componentKey}.key: 传递到组件的属性名（即为子组件的props.{key}）
 *
 * mapDataSource.{role角色id}.{componentKey}.field: 使用接口返回的数据某个字段，作为当前key的值
 *
 * mapDataSource.{role角色id}.{componentKey}.handler: 对接口返回的数据进行处理的函数
 */
export const mapDataSource = {
  // 销售
  13: {
    'GridPieChat-1': [
      { key: 'chatDataSource', field: 'assess', handler: transformAssess },
      { key: 'tableDataSource', field: 'tableAssess' },
      { key: 'chatTitleTotal', field: 'assess', handler: transformTotal },
      { key: 'chatTitle', field: '', label: '平均分' }
    ]
  },
  // 采购
  6: {
    'GridBarChat-1': [{ key: 'dataSource', field: 'topOnTimeRat', handler: null }],
    'GridBarChat-2': [{ key: 'dataSource', field: 'topDeliveryRat', handler: null }],
    'GridBarChat-3': [{ key: 'dataSource', field: 'topDeliveryRat', handler: null }],
    'GridPieChat-1': [
      { key: 'chatDataSource', field: 'assess', handler: transformAssess },
      { key: 'tableDataSource', field: 'tableAssess' },
      { key: 'chatTitleTotal', field: 'assess', handler: transformTotal },
      { key: 'chatTitle', field: '', label: '平均分' }
    ]
  },
  // 交付经理
  66: {
    'GridPieChat-1': [
      { key: 'chatDataSource', field: 'assess', handler: transformAssess },
      { key: 'tableDataSource', field: 'tableAssess' },
      { key: 'chatTitleTotal', field: 'assess', handler: transformTotal },
      { key: 'chatTitle', field: '', label: '平均分' }
    ]
  },
  // 仓库
  8: {
    'GridPieChat-1': [
      { key: 'chatDataSource', field: 'assess', handler: transformAssess },
      { key: 'tableDataSource', field: 'tableAssess' },
      { key: 'chatTitleTotal', field: 'assess', handler: transformTotal },
      { key: 'chatTitle', field: '', label: '平均分' }
    ]
  },
  // 质检
  7: {
    // 'GridBarChat-1': [{ key: 'dataSource', field: 'topOnTimeRat', handler: transformGroups }],
    // 'GridBarChat-2': [{ key: 'dataSource', field: 'topDeliveryRat', handler: transformQcRateGroups }],
    'GridPieChat-1': [
      { key: 'chatDataSource', field: 'assess', handler: transformAssess },
      { key: 'tableDataSource', field: 'tableAssess' },
      { key: 'chatTitleTotal', field: 'assess', handler: transformTotal },
      { key: 'chatTitle', field: '', label: '平均分' }
    ]
  }
}

function transformAssess(data) {
  const mapName = {
    oneStarSum: '一星',
    twoStarSum: '二星',
    threeStarSum: '三星',
    fourStarSum: '四星',
    fiveStarSum: '五星'
  }
  return Object.keys(data).map((key) => mapName[key] && { name: mapName[key], value: data[key] })
}

function transformTotal(data) {
  const nums = Object.values(data)
  return nums.reduce((acc, cur) => {
    return add(acc, cur)
  }, 0)
}

function transformRatGroups(data) {
  return data.map((item) => ({ groupId: item.accountName, value: item.onTimeRat }))
}

function transformQcRateGroups(data) {
  return data.map((item) => ({ groupId: item.accountName, value: item.deliveryRat }))
}

export const getSearchSchemas = (path: string): FormSchema[] => {
  console.log(mapSearchPermission[path])
  return [
    {
      ifShow: !!mapSearchPermission[path] && hasPermission(mapSearchPermission[path].dept),
      field: 'deptId',
      label: '部门',
      component: 'ApiTreeSelect',
      componentProps: ({ formModel }) => ({
        api: getDeptTree,
        treeSelectProps: {
          showCheckedStrategy: TreeSelect.SHOW_ALL,
          multiple: true,
          // treeCheckable: true,
          treeCheckStrictly: true,
          fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
          placeholder: '请选择',
          showSearch: true,
          treeDefaultExpandAll: true,
          optionFilterProp: 'name',
          filterTreeNode: (search, item) => {
            if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
            return false
          }
        },
        onChange(val) {
          if (val) {
            formModel.userId = void 0
          }
        }
      })
    },
    {
      ifShow: !!mapSearchPermission[path] && hasPermission(mapSearchPermission[path].user),
      field: 'userId',
      label: '人员',
      component: 'PagingApiSelect',
      componentProps: ({ formModel }) => ({
        api: (params) => getRoles({ ...params, roleId: mapSearchPermission[path].value }),
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true
        },
        onChange(val) {
          if (val) {
            formModel.deptId = void 0
          }
        }
      })
    },
    {
      field: 'month',
      label: '月份',
      component: 'DatePicker',
      defaultValue: dayjs().format('YYYY-MM'),
      componentProps: {
        picker: 'month',
        style: { width: '90%' },
        valueFormat: 'YYYY-MM'
      }
    }
  ]
}

export const mapSearchPermission = {
  '/dashboard/analysis/sales': {
    dept: 369,
    user: 370,
    value: 13
  },
  '/dashboard/analysis/customer_service': {
    dept: 371,
    user: 376,
    value: 5
  },
  '/dashboard/analysis/purchase': {
    dept: 367,
    user: 368,
    value: 6
  },
  '/dashboard/analysis/delivery_manager': {
    dept: 375,
    user: 380,
    value: 66
  },
  '/dashboard/analysis/finance': {
    dept: 374,
    user: 379,
    value: 9
  },
  '/dashboard/analysis/warehouse': {
    dept: 373,
    user: 378,
    value: 8
  },
  '/dashboard/analysis/inspection': {
    dept: 372,
    user: 377,
    value: 7
  }
}
