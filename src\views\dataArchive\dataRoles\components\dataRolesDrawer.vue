<template>
  <div>
    <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="90%" title="编辑" show-footer @ok="handleSubmit">
      <BasicForm @register="registerForm" />
    </BasicDrawer>
  </div>
</template>

<script setup lang="ts">
import { BasicDrawer, DrawerInstance, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { formSchemas } from '../datas/drawer.datas'
import { setRelateRoles } from '/@/api/dataArchive/dataRoles'
import { useMessage } from '/@/hooks/web/useMessage'

const emits = defineEmits<{ (e: 'success'): void; (e: 'register', $event: DrawerInstance): void }>()

const { createMessage } = useMessage()
const [registerDrawer, { closeDrawer }] = useDrawerInner(({ record }) => {
  console.log(record, 'record')
  setFieldsValue({ ...record })
})

const [registerForm, { setFieldsValue, validate }] = useForm({
  actionColOptions: { span: 24 },
  baseColProps: { span: 24 },
  labelAlign: 'right',
  labelWidth: 80,
  colon: true,
  showActionButtonGroup: false,
  schemas: formSchemas
})

async function handleSubmit() {
  const values = await validate()
  console.log(values)
  const { news } = await setRelateRoles({
    roles_id: values.roles_id,
    deptahs_id: values.deptahs_id,
    is_forbid: values.is_forbid
  })
  if (news === 'success') {
    createMessage.success('关联成功！')
    closeDrawer()
    emits('success')
  }
}
</script>
