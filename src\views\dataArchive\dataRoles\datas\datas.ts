import { BasicColumn } from '/@/components/Table'
import { DataRolesList } from '/@/api/dataArchive/model/types'
import { setDataRolesStatus } from '/@/api/dataArchive/dataRoles'
import { useMessage } from '/@/hooks/web/useMessage'
import { h, ref } from 'vue'
import { Tag } from 'ant-design-vue'
import { useRender } from '/@/components/Table/src/hooks/useRender'

const { createMessage } = useMessage()
export const mapLoading = ref<{ [key: number]: boolean }>({})
export const columns: BasicColumn[] = [
  {
    title: 'ID',
    dataIndex: 'roles_id'
  },
  {
    title: '角色名',
    dataIndex: 'name'
  },
  {
    title: '角色值',
    dataIndex: 'idname'
  },
  {
    title: '关联部门档案',
    dataIndex: 'deparment',
    customRender: ({ text }) => useRender.renderTags(text || [])
  },
  {
    title: '状态',
    dataIndex: 'is_forbid',
    customRender: ({ value }) => {
      return h(Tag, { color: value == 0 ? 'green' : 'red' }, () => (value == 0 ? '启用' : '禁用'))
    }
  }
]

export async function handleChangeStatus(val: number, record: DataRolesList, func: { reload: Function }) {
  mapLoading.value[record.roles_id] = true
  try {
    const { news } = await setDataRolesStatus({ roles_id: record.roles_id, is_forbid: val })
    if (news === 'success') {
      createMessage.success('修改成功')
    }
    await func.reload()
    mapLoading.value[record.roles_id] = false
  } catch (e) {
    console.log(e)
    func.reload()
    mapLoading.value[record.roles_id] = false
    throw new Error(`${e}`)
  }
}
