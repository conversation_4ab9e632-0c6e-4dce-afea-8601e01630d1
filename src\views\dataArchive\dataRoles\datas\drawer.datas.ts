import { FormSchema } from '/@/components/Form'
import { getDeptList } from '/@/api/commonUtils'

export const formSchemas: FormSchema[] = [
  {
    field: 'roles_id',
    component: 'Input',
    dynamicDisabled: true,
    label: 'ID',
    colProps: {
      span: 6
    },
    required: true
  },
  {
    field: 'name',
    label: '角色名',
    component: 'Input',
    colProps: {
      span: 6
    },
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'idname',
    label: '角色值',
    component: 'Input',
    colProps: {
      span: 6
    },
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'is_forbid',
    label: '状态',
    component: 'Switch',
    colProps: {
      span: 6
    },
    componentProps: {
      unCheckedValue: 1,
      checkedValue: 0,
      checkedChildren: '启用',
      unCheckedChildren: '禁用'
    },
    required: true
  },
  {
    field: 'deptahs_id',
    label: '关联部门档案',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptList,
      // resultField: 'items',
      treeSelectProps: {
        showCheckedStrategy: 'TreeSelect.SHOW_ALL',
        treeDefaultExpandAll: true,
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择',
        treeNodeFilterProp: 'name',
        allowClear: true,
        multiple: true
      }
    },
    colProps: {
      span: 24
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    // required: true,
    labelWidth: 120
  }
]
