<template>
  <div class="p-4">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <DataRolesDrawer @register="registerDrawer" @success="reload" />
  </div>
</template>

<script setup lang="ts" name="/dataArchive/dataRoles">
import { BasicTable, TableAction, useTable } from '/@/components/Table'
import { columns } from './datas/datas'
import { getDataRolesList } from '/@/api/dataArchive/dataRoles'
import { DataRolesList } from '/@/api/dataArchive/model/types'
import type { ActionItem } from '/@/components/Table'

import { useDrawer } from '/@/components/Drawer'
import DataRolesDrawer from './components/dataRolesDrawer.vue'

const [registerDrawer, { openDrawer }] = useDrawer()
const [registerTable, { reload }] = useTable({
  title: '数据角色',
  api: getDataRolesList,
  showIndexColumn: false,
  isTreeTable: true,
  pagination: false,
  striped: false,
  rowKey: 'id',
  bordered: true,
  canResize: false,
  columns,
  actionColumn: {
    width: 100,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  }
})

function createActions(record: DataRolesList) {
  return [
    {
      icon: 'clarity:note-edit-line',
      tooltip: '编辑',
      onClick: handleEdit.bind(null, record)
    }
  ] as ActionItem[]
}

function handleEdit(record: DataRolesList) {
  openDrawer(true, { record })
}
</script>
