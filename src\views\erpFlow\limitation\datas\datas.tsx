import { BasicColumn, FormSchema } from '/@/components/Table'
import { getDeptTree } from '/@/api/admin/dept'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { useI18n } from '/@/hooks/web/useI18n'
import { GET_STATUS_SCHEMA } from '/@/const/status'

const saleStore = useSaleOrderStore()

const { tm } = useI18n()

export const columns: BasicColumn[] = [
  {
    title: '订单号',
    dataIndex: 'source_uniqid',
    width: 170,
    resizable: true
  },
  {
    title: '项目ID',
    dataIndex: 'project_number',
    width: 100,
    resizable: true
  },
  {
    title: '项目名称',
    dataIndex: 'project_name',
    width: 250,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'dept_name',
    width: 170,
    resizable: true,
    customRender: ({ record }) => {
      console.log(record.type1.end_at)
      return (
        <div>
          {record.department ? <div>产品部：{record.department ?? '-'}</div> : null}
          {record.operation_name ? <div>业务部门：{record.operation_name ?? '-'}</div> : null}
        </div>
      )
    }
  },
  {
    title: '人员',
    dataIndex: 'person',
    width: 170,
    resizable: true,
    customRender: ({ record }) => (
      <div>
        <div>方案经理：{record.inCharge_name}</div>
        <div>项目经理：{record.program_incharge_name}</div>
        <div>交付经理：{record.delivery_incharge_name}</div>
      </div>
    )
  },
  {
    title: '可备货日期',
    dataIndex: 'type1',
    width: 150,
    resizable: true,
    customRender: ({ record }) =>
      record?.type1?.start_at ? (
        <span style={record?.type1?.is_overdue ? 'color: red' : ''}>{record?.type1?.start_at}</span>
      ) : (
        <span>-</span>
      )
  },
  {
    title: '拆单日期',
    dataIndex: 'type2',
    width: 150,
    resizable: true,
    customRender: ({ record }) =>
      record?.type2?.start_at ? (
        <span style={record?.type2?.is_overdue ? 'color: red' : ''}>{record?.type2?.start_at}</span>
      ) : (
        <span>-</span>
      )
  },
  {
    title: '备货日期',
    dataIndex: 'type3',
    width: 150,
    resizable: true,
    customRender: ({ record }) =>
      record?.type3?.start_at ? (
        <span style={record?.type3?.is_overdue ? 'color: red' : ''}>{record?.type3?.start_at}</span>
      ) : (
        <span>-</span>
      )
  },
  {
    title: '主管审核日期',
    dataIndex: 'type7',
    width: 150,
    resizable: true,
    customRender: ({ record }) =>
      record?.type7?.start_at ? (
        <span style={record?.type7?.is_overdue ? 'color: red' : ''}>{record?.type7?.start_at}</span>
      ) : (
        <span>-</span>
      )
  },
  {
    title: '生产完成日期',
    dataIndex: 'type4',
    width: 150,
    resizable: true,
    customRender: ({ record }) =>
      record?.type4?.start_at ? (
        <span style={record?.type4?.is_overdue ? 'color: red' : ''}>{record?.type4?.start_at}</span>
      ) : (
        <span>-</span>
      )
  },
  {
    title: '质检完成日期',
    dataIndex: 'type5',
    width: 150,
    resizable: true,
    customRender: ({ record }) =>
      record?.type5?.start_at ? (
        <span style={record?.type5?.is_overdue ? 'color: red' : ''}>{record?.type5?.start_at}</span>
      ) : (
        <span>-</span>
      )
  },
  {
    title: '质检审核日期',
    dataIndex: 'type8',
    width: 150,
    resizable: true,
    customRender: ({ record }) =>
      record?.type8?.start_at ? (
        <span style={record?.type8?.is_overdue ? 'color: red' : ''}>{record?.type8?.start_at}</span>
      ) : (
        <span>-</span>
      )
  },
  {
    title: '装箱完成日期',
    dataIndex: 'type6',
    width: 150,
    resizable: true,
    customRender: ({ record }) =>
      record?.type6?.start_at ? (
        <span style={record?.type6?.is_overdue ? 'color: red' : ''}>{record?.type6?.start_at}</span>
      ) : (
        <span>-</span>
      )
  },
  {
    title: '出库日期',
    dataIndex: 'est_finished_at',
    width: 150,
    resizable: true
  },
  {
    title: '交付日期',
    dataIndex: 'deliver_at',
    width: 150,
    resizable: true
  },
  {
    title: '最初逾期责任人',
    dataIndex: 'iability_type',
    width: 150,
    resizable: true,
    customRender: ({ text }) => tm('limitation.mapInitiallyType')?.[text] ?? text
  }
]

const status_schema = GET_STATUS_SCHEMA(saleStore.mapOrderStatusOptions)

export const searchFormSchema: FormSchema[] = [
  status_schema,
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        placeholder: '请选择',
        showSearch: true,
        optionFilterProp: 'name',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  // {
  //   label: '订单状态',
  //   field: 'status',
  //   component: 'Select',
  //   componentProps: {
  //     options: Object.keys(saleStore.saleStatus).map((key) => ({ value: key, label: saleStore.saleStatus[key] }))
  //   }
  // },
  {
    label: '销售单号',
    field: 'source_uniqid',
    component: 'Input'
  },
  {
    field: 'project_number',
    label: '项目ID',
    component: 'Input'
  },
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input'
  },
  {
    field: 'created_at',
    label: '创建时间',
    component: 'SingleRangeDate',
    componentProps: {
      allowEmpty: [true, true],
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  },
  {
    field: 'est_finished_at',
    label: '出库时间',
    component: 'SingleRangeDate',
    componentProps: {
      allowEmpty: [true, true],
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  },
  {
    field: 'deliver_at',
    label: '交付日期',
    component: 'SingleRangeDate',
    componentProps: {
      allowEmpty: [true, true],
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  },
  {
    field: 'is_stock_latter',
    label: '可备货日期>出库日期',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'iability_type',
    label: '最初逾期责任人',
    component: 'Select',
    componentProps: {
      options: tm('limitation.initiallyTypeList')
    }
  }
]
