<template>
  <div>
    <BasicTable :data-cachekey="routePath" @register="registerTable">
      <template #toolbar>
        <a-button v-if="hasPermission(461)" :loading="exporting" :disabled="exporting" type="primary" @click="handleExport">导出</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-button v-if="hasPermission(460)" type="primary" size="small" @click="onViewResponsibilityList(record)">订单责任列表</a-button>
        </template>
      </template>
    </BasicTable>
    <ResponsibilityListDrawer @register="registerResponsibilityListDrawer" />
  </div>
</template>

<script setup lang="ts">
import { BasicTable, useTable } from '/@/components/Table'
import { getLimitationList } from '/@/api/erp/sales'
import { columns, searchFormSchema } from '/@/views/erpFlow/limitation/datas/datas'
import ResponsibilityListDrawer from '/@/views/revisitManage/deliveryResponDivision/components/ResponsibilityListDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { usePermission } from '/@/hooks/web/usePermission'
import { ref } from 'vue'
import { useMessage } from '/@/hooks/web/useMessage'
import { useRoute } from 'vue-router'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'

const route = useRoute()
const { path: routePath } = route
const { createMessage } = useMessage()

const { hasPermission } = usePermission()

const exporting = ref(false)

const [registerTable, { getForm, setLoading }] = useTable({
  title: '销售单时效统计',
  api: getLimitationList,
  showIndexColumn: false,
  columns,
  rowKey: 'id',
  useSearchForm: true,
  actionColumn: {
    width: 150,
    dataIndex: 'action',
    title: '操作',
    fixed: 'right',
    ifShow: hasPermission(460)
  },
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas: searchFormSchema,
    fieldMapToTime: [
      ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['est_finished_at', ['est_finished_at_start', 'est_finished_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['deliver_at', ['deliver_at_start', 'deliver_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  }
})

const [registerResponsibilityListDrawer, { openDrawer: openResponsibilityListDrawer }] = useDrawer()

function onViewResponsibilityList(record) {
  openResponsibilityListDrawer(true, { searchInfo: { id: record.id } })
}
async function handleExport() {
  try {
    exporting.value = true
    setLoading(true)
    const params = getForm()?.getFieldsValue()
    const response = await getLimitationList({ ...params, is_excel: 1, pageSize: 10000 }, true)
    // const blob = new Blob([response.data], { type: response.headers['content-type'] })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    downloadLink.download = `销售单时效统计-${+new Date()}.xlsx`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    createMessage.success('导出成功')
  } catch (err) {
    createMessage.error('导出失败')
    throw new Error(err)
  } finally {
    setLoading(false)
    exporting.value = false
  }
}
</script>
