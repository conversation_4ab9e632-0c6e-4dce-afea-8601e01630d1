<template>
  <div>
    <BasicDrawer @register="registerDrawer" v-bind="$attrs" width="100%" destroy-on-close @close="handleClose" @ok="handleOk">
      <Row :gutter="20" type="flex">
        <Col flex="1">
          <Row :gutter="20" type="flex" align="middle">
            <Col flex="1" class="no-padding">
              <div class="flex justify-between mb-2 wrap-title">
                <div class="title"> 现有包裹清单 </div>
                <div>
                  <a-button class="mr-2" type="primary" @click="handleSelectGoods">全选</a-button>
                  <a-button type="primary" @click="selectProduct = []">全不选</a-button>
                </div>
              </div>
              <div class="border-1 border-[#f0f0f0] h-65vh">
                <scroll-container>
                  <CheckboxGroup v-model:value="selectProduct" class="w-full">
                    <div v-for="(pkg, idx) in originPackageList" :key="idx" class="bg-[#F4F5F5] w-99% package-product-wrap box-border m-2">
                      <!--                      <Checkbox :value="pkg.id" class="w-full checkbox-item !border-none">-->
                      <div class="w-full package-name mb-2 flex justify-between items-center">
                        <div>箱号：{{ pkg.strid }} - 项目ID：{{ pkg.projectNumber }}</div>
                        <Dropdown placement="top" trigger="hover">
                          <template #overlay>
                            <Menu @click="(e) => handleSplit(e, pkg)">
                              <MenuItem key="0">不使用包裹信息拆分</MenuItem>
                              <MenuItem :key="pkg.id">使用包裹信息拆分</MenuItem>
                            </Menu>
                          </template>
                          <Tooltip placement="left">
                            <template #title>将选中的包裹拆分成多个新包裹</template>
                            <a-button type="primary" class="mr-2" size="small">拆分包裹<DownOutlined /></a-button>
                          </Tooltip>
                        </Dropdown>
                      </div>
                      <Draggable
                        :disabled="true"
                        :sort="false"
                        :list="pkg.product"
                        group="packageList"
                        item-key="id"
                        :handle="['.product']"
                        @change="(data) => handleChange(data, idx)"
                      >
                        <template #item="{ element: product }">
                          <Tag color="blue" class="!mb-2 inline-block">
                            <Checkbox
                              :value="product.prodKey"
                              :disabled="sub(product.quantity, compPackageProductCount[product.prodKey]) <= 0"
                            >
                              <div>
                                <div class="product flex items-center p-1">
                                  <div class="product-name mb-1 mt-1 flex flex-col">
                                    <div class="prodect-linefeed">产品名称：{{ product.name }}</div>
                                    <div class="text-xs text-[#999]">产品编码：{{ product.puid }}</div>
                                  </div>
                                  <div class="product-count ml-3 text-blue-500">
                                    待分配数量：{{ sub(product.quantity, compPackageProductCount[product.prodKey]) }}
                                  </div>
                                </div>
                                <!--                            <div class="product-form">-->
                                <!--                              <BasicForm-->
                                <!--                                :ref="(el) => (productFormEl[product.itemKey] = el)"-->
                                <!--                                :key="product.itemKey"-->
                                <!--                                v-bind="productFormConfig"-->
                                <!--                                v-model:model="mapPackageProduct[product.itemKey]"-->
                                <!--                              />-->
                                <!--                            </div>-->
                              </div>
                            </Checkbox>
                          </Tag>
                        </template>
                      </Draggable>
                      <!--                      </Checkbox>-->
                    </div>
                  </CheckboxGroup>
                </scroll-container>
                <!--                <div v-else class="h-full flex justify-center items-center">-->
                <!--                  <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />-->
                <!--                </div>-->
              </div>
              <div class="flex justify-between w-full mt-2">
                <div></div>
                <div>
                  <Dropdown placement="top" trigger="hover">
                    <template #overlay>
                      <Menu @click="handleMergePackage">
                        <MenuItem key="0">不使用包裹信息合并</MenuItem>
                        <MenuItem v-for="selectPkg in compSelectPackage" :key="selectPkg.key">
                          使用 {{ selectPkg.strid }} 包裹信息
                        </MenuItem>
                      </Menu>
                    </template>
                    <Tooltip placement="right">
                      <template #title>将包裹中的产品合并到新的包裹中</template>
                      <a-button type="primary" class="mr-2">合并包裹<DownOutlined /></a-button>
                    </Tooltip>
                  </Dropdown>
                </div>
              </div>
            </Col>
          </Row>
        </Col>
        <Col>
          <Row type="flex" align="middle" class="h-full">
            <Col>
              <div class="w-30px h-30px border rounded-full flex justify-center items-center opacity-40">
                <SwapRightOutlined />
              </div>
            </Col>
          </Row>
        </Col>
        <Col span="14">
          <div class="flex justify-between mb-2 wrap-title">
            <div class="title"> 整合后包裹清单 </div>
            <div>
              <a-button type="primary" class="mr-2" @click="isProductSimple = !isProductSimple"
                >{{ isProductSimple ? '展开' : '收起' }}产品信息</a-button
              >
              <a-button type="primary" @click="isPackageSimple = !isPackageSimple"
                >{{ isPackageSimple ? '展开' : '收起' }}包裹信息</a-button
              >
            </div>
          </div>
          <div class="h-70vh no-padding border-1 border-[#f0f0f0]">
            <scroll-container v-if="packageList.length > 0">
              <div v-for="(pkg, idx) in packageList" :key="idx" class="bg-[#F4F5F5] w-99% package-product-wrap box-border m-2">
                <div class="package-name mb-2 flex justify-between items-center">
                  <div>包裹{{ pkg.key }}</div>
                  <a-button type="error" size="small" @click="handleRemovePackage(idx)">删除包裹</a-button>
                </div>
                <Draggable
                  :disabled="true"
                  :sort="false"
                  :list="pkg.product"
                  group="productList"
                  item-key="prodKey"
                  :class="{ 'border border-blue-500': isLightHeight }"
                  :handle="['.product']"
                  @change="(data) => handleChange(data, idx)"
                >
                  <template #item="{ element: product }">
                    <Tag color="blue" class="!mb-2 inline-block">
                      <div>
                        <div class="product flex items-center p-1">
                          <div class="product-name mb-1 mt-1 flex flex-col">
                            <div>产品名称：{{ product.name }}</div>
                            <div class="text-xs text-[#999]">所属箱号：{{ mapProductForPackage[product.prodKey].strid }}</div>
                            <div class="text-xs text-[#999]">产品编码：{{ product.puid }}</div>
                          </div>
                          <!--  <div v-show="isProductSimple" class="product-count ml-3 text-blue-500">×{{ product.buildQuantity }}</div>-->
                          <div v-show="isProductSimple" class="info-count ml-1">
                            <InputNumber
                              v-model:value="product.quantity"
                              :max="add(sub(+product.maxQuantity, compPackageProductCount[product.prodKey] ?? 0), product.quantity)"
                              prefix="×"
                              size="small"
                              min="0.01"
                              @change="(val) => handleCountChange(val, product)"
                            />
                          </div>
                          <a-button size="small" class="ml-1" type="default" @click="handleRemovePackageProduct(idx, product)"
                            >删除</a-button
                          >
                        </div>
                        <div v-show="!isProductSimple" class="product-form">
                          <Form
                            v-show="!isProductSimple"
                            :ref="(el) => (productFormEl[product.itemKey] = el)"
                            :key="product.itemKey"
                            :model="mapPackageProduct[product.itemKey]"
                            :name="`product-${product.itemKey}`"
                            :label-col="{ style: { width: `${productFormConfig.labelWidth}px` } }"
                          >
                            <Row>
                              <Col
                                :span="goodsSchemas.colProps?.span || productFormConfig.baseColProps.span"
                                v-for="goodsSchemas in productFormConfig.schemas"
                                :key="goodsSchemas.field"
                              >
                                <FormItem
                                  :label="goodsSchemas.label"
                                  :name="goodsSchemas.field"
                                  :rules="[{ required: goodsSchemas.required, message: `请输入${goodsSchemas.label}` }]"
                                >
                                  <template v-if="goodsSchemas.field === 'quantity'">
                                    <component
                                      :is="goodsSchemas.component"
                                      v-model:value="mapPackageProduct[product.itemKey][goodsSchemas.field]"
                                      placeholder="请输入"
                                      :max="
                                        add(
                                          sub(
                                            +mapPackageProduct[product.itemKey].maxQuantity,
                                            compPackageProductCount[product.prodKey] ?? 0
                                          ),
                                          mapPackageProduct[product.itemKey].quantity
                                        )
                                      "
                                      v-bind="goodsSchemas.componentProps"
                                    />
                                  </template>
                                  <template v-else>
                                    <component
                                      :is="goodsSchemas.component"
                                      v-model:value="mapPackageProduct[product.itemKey][goodsSchemas.field]"
                                      placeholder="请输入"
                                      v-bind="goodsSchemas.componentProps"
                                      :disabled="goodsSchemas.dynamicDisabled"
                                    />
                                  </template>
                                </FormItem>
                              </Col>
                            </Row>
                          </Form>
                          <!--                          <BasicForm-->
                          <!--                            :ref="(el) => (productFormEl[product.itemKey] = el)"-->
                          <!--                            :key="product.itemKey"-->
                          <!--                            v-bind="productFormConfig"-->
                          <!--                            v-model:model="mapPackageProduct[product.itemKey]"-->
                          <!--                          />-->
                        </div>
                      </div>
                    </Tag>
                  </template>
                </Draggable>
                <div v-show="!isPackageSimple">
                  <Form
                    v-show="!isPackageSimple"
                    :ref="(el) => setPackagesFormEl(el, idx, pkg)"
                    :model="packageList[idx]"
                    :name="`package-${pkg.key}`"
                    :label-col="{ style: { width: `${formConfig.labelWidth}px` } }"
                  >
                    <Row>
                      <Col
                        :span="schemas.colProps?.span || formConfig.baseColProps.span"
                        v-for="schemas in formConfig.schemas"
                        :key="schemas.field"
                      >
                        <template v-if="schemas.field === 'info'">
                          <Divider orientation="left">{{ schemas.label }}</Divider>
                        </template>
                        <template v-else-if="['width', 'height', 'length'].includes(schemas.field)">
                          <FormItem
                            :label="schemas.label"
                            :name="schemas.field"
                            :rules="[{ required: schemas.required, message: `请输入${schemas.label}` }]"
                          >
                            <component
                              :is="schemas.component"
                              v-model:value="packageList[idx][schemas.field]"
                              v-bind="schemas.componentProps"
                              @change="handleSetVolume(idx)"
                              placeholder="请输入"
                            />
                          </FormItem>
                        </template>
                        <template v-else>
                          <FormItem
                            :label="schemas.label"
                            :name="schemas.field"
                            :rules="[{ required: schemas.required, message: `请输入${schemas.label}` }]"
                          >
                            <component
                              :is="schemas.component"
                              v-model:value="packageList[idx][schemas.field]"
                              v-bind="schemas.componentProps"
                              :disabled="schemas.dynamicDisabled"
                              placeholder="请输入"
                            />
                          </FormItem>
                        </template>
                      </Col>
                    </Row>
                  </Form>
                  <!--                  <BasicForm-->
                  <!--                    v-if="packageList[idx]"-->
                  <!--                    v-bind="formConfig"-->
                  <!--                    :ref="(el) => (packageFormEl[pkg.key] = el)"-->
                  <!--                    v-model:model="packageList[idx]"-->
                  <!--                  />-->
                </div>
              </div>
            </scroll-container>
            <div v-else class="h-full flex justify-center items-center">
              <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
            </div>
          </div>
        </Col>
      </Row>
      <PackagesDrawer @register="registerPackagesDrawer" @split="handleSplitPackage" />
    </BasicDrawer>
  </div>
</template>

<script setup lang="ts">
import { useDrawerInner, BasicDrawer, useDrawer } from '/@/components/Drawer'
import {
  Row,
  Col,
  Tag,
  CheckboxGroup,
  Checkbox,
  InputNumber,
  Tooltip,
  Empty,
  Dropdown,
  Menu,
  MenuItem,
  Form,
  Divider
} from 'ant-design-vue'
import ScrollContainer from '/@/components/Container/src/ScrollContainer.vue'
// import { BasicForm } from '/@/components/Form'
import { ref } from 'vue'
import { SwapRightOutlined, DownOutlined } from '@ant-design/icons-vue'
import { cloneDeep, random, pick } from 'lodash-es'
import { add, div, mul, sub } from '/@/utils/math'
import { useMessage } from '/@/hooks/web/useMessage'
import Draggable from 'vuedraggable'
import {
  formConfig,
  originPackageList,
  compSelectPackage,
  packageList,
  compSelectPackageProduct,
  compPackageProductCount,
  packageFormEl,
  productFormConfig,
  mapPackageProduct,
  productFormEl,
  selectProduct,
  compOriginPackage,
  compMapProduct,
  mapProductForPackage,
  mapPackageFormEl,
  compUsableKeysList
} from '../datas/abnormalDrawer.datas'
import { addPackage, getPackageDetail } from '/@/api/erpFlow/packages'
import PackagesDrawer from './PackagesDrawer.vue'

const emits = defineEmits(['success', 'register'])
const { createMessage, notification } = useMessage()
const pkgFlag = ref(0) // 包裹的序号生成标识
const rightPkgFlag = ref<number>(1) // 右边的序号生成标识
// const curOrderIds = ref([]) // 选中的采购订单id
const isLightHeight = ref<boolean>(false) // 拖拽时是否高亮
const isProductSimple = ref<boolean>(true) // 是否收起/展开填写信息
const isPackageSimple = ref<boolean>(false) // 是否收起/展开填写信息
const FormItem = Form.Item

const [registerPackagesDrawer, { openDrawer: openPackageDrawer, setDrawerProps: setPackageDrawerProps }] = useDrawer()
const isSelectRowInStatus = ref()

const [registerDrawer, { closeDrawer, changeLoading, changeOkLoading }] = useDrawerInner(async ({ recordIds, Status }) => {
  try {
    changeLoading(true)
    changeOkLoading(true)
    const { items } = await getPackageDetail({ ids: recordIds })
    isSelectRowInStatus.value = Status
    console.log(isSelectRowInStatus.value)

    // 初始化包裹产品属性
    originPackageList.value = items.map((pkg) => {
      // pkgFlag += 1
      pkgFlag.value = 1 + pkgFlag.value
      return {
        ...pkg,
        key: pkgFlag.value,
        projectNumber: pkg.project_number,
        product: pkg.items.map((item) => ({
          ...item,
          packageKey: pkgFlag.value,
          // origin_package_id: pkg.id,
          prodKey: random(1, 1000000000), // 右边同类的包裹商品依靠这个字段判断
          maxQuantity: item.quantity,
          projectNumber: pkg.project_number,
          sale_work_id: pkg.packing_package_work.map((item) => item.sale_work_id)
        }))
      }
    })
  } catch (err) {
    notification.error({
      message: JSON.stringify(err)
    })
  } finally {
    changeLoading(false)
    changeOkLoading(false)
  }
})

function setPackagesFormEl(el, idx, pkg) {
  if (el) {
    packageFormEl.value[idx] = { key: pkg.key, el }
  } else {
    packageFormEl.value.splice(idx, 1)
  }
}

// 拖拽目标的数据
// function cloneDog(data) {
//   return cloneDeep({ ...data, prodKey: random(1, 1000000000) })
// }

// 监听包裹产品拖拽后发生的变化
function handleChange(data, pkgIdx) {
  const { added, removed } = data

  // 产品清单的产品移动到包裹列表触发
  if (added) {
    const { newIndex, element } = added
    // 检查是否已经存在这个商品，如果存在就合并
    const originProductIdx = originPackageList.value[pkgIdx].product.findIndex(
      (item, idx) => item.prodKey === element.prodKey && idx !== newIndex
    )
    console.log(originProductIdx)
    if (originProductIdx !== -1) {
      packageList.value[pkgIdx].product[originProductIdx].quantity = add(
        packageList.value[pkgIdx].product[originProductIdx].quantity,
        element.quantity
      )
      packageList.value[pkgIdx].product.splice(newIndex, 1)
    }
  }

  // 不同包裹互相移动会触发，需要判断包裹是否为空，包裹为空则移除包裹
  if (removed) {
    if (packageList.value[pkgIdx].product.length === 0) {
      packageList.value.splice(pkgIdx, 1)
    }
  }
}

function handleSetVolume(idx) {
  const { length, width, height } = packageList.value[idx]
  packageList.value[idx].volume = div(mul(mul(length ?? 0, width ?? 0), height ?? 0), 1000000, 6)
}

function handleClose() {
  packageList.value = []
  packageFormEl.value = []
  productFormEl.value = {}
  // curOrderIds.value = []
  pkgFlag.value = 0
  rightPkgFlag.value = 1
  originPackageList.value = []
}

// 同步简单模式下form中的商品数量
async function handleCountChange(val, product) {
  mapPackageProduct.value[product.itemKey].quantity = val
  // const productForm = productFormEl.value[product.itemKey]
  // const { formActionType } = productForm
  // const { setFieldsValue } = formActionType
  // await setFieldsValue({ quantity: val })
}

// 合并包裹触发
function handleMergePackage(e) {
  const selectFields = ['height', 'width', 'length', 'method', 'quantity', 'volume', 'weight', 'inCharge']

  // 对象保留的属性
  const pickGoodsFields = [
    'id',
    'name',
    'imgs',
    'material',
    'quantity',
    'unit',
    'width',
    'height',
    'length',
    'code',
    'request_id',
    'request_sub_id',
    'item_purchase_id',
    'item_purchase_sub_id',
    'item_stocking_id',
    'puid',
    'type',
    'prodKey',
    'maxQuantity',
    'packing_package_id',
    'purchase_work_id',
    'sale_work_id'
  ]

  const selectGoods = cloneDeep(compSelectPackageProduct.value)
  const isSameProject = [...new Set(selectGoods.map((item) => item.projectNumber))]?.length === 1
  if (!isSameProject) return createMessage.error('请选择相同项目的包裹进行合并！')

  if (compSelectPackageProduct.value.length === 0) return createMessage.error('请先选择需要合并的包裹！')

  // 附带的商品信息
  const selectInfo = e.key === 0 ? {} : pick(cloneDeep(compOriginPackage.value[e.key]), selectFields)

  // 整理需要合并的包裹产品
  const mergeProductList = selectGoods.map((item) => {
    return {
      ...pick(item, pickGoodsFields),
      itemKey: random(1, 100000000),
      quantity: sub(item.quantity, compPackageProductCount.value[item.prodKey])
    }
  })

  // 更新到右边
  packageList.value.push({
    key: rightPkgFlag.value,
    ...selectInfo,
    type: 1,
    product: mergeProductList
  })
  rightPkgFlag.value += 1
  selectProduct.value = []
}

// 拆分包裹
function handleSplit(e, pkg) {
  const useFields = ['height', 'width', 'length', 'method', 'quantity', 'volume', 'weight', 'inCharge']
  let useInfo = {}
  if (e.key !== '0') {
    useInfo = pick(pkg, useFields)
  }
  // console.log(useInfo)
  const splitPkgList = cloneDeep(pkg)
  splitPkgList.product = splitPkgList.product.map((goods) => {
    const useCount = sub(goods.quantity, compPackageProductCount.value[goods.prodKey] ?? 0)
    // 数量设置为可以使用数量
    return { ...goods, quantity: useCount, project_number: goods.projectNumber }
  })
  // 打开包裹新增
  setPackageDrawerProps({ title: '包裹异动拆分', showFooter: true })
  openPackageDrawer(true, { type: 'split', packageInfo: splitPkgList, useInfo })
  selectProduct.value = []
}

// 拆分完成接收数据
function handleSplitPackage({ packages }) {
  // console.log(packages)
  const splitPackageList = packages.map((item) => {
    return {
      ...item,
      type: 2,
      key: rightPkgFlag.value++,
      product: item.product.map((item) => ({
        ...item,
        quantity: item.buildQuantity,
        maxQuantity: compMapProduct.value[item.prodKey].maxQuantity
      }))
    }
  })
  packageList.value = packageList.value.concat(splitPackageList)
}

// 移除包裹中的产品
function handleRemovePackageProduct(pkgIdx, product) {
  if (packageList.value[pkgIdx]?.product?.length <= 1) return createMessage.error('包裹的最后一个商品不允许删除')
  packageList.value[pkgIdx].product = packageList.value[pkgIdx].product.filter((item) => product.itemKey !== item.itemKey)
}

// 移除包裹
function handleRemovePackage(idx) {
  packageList.value.splice(idx, 1)
}

// 验证包裹数量
async function validPackageCount() {
  let errKey: number | string | null = null

  if (packageList.value.length === 0) {
    createMessage.error(`不存在包裹`)
    return false
  }

  try {
    // 验证包裹信息
    for (const packages of packageList.value) {
      errKey = packages.key
      const packageForm = mapPackageFormEl.value[packages.key]
      // const { formActionType } = packageForm
      // const { validate: validatePackage } = formActionType
      await packageForm.validate()
    }
  } catch (err) {
    console.log(err)
    createMessage.error(`包裹${errKey}信息未完善！`)
    // validResult = false
    return false
  }
  let noUseCount = true
  for (const key of Object.keys(compPackageProductCount.value)) {
    const curProd = compMapProduct.value[key]
    if (sub(curProd.maxQuantity, compPackageProductCount.value[key]) > 0) {
      const curPkg = mapProductForPackage.value[key]

      createMessage.error(`箱号${curPkg.strid} 的 ${curProd.name} 的商品存在待分配数量`)
      noUseCount = false
      break
    }
  }

  return noUseCount
}

function getParams() {
  return {
    packageList: packageList.value.map((packages) => {
      const packing_package_id_origin = [...new Set(packages.product.map((item) => item.packing_package_id))]
      if (packing_package_id_origin.includes(null)) {
        throw new Error('packing_package_id_origin or purchase_work_ids has null')
      }
      return {
        ...packages,
        packing_package_id_origin: [...new Set(packages.product.map((item) => item.packing_package_id))],
        purchase_work_ids: [...new Set(packages.product.map((item) => item.purchase_work_id))].filter((item) => item),
        sale_work_ids: [...new Set(packages.product.map((item) => item.sale_work_id).flat())].filter((item) => item),
        items: packages.product.map((product) => ({
          ...pick(product, [
            'id',
            'name',
            'material',
            'quantity',
            'unit',
            'code',
            'remark',
            'puid',
            'request_id',
            'purchase_work_id',
            'item_stocking_id'
          ]),
          imgs: product.imgs ?? [],
          quantity: product.quantity,
          request_sub_id: product.request_sub_id ? product.request_sub_id : void 0,
          type: product.type,
          item_purchase_id: product.item_purchase_id ?? void 0,
          item_purchase_sub_id: product.item_purchase_sub_id ?? void 0,
          size: {
            width: product.width,
            height: product.height,
            length: product.length
          }
        }))
      }
    })
  }
}

function handleSelectGoods() {
  selectProduct.value = cloneDeep(compUsableKeysList.value)
}

// 提交
async function handleOk() {
  const validResult = await validPackageCount()
  if (!validResult) return
  try {
    changeLoading(true)
    changeOkLoading(true)
    const data = getParams()

    if (isSelectRowInStatus.value) {
      // 标记是否存在空的 item_stocking_id
      let hasEmptyItemStockingId = false

      // 外层循环：遍历 data 数组
      for (const item of data.packageList) {
        // 检查当前元素的 items 数组是否存在且包含空的 item_stocking_id
        if (item.items && item.items.some((subItem) => !subItem.item_stocking_id)) {
          hasEmptyItemStockingId = true
          break // 一旦发现空值，立即退出循环
        }
      }

      // 如果存在空值，显示错误并返回
      if (hasEmptyItemStockingId) {
        createMessage.error('item_stocking_id不能为空')
        return
      }
    }
    const { msg } = await addPackage(data)
    if (msg === 'success') {
      emits('success')
      closeDrawer()
      handleClose()
      setTimeout(() => {
        changeLoading(false)
        changeOkLoading(false)
      }, 3000)
      createMessage.success('提交成功')
      return
    }
    createMessage.success('提交失败')
    changeLoading(false)
    changeOkLoading(false)
  } catch (err) {
    // createMessage.error('提交失败')
    changeLoading(false)
    changeOkLoading(false)
    notification.error({
      message: err?.message
    })
  }
}
</script>

<style lang="less" scoped>
.wrap-title {
  .title {
    font-weight: 800;
    font-size: 16px;
    color: #383838;
  }
}

.package-product-wrap {
  padding: 15px;
  border-radius: 10px;

  &:last-child {
    margin-bottom: 0;
  }
  .package-name {
    font-weight: bold;
    font-size: 16px;
    color: #383838;
  }

  .product {
    display: flex;
    justify-content: space-between;
    .product-name {
      font-size: 14px;
      color: #27335b;
      font-weight: 600;
      .prodect-linefeed {
        width: 300px;
        overflow: hidden;
        white-space: pre-wrap; /* 保留空格和换行符 */
        overflow-wrap: break-word; /* 允许在单词内换行 */
        text-overflow: ellipsis; /* 文本溢出显示省略号 */
        max-height: 2.8em; /* 限制高度为两行 */
        line-height: 1.4em; /* 设置行高 */
        word-break: break-all;
      }
    }

    .product-count {
      font-weight: bold;
      font-size: 16px;
      //color: #27335b;
    }
  }
}

::v-deep(.checkbox-item) {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  border-top: 1px solid #fff;
  border-left: 1px solid #fff;
  border-right: 1px solid #fff;

  &.is-active {
    border: 1px solid #0960bd !important;
  }
  > span:not(.ant-checkbox) {
    display: inline-block;
    width: 100%;
  }
}

//::v-deep(.radio-item) {
//  border-radius: 10px;
//
//  > span:not(.ant-radio) {
//    display: block;
//    width: 100%;
//  }
//}

.product-info {
  .info-title {
    font-weight: 500;
    font-size: 14px;
    color: #27335b;
  }

  .info-desc {
    font-weight: 400;
    font-size: 14px;
    color: #777777;
  }

  .info-count {
    font-weight: bold;
    font-size: 18px;
    color: #27335b;
  }
}

.no-padding {
  ::v-deep(.scrollbar__wrap) {
    padding: 0 5px 0 0 !important;
  }
}

.form-wrap {
  ::v-deep(.ant-form-item) {
    margin-bottom: 0 !important;
  }
}
</style>
