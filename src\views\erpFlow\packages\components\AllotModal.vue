<template>
  <BasicModal @register="register" title="包裹调拨" @ok="handleOk" defaultFullscreen>
    <Alert message="可以点击表头批量更改调拨仓库与仓库仓位,也可自行单独更改" show-icon />
    <BasicTable @register="registerTable">
      <template #headerCell="{ column }">
        <template v-if="column.dataIndex === 'warehouse_id_allot'">
          <!-- {{ column.customTitle }} -->
          <div class="text-center">
            <Popover trigger="click" title="批量填入仓库">
              <template #content>
                <PagingApiSelect
                  style="width: 188px"
                  placeholder="请选择仓库`"
                  :pagingMode="true"
                  :api="getWarehouse"
                  :search-mode="true"
                  :always-load="false"
                  return-params-field="id"
                  :select-props="{
                    fieldNames: { value: 'id', label: 'name' },
                    placeholder: '请选择',
                    style: { minWidth: '188px' }
                  }"
                  resultField="items"
                  @change="
                    (_, shall) => {
                      warehouseSelect = shall
                    }
                  "
                />
                <Button style="margin-left: 5px" type="primary" @click="handleBatchEdit('war')">确定</Button>
              </template>
              <span style="margin-right: 10px">
                {{ column.customTitle }}
                <FormOutlined />
              </span>
            </Popover>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'warehouse_item_id_allot'">
          <!-- {{ column.customTitle }} -->
          <div class="text-center">
            <Tooltip title="请选取仓库">
              <Popover trigger="click" title="批量填入仓库仓位">
                <template #content>
                  <PagingApiSelect
                    style="width: 188px"
                    placeholder="请选择仓位`"
                    :pagingMode="true"
                    :api="() => getWMI({ warehouse_id: warehouseSelect.id ? warehouseSelect.id : null })"
                    :search-mode="true"
                    :always-load="false"
                    :select-props="{
                      fieldNames: { value: 'id', label: 'name' },
                      placeholder: '请选择',
                      style: { minWidth: '188px' },
                      optionFilterProp: 'name'
                    }"
                    resultField="items"
                    @change="
                      (_, shall) => {
                        warehouseSelect = shall
                      }
                    "
                  />
                  <Button style="margin-left: 5px" type="primary" @click="handleBatchEdit('wmi')">确定</Button>
                </template>
                <span style="margin-right: 10px">
                  {{ column.customTitle }}
                  <FormOutlined />
                </span>
              </Popover>
            </Tooltip>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'cancel_remark'">
          <div class="text-center">
            <Popover trigger="click" title="批量填入作废备注">
              <template #content>
                <Textarea :rows="4" v-model:value="cancel_remark" />
                <Button class="mt-3" style="width: 100%" type="primary" @click="handleBatchEdit('clean')">确定</Button>
              </template>
              <span style="margin-right: 10px">
                {{ column.customTitle }}
                <FormOutlined />
              </span>
            </Popover>
          </div>
        </template>
        <template v-else>{{ column.customTitle }} </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { useTable } from '/@/components/Table'
import BasicTable from '/@/components/Table/src/BasicTable.vue'
import { columns, cleancolumns } from '../datas/modal'
import { Popover, Button, Tooltip, Alert, message, Textarea } from 'ant-design-vue'
import { FormOutlined } from '@ant-design/icons-vue'
import { ref } from 'vue'
import PagingApiSelect from '/@/components/Form/src/components/PagingApiSelect.vue'
import { getWarehouse, getWMI } from '/@/api/baseData/warehouse'
import { getallot, setIsCancel } from '/@/api/erpFlow/packages'
//仓库id
const warehouseSelect = ref<any>()
const cancel_remark = ref('')
const type = ref()
//id
const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
  console.log(data)
  type.value = data.type
  setTableData(data.selectRow)
  data.type == 'edit' ? setColumns(columns) : setColumns(cleancolumns)
})

const [registerTable, { setTableData, getDataSource, setColumns }] = useTable({
  useSearchForm: false,
  showTableSetting: false,
  showIndexColumn: false
})
const emit = defineEmits(['success', 'register'])

//批量更改
function handleBatchEdit(type: string) {
  const data = getDataSource()
  switch (type) {
    case 'war':
      data.forEach((item) => {
        if (warehouseSelect.value) {
          item.warehouse_id_allot = warehouseSelect.value.name
          item.warehouse_id_new = warehouseSelect.value.id
        }
      })
      break
    case 'wmi':
      data.forEach((item) => {
        if (warehouseSelect.value) {
          item.warehouse_item_id_allot = warehouseSelect.value.name
          item.warehouse_item_id_new = warehouseSelect.value.id
        }
      })
      break
    case 'clean':
      data.forEach((item) => {
        if (cancel_remark.value) {
          console.log('1')

          item.cancel_remark = cancel_remark.value
        }
      })
      break
  }
}
// 提交
async function handleOk() {
  try {
    changeOkLoading(true)
    const data = getDataSource()
    for (const item of data) {
      if ((!item.warehouse_id_new || !item.warehouse_item_id_new) && type.value !== 'clean') {
        changeOkLoading(false)
        return message.error('请完善调拨仓库与仓位!')
      }
      if (type.value == 'clean' && !item.cancel_remark) {
        changeOkLoading(false)
        return message.error('请完善作废备注!')
      }
    }
    const params =
      type.value !== 'clean'
        ? data.map((item) => {
            return {
              packing_package_id: item.id,
              warehouse_id: item.warehouse_id_new,
              warehouse_item_id: item.warehouse_item_id_new,
              warehouse_id_origin: item.warehouse_id,
              warehouse_item_id_origin: item.warehouse_item_id
            }
          })
        : data.map((item) => {
            return {
              id: item.id,
              cancel_remark: item.cancel_remark
            }
          })

    type.value !== 'clean'
      ? getallot({
          packageList: params
        })
      : setIsCancel({ cancelList: params })

    closeModal()
    emit('success')

    setTimeout(() => {
      changeOkLoading(false)
    }, 3000)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  }
}
</script>
