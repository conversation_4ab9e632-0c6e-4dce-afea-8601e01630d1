<template>
  <BasicModal @register="registerModal" v-bind="$attrs" title="编辑" width="40%" @ok="handleOk">
    <BasicForm @register="registerForm">
      <!--      <template #Summary="{ model }">-->
      <!--        <FormItemRest>-->
      <!--          <Popover v-for="item in model.packingList" :key="item.id" trigger="click">-->
      <!--            <template #content>-->
      <!--              <div class="w-[700px] h-[300px]">-->
      <!--                <BasicTable :api="(params) => getPackageDetail({ ...params, ids: [item.id] })" @register="registerTable" />-->
      <!--              </div>-->
      <!--            </template>-->
      <!--            <Tag :class="bgcshow == item.id ? 'tagbgc' : ''">-->
      <!--              {{ item.strid }}-->
      <!--            </Tag>-->
      <!--          </Popover>-->
      <!--        </FormItemRest>-->
      <!--      </template>-->
    </BasicForm>
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { columns, schemas } from '../datas/createBoxing.datas'
// import { Tag, Popover, Form } from 'ant-design-vue'
import { createBoxingOrder, getPackageDetail } from '/@/api/erpFlow/packages'
import { useMessage } from '/@/hooks/web/useMessage'
import { BasicTable, useTable } from '/@/components/Table'
import { add } from '/@/utils/math'
// import { ref } from 'vue'
// const FormItemRest = Form.ItemRest
const emits = defineEmits(['success', 'register'])

const { createMessage } = useMessage()

const [registerForm, { setFieldsValue, validate }] = useForm({
  baseColProps: { span: 12 },
  showActionButtonGroup: false,
  labelCol: { span: 5 },
  schemas,
  colon: true
})

const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(({ rows }) => {
  changeLoading(true)
  changeOkLoading(true)
  setFieldsValue({ packingList: rows, supplier: '敬城建材  George Construction' })
  initTableData(rows)
})

//商品表格
const [registerTable, { setTableData }] = useTable({
  title: '商品汇总',
  columns,
  maxHeight: 300,
  canResize: false,
  dataSource: []
})

async function initTableData(rows) {
  try {
    const ids = rows.map((item) => item.id)
    const { items } = await getPackageDetail({ ids })
    const dataArr = items.map((item) => item.items).flat()
    const requestIds = [...new Set(dataArr.map((item) => item.request_id))]
    const dataSource = requestIds.map((requestId) => {
      const itemList = dataArr.filter((item) => item.request_id === requestId)
      const [data] = itemList
      return {
        ...data,
        quantity: itemList.reduce((total, item) => add(total, +item.quantity), 0)
      }
    })
    setTableData(dataSource)
    changeOkLoading(false)
    changeLoading(false)
    // console.log(dataSource)
  } catch (e) {
    createMessage.error('处理商品汇总出现错误')
  }
}

// async function handleVisibleChange(e, item) {
//   console.log(e, item)
//   if (e == true) {
//     const { items } = await getPackageDetail({ ids: [item.id] })
//     bgcshow.value = item.id
//     await setTableData(items[0].items)
//   } else {
//     bgcshow.value = item.idnull
//   }
// }
async function handleOk() {
  try {
    changeOkLoading(true)
    changeLoading(true)
    const data = await validate()
    data.packingList = data.packingList.map((item) => {
      return { id: item.id }
    })
    const { msg } = await createBoxingOrder(data)
    if (msg === 'success') {
      createMessage.success('提交成功')
      closeModal()
      emits('success')
    } else {
      createMessage.error('提交失败')
    }

    setTimeout(() => {
      changeOkLoading(false)
      changeLoading(false)
    }, 3000)
  } catch (err) {
    changeOkLoading(false)
    changeLoading(false)
    console.log(err)
    createMessage.error('提交失败')
  }
}
</script>
<style lang="less" scoped>
.tagbgc {
  background-color: skyblue;
}
</style>
