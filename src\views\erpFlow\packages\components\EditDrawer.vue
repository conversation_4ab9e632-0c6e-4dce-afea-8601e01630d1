<template>
  <BasicDrawer @register="registerDrawer" v-bind="$attrs" width="90%" destroy-on-close @ok="handleOk">
    <BasicForm :ref="(el) => (formRef = el)" @register="registerForm">
      <template #Items="{ model }">
        <FormItemRest>
          <Popover v-model:visible="addVisible" trigger="click" placement="right" @visible-change="handleVisibleChange">
            <template #content>
              <div class="flex">
                <div class="w-[300px] mr-2">
                  <PagingApiSelect v-model:value="purchaseId" v-bind="purchasePagingSelectConfig" />
                </div>
                <div class="w-[300px] mr-2">
                  <PagingApiSelect v-model:value="selectValue" v-bind="itemPagingSelectConfig" @options-change="handleSyncGoodsList" />
                </div>
                <a-button type="primary" @click="handleAddGoods">确定添加</a-button>
              </div>
            </template>
            <a-button type="primary" :disabled="!isUpdates">新增明细</a-button>
          </Popover>
          <div class="flex w-full">
            <VxeBasicTable
              :ref="(el) => (tableRef = el)"
              class="!p-0 mt-1 flex-1"
              :data="model.items ?? []"
              v-bind="gridOptions"
              :editConfig="{ trigger: 'click', mode: 'cell', showStatus: true, enabled: isUpdates, showIcon: true }"
              :edit-rules="validRules"
            />
          </div>
        </FormItemRest>
      </template>
      <template #DescItems="{ model }">
        <FormItemRest>
          <!-- <Popover v-model:visible="addVisible" trigger="click" placement="right" @visible-change="handleVisibleChange">
            <template #content>
              <div class="flex">
                <div class="w-[300px] mr-2">
                  <PagingApiSelect v-model:value="purchaseId" v-bind="purchasePagingSelectConfig" />
                </div>
                <div class="w-[300px] mr-2">
                  <PagingApiSelect v-model:value="selectValue" v-bind="itemPagingSelectConfig" @options-change="handleSyncGoodsList" />
                </div>
                <a-button type="primary" @click="handleAddGoods">确定添加</a-button>
              </div>
            </template>
            <a-button type="primary">新增明细</a-button>
          </Popover> -->
          <div class="flex w-full">
            <VxeBasicTable
              :ref="(el) => (tableRefs = el)"
              class="!p-0 mt-1 flex-1"
              :data="model.desc_items ?? []"
              v-bind="descgridOptions"
              :editConfig="{ trigger: 'click', mode: 'cell', showStatus: true, enabled: true, showIcon: true }"
              :edit-rules="validRules"
            />
          </div>
        </FormItemRest>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, PagingApiSelect, useForm } from '/@/components/Form'
import {
  gridOptions,
  descgridOptions,
  itemPagingSelectConfig,
  selectValue,
  purchaseId,
  purchasePagingSelectConfig,
  schemas,
  mapGoodsList,
  tableRef,
  tableRefs,
  validRules,
  formRef,
  removeItemsList,
  projectNumber
} from '/@/views/erpFlow/packages/datas/editDrawer.data'
import { VxeBasicTable } from '/@/components/VxeTable'
import { editPackage, getPackageDetail } from '/@/api/erpFlow/packages'
import { useMessage } from '/@/hooks/web/useMessage'
import { Form, Popover } from 'ant-design-vue'
import { ref } from 'vue'
import { add } from '/@/utils/math'
import { random } from 'lodash-es'

const FormItemRest = Form.ItemRest
const { createMessage, notification } = useMessage()
const emits = defineEmits(['success', 'register'])
const addVisible = ref(false)
const isUpdates = ref(true)

const [registerDrawer, { changeLoading, closeDrawer, changeOkLoading }] = useDrawerInner(async (data) => {
  try {
    projectNumber.value = null
    changeLoading(true)
    removeItemsList.value = []
    purchaseId.value = void 0
    selectValue.value = void 0
    mapGoodsList.value = {}
    const { type, record, isUpdate } = data
    isUpdates.value = isUpdate

    const mapHandler = {
      edit: handleEdit,
      detail: handleDetail
    }
    const { items } = await getPackageDetail({ ids: [record.id] })

    const details = items?.[0]
    if (!details) return
    projectNumber.value = details.project_number
    for (const goods of details.items) {
      goods.deal_type = 2
      goods.maxQuantity = add(goods.qty_pkg_left ?? 0, goods.quantity ?? 0)
      goods.itemKey = random(1, 10000000)
    }
    await setFieldsValue(details)
    await updateSchema([
      {
        field: 'inCharge',
        componentProps: { defaultOptions: [{ name: items?.[0].inCharge_name, id: items?.[0].inCharge }] }
      }
    ])
    // productList.value = items?.[0]?.items || []
    mapHandler[type](data)
    if (isUpdate == false) {
      await tableRef.value.hideColumn('action')
      await updateSchema(schemas.map((item) => ({ ...item, componentProps: { disabled: true } })))
    }
    changeLoading(false)
  } catch (err) {
    notification.error({
      message: JSON.stringify(err)
    })
  }
})

const [registerForm, { setFieldsValue, validate, updateSchema, getFieldsValue }] = useForm({
  baseColProps: { span: 8 },
  labelAlign: 'right',
  labelWidth: 100,
  // colon: true,
  showActionButtonGroup: false,
  schemas
})

function handleEdit({ type, record }) {
  console.log(type, record)
}

function handleDetail({ type, record }) {
  console.log(type, record)
}

async function handleOk() {
  try {
    changeOkLoading(true)
    changeLoading(true)
    const errMap = await tableRef.value.validate(true)
    if (errMap) return createMessage.error('产品存在未填写信息')
    const formData = await validate()
    const data = {
      ...formData,
      purchase_work_ids: [...new Set(formData.items.map((item) => item.purchase_work_id).filter((item) => item))],
      items: formData.items
        .map((item) => ({
          ...item,
          // ...pick(item, ['name', 'quantity', 'material', 'code', 'remark', 'size', 'imgs', 'id']),
          size: { width: item.width, height: item.height, length: item.length },
          imgs: item.imgs ?? [],
          type: item.type,
          deal_type: item.deal_type,
          id: item.deal_type === 1 ? void 0 : item.id
        }))
        .concat(removeItemsList.value)
    }
    const { msg } = await editPackage(data)
    if (msg === 'success') {
      emits('success')
      closeDrawer()
      createMessage.success('提交成功')
      setTimeout(() => {
        changeOkLoading(false)
        changeLoading(false)
      }, 3000)
      return
    }
    createMessage.error('提交失败')
    changeOkLoading(false)
    changeLoading(false)
  } catch (err) {
    console.log(err)
    createMessage.error('提交失败')
    changeOkLoading(false)
    changeLoading(false)
  }
}

function handleSyncGoodsList(options) {
  for (const goods of options) {
    mapGoodsList.value[goods.id] = goods
  }
}

function handleVisibleChange(visible) {
  // console.log(visible)
  if (!visible) {
    selectValue.value = []
    purchaseId.value = []
  }
}

function handleAddGoods() {
  const selectGoods: any[] = []
  for (const goods of selectValue.value) {
    const curGoods = mapGoodsList.value[goods]
    selectGoods.push({ ...curGoods, quantity: curGoods.qty_pkg_left ? curGoods.qty_pkg_left : 0 })
  }
  console.log(selectGoods, tableRef)
  // tableRef.value?.insert(selectGoods)
  const formData = getFieldsValue()
  setFieldsValue({ items: (formData.items ?? []).concat(selectGoods) })
  addVisible.value = false
  selectValue.value = []
}
</script>
