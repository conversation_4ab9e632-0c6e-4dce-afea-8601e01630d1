<template>
  <BasicModal v-bind="$attrs" @register="registerModal" showFooter title="导出预览" width="40%" destroyOnClose>
    <table id="print-table" v-if="compRowsData && compRowsData.length > 0" class="m-auto">
      <tbody id="table-body">
        <tr v-for="(row, idx) in compRowsData" ref="tableRowRef" :key="row.strid">
          <td>
            <ExportTagItem :ref="(el) => (TagItem[idx] = el)" :paper="paper" :wrap-rect="wrapRect" :row="row" />
          </td>
        </tr>
      </tbody>
    </table>
    <template #footer>
      <a-button :disabled="loading" type="primary" @click="handleExportPdf">导出PDF</a-button>
      <a-button :disabled="loading" type="primary" @click="handlePrint">打印</a-button>
      <a-button type="default" @click="closeModal">关闭</a-button>
    </template>
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { ref, computed } from 'vue'
// import { QrCode } from '/@/components/Qrcode'
import printJS from 'print-js'
import { isArray } from 'lodash-es'
import ExportTagItem from '/@/views/erpFlow/packages/components/ExportTagItem.vue'

// const purchaseWrap = ref<HTMLElement[]>([])
const loading = ref<boolean>(false)
const tableRowRef = ref(null)
const TagItem = ref<HTMLElement[]>([])
const propsData = ref({})
const paper = {
  width: 60,
  height: 40
}
const wrapRect = ref<{ width: number; height: number }>({
  width: mmToPx(paper.width),
  height: mmToPx(paper.height)
})

const [registerModal, { closeModal, changeLoading }] = useModalInner((data) => {
  TagItem.value = []
  propsData.value = data
})

const compRowsData = computed(() => {
  const rows = propsData.value.rows || []
  const rowsData = []
  for (const row of rows) {
    if (!row.purchase_strids || (isArray(row.purchase_strids) && row.purchase_strids?.length === 0)) {
      rowsData.push({ ...row })
      continue
    }
    const { purchase_strids: purchaseId } = row
    for (const id of purchaseId) {
      rowsData.push({ ...row, purchaseId: id.strid })
    }
  }
  return rowsData
})

function mmToPx(mm: number) {
  const div = document.createElement('div')
  div.style.cssText = 'height: 1in; left: -100%; position: absolute; top: -100%; width: 1in;'
  document.body.appendChild(div)
  const devicePixelRatio = window.devicePixelRatio || 1
  const dpi = div.offsetWidth * devicePixelRatio
  const pxPerIn = dpi / 25.4
  return parseInt(mm * pxPerIn)
}

async function handlePrint() {
  loading.value = true
  changeLoading(true)
  // const printArr = []
  // for (const tr of tableRowRef.value) {
  //   const img = await captureAndPushImageToArr(tr)
  //   printArr.push(img)
  // }
  const printArr = await Promise.all(TagItem.value.map((item) => item.genderImg()))
  console.log(printArr[0])
  printJS({
    printable: printArr,
    type: 'image',
    imageStyle: `width: 100%`
  })
  loading.value = false
  changeLoading(false)
}

async function handleExportPdf() {
  loading.value = true
  changeLoading(true)
  try {
    const printArr = await Promise.all(TagItem.value.map((item) => item.genderImg()))
    // for (const tr of tableRowRef.value) {
    //   const img = await captureAndPushImageToArr(tr)
    //   printArr.push(img)
    // }
    // 动态导入 jsPDF 包
    const { default: jsPDF } = await import('jspdf')
    // 将图片数组转换为 PDF
    const pdf = new jsPDF({
      unit: 'mm',
      orientation: '1',
      format: [paper.width, paper.height] // 设置纸张大小为60*60mm
    })
    for (let i = 0; i < printArr.length; i++) {
      const img = printArr[i]
      if (i > 0) {
        pdf.addPage()
      }
      pdf.addImage(img, 'PNG', 0, 0, paper.width, paper.height)
    }

    pdf.save(`包裹二维码-${+new Date()}.pdf`)
  } catch (err) {
    console.log(err)
  } finally {
    loading.value = false
    changeLoading(false)
  }
}

// async function captureAndPushImageToArr(imageToPrint) {
//   return new Promise(async (resolve) => {
//     // 动态导入 html2canvas 包
//     const html2canvas = await import('html2canvas')
//     const canvas = await html2canvas.default(imageToPrint, {
//       width: wrapRect.value.width,
//       height: wrapRect.value.height,
//       dpi: 160, //分辨率
//       scale: 1.8,
//       useCORS: true //允许canvas画布内 可以跨域请求外部链接图片, 允许跨域请求
//       // scrollY: imageToPrint.offsetHeight // 关键代码，截取长度
//     })
//     const imageData = canvas.toDataURL('image/png')
//     const img = new Image()
//     img.src = imageData
//     canvas.remove()
//     resolve(img.src)
//   })
// }
</script>

<style scoped lang="less">
.common-row {
  font-size: 12px;
  color: black;
  font-weight: 900;
  line-height: 22px;

  &.purchase {
    font-size: 18px;
    font-weight: 900;
  }
}
</style>
