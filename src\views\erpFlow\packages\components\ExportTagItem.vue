<template>
  <div ref="cellWrap" class="cell-wrap" :style="{ width: `${wrapRect.width}px`, height: `${wrapRect.height}px`, border: '1px solid #ccc' }">
    <div>
      <div ref="purchaseWrap" class="common-row purchase" :style="{ fontSize: purchaseSize + 'px' }">
        {{ row.purchaseId }}
      </div>
      <div class="common-row">NO：{{ row.strid }}</div>
      <div class="common-row">L*W*H（CM）：{{ row.length }}*{{ row.width }}*{{ row.height }}</div>
      <div class="inline-block w-[63%] align-top">
        <div class="common-row">QTY：{{ row.quantity }}</div>
        <div class="common-row">weight：{{ row.weight }}</div>
        <div class="common-row">{{ row.remark }}</div>
      </div>
      <div class="text-right inline-block">
        <QrCode :value="row.strid" :width="wrapRect.width * 0.35" :options="{ margin: 0 }" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { QrCode } from '/@/components/Qrcode'
import { onMounted, ref } from 'vue'
import { domToDataUrl } from 'modern-screenshot'

const props = defineProps({
  row: {
    type: Object,
    default: () => ({})
  },
  paper: {
    type: Object,
    default: () => ({
      width: 0,
      height: 0
    })
  },
  wrapRect: {
    type: Object,
    default: () => ({
      width: 0,
      height: 0
    })
  }
})

const cellWrap = ref<HTMLElement | null>(null)

const purchaseSize = ref(18)

const purchaseWrap = ref<HTMLElement | null>(null)

onMounted(async () => {
  adjustTextToFitDiv(purchaseWrap.value)
})

/**
 * 用于判断div中是否一行显示文字完整；
 * 生成一个div，并设置初始样式，appendChild到页面，如果这个append元素的长度比当前页面的元素长，则不断缩小字体大小适应div
 * 直到appendChild元素长度少于当前页面的元素
 * @param container 页面上的元素
 * @param idx 渲染的页面元素
 */
const adjustTextToFitDiv = (container) => {
  if (container) {
    const div = container
    const text = props.row.purchaseId
    const tempElement = document.createElement('div')
    tempElement.style.position = 'absolute'
    // tempElement.style.left = '50%';
    // tempElement.style.top = '50%';
    tempElement.style.fontWeight = '900'
    tempElement.style.visibility = 'hidden'
    tempElement.style.whiteSpace = 'nowrap'
    // tempElement.style.display = 'inline';
    tempElement.textContent = text
    document.body.appendChild(tempElement)

    let fontSize = parseInt(window.getComputedStyle(div).fontSize, 10)
    tempElement.style.fontSize = fontSize + 'px'

    const step = 0.5

    while (tempElement.offsetWidth > props.wrapRect.width - 3 && fontSize > 0) {
      fontSize -= step
      tempElement.style.fontSize = fontSize + 'px'
    }

    purchaseSize.value = fontSize
    document.body.removeChild(tempElement)
  }
}

async function genderImg() {
  return new Promise(async (resolve) => {
    const imageData = await domToDataUrl(cellWrap.value, {
      width: props.wrapRect.width,
      height: props.wrapRect.height,
      quality: 1,
      scale: 1.8,
      backgroundColor: '#ffffff'
    })
    resolve(imageData)
  })
}

defineExpose({
  genderImg
})
</script>

<style scoped lang="less">
.common-row {
  font-size: 11px;
  color: black;
  white-space: nowrap;
  font-weight: 900;
  line-height: 22px;

  &.purchase {
    font-size: 18px;
    font-weight: 900;
  }
}
</style>
