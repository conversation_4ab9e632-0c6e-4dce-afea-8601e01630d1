<template>
  <BasicModal v-bind="$attrs" @register="registerModal" defaultFullscreen showFooter title="导出预览" width="70%" destroyOnClose>
    <div id="print-table" class="printtable">
      <div id="table-body" v-if="compRowsData && compRowsData.length > 0">
        <div
          class="table-item"
          ref="tableRowRef"
          v-for="(row, idx) in !lightfixture ? compRowsData : isNewStyle ? compRowsNewItemData : compRowsItemData"
          :key="row.strid"
        >
          <!-- <div> -->
          <Exportcomplexitem
            :ref="(el) => (TagItem[idx] = el)"
            :paper="paper"
            :wrap-rect="wrapRect"
            :row="row"
            :logohiden="type"
            :lightfixture="lightfixture"
            :isNewStyle="isNewStyle"
          />
          <!-- </div> -->
        </div>
      </div>
    </div>
    <template #footer>
      <a-button :disabled="loading" type="primary" @click="handleExportPdf('pdf')">导出pdf</a-button>
      <a-button :disabled="loading" type="primary" @click="handleExportPdf('Img')">导出图片压缩包</a-button>
      <a-button :disabled="loading" type="primary" @click="handlePrint">打印</a-button>
      <a-button type="default" @click="closeModal">关闭</a-button>
    </template>
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { ref } from 'vue'
// import { QrCode } from '/@/components/Qrcode'
import printJS from 'print-js'
import Exportcomplexitem from './Exportcomplexitem.vue'
import { getPackageDetail } from '/@/api/erpFlow/packages'
import JSZip from 'jszip'
import { saveAs } from 'file-saver'

// const purchaseWrap = ref<HTMLElement[]>([])
const loading = ref<boolean>(false)
const tableRowRef = ref(null)
const TagItem = ref<any[]>([])
const compRowsData = ref<any>([])
const compRowsItemData = ref<any>([])
const compRowsNewItemData = ref<any>([])
const type = ref()
const lightfixture = ref()
const isNewStyle = ref()
const paper = {
  width: 224.5,
  height: 206.5
}
const sourceuniqId = ref()
const wrapRect = ref<{ width: number; height: number }>({
  width: mmToPx(paper.width),
  height: mmToPx(paper.height)
})

const [registerModal, { closeModal, changeLoading }] = useModalInner(async (data) => {
  TagItem.value = []
  type.value = data.type
  lightfixture.value = data.lightfixture
  isNewStyle.value = data?.isNewStyle ?? false
  console.log(lightfixture.value)

  compRowsData.value = []
  const { items } = await getPackageDetail({ ids: [...data.rowskeys] })
  compRowsData.value = items
  compRowsItemData.value = items[0].items.map((item) => {
    return {
      ...item,
      num: items[0].num,
      packstrid: items[0].strid
    }
  })
  compRowsNewItemData.value = []
  for (let i = 0; i < items[0].items.length; i += 4) {
    const chunk = items[0].items.slice(i, i + 4).map((item) => ({
      ...item,
      num: items[0].num,
      packstrid: items[0].strid
    }))
    compRowsNewItemData.value.push({
      items: chunk,
      num: items[0].num,
      packstrid: items[0].strid
    })
  }
  console.log(compRowsNewItemData.value)

  sourceuniqId.value = items[0].items[0].source_uniqid
})

function mmToPx(mm: number) {
  const div = document.createElement('div')
  div.style.cssText = 'height: 1in; left: -100%; position: absolute; top: -100%; width: 1in; '
  document.body.appendChild(div)
  const devicePixelRatio = window.devicePixelRatio || 1
  const dpi = div.offsetWidth * devicePixelRatio
  const pxPerIn = dpi / 25.4
  return parseInt(mm * pxPerIn)
}

async function handlePrint() {
  loading.value = true
  changeLoading(true)
  // const printArr = []
  // for (const tr of tableRowRef.value) {
  //   const img = await captureAndPushImageToArr(tr)
  //   printArr.push(img)
  // }
  const printArr = await Promise.all(TagItem.value.map((item) => item.genderImg()))
  console.log(printArr[0])
  printJS({
    printable: printArr,
    type: 'image',
    imageStyle: `width: 100%`
  })
  loading.value = false
  changeLoading(false)
}

interface ExportcomplexitemInstance {
  genderImg: () => Promise<string>
}

async function handleExportPdf(type: 'pdf' | 'Img') {
  if (!TagItem.value || TagItem.value.length === 0) {
    return
  }

  loading.value = true
  changeLoading(true)
  try {
    const zip = new JSZip()
    const outerFolderName = `单号:${sourceuniqId.value}-包裹-${TagItem.value.length}个`
    const chunkSize = 50
    const totalChunks = Math.ceil(TagItem.value.length / chunkSize)

    if (type === 'Img') {
      for (let i = 0; i < totalChunks; i++) {
        const start = i * chunkSize
        const end = Math.min(start + chunkSize, TagItem.value.length)
        const chunk = TagItem.value
          .slice(start, end)
          .filter((item): item is ExportcomplexitemInstance => item && typeof item.genderImg === 'function')

        if (chunk.length === 0) continue

        const printArr = await Promise.all(chunk.map((item) => item.genderImg()))

        for (let j = 0; j < printArr.length; j++) {
          const folderPath = `${outerFolderName}`
          if (printArr[j]) {
            zip.folder(folderPath)?.file(`包裹二维码-${j + 1}-${+new Date()}.jpg`, dataURLtoBlob(printArr[j]))
          }
        }
      }
      const content = await zip.generateAsync({ type: 'blob' })
      saveAs(content, `包裹二维码-${+new Date()}.zip`)
    } else {
      for (let i = 0; i < totalChunks; i++) {
        const start = i * chunkSize
        const end = Math.min(start + chunkSize, TagItem.value.length)
        const chunk = TagItem.value.slice(start, end).filter((item) => item && typeof item.genderImg === 'function')

        if (chunk.length === 0) continue

        const printArr = await Promise.all(chunk.map((item) => item.genderImg()))

        const { default: jsPDF } = await import('jspdf')
        const pdf = new jsPDF({
          unit: 'mm',
          orientation: '1',
          format: [paper.width, paper.height]
        })

        for (let j = 0; j < printArr.length; j++) {
          const img = printArr[j]
          if (img) {
            if (j > 0) {
              pdf.addPage()
            }
            pdf.addImage(img, 'JPEG', 0, 0, paper.width, paper.height)
          }
        }
        pdf.save(`包裹二维码-${+new Date()}.pdf`)
      }
    }
  } catch (error) {
    console.error('Export error:', error)
  } finally {
    loading.value = false
    changeLoading(false)
  }
}

function dataURLtoBlob(dataurl) {
  const arr = dataurl.split(',') //分割为数组，分割到第一个逗号
  const mime = arr[0].match(/:(.*?);/)[1] //获取分割后的base64前缀中的类型
  const bstr = window.atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new Blob([u8arr], { type: mime })
}

// async function captureAndPushImageToArr(imageToPrint) {
//   return new Promise(async (resolve) => {
//     // 动态导入 html2canvas 包
//     const html2canvas = await import('html2canvas')
//     const canvas = await html2canvas.default(imageToPrint, {
//       width: wrapRect.value.width,
//       height: wrapRect.value.height,
//       dpi: 160, //分辨率
//       scale: 1.8,
//       useCORS: true //允许canvas画布内 可以跨域请求外部链接图片, 允许跨域请求
//       // scrollY: imageToPrint.offsetHeight // 关键代码，截取长度
//     })
//     const imageData = canvas.toDataURL('image/png')
//     const img = new Image()
//     img.src = imageData
//     canvas.remove()
//     resolve(img.src)
//   })
// }
</script>

<style scoped lang="less">
.printtable {
  width: 100%;
  #table-body {
    display: flex;
    flex-wrap: wrap; /* 允许项目换行 */
    gap: 10px; /* 设置项目之间的间隔 */
    padding: 10px;
    // border: 1px solid #ccc;
    .table-item {
      width: 845px;
      // height: 745px;
      padding: 10px;
      flex: 0 0 calc(50% - 5px); /* 计算项目宽度，减去gap的一半 */
      min-width: calc(50% - 5px); /* 最小宽度 */
    }
  }
}
</style>
