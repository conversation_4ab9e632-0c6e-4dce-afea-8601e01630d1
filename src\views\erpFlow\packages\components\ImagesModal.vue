<template>
  <BasicModal @register="registerModal" title="图片预览" width="800px" :min-height="300">
    <div class="flex items-center flex-wrap">
      <div class="w-100px h-100px m-2" v-for="(item, index) in imagedata" :key="index">
        <Image :src="item" alt="" class="w-full h-full object-cover" width="100%" height="100%" />
      </div>
    </div>
  </BasicModal>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { Image } from 'ant-design-vue'

const imagedata = ref()

const [registerModal] = useModalInner((data) => {
  console.log(data)
  imagedata.value = data.data
})
</script>
