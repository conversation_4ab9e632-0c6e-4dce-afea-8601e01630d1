<template>
  <BasicModal @register="register" title="拼柜包裹导入" @ok="handleBeforeOk" width="80%" :height="700" destroyOnClose>
    <Upload :beforeUpload="handleImport" :showUploadList="false">
      <Button type="primary">选择上传文件</Button>
    </Upload>
    <Button type="primary" class="ml-2" @click="handleadd">新增手写商品</Button>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'file'">
          <TableImg :imgList="record.file" :simpleShow="true" />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicTable, useTable, TableImg, TableAction, ActionItem } from '/@/components/Table'
import { ref, watch } from 'vue'
import { Upload, UploadFile, message, Button } from 'ant-design-vue'
import ExcelJS from 'exceljs'
import JSZip from 'jszip'
import { cloneDeep, values } from 'lodash-es'
import { commonPackingInfokey, compareFilePaths, formatDate, columns, validKey } from '../datas/importCustom.data'
import { addPackage } from '/@/api/erpFlow/packages'
import { div, mul } from '/@/utils/math'
import { useMessage } from '/@/hooks/web/useMessage'
import type { EditRecordRow } from '/@/components/Table'
const { createMessage, createConfirm } = useMessage()
let worker
let drawings = ref({})
const submiting = ref(false)
//附件
const filesList = ref<UploadFile[]>([])

const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
  console.log(data)
  setTableData([])
})

const emit = defineEmits(['success', 'register'])

function handleBeforeOk() {
  const tableData = getDataSource()
  const valid = tableData.every((item) => {
    return validKey.every((key) => item[key])
  })
  if (!valid) return createMessage.error('导入的字段存在空值！')
  createConfirm({
    title: '确定导入吗？',
    content: '正在进行包裹导入，是否继续？',
    iconType: 'warning',
    onOk: handleOk
  })
}

// 提交
async function handleOk() {
  try {
    if (submiting.value) return
    changeOkLoading(true)
    submiting.value = true
    const tableData = getDataSource()
    console.log(tableData)

    if (tableData.length == 0) return message.error('请先导入文件')
    const tableRowNo = [...new Set(tableData.map((item) => item.strid))]
    const params: any = []
    for (const strid of tableRowNo) {
      const goodsInfo = tableData.filter((item) => strid === item.strid)
      const [common] = goodsInfo
      console.log(common)

      params.push({
        is_join: 1,
        project_number: common.project_number,
        length: common.length,
        width: common.width,
        height: common.height,
        weight: common.weight,
        quantity: common.packageQuantity ? common.packageQuantity : 1,
        method: `${common.method}` ?? '纸箱',
        volume: div(mul(mul(common.width, common.length), common.height), 1000000, 6),
        purchase_work_ids: [],
        join_source_uniqid: `${common.join_source_uniqid}`,
        remark: goodsInfo.map((good) => good.remark).join(';'),
        items: goodsInfo.map((goods) => {
          return {
            name: goods.name ? `${goods.name}` : '/',
            imgs: [],
            size: {
              width: 0,
              height: 0,
              length: 0
            },
            material: goods.material ? `${goods.material}` : '/',
            quantity: goods.quantity ? goods.quantity : 1,
            unit: goods.unit ? `${goods.unit}` : '/',
            code: goods.code ? `${goods.code}` : '/',
            remark: goods.remark ? `${goods.remark}` : '/',
            type: 1
          }
        })
      })
    }

    console.log(params)

    const { msg } = await addPackage({ packageList: params })
    if (msg === 'success') {
      emit('success')
      closeModal()
      setTimeout(() => {
        changeOkLoading(false)
        submiting.value = false
      }, 2000)
      createMessage.success('创建成功')
      return
    }
    submiting.value = false
    createMessage.success('创建失败')

    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    submiting.value = false
    createMessage.error('创建失败')
    throw new Error(`${e}`)
  }
}

async function handleImport(file) {
  let base64ImageList = [] // 用来存放excel导入的base64图片
  drawings.value = {}
  let fileKeyArr = ref<any>([])
  const headers = ref<any>([])
  try {
    const zip = new JSZip() // 创建jszip实例
    let zipLoadResult = await zip.loadAsync(file) // 将xlsx文件转zip文件
    // 解析图片，先拿到全部的图片
    for (let key in zipLoadResult['files']) {
      // 遍历结果中的files对象
      if (key.indexOf('media/image') != -1) {
        // 这里拿到的key顺序需要重新处理一下
        fileKeyArr.value.push(key)
      }
    }
    fileKeyArr.value.sort(compareFilePaths)
    fileKeyArr.value.forEach((key) => {
      let res = zip.file(zipLoadResult['files'][key].name).async('base64')
      base64ImageList.push(res)
    })
    base64ImageList = await Promise.all(base64ImageList)
    // 解析位置，图片与位置映射起来
    for (let key in zipLoadResult['files']) {
      if (key.indexOf('xl/drawings') != -1) {
        const xmlStr = await zip.file(zipLoadResult['files'][key].name)?.async('string')
        const parser = new DOMParser()
        const xmlDoc = parser.parseFromString(xmlStr, 'text/xml')
        let cellAnchors: any = []
        cellAnchors.push(xmlDoc.getElementsByTagName('xdr:oneCellAnchor'))
        cellAnchors.push(xmlDoc.getElementsByTagName('xdr:twoCellAnchor'))
        cellAnchors.forEach((item) => {
          item?.forEach((anchorsItem) => {
            const fromCol = parseInt(anchorsItem.getElementsByTagName('xdr:from')[0].getElementsByTagName('xdr:col')[0].textContent) + 1
            const fromRow = parseInt(anchorsItem.getElementsByTagName('xdr:from')[0].getElementsByTagName('xdr:row')[0].textContent) + 1
            if (!drawings.value[fromRow]) {
              drawings.value[fromRow] = {}
            }
            if (!drawings.value[fromRow][fromCol]) {
              drawings.value[fromRow][fromCol] = []
            }
            let thisRid
            values(anchorsItem.getElementsByTagName('xdr:pic')[0].children[1].getElementsByTagName('a:blip')[0].attributes).forEach(
              (item) => {
                if (item.value.indexOf('rId') > -1) {
                  // 这里的图片id和excel当前操作的行顺序是一样的道理的
                  thisRid = item.value
                }
              }
            )
            drawings.value[fromRow][fromCol].push(`data:image/png;base64,${base64ImageList[thisRid.substring(3) - 1]}`)
          })
        })
      }
    }
    // 解析数据
    let fileReader = new FileReader() // 构建fileReader对象
    fileReader.readAsArrayBuffer(file) // 读取指定文件内容
    const tableDataobj = ref<any>([])
    // 读取操作完成时
    fileReader.onload = async function (e: any) {
      let buffer = e.target.result // 取得buffer数据
      const workbook = new ExcelJS.Workbook()
      await workbook.xlsx.load(buffer)
      const worksheet: any = workbook.worksheets[0]
      worksheet.eachRow({ includeEmpty: false }, (row, rowIndex) => {
        if (String(row._cells[0]?.value).toLowerCase() == '包裹号' && rowIndex == 1) {
          // headers.value = row.values.slice(1).map((key) => {
          //   let newKey
          //   // 在读取需要拿数据的行的时候，有些列可能不是字符串，是一个对象，需要特殊处理一下
          //   if (typeof key === 'string') {
          //     newKey = formartKey(key).toLowerCase()
          //   } else {
          //     let newCell = ''
          //     key?.richText?.forEach((richTextItem) => {
          //       newCell = `${newCell}${richTextItem.text}`
          //     })
          //     newKey = newCell.toLowerCase()
          //   }
          //   return newKey
          // })
          headers.value = Array.from({ length: row.values.length - 1 }, (_, i) => i + 1)
          console.log(row.values)
        }
        if (rowIndex > 1) {
          let rowValues = row.values
          rowValues.splice(2, 0)
          let tableData = formatDate(headers, rowValues)
          tableDataobj.value.push(tableData)
        }
      })
      // tableDataobj.value.forEach((item, index) => {
      //   Object.keys(drawings.value).forEach((val) => {
      //     if (Number(val) - 3 == index) {
      //       item['*产品图片productpicture'] = drawings.value[val][3]
      //     }
      //   })
      // })
      let tables = tableDataobj.value.map((item) => {
        const renamedobj = {}
        for (const oldkey in item) {
          if (item.hasOwnProperty(oldkey) && commonPackingInfokey.hasOwnProperty(oldkey)) {
            const newkey = commonPackingInfokey[oldkey]
            renamedobj[newkey] = item[oldkey]
          }
        }
        return renamedobj
      })
      const dataSource = [...tables].concat(cloneDeep(getDataSource()))
      console.log(dataSource)

      setTableData(dataSource)
      tables = []
      tableDataobj.value = []
      drawings.value = null
    }
  } catch (error) {
    console.log(error)
    drawings.value = null
    worker.terminate()
  }
}
// function handleChange({ file }) {
//   return (file.status = 'success')
// }
watch(
  () => filesList.value,
  (val) => {
    if (Object.keys(val).length < 1) {
      setTableData([])
    }
  }
)

//tab填充
const [registerTable, { setTableData, getDataSource, deleteTableDataRecord, getColumns, updateTableDataRecord }] = useTable({
  showTableSetting: false,
  showIndexColumn: false,
  columns,
  useSearchForm: false,
  isTreeTable: false,
  bordered: true,
  canResize: false,
  pagination: {
    pageSize: 10,
    pageSizeOptions: ['10', '20', '100']
  },
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action'
    // slots: { customRender: 'action' },
  }
})

//保存点击,其他禁用
const currentEditKeyRef = ref('')
function createActions(record: EditRecordRow): ActionItem[] {
  if (!record.editable) {
    return [
      {
        label: '编辑',
        disabled: record.is_active !== 1 || currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        ifShow: record.is_check2 !== 2 && !record.is_cancel,
        onClick: handleEdit.bind(null, record)
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          placement: 'right',
          confirm: handleDelete.bind(null, record)
        },
        disabled: record.is_active !== 1 || currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        // ifShow: record.is_check2 !== 2 && !record.is_cancel
        ifShow: record.is_check2 !== 2 && !record.is_cancel
      },
      {
        label: '复制',
        disabled: record.is_active !== 1 || currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        ifShow: record.is_check2 !== 2 && !record.is_cancel && record.is_check !== 2,
        // ifShow: !(record.is_check == 2) && record.is_check2 !== 2,
        onClick: handlecopylink.bind(null, record)
      }
    ]
  }
  return [
    {
      label: '保存',
      onClick: handleSave.bind(null, record)
    },
    {
      label: '取消',
      popConfirm: {
        title: '是否取消编辑',
        placement: 'right',
        confirm: handleCancel.bind(null, record)
      }
    }
  ]
}

//格式化某一行的数据
function formatObject(record) {
  // getDataSource()有我们不需要的属性,所以需要清除
  let temporary = {}
  for (let colName of getColumns()) {
    if (colName.key !== 'action') {
      temporary[`${colName.dataIndex}`] = record[`${colName.dataIndex}`]
    }
  }
  return temporary
}

//单独部分时
function handleadd() {
  console.log(getDataSource())
  const newRowDataItem = {
    is_active: 1
  }
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
}

// 存储编辑前的record
const beforeRecord = ref()
function handleEdit(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  beforeRecord.value = cloneDeep(record)
  record.onEdit?.(true, false)
}

//删除tabel
function handleDelete(record) {
  deleteTableDataRecord(record.key)
}

//复制明细
function handlecopylink(record) {
  const newrecord = formatObject(record)
  const newRowDataItem = {
    ...newrecord
  }
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
}
//保存
async function handleSave(record: EditRecordRow) {
  // 校验
  const valid = await record.onValid?.()
  if (valid) {
    try {
      if (!record.project_number) {
        message.error({ content: '包裹项目ID为必填项,请完成填写' })

        return
      }
      if (!record.method) {
        message.error({ content: '包裹打包方式为必填,请完成填写' })

        return
      }
      if (!record.length || !record.width || !record.height || !record.weight) {
        message.error({ content: '包裹长,宽,高,重量为必填项,请完成填写' })

        return
      }

      //TODO 此处将数据提交给服务器保存
      // ...
      // 保存之后提交编辑状态

      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
      }
      message.success({ content: '数据已保存', key: 'saving' })
      // meesg.value = false
    } catch (error) {
      message.error({ content: '保存失败', key: 'saving' })
      throw new Error(`${error}`)
    }
  } else {
    message.error({ content: '请填写正确的数据', key: 'saving' })
  }
}
//取消
function handleCancel(record: EditRecordRow) {
  currentEditKeyRef.value = ''
  updateTableDataRecord(record.key, { ...beforeRecord.value })
  record.onEdit?.(false, false)
}
</script>
