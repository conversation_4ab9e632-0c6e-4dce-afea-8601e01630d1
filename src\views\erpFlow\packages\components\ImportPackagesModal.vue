<template>
  <BasicModal @register="register" title="包裹导入" @ok="handleBeforeOk" width="80%" :height="700" destroyOnClose>
    <!--    <template #footer>-->
    <!--      <Button @click="closeModal">取消</Button>-->
    <!--      <Popconfirm title="导入会覆盖包裹之前的描述产品，确定导入吗！" placement="topRight" @confirm="handleOk">-->
    <!--        <Button type="primary">确定</Button>-->
    <!--      </Popconfirm>-->
    <!--    </template>-->
    <Upload :beforeUpload="handleImport" :showUploadList="false">
      <a-button type="primary">选择上传文件</a-button>
    </Upload>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'file'">
          <TableImg :imgList="record.file" :simpleShow="true" />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicTable, useTable, TableImg } from '/@/components/Table'
import { ref, watch } from 'vue'
import { Upload, UploadFile, message } from 'ant-design-vue'
import ExcelJS from 'exceljs'
import JSZip from 'jszip'
import { isNumber, values } from 'lodash-es'
import {
  commonPackingInfokey,
  compareFilePaths,
  formartKey,
  formatDate,
  columns,
  validKey,
  validNumberKey
} from '../datas/importPackages.data'
import { addPackage } from '/@/api/erpFlow/packages'
import { div, mul } from '/@/utils/math'
import { useMessage } from '/@/hooks/web/useMessage'

const { createMessage, createConfirm } = useMessage()
let worker
let drawings = ref({})
//附件
const filesList = ref<UploadFile[]>([])
const submiting = ref(false)

const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
  console.log(data)
  drawings.value = {}
  setTableData([])
})

const emit = defineEmits(['success', 'register'])

function handleBeforeOk() {
  const tableData = getDataSource()
  const valid = tableData.every((item) => {
    return validKey.every((key) => item[key]) && validNumberKey.every((key) => validNumber(item[key]))
  })
  if (!valid) return createMessage.error('导入的字段存在空值或不合法！请检查数值字段（如长宽高）')
  createConfirm({
    title: '确定导入吗？',
    content: '正在进行包裹导入，是否继续？',
    onOk: handleOk,
    iconType: 'warning'
  })
}

function validNumber(value) {
  const num = Number(value)
  return isNumber(num) && !isNaN(num)
}

// 提交
async function handleOk() {
  try {
    if (submiting.value) return
    changeOkLoading(true)
    submiting.value = true
    const tableData = getDataSource()
    console.log(tableData)
    if (tableData.length == 0) return message.error('请先导入文件')
    const tableRowNo = [...new Set(tableData.map((item) => item.no))]
    const params: any = []
    for (const no of tableRowNo) {
      const goodsInfo = tableData.filter((item) => no === item.no)
      const [common] = goodsInfo
      params.push({
        quantity: common.packingQuantity,
        method: common.method ? `${common.method}` : '',
        length: common.packageLength,
        width: common.packageWidth,
        height: common.packageHeight,
        weight: common.weight,
        volume: div(mul(mul(common.packageWidth, common.packageLength), common.packageHeight), 1000000, 6),
        supplier_strid: common.supplier_strid ? `${common.supplier_strid}` : '',
        purchase_work_ids: goodsInfo.map((item) => item.purchase_work_ids),
        items: goodsInfo.map((goods) => {
          return {
            name: goods.name ? `${goods.name}` : '',
            imgs: [],
            material: goods.material ? `${goods.material}` : '',
            quantity: goods.quantity,
            unit: goods.unit ? `${goods.unit}` : '',
            size: {
              width: goods.goodsWidth ?? 0,
              length: goods.goodsLength ?? 0,
              height: goods.goodsHeight ?? 0
            },
            code: goods.code ? `${goods.code}` : '',
            remark: goods.remark ? `${goods.remark}` : '',
            request_id: goods.request_id,
            request_sub_id: goods.request_sub_id,
            item_purchase_id: goods.item_purchase_id,
            item_purchase_sub_id: goods.item_purchase_sub_id,
            puid: goods.puid,
            type: goods.type
          }
        })
      })
    }

    console.log(params)

    const { msg } = await addPackage({ packageList: params })
    if (msg === 'success') {
      emit('success')
      closeModal()
      setTimeout(() => {
        submiting.value = false
        changeOkLoading(false)
      }, 2000)
      createMessage.success('创建成功')
      return
    }
    createMessage.success('创建失败')
    submiting.value = false
    //
    // await updateDesc({ desc_items: params })
    // closeModal()
    // emit('success')

    changeOkLoading(false)
  } catch (e) {
    submiting.value = false
    changeOkLoading(false)
    createMessage.error('创建失败')
    throw new Error(`${e}`)
  }
}

async function handleImport(file) {
  let base64ImageList = [] // 用来存放excel导入的base64图片
  drawings.value = {}
  let fileKeyArr = ref<any>([])
  const headers = ref([])
  try {
    const zip = new JSZip() // 创建jszip实例
    let zipLoadResult = await zip.loadAsync(file) // 将xlsx文件转zip文件
    // 解析图片，先拿到全部的图片
    for (let key in zipLoadResult['files']) {
      // 遍历结果中的files对象
      if (key.indexOf('media/image') != -1) {
        // 这里拿到的key顺序需要重新处理一下
        fileKeyArr.value.push(key)
      }
    }
    fileKeyArr.value.sort(compareFilePaths)
    fileKeyArr.value.forEach((key) => {
      let res = zip.file(zipLoadResult['files'][key].name).async('base64')
      base64ImageList.push(res)
    })
    base64ImageList = await Promise.all(base64ImageList)
    // 解析位置，图片与位置映射起来
    for (let key in zipLoadResult['files']) {
      if (key.indexOf('xl/drawings') != -1) {
        const xmlStr = await zip.file(zipLoadResult['files'][key].name)?.async('string')
        const parser = new DOMParser()
        const xmlDoc = parser.parseFromString(xmlStr, 'text/xml')
        let cellAnchors: any = []
        cellAnchors.push(xmlDoc.getElementsByTagName('xdr:oneCellAnchor'))
        cellAnchors.push(xmlDoc.getElementsByTagName('xdr:twoCellAnchor'))
        cellAnchors.forEach((item) => {
          item?.forEach((anchorsItem) => {
            const fromCol = parseInt(anchorsItem.getElementsByTagName('xdr:from')[0].getElementsByTagName('xdr:col')[0].textContent) + 1
            const fromRow = parseInt(anchorsItem.getElementsByTagName('xdr:from')[0].getElementsByTagName('xdr:row')[0].textContent) + 1
            if (!drawings.value[fromRow]) {
              drawings.value[fromRow] = {}
            }
            if (!drawings.value[fromRow][fromCol]) {
              drawings.value[fromRow][fromCol] = []
            }
            let thisRid
            values(anchorsItem.getElementsByTagName('xdr:pic')[0].children[1].getElementsByTagName('a:blip')[0].attributes).forEach(
              (item) => {
                if (item.value.indexOf('rId') > -1) {
                  // 这里的图片id和excel当前操作的行顺序是一样的道理的
                  thisRid = item.value
                }
              }
            )
            drawings.value[fromRow][fromCol].push(`data:image/png;base64,${base64ImageList[thisRid.substring(3) - 1]}`)
          })
        })
      }
    }
    // 解析数据
    let fileReader = new FileReader() // 构建fileReader对象
    fileReader.readAsArrayBuffer(file) // 读取指定文件内容
    const tableDataobj = ref<any>([])
    // 读取操作完成时
    fileReader.onload = async function (e: any) {
      let buffer = e.target.result // 取得buffer数据
      const workbook = new ExcelJS.Workbook()
      await workbook.xlsx.load(buffer)
      const worksheet: any = workbook.worksheets[0]
      worksheet.eachRow({ includeEmpty: false }, (row, rowIndex) => {
        if (String(row._cells[0]?.value).toLowerCase() == '*no.' && rowIndex == 1) {
          headers.value = row.values.slice(1).map((key) => {
            let newKey
            // 在读取需要拿数据的行的时候，有些列可能不是字符串，是一个对象，需要特殊处理一下
            if (typeof key === 'string') {
              newKey = formartKey(key).toLowerCase()
            } else {
              let newCell = ''
              key?.richText?.forEach((richTextItem) => {
                newCell = `${newCell}${richTextItem.text}`
              })
              newKey = newCell.toLowerCase()
            }
            return newKey
          })
        }
        if (rowIndex > 1) {
          let rowValues = row.values
          rowValues.splice(2, 0)
          let tableData = formatDate(headers, rowValues)
          tableDataobj.value.push(tableData)
        }
      })
      // tableDataobj.value.forEach((item, index) => {
      //   Object.keys(drawings.value).forEach((val) => {
      //     if (Number(val) - 3 == index) {
      //       item['*产品图片productpicture'] = drawings.value[val][3]
      //     }
      //   })
      // })
      let tables = tableDataobj.value.map((item) => {
        const renamedobj = {}
        for (const oldkey in item) {
          if (item.hasOwnProperty(oldkey) && commonPackingInfokey.hasOwnProperty(oldkey)) {
            const newkey = commonPackingInfokey[oldkey]
            renamedobj[newkey] = item[oldkey]
          }
        }
        return renamedobj
      })
      setTableData(tables)
      tables = []
      tableDataobj.value = []
      drawings.value = null
    }
  } catch (error) {
    console.log(error)
    drawings.value = null
    worker.terminate()
  }
}
// function handleChange({ file }) {
//   return (file.status = 'success')
// }
watch(
  () => filesList.value,
  (val) => {
    if (Object.keys(val).length < 1) {
      setTableData([])
    }
  }
)

//tab填充
const [registerTable, { setTableData, getDataSource }] = useTable({
  showTableSetting: false,
  showIndexColumn: false,
  columns,
  useSearchForm: false,
  isTreeTable: false,
  bordered: true,
  canResize: false,
  pagination: {
    pageSize: 10,
    pageSizeOptions: ['10', '20', '100']
  }
})
</script>
