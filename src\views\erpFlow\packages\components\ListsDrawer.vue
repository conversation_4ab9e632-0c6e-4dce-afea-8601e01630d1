<template>
  <BasicDrawer @register="registerDrawer" v-bind="$attrs" width="90%" title="调拨/转换" falsedestroy-on-close>
    <BasicTable @register="registerallotTable" />
    <BasicTable @register="registerconvertTable" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable } from '/@/components/Table'
import { getallotList, getconvertList } from '/@/api/erpFlow/packages'
import { allot, convert } from '../datas/ListstDrawer.data'

const allotdata = ref([])
const convertdata = ref([])
const [registerDrawer] = useDrawerInner(async (data) => {
  //   id.value = data.id
  const allot = await getallotList({
    packing_package_id: data.id
  })
  const convert = await getconvertList({
    packing_package_id: data.id
  })
  allotdata.value = allot.items
  convertdata.value = convert.items
})

const [registerallotTable] = useTable({
  title: '包裹调拨列表',
  useSearchForm: false,
  showTableSetting: false,
  showIndexColumn: false,
  dataSource: allotdata,
  //   api: getallotList,
  columns: allot,
  //   beforeFetch: (params) => {
  //     return {
  //       ...params,
  //       packing_package_id: id.value
  //     }
  //   },
  maxHeight: 400
})
const [registerconvertTable] = useTable({
  title: '包裹转换列表',
  useSearchForm: false,
  showTableSetting: false,
  showIndexColumn: false,
  columns: convert,
  dataSource: convertdata,
  //   api: getconvertList,
  //   beforeFetch: (params) => {
  //     return {
  //       ...params,
  //       packing_package_id: id.value
  //     }
  //   },
  maxHeight: 400
})
</script>
