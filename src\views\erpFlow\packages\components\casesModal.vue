<template>
  <BasicModal @register="registerModal" @ok="handleOk">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { ref } from 'vue'
import { setPkgQuantit } from '/@/api/erpFlow/packages'

const propsData = ref({})
const [registerModal, { changeOkLoading, closeModal }] = useModalInner((data) => {
  console.log(data)
  resetFields()
  propsData.value = data
  setFieldsValue(data)
})
const [registerForm, { validate, resetFields, setFieldsValue }] = useForm({
  baseColProps: { span: 21 },
  labelAlign: 'right',
  labelWidth: 100,
  // colon: true,
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'pkg_quantity',
      label: '打包件数',
      component: 'InputNumber',
      required: true,
      componentProps: {
        min: 0
      }
    },
    {
      field: 'length',
      label: '长',
      component: 'InputNumber',
      required: true,
      componentProps: {
        min: 0
      }
    },
    {
      field: 'width',
      label: '宽',
      component: 'InputNumber',
      required: true,
      componentProps: {
        min: 0
      }
    },
    {
      field: 'height',
      label: '高',
      component: 'InputNumber',
      required: true,
      componentProps: {
        min: 0
      }
    },
    {
      field: 'weight',
      label: '重量',
      component: 'InputNumber',
      required: true,
      componentProps: {
        min: 0
      }
    },
    {
      field: 'volume',
      label: '体积',
      component: 'InputNumber',
      required: true,
      componentProps: {
        min: 0
      }
    }
  ]
})
const emit = defineEmits(['success'])
async function handleOk() {
  changeOkLoading(true)
  const fromdata = await validate()
  console.log(fromdata)

  setPkgQuantit({ ...fromdata, id: propsData.value.id })
  setTimeout(() => {
    changeOkLoading(false)
    emit('success')
    closeModal()
  }, 1000)
}
</script>
