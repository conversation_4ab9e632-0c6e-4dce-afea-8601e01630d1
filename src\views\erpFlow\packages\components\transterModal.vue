<template>
  <BasicModal @register="register" @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #PackageList>
        <div>
          <Tag v-for="(item, index) in packageListstrid" :key="index" color="blue">{{ item }}</Tag>
        </div>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { transterchemas } from '../datas/transter.data'
import { Tag } from 'ant-design-vue'
import { ref } from 'vue'
import { packagefastConversion } from '/@/api/erpFlow/packages'

const packageListstrid = ref([])
const packageListid = ref([])
const emit = defineEmits(['success'])

const [register, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  resetFields()
  packageListstrid.value = data.recoed.map((item) => item.strid)
  packageListid.value = data.recoed.map((item) => item.id)
  await setFieldsValue({
    packageList: packageListid.value,
    source_uniqid: data.recoed[0].source_uniqids[0].source_uniqid
  })
})
const [registerForm, { setFieldsValue, validate, resetFields }] = useForm({
  schemas: transterchemas,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelCol: { span: 5 }
})

async function handleSubmit() {
  try {
    changeOkLoading(true)
    const formData = await validate()
    await packagefastConversion({ ...formData })
    setTimeout(() => {
      closeModal()
      emit('success')
      changeOkLoading(false)
    }, 1000)
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    console.log(e)
  }
}
</script>
