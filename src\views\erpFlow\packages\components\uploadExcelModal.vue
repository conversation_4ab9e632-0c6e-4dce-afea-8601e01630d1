<template>
  <BasicModal @register="register" title="包裹导入" @ok="handleOk" width="80%" :height="700">
    <template #footer>
      <Button @click="closeModal">取消</Button>
      <Popconfirm title="导入会覆盖包裹之前的描述产品，确定导入吗！" placement="topRight" @confirm="handleOk">
        <Button type="primary">确定</Button>
      </Popconfirm>
    </template>
    <BasicForm @register="registerform">
      <template #Uploads>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          :before-upload="handleImport"
          @change="handleChange"
          :multiple="true"
        >
          <Button type="primary"> 选择文件 </Button>
        </Upload>
      </template>
    </BasicForm>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'file'">
          <TableImg :imgList="record.file" :simpleShow="true" />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicTable, useTable, TableImg } from '/@/components/Table'
import { BasicForm, useForm } from '/@/components/Form'
import { UploadSchemas } from '../datas/modal'
import { ref, watch } from 'vue'
import { Upload, Button, UploadFile, Popconfirm, message } from 'ant-design-vue'
import ExcelJS from 'exceljs'
import JSZip from 'jszip'
import { values } from 'lodash-es'
import { commonPackingInfokey, compareFilePaths, formartKey, formatDate, columns } from '../datas/upload.data'
import { updateDesc } from '/@/api/erpFlow/packages'

let worker
let drawings = ref({})

//附件
const filesList = ref<UploadFile[]>([])

const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
  console.log(data)
})
const [registerform] = useForm({
  schemas: UploadSchemas,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelCol: { span: 5 }
})

const emit = defineEmits(['success', 'register'])

// 提交
async function handleOk() {
  try {
    await changeOkLoading(true)
    const tableData = await getDataSource()
    console.log(tableData)
    if (tableData.length == 0) return message.error('请先导入文件')
    const params = tableData.map((item) => {
      return {
        packing_package_string: item.strid.toString(),
        imgs: item.file,
        name: item.name,
        material: item.material.toString(),
        unit: item.unit,
        quantity: item.quantity,
        code: item.code.toString(),
        remark: item.remark,
        size: {
          width: item.width,
          height: item.height,
          length: item.length
        }
      }
    })

    await updateDesc({ desc_items: params })
    closeModal()
    emit('success')

    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  } finally {
    changeOkLoading(false)
  }
}

async function handleImport(file) {
  let base64ImageList = [] // 用来存放excel导入的base64图片
  drawings.value = {}
  let fileKeyArr = ref<any>([])
  const headers = ref([])
  try {
    const zip = new JSZip() // 创建jszip实例
    let zipLoadResult = await zip.loadAsync(file) // 将xlsx文件转zip文件
    // 解析图片，先拿到全部的图片
    for (let key in zipLoadResult['files']) {
      // 遍历结果中的files对象
      if (key.indexOf('media/image') != -1) {
        // 这里拿到的key顺序需要重新处理一下
        fileKeyArr.value.push(key)
      }
    }
    fileKeyArr.value.sort(compareFilePaths)
    fileKeyArr.value.forEach((key) => {
      let res = zip.file(zipLoadResult['files'][key].name).async('base64')
      base64ImageList.push(res)
    })
    base64ImageList = await Promise.all(base64ImageList)
    // 解析位置，图片与位置映射起来
    for (let key in zipLoadResult['files']) {
      if (key.indexOf('xl/drawings') != -1) {
        const xmlStr = await zip.file(zipLoadResult['files'][key].name)?.async('string')
        const parser = new DOMParser()
        const xmlDoc = parser.parseFromString(xmlStr, 'text/xml')
        let cellAnchors: any = []
        cellAnchors.push(xmlDoc.getElementsByTagName('xdr:oneCellAnchor'))
        cellAnchors.push(xmlDoc.getElementsByTagName('xdr:twoCellAnchor'))
        cellAnchors.forEach((item) => {
          item?.forEach((anchorsItem) => {
            const fromCol = parseInt(anchorsItem.getElementsByTagName('xdr:from')[0].getElementsByTagName('xdr:col')[0].textContent) + 1
            const fromRow = parseInt(anchorsItem.getElementsByTagName('xdr:from')[0].getElementsByTagName('xdr:row')[0].textContent) + 1
            if (!drawings.value[fromRow]) {
              drawings.value[fromRow] = {}
            }
            if (!drawings.value[fromRow][fromCol]) {
              drawings.value[fromRow][fromCol] = []
            }
            let thisRid
            values(anchorsItem.getElementsByTagName('xdr:pic')[0].children[1].getElementsByTagName('a:blip')[0].attributes).forEach(
              (item) => {
                if (item.value.indexOf('rId') > -1) {
                  // 这里的图片id和excel当前操作的行顺序是一样的道理的
                  thisRid = item.value
                }
              }
            )
            drawings.value[fromRow][fromCol].push(`data:image/png;base64,${base64ImageList[thisRid.substring(3) - 1]}`)
          })
        })
      }
    }
    // 解析数据
    let fileReader = new FileReader() // 构建fileReader对象
    fileReader.readAsArrayBuffer(file) // 读取指定文件内容
    const tableDataobj = ref<any>([])
    // 读取操作完成时
    fileReader.onload = async function (e: any) {
      let buffer = e.target.result // 取得buffer数据
      const workbook = new ExcelJS.Workbook()
      await workbook.xlsx.load(buffer)
      const worksheet: any = workbook.worksheets[0]
      worksheet.eachRow({ includeEmpty: false }, (row, rowIndex) => {
        if (String(row._cells[0]?.value).toLowerCase() == '*no.' && rowIndex == 2) {
          headers.value = row.values.slice(1).map((key) => {
            let newKey
            // 在读取需要拿数据的行的时候，有些列可能不是字符串，是一个对象，需要特殊处理一下
            if (typeof key === 'string') {
              newKey = formartKey(key).toLowerCase()
            } else {
              let newCell = ''
              key?.richText?.forEach((richTextItem) => {
                newCell = `${newCell}${richTextItem.text}`
              })
              newKey = newCell.toLowerCase()
            }
            return newKey
          })
        }
        if (rowIndex > 2) {
          let rowValues = row.values
          rowValues.splice(2, 0)
          let tableData = formatDate(headers, rowValues)
          tableDataobj.value.push(tableData)
        }
      })
      tableDataobj.value.forEach((item, index) => {
        Object.keys(drawings.value).forEach((val) => {
          if (Number(val) - 3 == index) {
            item['*产品图片productpicture'] = drawings.value[val][3]
          }
        })
      })
      let tables = tableDataobj.value.map((item) => {
        const renamedobj = {}
        for (const oldkey in item) {
          if (item.hasOwnProperty(oldkey) && commonPackingInfokey.hasOwnProperty(oldkey)) {
            const newkey = commonPackingInfokey[oldkey]
            renamedobj[newkey] = item[oldkey]
          }
        }
        return renamedobj
      })
      setTableData(tables)
      tables = []
      tableDataobj.value = []
      drawings.value = null
    }
  } catch (error) {
    console.log(error)
    drawings.value = null
    worker.terminate()
  }
}
function handleChange({ file }) {
  return (file.status = 'success')
}
watch(
  () => filesList.value,
  (val) => {
    if (Object.keys(val).length < 1) {
      setTableData([])
    }
  }
)

//tab填充
const [registerTable, { setTableData, getDataSource }] = useTable({
  showTableSetting: false,
  showIndexColumn: false,
  columns,
  useSearchForm: false,
  isTreeTable: false,
  canResize: false,
  pagination: {
    pageSize: 10,
    pageSizeOptions: ['10', '20', '100']
  }
})
</script>
