<template>
  <BasicModal @register="register" width="800px" @ok="handleok">
    <Alert v-if="types == 'packing'" message="当前项目收款金额不足出库所需金额，请及时回款和做收款单，否则将影响生成出库单" type="error" />
    <BasicTable @register="registerTable" />
    <BasicTable @register="registerTablepack" v-if="types !== 'packages'" />
  </BasicModal>
</template>
<script setup lang="tsx">
import { ref } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicTable, useTable } from '/@/components/Table'
import { Alert } from 'ant-design-vue'

const emit = defineEmits(['skip', 'packingedit', 'packingopen'])
const types = ref()
const records = ref()
const [register, { closeModal }] = useModalInner((data) => {
  console.log(data)
  types.value = data.type
  records.value = data.table
  if (data.type == 'packages') {
    setTableData(data.record)
  } else {
    setTableData(data.record.saleList)
    setTableDatapack(data.record.noPackingCount)
  }
})
const [registerTable, { setTableData }] = useTable({
  showTableSetting: false,
  maxHeight: types.value === 'packages' ? 500 : 180,
  title: '欠款销售单',
  columns: [
    {
      title: '销售单号',
      dataIndex: 'source_uniqid',
      width: 150
    },
    {
      title: '出库前欠款金额',
      dataIndex: 'arrears',
      width: 150,
      customRender({ value }) {
        return <sapn style={'color: red'}>{value}</sapn>
      }
    },
    {
      title: '总金额(已装箱未出库加已出库)',
      dataIndex: 'total',
      width: 200
    },
    {
      title: '实收金额',
      dataIndex: 'received_actual',
      width: 150
    }
  ]
})
const [registerTablepack, { setTableData: setTableDatapack }] = useTable({
  showTableSetting: false,
  maxHeight: 180,
  title: '销售单未装箱包裹统计',
  columns: [
    {
      title: '销售单号',
      dataIndex: 'source_uniqid',
      width: 150
    },
    {
      title: '包裹数量',
      dataIndex: 'no_packing_count',
      width: 150
    }
  ]
})

function handleok() {
  switch (types.value) {
    case 'packages':
      emit('skip')
      break
    case 'packing':
      ''
      break
    case 'packingedit':
      emit('packingedit', 'edit')
      break
    case 'packingdetail':
      emit('packingedit', 'detail')
      break
    case 'noPackingCount':
      emit('packingopen', records.value)
      break
  }
  closeModal()
}
</script>
<style lang="less">
.tooltp {
  width: 100%;
  height: 30px;
  background: #f5f5f5;
  line-height: 30px;
  text-align: center;
}
</style>
