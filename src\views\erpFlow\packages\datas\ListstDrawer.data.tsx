import { BasicColumn } from '/@/components/Table'

export const allot: BasicColumn[] = [
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 150,
    resizable: true
  },
  {
    title: '原仓库',
    dataIndex: 'warehouse_name_origin',
    width: 150,
    resizable: true
  },
  {
    title: '原仓库仓位',
    dataIndex: 'warehouse_item_name_origin',
    width: 150,
    resizable: true
  },
  {
    title: '现仓库',
    dataIndex: 'warehouse_name',
    width: 150,
    resizable: true
  },
  {
    title: '现仓库仓位',
    dataIndex: 'warehouse_item_name',
    width: 150,
    resizable: true
  }
]
export const convert: BasicColumn[] = [
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 150,
    resizable: true
  },
  {
    title: '原箱号',
    dataIndex: 'strid_origin',
    width: 150,
    resizable: true
  },
  {
    title: '现箱号',
    dataIndex: 'strid',
    width: 150,
    resizable: true
  }
]
