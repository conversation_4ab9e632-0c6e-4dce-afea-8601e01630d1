import { ref, computed } from 'vue'
import { add, sub } from '/@/utils/math'
import { formSchemas, productFormSchemas } from '/@/views/erpFlow/packages/datas/drawer.data'
import { uniqBy } from 'lodash-es'
import { Input, InputNumber } from 'ant-design-vue'

// export const selectPackage = ref<number[]>([])

// 左边选中的商品
export const selectProduct = ref<number[]>([])

// 包裹异动右边的整合后包裹清单
export const packageList = ref<any>([])

// 包裹中的formEl集合
export const packageFormEl = ref([])

// 无法使用productFormEl那种，会有一个坑
export const mapPackageFormEl = computed(() => {
  const gather = {}
  for (const formEl of packageFormEl.value) {
    gather[formEl.key] = formEl.el
  }
  return gather
})

// 右边商品的form配置
export const productFormConfig = {
  labelWidth: 80,
  baseColProps: { span: 8 },
  showActionButtonGroup: false,
  schemas: productFormSchemas.map((item) =>
    item.field === 'buildQuantity'
      ? {
          field: 'quantity',
          label: '数量',
          component: InputNumber,
          required: true,
          componentProps: {
            min: 0.01,
            step: 1,
            prefix: '×',
            style: 'width: 100%'
          }
          // componentProps: ({ formModel }) => {
          //   return {
          //     max: add(sub(+formModel.maxQuantity, compPackageProductCount.value[formModel.prodKey] ?? 0), formModel.quantity),
          //     min: 0.01,
          //     step: 1,
          //     prefix: '×',
          //     style: 'width: 100%'
          //   }
          // }
        }
      : item
  )
  // layout: 'inline'
}

// 包裹商品中的formEl集合
export const productFormEl = ref({})

// 包裹异动的左边包裹列表
export const originPackageList = ref<Array<{ key: number; product: any[] }>>([])

// 右边包裹信息的form配置
export const formConfig = {
  labelWidth: 100,
  baseColProps: { span: 8 },
  showActionButtonGroup: false,
  schemas: [...formSchemas, { field: 'type_remark', label: '合并/拆分备注', component: Input, colProps: { span: 24 } }],
  layout: 'inline'
}

// 动态计算出selectPackage的id和项
export const compOriginPackage = computed(() => {
  const originPackage = {}
  for (const packageItem of originPackageList.value) {
    originPackage[packageItem.key] = packageItem
  }
  return originPackage
})

// 可以根据产品的itemKey获取到对应的包裹
export const mapProductForPackage = computed(() => {
  const prodGather: { [key in string]: any } = {}
  for (const packages of originPackageList.value) {
    for (const product of packages.product) {
      prodGather[product.prodKey] = packages
    }
  }
  return prodGather
})

// 映射选中商品的包裹
export const compMapSelectPackage = computed(() => {
  const selectPkg = {}
  for (const selectId of selectProduct.value) {
    // selectPkg[selectId] = compOriginPackage.value[selectId]
    selectPkg[selectId] = mapProductForPackage.value[selectId]
  }
  return selectPkg
})

// 获取选中商品的外层包裹
export const compSelectPackage = computed(() => {
  return uniqBy(Object.values(compMapSelectPackage.value), 'key')
})

// 包裹异动左边产品列表
export const compProductList = computed(() => {
  let productList = []
  for (const product of originPackageList.value) {
    if (product.product.length > 0) productList = productList.concat(product.product)
  }
  return productList
})

// 左边包裹商品列表prodKey的映射
export const compMapProduct = computed(() => {
  const mapGoods = {}
  for (const good of compProductList.value) {
    mapGoods[good.prodKey] = good
  }
  return mapGoods
})

export const compUsableKeysList = computed(() => {
  const selectKey: number[] = []
  for (const key of Object.keys(compMapProduct.value)) {
    const product = compMapProduct.value[key]
    const quantity = product.quantity
    const useCount = compPackageProductCount.value[product.prodKey]
    if (sub(quantity, useCount) > 0) {
      selectKey.push(+key)
    }
  }
  return selectKey
})

// 获取选中的包裹商品
export const compSelectPackageProduct = computed(() => {
  return compProductList.value.filter((product) => {
    return selectProduct.value.includes(product.prodKey)
  })
})

// 包裹异动右边产品列表
export const compPackageProduct = computed(() => {
  let prodList = []
  for (const prod of packageList.value) {
    prodList = prodList.concat(prod.product)
  }
  return prodList
})

// 对每个包裹的商品进行统计，计算已进行在包裹的商品数量
export const compPackageProductCount = computed(() => {
  const calcCountGather = {}
  for (const product of compProductList.value) {
    calcCountGather[product.prodKey] = compPackageProduct.value
      .filter((item) => item.prodKey === product.prodKey)
      .reduce((acc, prod) => add(acc, prod.quantity), 0)
  }
  return calcCountGather
})

// 动态计算各个商品的prodKey集合，方便使用mapPackageProduct[prodKey]获取
export const mapPackageProduct = computed(() => {
  const prodGather = {}
  for (const product of compPackageProduct.value) {
    prodGather[product.itemKey] = product
  }
  return prodGather
})
