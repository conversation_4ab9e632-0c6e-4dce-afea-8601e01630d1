import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'

export const schemas: FormSchema[] = [
  // {
  //   label: '销售单号',
  //   field: 'sale_strid',
  //   component: 'Input'
  // },
  {
    field: 'urgent_level',
    label: '紧急程度',
    component: 'Select',
    required: true,
    componentProps: {
      options: [
        { label: '一般', value: 1 },
        { label: '紧急', value: 2 },
        { label: '非常紧急', value: 3 }
      ],
      placeholder: '请选择',
      showSearch: true
    }
  },
  {
    label: '唛头',
    field: 'buyer',
    component: 'Input',
    required: true
  },
  {
    label: '国家',
    field: 'country',
    component: 'Input',
    required: true
  },
  {
    field: 'cabinet_number',
    label: '柜号',
    component: 'Input'
    // colProps: { span: 24 }
  },
  {
    field: 'plate_number',
    label: '车牌号',
    component: 'Input'
    // colProps: { span: 24 }
  },
  {
    label: '供应商',
    field: 'supplier',
    component: 'Input',
    // required: true,
    dynamicDisabled: true
  },
  {
    label: '出货时间',
    field: 'shipmentAt',
    component: 'DatePicker',
    required: true,
    componentProps: {
      showTime: true
    }
  },
  {
    label: '出货地址',
    field: 'shipmentAddr',
    component: 'Input',
    required: true
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input'
  },
  {
    label: '选中包裹',
    field: 'packingList',
    component: 'Select',
    show: false
    // slot: 'PackingList'
  }
]

export const columns: BasicColumn[] = [
  {
    title: '名称',
    dataIndex: 'name',
    width: 150,
    resizable: true
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    width: 150,
    resizable: true
  },
  {
    title: '材质',
    dataIndex: 'material',
    width: 150,
    resizable: true
  },
  {
    title: '长',
    dataIndex: 'length',
    width: 150,
    resizable: true
  },
  {
    title: '宽',
    dataIndex: 'width',
    width: 150,
    resizable: true
  },
  {
    title: '高',
    dataIndex: 'height',
    width: 150,
    resizable: true
  },
  {
    title: '海关码',
    dataIndex: 'code',
    width: 150,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 150,
    resizable: true
  }
]
