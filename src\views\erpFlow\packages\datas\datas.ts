import { BasicColumn, FormSchema } from '/@/components/Table'
import { getStaffList } from '/@/api/baseData/staff'
import { useI18n } from '/@/hooks/web/useI18n'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { getWorkList } from '/@/api/commonUtils'
import { isNullOrUnDef } from '/@/utils/is'

const { tm } = useI18n()
export const searchSchemas: FormSchema[] = [
  {
    field: 'strid',
    label: '包裹号',
    component: 'Input'
  },
  {
    field: 'packing_strid',
    label: '装箱单号',
    component: 'Input'
  },
  {
    field: 'join_source_uniqid',
    label: '拼货销售单号',
    component: 'Input'
  },
  {
    field: 'is_old',
    label: '是否旧包裹',
    component: 'Select',
    defaultValue: 0,
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'creator',
    label: '创建人',
    component: 'PagingApiSelect',
    componentProps: {
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    }
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'PagingApiSelect',
    componentProps: {
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    }
  },
  {
    field: 'stock_inCharge',
    label: '备货操作人',
    component: 'PagingApiSelect',
    componentProps: {
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    }
  },
  {
    field: 'created_at',
    label: '创建时间',
    component: 'SingleRangeDate'
  },
  {
    field: 'is_out',
    label: '是否已出库',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未出库', value: 0 },
        { label: '出库中', value: 1 },
        { label: '已出库', value: 2 }
      ]
    }
  },
  {
    field: 'is_retreat',
    label: '是否退货',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未退货', value: 0 },
        { label: '退货中', value: 1 },
        { label: '已退货备货', value: 2 },
        { label: '已退货', value: 3 }
      ]
    }
  },
  {
    field: 'is_stock',
    label: '是否已备货',
    component: 'Select',
    componentProps: {
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ]
    }
  },
  {
    field: 'is_in',
    label: '是否已入库',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未入库', value: 0 },
        { label: '入库中', value: 1 },
        { label: '已入库', value: 2 }
      ]
    }
  },
  {
    field: 'stock_source',
    label: '操作端来源',
    component: 'Select',
    componentProps: {
      options: [
        { label: 'pc', value: 1 },
        { label: '小程序', value: 2 }
      ]
    }
  },
  {
    field: 'sale_work_ids',
    label: '销售订单',
    component: 'PagingApiSelect',
    componentProps: {
      mode: 'multiple',
      resultField: 'items',
      api: getWorkList,
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      searchParamField: 'source_uniqid',
      selectProps: {
        allowClear: true,
        fieldNames: { value: 'id', label: 'source_uniqid' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'source_uniqid'
      },
      params: {
        types: [3, 2]
      }
    }
  },
  {
    field: 'purchase_work_ids',
    label: '采购订单',
    component: 'PagingApiSelect',
    componentProps: {
      mode: 'multiple',
      resultField: 'items',
      api: getWorkList,
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      searchParamField: 'strid',
      selectProps: {
        allowClear: true,
        fieldNames: { value: 'id', label: 'purchase_strid' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'strid'
      },
      params: {
        type: 4
      }
    }
  },
  {
    field: 'project_number',
    label: '项目ID',
    component: 'Input'
  },
  {
    field: 'is_join',
    label: '是否拼货',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'is_cancel',
    label: '是否作废',
    component: 'Select',
    defaultValue: 0,
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'supplier_strid',
    label: '供应商箱号',
    component: 'Input'
  }
]

export const columns: BasicColumn[] = [
  {
    title: '项目ID',
    dataIndex: 'project_number',
    width: 100,
    resizable: true
  },
  {
    title: '项目',
    dataIndex: 'project_name',
    width: 200,
    resizable: true
  },
  {
    title: '装箱单号',
    dataIndex: 'packing_strid',
    width: 250,
    resizable: true
  },
  {
    title: '供应商箱号',
    dataIndex: 'supplier_strid',
    width: 250,
    resizable: true
  },
  {
    title: '包裹号',
    dataIndex: 'strid',
    width: 250,
    resizable: true
  },
  {
    title: '拼货销售单号',
    dataIndex: 'join_source_uniqid',
    width: 250,
    resizable: true
  },
  {
    title: '销售单号',
    dataIndex: 'source_uniqids',
    width: 200,
    resizable: true,
    customRender: ({ text }) => {
      const nameArr = text.map((item) => item.source_uniqid)
      return text ? useRender.renderTags(nameArr) : '-'
    }
  },
  {
    title: '采购单号',
    dataIndex: 'purchase_strids',
    width: 250,
    resizable: true,
    customRender: ({ text }) => {
      const nameArr = text.map((item) => item.strid)
      return text ? useRender.renderTags(nameArr) : '-'
    }
  },

  {
    title: '是否已出库',
    dataIndex: 'is_out',
    customRender: ({ text }) => {
      const map = {
        2: { label: '已出库', color: 'green' },
        1: { label: '出库中', color: 'green' },
        0: { label: '未出库', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },

  {
    title: '是否已入库',
    dataIndex: 'is_in',
    customRender: ({ text }) => {
      const map = {
        2: { label: '已入库', color: 'green' },
        1: { label: '入库中', color: 'green' },
        0: { label: '未入库', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否已作废',
    dataIndex: 'is_cancel',
    customRender: ({ text }) => {
      const map = {
        1: { label: '已作废', color: 'red' },
        0: { label: '未作废', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否已备货',
    dataIndex: 'is_stock',
    customRender: ({ text }) => {
      const map = {
        1: { label: '是', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否已报废',
    dataIndex: 'is_scrap',
    customRender: ({ text }) => {
      const map = {
        1: { label: '是', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否退货',
    dataIndex: 'is_retreat',
    customRender: ({ text }) => {
      const map = {
        3: { label: '已退货 ', color: 'red' },
        2: { label: '已退货备货 ', color: 'red' },
        1: { label: '退货中 ', color: 'red' },
        0: { label: '未退货', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  // {
  //   title: '是否报废',
  //   dataIndex: 'is_scrap',
  //   customRender: ({ text }) => {
  //     const map = {
  //       1: { label: '是 ', color: 'red' },
  //       0: { label: '否', color: 'green' }
  //     }
  //     const curTag = map[text]
  //     if (!curTag) return text
  //     return useRender.renderTag(curTag.label, curTag.color)
  //   }
  // },
  {
    title: '是否旧包裹',
    dataIndex: 'is_old',
    customRender: ({ text }) => {
      const curTag = tm(`tag.tagColor.${text}`)
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '包装产品数量',
    dataIndex: 'quantity',
    width: 150,
    resizable: true
  },
  {
    title: '是否拼货',
    dataIndex: 'is_join',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        1: { label: '是', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '打包方式',
    dataIndex: 'method',
    width: 150,
    resizable: true
  },
  {
    title: '打包件数',
    dataIndex: 'pkg_quantity',
    width: 150,
    resizable: true
  },
  {
    title: '包裹长度（cm）',
    dataIndex: 'length',
    width: 150,
    resizable: true
  },
  {
    title: '包裹宽度（cm）',
    dataIndex: 'width',
    width: 150,
    resizable: true
  },
  {
    title: '包裹高度（cm）',
    dataIndex: 'height',
    width: 150,
    resizable: true
  },
  {
    title: '重量(KG)',
    dataIndex: 'weight',
    width: 150,
    resizable: true
  },
  {
    title: '体积(CBM)',
    dataIndex: 'volume',
    width: 150,
    resizable: true
  },
  {
    title: '仓库',
    dataIndex: 'warehouse_name',
    width: 150,
    resizable: true
  },
  {
    title: '仓位',
    dataIndex: 'warehouse_item_name',
    width: 150,
    resizable: true
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '备货操作人',
    dataIndex: 'stock_inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '操作端来源',
    dataIndex: 'stock_source',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : text === 1 ? 'pc' : '小程序'
    }
  },
  {
    title: '序列号',
    dataIndex: 'num',
    width: 150,
    resizable: true
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 150,
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 150,
    resizable: true
  },

  {
    title: '备注',
    dataIndex: 'remark',
    width: 150,
    resizable: true
  }
]

/** 子表格 */
export const childRenColumns: BasicColumn[] = [
  {
    title: '名称',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    width: 100,
    resizable: true
  },
  {
    title: '材质',
    dataIndex: 'material',
    width: 100,
    resizable: true
  },
  {
    title: '长',
    dataIndex: 'length',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text + 'cm' : '-'
    }
  },
  {
    title: '宽',
    dataIndex: 'width',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text + 'cm' : '-'
    }
  },
  {
    title: '高',
    dataIndex: 'height',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text + 'cm' : '-'
    }
  },
  {
    title: '海关码',
    dataIndex: 'code',
    width: 100,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 300,
    resizable: true
  }
]
