import { FormSchema, PagingApiSelect } from '/@/components/Form'
import { getAccountList } from '/@/api/commonUtils'
import { ref, computed, nextTick } from 'vue'
import { add, div, mul } from '/@/utils/math'
import { Input, InputNumber, Select, Divider, Tooltip, Button, Form } from 'ant-design-vue'
import { useI18n } from '/@/hooks/web/useI18n'
import { Icon } from '/@/components/Icon'
import { useMessage } from '/@/hooks/web/useMessage'

export const enableBaseInfo = ref<boolean>(true)

const { createMessage } = useMessage()

const { tm } = useI18n()
// 包裹列表
export const packageList = ref<Array<{ key: number; product: any[]; projectNumber: string | number }>>([]) // 包裹列表数据

// 勾选的商品
export const selectProduct = ref([]) // 选中的子产品

// 采购单的商品列表
export const purchaseProductList = ref([]) // 后端返回的列表
// 产品清单，将子产品和主产品汇总到产品清单里面
export const compProductList = computed(() => {
  let productList = []
  for (const product of purchaseProductList.value) {
    // 如果有子产品，就讲子产品放进去
    if (product.items_sub?.length > 0) {
      productList = productList.concat(product.items_sub)
    } else {
      productList.push(product)
    }
  }
  return productList
})

// 产品id映射，可以通过mapProductList.value[主产品key]这样去获取当前主产品
export const mapProductList = computed(() => {
  const map = {}
  for (const item of compProductList.value) {
    map[item.key] = item
  }
  return map
})

// 计算已勾选的产品
export const compSelectProduct = computed(() => {
  const list = []
  for (const prod of selectProduct.value) {
    list.push(mapProductList.value[prod])
  }
  return list
})

// 获取每个包裹中的商品
export const compPackageProduct = computed(() => {
  let list = []
  for (const prod of packageList.value) {
    list = list.concat(prod.product)
  }
  return list
})

// 对每个包裹的商品进行统计，计算已进行在包裹的商品数量
export const compPackageProductCount = computed(() => {
  const calcCountGather = {}
  for (const product of compProductList.value) {
    calcCountGather[product.key] = compPackageProduct.value
      .filter((item) => item.key === product.key)
      .reduce((acc, prod) => add(acc, prod.buildQuantity), 0)
  }
  return calcCountGather
})

// 计算每个包裹同类的商品集合
export const mapPackageProductForKey = computed(() => {
  const prodGather = {}
  for (const prod of compPackageProduct.value) {
    prodGather[prod.key] = [...(prodGather[prod.key] ?? []), prod]
  }
  return prodGather
})

// 动态计算各个商品的itemKey集合，方便使用mapPackageProduct[itemKey]获取
export const mapPackageProduct = computed(() => {
  const prodGather = {}
  for (const product of compPackageProduct.value) {
    prodGather[product.itemKey] = product
  }
  return prodGather
})

// 可以根据产品的itemKey获取到对应的包裹
export const mapProductForPackage = computed(() => {
  const prodGather: { [key in string]: any } = {}
  for (const packages of packageList.value) {
    for (const product of packages.product) {
      prodGather[product.itemKey] = packages
    }
  }
  return prodGather
})

// 包裹中的formEl集合
export const packageFormEl = ref<Array<{ key: string | number; el: InstanceType<typeof Form> }>>([])

// 无法使用productFormEl那种，会有一个坑
export const mapPackageFormEl = computed<{ [key in string]: InstanceType<typeof Form> }>(() => {
  const gather = {}
  for (const formEl of packageFormEl.value) {
    gather[formEl.key] = formEl.el
  }
  return gather
})

// 包裹商品中的formEl集合
export const productFormEl = ref<{ [key in string]: InstanceType<typeof Form> }>({})

export const formSchemas: FormSchema[] = [
  {
    field: 'info',
    label: '包裹信息',
    component: 'Divider',
    colProps: {
      span: 24
    }
  },
  {
    field: 'batch_code',
    label: '批次',
    component: Input
    // required: true
  },
  {
    field: 'quantity',
    label: '包装产品数量',
    component: InputNumber,
    componentProps: {
      min: 1
    },
    required: true
  },
  {
    field: 'method',
    label: '打包方式',
    component: Select,
    componentProps: {
      options: tm('packages.packagesMethods')
    },
    required: true
  },
  {
    field: 'length',
    label: '长(CM)',
    component: InputNumber,
    // componentProps: ({ formModel, formActionType }) => {
    //   return {
    //     onChange: (val) => {
    //       formActionType?.setFieldsValue({
    //         volume: div(Math.floor(mul(mul(mul(val ?? 0, formModel.width ?? 0), formModel.height ?? 0), 10000)), ***********)
    //       })
    //     }
    //   }
    // },
    required: true
  },
  {
    field: 'width',
    label: '宽(CM)',
    component: InputNumber,
    // componentProps: ({ formModel, formActionType }) => {
    //   return {
    //     onChange: (val) => {
    //       formActionType?.setFieldsValue({
    //         volume: div(Math.floor(mul(mul(mul(formModel.length ?? 0, val ?? 0), formModel.height ?? 0), 10000)), ***********)
    //       })
    //     }
    //   }
    // },
    required: true
  },
  {
    field: 'height',
    label: '高(CM)',
    component: InputNumber,
    // componentProps: ({ formModel, formActionType }) => {
    //   return {
    //     onChange: (val) => {
    //       formActionType?.setFieldsValue({
    //         volume: div(Math.floor(mul(mul(mul(formModel.length ?? 0, formModel.width ?? 0), val ?? 0), 10000)), ***********)
    //       })
    //     }
    //   }
    // },
    required: true
  },
  {
    field: 'weight',
    label: '重量(KG)',
    component: InputNumber,
    required: true
  },
  {
    field: 'volume',
    label: '体积(CBM)',
    component: InputNumber,
    // dynamicDisabled: true,
    required: true
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: PagingApiSelect,
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      returnParamsField: 'id',
      pagingMode: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
    // required: true
  },
  {
    field: 'supplier_strid',
    label: '供应商箱号',
    component: Input
    // required: true
  },
  {
    field: 'remark',
    label: '备注',
    component: Input,
    colProps: { span: 16 }
  }
]

export const packagesFormSchemas: FormSchema[] = [
  {
    field: 'info',
    label: '通用包裹信息',
    component: 'Divider',
    colProps: {
      span: 24
    },
    renderColContent: () => (
      <div>
        <Divider orientation="left">
          <span class="text-base">通用包裹信息</span>
          <Tooltip>
            {{
              title: () => (
                <div>
                  通用包裹信息启用时，生成的包裹信息默认会根据下方的填写自动写入创建的包裹中；
                  <br />
                  <span class="text-[#FFFF00]">禁用则不会进行信息写入</span>
                </div>
              ),
              default: () => <Icon icon="ant-design:info-circle-outlined" class="ml-1" />
            }}
          </Tooltip>
          <Button class="ml-4" size="small" type="primary" onClick={() => handleEnabledBaseInfo()}>
            {enableBaseInfo.value ? '设为禁用' : '设为启用'}
          </Button>
        </Divider>
      </div>
    )
  },
  {
    field: 'length',
    label: '长(CM)',
    dynamicDisabled: () => !enableBaseInfo.value,
    component: 'InputNumber',
    defaultValue: 0.01,
    componentProps: ({ formModel, formActionType }) => {
      return {
        min: 0.01,
        onChange: (val) => {
          nextTick(() => {
            formModel.width &&
              formModel.height &&
              val &&
              formActionType?.setFieldsValue({
                volume: div(mul(mul(val ?? 0, formModel.width ?? 0), formModel.height ?? 0), 1000000, 6)
              })
          })
        }
      }
    }
  },
  {
    field: 'width',
    label: '宽(CM)',
    dynamicDisabled: () => !enableBaseInfo.value,
    component: 'InputNumber',
    defaultValue: 0.01,
    componentProps: ({ formModel, formActionType }) => {
      return {
        min: 0.01,
        onChange: (val) => {
          nextTick(() => {
            formModel.length &&
              formModel.height &&
              val &&
              formActionType?.setFieldsValue({
                volume: div(mul(mul(val ?? 0, formModel.length ?? 0), formModel.height ?? 0), 1000000, 6)
              })
          })
        }
      }
    }
  },
  {
    field: 'height',
    label: '高(CM)',
    dynamicDisabled: () => !enableBaseInfo.value,
    component: 'InputNumber',
    defaultValue: 0.01,
    componentProps: ({ formModel, formActionType }) => {
      return {
        min: 0.01,
        onChange: (val) => {
          nextTick(() => {
            formModel.width &&
              formModel.length &&
              val &&
              formActionType?.setFieldsValue({
                volume: div(mul(mul(val ?? 0, formModel.width ?? 0), formModel.length ?? 0), 1000000, 6)
              })
          })
        }
      }
    }
  },
  {
    field: 'weight',
    label: '重量(KG)',
    dynamicDisabled: () => !enableBaseInfo.value,
    component: 'InputNumber',
    componentProps: {
      min: 0.01
    }
  },
  {
    field: 'volume',
    label: '体积(CBM)',
    dynamicDisabled: () => !enableBaseInfo.value,
    component: 'InputNumber',
    // dynamicDisabled: true,
    colProps: { span: 3 },
    componentProps: {
      min: 0.000001
    }
  },
  {
    field: 'batch_code',
    label: '批次',
    component: 'Input',
    dynamicDisabled: () => !enableBaseInfo.value,
    colProps: { span: 3 }
    // required: true
  },
  {
    field: 'quantity',
    label: '包装产品数量',
    dynamicDisabled: () => !enableBaseInfo.value,
    colProps: { span: 3 },
    component: 'InputNumber',
    componentProps: {
      min: 1
    }
  },
  {
    field: 'method',
    label: '打包方式',
    dynamicDisabled: () => !enableBaseInfo.value,
    colProps: { span: 3 },
    component: 'Select',
    componentProps: {
      options: tm('packages.packagesMethods')
    }
  },
  {
    field: 'inCharge',
    label: '负责人',
    dynamicDisabled: () => !enableBaseInfo.value,
    component: 'PagingApiSelect',
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      returnParamsField: 'id',
      pagingMode: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: { span: 4 }
  }
]

export const formConfig = {
  labelWidth: 100,
  baseColProps: { span: 8 },
  showActionButtonGroup: false,
  schemas: formSchemas,
  layout: 'inline'
}

export const productFormSchemas: FormSchema[] = [
  {
    field: 'buildQuantity',
    label: '数量',
    component: InputNumber,
    required: true,
    componentProps: {
      // min: 0.01,
      step: 1,
      prefix: '×',
      style: 'width: 100%'
    }
    // componentProps: ({ formModel }) => {
    //   return {
    //     max: add(sub(+formModel.maxQuantity, compPackageProductCount.value[formModel.key] ?? 0), formModel.buildQuantity),
    //     min: 0.01,
    //     step: 1,
    //     prefix: '×',
    //     style: 'width: 100%'
    //   }
    // }
  },
  {
    field: 'material',
    label: '材质',
    component: Input,
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'length',
    label: '长(CM)',
    component: InputNumber,
    // required: true,
    dynamicDisabled: true
  },
  {
    field: 'width',
    label: '宽(CM)',
    component: InputNumber,
    // required: true,
    dynamicDisabled: true
  },
  {
    field: 'height',
    label: '高(CM)',
    component: InputNumber,
    // required: true,
    dynamicDisabled: true
  },
  {
    field: 'code',
    label: '海关码',
    component: Input,
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'remark',
    label: '备注',
    component: Input,
    colProps: { span: 24 }
  }
]

export const productFormConfig = {
  labelWidth: 80,
  baseColProps: { span: 8 },
  showActionButtonGroup: false,
  schemas: productFormSchemas
  // layout: 'inline'
}

function handleEnabledBaseInfo() {
  enableBaseInfo.value = !enableBaseInfo.value
  createMessage.success(`通用包裹信息已${enableBaseInfo.value ? '启用' : '禁用'}`)
}
