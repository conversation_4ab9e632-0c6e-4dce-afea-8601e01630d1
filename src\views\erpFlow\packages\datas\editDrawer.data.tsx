import { FormSchema } from '/@/components/Form'
// import { formSchemas } from '/@/views/erpFlow/packages/datas/drawer.data'
import { reactive, ref, h, withDirectives } from 'vue'
import { BasicTableProps } from '/@/components/Table'
import { InputNumber, Button, Input } from 'ant-design-vue'
import { div, mul, add } from '/@/utils/math'
import { getRelatePurchaseList } from '/@/api/erp/inWarehouse'
import loadingDirective from '/@/directives/loading'
import { getPurchaseDetail } from '/@/api/erp/purchaseOrder'
import { VxeTablePropTypes } from 'vxe-table'
import { cloneDeep, random } from 'lodash-es'
import { getAccountList } from '/@/api/commonUtils'
// import { getDeptSelectTree } from '/@/api/admin/dept'

export const pagingLoading = ref<boolean>(false)
// export const productList = ref([])

export const loading = ref<boolean>(false)

export const purchaseId = ref(void 0)

export const selectValue = ref(void 0)

export const mapGoodsList = ref({})

export const tableRef = ref(null)

export const tableRefs = ref(null)

export const formRef = ref(null)

export const removeItemsList = ref<any[]>([])

export const projectNumber = ref<number | string | null>(null)

export const schemas: FormSchema[] = [
  {
    field: 'batch_code',
    label: '批次',
    component: 'Input'
  },
  {
    field: 'quantity',
    label: '包装产品数量',
    component: 'InputNumber',
    componentProps: {
      min: 1
    },
    required: true
  },
  {
    field: 'method',
    label: '打包方式',
    component: 'Input',
    required: true
  },
  {
    field: 'length',
    label: '长(CM)',
    component: 'InputNumber',
    componentProps: ({ formModel, formActionType }) => {
      return {
        min: 1,
        onChange: (val) => {
          formActionType?.setFieldsValue({
            volume: div(mul(mul(val ?? 0, formModel.width ?? 0), formModel.height ?? 0), 1000000, 6)
          })
        }
      }
    },
    required: true
  },
  {
    field: 'width',
    label: '宽(CM)',
    component: 'InputNumber',
    componentProps: ({ formModel, formActionType }) => {
      return {
        min: 1,
        onChange: (val) => {
          formActionType?.setFieldsValue({
            volume: div(mul(mul(formModel.length ?? 0, val ?? 0), formModel.height ?? 0), 1000000, 6)
          })
        }
      }
    },
    required: true
  },
  {
    field: 'height',
    label: '高(CM)',
    component: 'InputNumber',
    componentProps: ({ formModel, formActionType }) => {
      return {
        min: 1,
        onChange: (val) => {
          formActionType?.setFieldsValue({
            volume: div(mul(mul(formModel.length ?? 0, formModel.width ?? 0), val ?? 0), 1000000, 6)
          })
        }
      }
    },
    required: true
  },
  {
    field: 'weight',
    label: '重量(KG)',
    component: 'InputNumber',
    componentProps: {
      min: 1
    },
    required: true
  },
  {
    field: 'volume',
    label: '体积(CBM)',
    component: 'InputNumber',
    dynamicDisabled: true,
    required: true
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
    // required: true
  },
  {
    field: 'supplier_strid',
    label: '供应商箱号',
    component: 'Input'
    // required: true
  },
  {
    label: '打包件数',
    field: 'pkg_quantity',
    component: 'Input'
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea'
  },
  {
    label: '包裹详情',
    field: 'items',
    component: 'Select',
    slot: 'Items',
    // colSlot: 'Items',
    colProps: { span: 24 }
  },
  {
    label: '描述商品',
    field: 'desc_items',
    component: 'Select',
    slot: 'DescItems',
    // colSlot: 'Items',
    colProps: { span: 24 }
  },

  {
    label: 'ID',
    field: 'id',
    component: 'Input',
    show: false
  }
]

export const columns = [
  {
    title: '所属销售单',
    field: 'source_uniqid'
  },
  {
    title: '所属采购单',
    field: 'purchase_strid'
  },
  {
    title: '名称',
    field: 'name'
  },
  {
    title: '数量',
    field: 'quantity',
    editRender: { name: 'AInput', placeholder: '请点击输入' },
    slots: {
      edit: ({ row }) => (
        <InputNumber v-model:value={row.quantity} max={row.maxQuantity} prefix="×" size="small" min="0.01" class="!w-full" />
      )
    }
  },
  {
    title: '材质',
    field: 'material',
    editRender: { name: 'AInput', placeholder: '请点击输入' },
    slots: {
      edit: ({ row }) => <Input v-model:value={row.material} size="small" min="0.01" class="!w-full" />
    }
  },
  {
    title: '长',
    field: 'length',
    editRender: { name: 'AInput', placeholder: '请点击输入' },
    slots: {
      edit: ({ row }) => <InputNumber v-model:value={row.length} size="small" min="0" class="!w-full" />
    }
  },
  {
    title: '宽',
    field: 'width',
    editRender: { name: 'AInput', placeholder: '请点击输入' },
    slots: {
      edit: ({ row }) => <InputNumber v-model:value={row.width} size="small" min="0.01" class="!w-full" />
    }
  },
  {
    title: '高',
    field: 'height',
    editRender: { name: 'AInput', placeholder: '请点击输入' },
    slots: {
      edit: ({ row }) => <InputNumber v-model:value={row.height} size="small" min="0.01" class="!w-full" />
    }
  },
  {
    title: '海关码',
    field: 'code',
    editRender: { name: 'AInput', placeholder: '请点击输入' }
  },
  {
    title: '备注',
    field: 'remark',
    editRender: { name: 'AInput', placeholder: '请点击输入' }
  },
  {
    title: '操作',
    field: 'action',
    slots: {
      default: ({ row }) => (
        <>
          <Button type="error" size="small" onClick={() => handleDel(row)}>
            删除
          </Button>
        </>
      )
    }
  }
]
const descolumns = columns.filter((item) => !['action', 'source_uniqid', 'purchase_strid'].includes(item.field))

function handleDel(row) {
  row.deal_type = 3
  if (row.packing_package_id) {
    removeItemsList.value.push(cloneDeep(row))
  }
  // tableRef.value?.remove(row)

  const formData = formRef.value?.formActionType?.getFieldsValue()
  formRef.value?.formActionType?.setFieldsValue({ items: formData.items.filter((item) => item.itemKey !== row.itemKey) })
}

export const gridOptions = reactive<BasicTableProps>({
  id: 'VxeTable',
  loading,
  keepSource: true,
  // editConfig: { trigger: 'click', mode: 'cell', enabled: true, showIcon: true },
  columns,
  // height: 700,
  proxyConfig: null,
  toolbarConfig: null
})

export const descgridOptions = reactive<BasicTableProps>({
  id: 'VxeTable',
  loading,
  keepSource: true,
  // editConfig: { trigger: 'click', mode: 'cell', enabled: true, showIcon: true },
  columns: descolumns,
  // height: 700,
  proxyConfig: null,
  toolbarConfig: null
})

export const purchasePagingSelectConfig = {
  resultField: 'items',
  api: (params) => getRelatePurchaseList({ ...params, type: 4, project_number: projectNumber.value }),
  searchMode: true,
  pagingMode: true,
  pagingSize: 20,
  searchParamField: 'strid',
  selectProps: {
    class: 'w-[300px]',
    // mode: 'multiple',
    allowClear: true,
    fieldNames: { value: 'doc_id', label: 'strid' },
    showSearch: true,
    placeholder: '请选择采购订单',
    optionFilterProp: 'strid',
    dropdownRender: ({ menuNode }) => {
      const vNode = h('div', {}, menuNode)

      return withDirectives(vNode, [[loadingDirective, pagingLoading.value]])
    }
  },
  // params: {
  //   type: 4,
  //   project_number: projectNumber.value
  // },
  onChange: () => {
    selectValue.value = []
  }
}

export const itemPagingSelectConfig = {
  resultField: 'items',
  api: (params) => formatPurchaseDetail(params),
  searchMode: true,
  pagingMode: true,
  pagingSize: 20,
  searchParamField: 'name',
  selectProps: {
    class: 'w-[300px]',
    fieldNames: { value: 'id', label: 'name' },
    // treeDefaultExpandAll: true,
    showSearch: true,
    mode: 'multiple',
    allowClear: true,
    placeholder: '请选择添加商品',
    optionFilterProp: 'name'
  }
}

// 中间转换一下，产品的work_id替换成采购单的doc,树形结构转成一维数组
async function formatPurchaseDetail(params) {
  try {
    if (!purchaseId.value) return []
    const { items, doc } = await getPurchaseDetail({ ...params, doc_id: purchaseId.value, pageSize: 1000 })
    const curTableData = tableRef.value
      ?.getTableData(true)
      .visibleData.map((item) => item.puid)
      .filter((item) => item)
    return (
      items
        // 处理好直接添加到明细的数据字段
        .map((goods) => ({
          ...goods,
          purchase_work_id: doc.work_id,
          work_id: doc.work_id,
          request_id: goods.request_id,
          request_sub_id: null,
          deal_type: 1,
          type: 1,
          maxQuantity: add(goods.qty_pkg_left ?? 0, goods.quantity ?? 0),
          quantity: goods.quantity ?? 0,
          itemKey: random(1, 1000000),
          item_purchase_id: goods.purchase_id,
          items_sub: goods.items_sub.map((subGoods) => ({
            ...subGoods,
            purchase_work_id: doc.work_id,
            work_id: doc.work_id,
            request_id: subGoods.request_id,
            request_sub_id: subGoods.id,
            deal_type: 1,
            type: 2,
            maxQuantity: add(subGoods.quantity ?? 0, subGoods.qty_pkg_left ?? 0),
            quantity: subGoods.quantity ?? 0,
            item_purchase_id: subGoods.item_purchase_id,
            item_purchase_sub_id: subGoods.id,
            itemKey: random(1, 1000000)
          }))
        }))
        // 二维变成一维数组
        .reduce((acc, item) => {
          if (item.items_sub.length > 0) {
            return [...acc, ...item.items_sub]
          }
          return [...acc, item]
        }, [])
        // 过滤数据,已经在表格中的数据和最大值等于0 的数据不显示在列表
        .filter((item) => item.maxQuantity > 0 && !curTableData.includes(item.puid))
    )
  } catch (err) {
    console.log(err)
  }
}

interface RowFields {
  code: string
  material?: string
  quantity?: number
  remark?: string
  // width: number
  // height: number
  // length: number
}

export const validRules = ref<VxeTablePropTypes.EditRules<RowFields>>({
  quantity: [{ required: true, message: '必须填写产品数量' }],
  material: [{ required: true, message: '必须填写材质' }],
  // width: [{ required: true, message: '必须填写宽度' }],
  // height: [{ required: true, message: '必须填写高度' }],
  // length: [{ required: true, message: '必须填写长度' }],
  code: [{ required: true, message: '必须填写海关码' }]
})
