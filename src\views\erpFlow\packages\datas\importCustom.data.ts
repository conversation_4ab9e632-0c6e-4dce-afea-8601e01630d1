import { BasicColumn } from '/@/components/Table'
import { div, mul } from '/@/utils/math'

export const commonPackingInfokey = {
  '1': 'strid',
  '2': 'project_number',
  '3': 'join_source_uniqid',
  '4': 'name',
  '5': 'method',
  '6': 'packageQuantity',
  '7': 'material',
  '8': 'length',
  '9': 'width',
  '10': 'height',
  '11': 'volume',
  '12': 'weight',
  '13': 'unit',
  '14': 'quantity',
  '15': 'code',
  '16': 'remark'
}

// 获取单元格信息

export function formatDate(headers, rowData) {
  return headers.value.reduce((acc, header, cloIndex) => {
    acc[header] = rowData.slice(1)[cloIndex]
    return acc
  }, {})
}

// 自定义排序函数，按照文件名中的数字部分进行数值排序
export function compareFilePaths(a, b) {
  const numA = parseInt(a.match(/\d+/)[0]) // 提取数字部分并转换为整数
  const numB = parseInt(b.match(/\d+/)[0]) // 提取数字部分并转换为整数
  return numA - numB
}
export const formartKey = (key) => {
  const newline = /\n/g
  const spaces = / /g
  let newKey = key

  if (newline.test(newKey)) {
    newKey = newKey.replace(newline, '')
  }
  if (spaces.test(newKey)) {
    newKey = newKey.replace(spaces, '')
  }
  return newKey
}

export const columns: BasicColumn[] = [
  {
    title: '包裹号',
    dataIndex: 'strid',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },
  {
    title: '项目ID',
    dataIndex: 'project_number',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber'
  },
  {
    title: '拼货销售单号',
    dataIndex: 'join_source_uniqid',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },
  {
    title: '产品名称',
    dataIndex: 'name',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },

  {
    title: '打包方式',
    dataIndex: 'method',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },
  {
    title: '包装产品数量',
    dataIndex: 'packageQuantity',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber'
  },
  {
    title: '材质',
    dataIndex: 'material',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },
  {
    title: '包裹长',
    dataIndex: 'length',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber'
  },
  {
    title: '包裹宽',
    dataIndex: 'width',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber'
  },
  {
    title: '包裹高',
    dataIndex: 'height',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber'
  },
  {
    title: '体积',
    dataIndex: 'volume',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber',
    customRender: ({ record }) => div(mul(mul(+record.length, +record.width), +record.height), 1000000, 6)
  },
  {
    title: '重量',
    dataIndex: 'weight',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber'
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },
  {
    title: '产品数量',
    dataIndex: 'quantity',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber'
  },
  {
    title: '海关编码',
    dataIndex: 'code',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'Textarea'
  },
  {
    title: 'is_active',
    dataIndex: 'is_active',
    width: 150,
    ifShow: false
  }
]

export const validKey = [
  // 'strid',
  'project_number',
  // 'name',
  // 'imgs',
  // 'material',
  'length',
  'width',
  'height',
  'weight',
  // 'unit',
  // 'quantity'
  // 'code',
  // 'remark',
  'method'
  // 'packageQuantity'
]
