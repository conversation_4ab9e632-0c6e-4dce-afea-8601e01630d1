import { BasicColumn } from '/@/components/Table'
import { div, mul } from '/@/utils/math'

export const commonPackingInfokey = {
  '*no.': 'no',
  '*产品中英文品名productname': 'name',
  '*材质material中英文': 'material',
  '*产品长length（mm）': 'goodsLength',
  '*产品宽width（mm）': 'goodsWidth',
  '*产品高或厚度heithorthickness（mm）': 'goodsHeight',
  '*单位Unit': 'unit',
  '*产品数量qty': 'quantity',
  '*海关编码Hscode': 'code',
  '备注/唛头remarks/shippingmark': 'remark',
  '*包装产品数量': 'packingQuantity',
  '*单位unit': 'unit',
  '*打包方式实木/熏蒸/夹板/胶合板': 'method',
  '*毛重g.w.(kgs)': 'weight',
  '*海关编码hscode': 'code',
  '包装尺寸宽（cm）': 'packageWidth',
  '包装尺寸长（cm）': 'packageLength',
  '包装尺寸高（cm）': 'packageHeight',
  '*主产品id': 'request_id',
  子产品id: 'request_sub_id',
  '*采购单主产品id': 'item_purchase_id',
  采购单子产品id: 'item_purchase_sub_id',
  '*采购单任务id集合': 'purchase_work_ids',
  供应商箱号: 'supplier_strid',
  '*产品类型': 'type',
  '*puid': 'puid'
}

// 获取单元格信息

export function formatDate(headers, rowData) {
  return headers.value.reduce((acc, header, cloIndex) => {
    acc[header] = rowData.slice(1)[cloIndex]
    return acc
  }, {})
}

// 自定义排序函数，按照文件名中的数字部分进行数值排序
export function compareFilePaths(a, b) {
  const numA = parseInt(a.match(/\d+/)[0]) // 提取数字部分并转换为整数
  const numB = parseInt(b.match(/\d+/)[0]) // 提取数字部分并转换为整数
  return numA - numB
}
export const formartKey = (key) => {
  const newline = /\n/g
  const spaces = / /g
  let newKey = key

  if (newline.test(newKey)) {
    newKey = newKey.replace(newline, '')
  }
  if (spaces.test(newKey)) {
    newKey = newKey.replace(spaces, '')
  }
  return newKey
}

export const columns: BasicColumn[] = [
  {
    title: '*No',
    dataIndex: 'no',
    width: 150
  },
  {
    title: '*产品中英文品名',
    dataIndex: 'name',
    width: 150
  },
  {
    title: '*材质',
    dataIndex: 'material',
    width: 150
  },
  {
    title: '*产品数量',
    dataIndex: 'quantity',
    width: 150
  },
  {
    title: '*单位',
    dataIndex: 'unit',
    width: 150
  },
  {
    title: '产品尺寸（L*W*H）（cm）',
    dataIndex: 'size',
    children: [
      {
        title: '*长(cm)',
        dataIndex: 'goodsLength',
        width: 150
      },
      {
        title: '*宽(cm)',
        dataIndex: 'goodsWidth',
        width: 150
      },
      {
        title: '*高或厚度(cm)',
        dataIndex: 'goodsHeight',
        width: 150
      }
    ]
  },
  {
    title: '包装明细',
    dataIndex: 'packing',
    children: [
      {
        title: '*包装产品数量',
        dataIndex: 'packingQuantity',
        width: 150
      },
      {
        title: '*打包方式',
        dataIndex: 'method',
        width: 150
      },
      {
        title: '*包装尺寸长（cm）',
        dataIndex: 'packageLength',
        width: 150
      },
      {
        title: '*包装尺寸宽（cm）',
        dataIndex: 'packageWidth',
        width: 150
      },
      {
        title: '*包装尺寸高（cm）',
        dataIndex: 'packageHeight',
        width: 150
      },
      {
        title: '*体积（CBM）',
        dataIndex: 'volume',
        width: 150,
        customRender: ({ record }) => {
          return div(mul(mul(record.packageLength, record.packageWidth), record.packageHeight), 1000000, 6)
        }
      }
    ]
  },
  {
    title: '*毛重G.W（KGS）',
    dataIndex: 'weight',
    width: 150
  },
  {
    title: '*海关编码（HS CODE）',
    dataIndex: 'code',
    width: 150
  },
  {
    title: '备注/唛头',
    dataIndex: 'remark',
    width: 150
  }
]

export const validKey = [
  'no',
  'name',
  'material',
  // 'goodsLength',
  // 'goodsWidth',
  // 'goodsHeight',
  'unit',
  'quantity',
  // 'code',
  // 'remark',
  'packingQuantity',
  'unit',
  'method',
  'weight',
  'code',
  'packageWidth',
  'packageLength',
  'packageHeight',
  'request_id',
  // 'request_sub_id',
  'item_purchase_id',
  // 'item_purchase_sub_id',
  'purchase_work_ids',
  // 'supplier_strid',
  'type',
  'puid'
]

export const validNumberKey = [
  'quantity',
  'packingQuantity',
  'weight',
  'packageWidth',
  'packageLength',
  'packageHeight',
  'goodsWidth',
  'goodsLength',
  'goodsHeight'
]
