import { getWarehouse, getWMI } from '/@/api/baseData/warehouse'
import { BasicColumn, FormSchema } from '/@/components/Table'

async function getWMIFn(record) {
  console.log(record.warehouse_id)
  const { items } = record.warehouse_id ? await getWMI({ warehouse_id: record.warehouse_id }) : await getWMI()
  return items
}

export const columns: BasicColumn[] = [
  {
    title: '包裹ID',
    dataIndex: 'id',
    width: 150,
    ifShow: false
  },
  {
    title: '包裹号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '调入仓库',
    dataIndex: 'warehouse_id_allot',
    width: 350,
    resizable: true,
    edit: true,
    // editable: true,
    editComponent: 'PagingApiSelect',
    editComponentProps: ({ record }) => {
      return {
        api: getWarehouse,
        searchMode: true,
        pagingMode: true,
        resultField: 'items',
        searchParamField: 'name',
        editText: true,
        selectProps: {
          allowClear: true,
          fieldNames: { value: 'name', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          style: {
            minWidth: 'calc(100% - 100px)'
          },
          onChange(_, shall) {
            return (record.warehouse_id_new = shall.id)
          }
        }
      }
    }
  },
  {
    title: '仓库id',
    dataIndex: 'warehouse_id_new',
    ifShow: false
  },
  {
    title: '调入仓位',
    dataIndex: 'warehouse_item_id_allot',
    edit: true,
    // editable: true,
    editComponent: 'PagingApiSelect',
    editComponentProps: ({ record }) => {
      return {
        api: getWMIFn.bind(null, record),
        searchMode: true,
        pagingMode: true,
        resultField: 'items',
        searchParamField: 'name',
        editText: true,
        selectProps: {
          allowClear: true,
          fieldNames: { value: 'name', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          style: { minWidth: 'calc(100% - 100px)' }
        },
        onChange(_, shall) {
          return (record.warehouse_item_id_new = shall.id)
        }
      }
    }
  },
  {
    title: '仓库仓位id',
    dataIndex: 'warehouse_item_id_new',
    ifShow: false
  },
  {
    title: '原仓库',
    dataIndex: 'warehouse_name'
  },
  {
    title: '原仓库',
    dataIndex: 'warehouse_id',
    ifShow: false
  },
  {
    title: '原仓库仓位',
    dataIndex: 'warehouse_item_name'
  },
  {
    title: '原仓库仓位',
    dataIndex: 'warehouse_item_id',
    ifShow: false
  }
]
export const cleancolumns: BasicColumn[] = [
  {
    title: '作废备注',
    dataIndex: 'cancel_remark',
    resizable: true,
    edit: true,
    editComponent: 'Textarea',
    editComponentProps: {
      placeholder: '请输入作废备注',
      editText: true
    }
  },
  {
    title: '包裹ID',
    dataIndex: 'id',
    width: 300,
    ifShow: false
  },
  {
    title: '箱号',
    resizable: true,
    dataIndex: 'strid',
    width: 300
  },
  {
    title: '包装产品数量',
    dataIndex: 'quantity',
    width: 150,
    resizable: true
  },
  {
    title: '打包方式',
    dataIndex: 'method',
    width: 150,
    resizable: true
  },
  {
    title: '包裹长度（cm）',
    dataIndex: 'length',
    width: 150,
    resizable: true
  },
  {
    title: '包裹宽度（cm）',
    dataIndex: 'width',
    width: 150,
    resizable: true
  },
  {
    title: '包裹高度（cm）',
    dataIndex: 'height',
    width: 150,
    resizable: true
  },
  {
    title: '重量',
    dataIndex: 'weight',
    width: 150,
    resizable: true
  },
  {
    title: '体积',
    dataIndex: 'volume',
    width: 150,
    resizable: true
  },
  {
    title: '原仓库',
    dataIndex: 'warehouse_name'
  },
  {
    title: '原仓库仓位',
    dataIndex: 'warehouse_item_name'
  }
]
export const UploadSchemas: FormSchema[] = [
  {
    field: 'file',
    component: 'Upload',
    label: '上传文件',
    colProps: {
      span: 24
    },
    slot: 'Uploads'
  }
]
