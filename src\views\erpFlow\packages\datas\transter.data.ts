import { FormSchema } from '/@/components/Form'

export const transterchemas: FormSchema[] = [
  {
    label: '是否备货并退货',
    field: 'is_retreat',
    component: 'RadioButtonGroup',
    required: true,
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  },
  {
    label: '包裹合集',
    field: 'packageList',
    slot: 'PackageList',
    component: 'Input',
    required: true
  },

  {
    label: 'source_uniqid',
    field: 'source_uniqid',
    component: 'Input',
    show: false
  }
]
