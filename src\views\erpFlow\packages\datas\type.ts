export interface datatype {
  strid: string
  name: string
  //   file: string[]
  material: string
  length: number | string | null
  width: number
  height: number
  unit: string
  quantity: string
  packing_quantity: string
  packing_method: string
  package_dimensions: string
  kg: string
  volume: string
  code: string
  remark: string
}

export interface CellInfoType {
  rowId: string
  colId: string
  cell: string
  content: number | string | null
  rowspan: number
  colspan: number
  rowMergeDetail: string[]
  colMergeDetail: string[]
}
