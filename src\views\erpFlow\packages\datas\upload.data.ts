import { BasicColumn } from '/@/components/Table'

export const commonPackingInfokey = {
  '*no.': 'strid',
  '*产品中英文品名productname': 'name',
  '*产品图片productpicture': 'file',
  '*材质material': 'material',
  '*长length（cm）': 'length',
  '*宽width（cm）': 'width',
  '*高或厚度heithorthickness（cm）': 'height',
  '*单位unit': 'unit',
  '*产品数量quantity': 'quantity',
  '*海关编码hscode': 'code',
  '*备注remarks': 'remark'
}

// 获取单元格信息

export function formatDate(headers, rowData) {
  return headers.value.reduce((acc, header, cloIndex) => {
    acc[header] = rowData.slice(1)[cloIndex]
    return acc
  }, {})
}

// 自定义排序函数，按照文件名中的数字部分进行数值排序
export function compareFilePaths(a, b) {
  const numA = parseInt(a.match(/\d+/)[0]) // 提取数字部分并转换为整数
  const numB = parseInt(b.match(/\d+/)[0]) // 提取数字部分并转换为整数
  return numA - numB
}
export const formartKey = (key) => {
  const newline = /\n/g
  const spaces = / /g
  let newKey = key

  if (newline.test(newKey)) {
    newKey = newKey.replace(newline, '')
  }
  if (spaces.test(newKey)) {
    newKey = newKey.replace(spaces, '')
  }
  return newKey
}

export const columns: BasicColumn[] = [
  {
    title: '包裹号',
    dataIndex: 'strid',
    width: 150
  },
  {
    title: '产品中英文品名',
    dataIndex: 'name',
    width: 150
  },
  {
    title: '产品图片',
    dataIndex: 'file',
    width: 150
  },
  {
    title: '材质',
    dataIndex: 'material',
    width: 150
  },
  {
    title: '长(cm)',
    dataIndex: 'length',
    width: 150
  },
  {
    title: '宽(cm)',
    dataIndex: 'width',
    width: 150
  },
  {
    title: '高或厚度(cm)',
    dataIndex: 'height',
    width: 150
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 150
  },
  {
    title: '产品数量',
    dataIndex: 'quantity',
    width: 150
  },
  {
    title: '海关编码',
    dataIndex: 'code',
    width: 150
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 150
  }
]
