<template>
  <BasicModal @register="registerModal" v-bind="$attrs" title="编辑" width="80%" @ok="handleConfirm">
    <BasicForm @register="registerForm">
      <!--      <template #Imgs>-->
      <!--        <Upload-->
      <!--          v-model:file-list="imgFileList"-->
      <!--          accept="image/*"-->
      <!--          action="/api/oss/putImg2Stocking"-->
      <!--          list-type="picture-card"-->
      <!--          :custom-request="handleRequest"-->
      <!--          @remove="handleRemove"-->
      <!--        >-->
      <!--          <div>-->
      <!--            <plus-outlined />-->
      <!--            <div style="margin-top: 8px">Upload</div>-->
      <!--          </div>-->
      <!--        </Upload>-->
      <!--      </template>-->
    </BasicForm>
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { modalFormSchemas } from '/@/views/erpFlow/packings/datas/operate.datas'
import type { ModalMethods } from '/@/components/Modal'
import { omit } from 'lodash-es'
import { ref } from 'vue'
// import { Upload, UploadFile } from 'ant-design-vue'
// import { PlusOutlined } from '@ant-design/icons-vue'
// import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
// import { commonImgUpload } from '/@/api/commonUtils/upload'

const emits = defineEmits<{ (e: 'updateRecord', data: any, key: number): void; (e: 'register', $event: ModalMethods): void }>()
const key = ref<number>()
// const imgFileList = ref<UploadFile[]>([])
const [registerModal, { closeModal, changeOkLoading }] = useModalInner((data) => {
  key.value = data.record.value
  setFieldsValue(data.record)
})

const [registerForm, { setFieldsValue, validate }] = useForm({
  baseColProps: { span: 8 },
  showActionButtonGroup: false,
  labelCol: { style: 'width: 100px' },
  schemas: modalFormSchemas
})

async function handleConfirm() {
  try {
    await changeOkLoading(true)
    const values = await validate()
    emits('updateRecord', key.value, values.id ? values : omit(values, 'id'))
    await closeModal()
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  } finally {
    changeOkLoading(false)
  }
}
</script>
