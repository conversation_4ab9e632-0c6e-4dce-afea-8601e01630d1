<template>
  <div class="items-comp mb-5">
    <Card>
      <template #title>
        <span class="mr-2">
          {{ `包裹号：${props.pkgItem?.strid || '-'}` }}
        </span>
        <CloseSquareTwoTone style="font-size: 18px" @click="emits('remove', props.itemKey)" />
      </template>
      <BasicForm @register="registerForm" :model="props.pkgItem" @field-value-change="handleUpdateForm">
        <template #Items>
          <FormItemRest>
            <!--          <Button size="small" type="primary">添加产品</Button>-->
            <!--          <TreeSelect :tree-data="outWarehouseItemOptions" treeCheckable @change="handleTreeSelect" />-->
            <BasicTable class="mt-2" @register="registerTable">
              <template #bodyCell="{ record, column }">
                <template v-if="column.dataIndex === 'action'">
                  <TableAction :actions="createActions(record)" />
                </template>
                <!--                <template v-if="column.dataIndex === 'relateStocking'">-->
                <!--                  <TreeSelect-->
                <!--                    v-model:value="record.itemOutId"-->
                <!--                    :tree-data="outWarehouseItemOptions"-->
                <!--                    :fieldNames="{ children: 'children', value: 'itemOutId', label: 'label' }"-->
                <!--                    treeDefaultExpandAll-->
                <!--                    @change="(val) => handleStockingTreeSelect(val, record)"-->
                <!--                    @dropdown-visible-change="handleDropdownVisibleChange"-->
                <!--                  />-->
                <!--                </template>-->
              </template>
            </BasicTable>
          </FormItemRest>
        </template>
      </BasicForm>
    </Card>
    <ItemModal @register="registerEditModal" @update-record="handleUpdateRecord" />
  </div>
</template>

<script setup lang="ts">
import { BasicForm, useForm } from '/@/components/Form'
import { Form, Card } from 'ant-design-vue'
// import { Form, TreeSelect, Card } from 'ant-design-vue'
import {
  // getOutWarehouseItems,
  getPkgFormSchemas,
  // mapOutWarehouseItem,
  productColumns
  // outWarehouseItemOptions
} from '../datas/operate.datas'
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import ItemModal from '/@/views/erpFlow/packings/components/ItemModal.vue'
import { useModal } from '/@/components/Modal'
import { computed, nextTick, onMounted } from 'vue'
import { PackagePageOperateTypeEnum } from '/@/enums/packageEnum'
import { useRoute } from 'vue-router'
import { cloneDeep } from 'lodash-es'
import { CloseSquareTwoTone } from '@ant-design/icons-vue'
// import ApiTreeSelect from '/@/components/Form/src/components/ApiTreeSelect.vue'

const props = withDefaults(
  defineProps<{
    pkgItem: any
    itemKey: string | null
  }>(),
  {
    pkgItem: {},
    itemKey: null
  }
)

onMounted(() => {
  nextTick(async () => {
    await setFieldsValue(cloneDeep(props.pkgItem))
    setTableData(cloneDeep(props.pkgItem.items))
  })
  // setTableData(props.pkgItem.items)
})

const route = useRoute()
const operateType = computed<PackagePageOperateTypeEnum>(() => route.params.type as PackagePageOperateTypeEnum)
const [registerEditModal, { openModal }] = useModal()
// const emits = defineEmits<{ editTableData: (record: any) => void }>()
const emits = defineEmits<{ (e: 'update:pkgItem', value: any): void; (e: 'remove', key: number): void }>()

const FormItemRest = Form.ItemRest
const [registerForm, { setFieldsValue, getFieldsValue, validate }] = useForm({
  labelWidth: 100,
  baseColProps: { span: 8, style: 'padding: 0 10px' },
  actionColOptions: { span: 24 },
  schemas: getPkgFormSchemas(operateType.value),
  showActionButtonGroup: false,
  layout: 'vertical'
})

const [registerTable, { setTableData, updateTableDataRecord, deleteTableDataRecord }] = useTable({
  showIndexColumn: false,
  inset: true,
  pagination: false,
  maxHeight: 400,
  rowKey: 'value',
  columns: productColumns,
  scroll: { y: 300, x: '100%' },
  size: 'small'
  // actionColumn: {
  //   width: 150,
  //   title: '操作',
  //   dataIndex: 'action',
  //   fixed: 'right'
  // }
})

// function handleTreeSelect(value: number[]) {
//   const tableDataKey = getDataSource().map((item) => item.value)
//   if (tableDataKey.length > value.length) {
//     // 删除项
//     const removeKeys = difference(tableDataKey, value)
//     deleteTableDataRecord(removeKeys)
//   } else {
//     // 增加项
//     const addList = difference(value, tableDataKey)
//     addList
//       .map((val) => mapOutWarehouseItem.value[val])
//       .forEach((record) => insertTableDataRecord({ ...commonProductDetail, ...record, imgs: isArray(record.imgs) ? record.imgs : [] }))
//   }
//   setFieldsValue({ items: getDataSource() })
//   const pkgItem = {}
//   // 避免更新了空值触发了错误验证
//   for (const key of Object.keys(props.pkgItem)) {
//     if (props.pkgItem[key]) pkgItem[key] = props.pkgItem[key]
//   }
//   emits('update:pkgItem', { ...pkgItem, items: getDataSource() })
// }

// function handleStockingTreeSelect(value, record) {
//   const option = mapOutWarehouseItem.value[value]
//   handleUpdateRecord(record.value, Object.assign(record, { requestId: option.request_id, itemOutId: option.itemOutId }))
//   handleUpdateForm()
// }

function createActions(record) {
  return [
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record)
    },
    {
      label: '删除',
      onClick: handleDel.bind(null, record)
    }
  ]
}

function handleDel(record) {
  deleteTableDataRecord(record.value)
  const formData = getFieldsValue()
  // 如果是编辑，并且record有id，即是已经在数据库中，需要将type改成3
  // 如果是新增，将表格中的这条数据和表单中的这条数据一起删掉
  if (record.id && operateType.value === PackagePageOperateTypeEnum.EDIT) {
    for (const formRecord of formData.items) {
      // 找到form表单中需要删除的数据，将这条数据的type变成3：删除
      if (record.value === formRecord.value) {
        formRecord.type = 3
      }
    }
  } else {
    const idx = formData.items.findIndex((formRecord) => record.value === formRecord.value)
    formData.items.splice(idx, 1)
    setFieldsValue(formData)
  }
  // 更新父组件数据
  emits('update:pkgItem', formData)
}

function handleEdit(record) {
  openModal(true, { record })
}

function handleUpdateRecord(key, record) {
  updateTableDataRecord(key, record)
  const formData = getFieldsValue()
  const idx = formData.items.findIndex((formRecord) => key === formRecord.value)
  formData.items.splice(idx, 1, Object.assign(formData.items[idx], record))
  setFieldsValue(formData)
  handleUpdateForm()
}

function handleUpdateForm() {
  emits('update:pkgItem', getFieldsValue())
}

// function handleDropdownVisibleChange(open: boolean) {
//   if (open) getOutWarehouseItems()
// }

defineExpose({
  validate,
  itemKey: props.itemKey
})
</script>
