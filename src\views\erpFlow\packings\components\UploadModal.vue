<template>
  <BasicModal @register="register" title="附件上传" @ok="handleOk" width="1200px">
    <BasicForm @register="registerform">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template></BasicForm
    >
    <BasicTable @register="registertable" :search-info="{ work_id: init_id }">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'files'">
          <div
            v-for="(newVal, index) in record.files"
            :key="index"
            style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px"
          >
            <a-button
              type="primary"
              size="small"
              @click="handleDownload(newVal.url, newVal.name ?? `附件${index + 1}`)"
              style="margin-left: 8px"
            >
              <download-outlined />
              下载
            </a-button>
            <a :href="newVal" target="_blank" @click="handlePreview(newVal.url, $event)">{{ newVal.name ?? `附件${index + 1}` }}</a>
          </div>
        </template>
      </template>
    </BasicTable>
    <PreviewFile @register="registerpreModal" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { UploadSchemas } from '../datas/model'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { UploadFile, Upload, message } from 'ant-design-vue'
import { ref, watch } from 'vue'
import { PlusOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import { BasicTable, useTable } from '/@/components/Table'
import { createImgPreview } from '/@/components/Preview'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { nextTick } from 'vue'
import { packinggetPackingFilesList, packinguploadPackingFiles } from '/@/api/erpFlow/packings'

//id
const init_id = ref()
const [registerpreModal, { openModal }] = useModal()

const [register, { closeModal, changeOkLoading, changeLoading }] = useModalInner(async (data) => {
  console.log(data)
  filesList.value = []
  init_id.value = data.id
  nextTick(async () => {
    setLoading(true)
    const { items } = await packinggetPackingFilesList({ packing_id: data.id })
    setTableData(items)
    setLoading(false)
  })
})
const [registerform, { setFieldsValue, getFieldsValue }] = useForm({
  schemas: UploadSchemas,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelCol: { span: 5 }
})

const [registertable, { setTableData, setLoading }] = useTable({
  showIndexColumn: false,
  title: '附件列表',
  columns: [
    {
      title: '上传人',
      dataIndex: 'person_name',
      width: 100,
      resizable: true
    },
    {
      title: '上传时间',
      dataIndex: 'created_at',
      width: 100,
      resizable: true
    },
    {
      title: '附件',
      dataIndex: 'files',
      width: 100,
      resizable: true
    }
  ],
  pagination: false,
  canResize: false
})

//附件
const filesList = ref<UploadFile[]>([])

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({
      files: val.map((item) => {
        return {
          url: item.url,
          name: item.name
        }
      })
    })
  }
)

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    console.log(file)
    changeOkLoading(true)
    const curFile = filesList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({
      files: filesList.value.map((item) => {
        return {
          url: item.url,
          name: item.name
        }
      })
    })
    // 判断是否所有的文件都已经上传成功，如果有uploading状态的就不修改按钮状态
    const isAllDone = filesList.value.every((item) => item.url)
    if (isAllDone) {
      changeOkLoading(false)
    }
  } catch (err) {
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}

const emit = defineEmits(['success', 'register'])
// 提交
async function handleOk() {
  try {
    await changeOkLoading(true)
    const formdata = await getFieldsValue()
    console.log(filesList.value)
    const params = {
      id: init_id.value,
      files: formdata.files
    }
    const res: any = await packinguploadPackingFiles(params)
    console.log(res)

    if (res.news == 'success') {
      emit('success')
      await closeModal()
      message.success('附件上传成功')
      filesList.value = []
    }
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  } finally {
    changeOkLoading(false)
  }
}

async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return message.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]

  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}

// 下载文件
async function handleDownload(fileUrl: string, fileName: string) {
  if (!fileUrl) {
    message.error('没有找到文件路径，请联系管理员')
    return
  }

  try {
    changeLoading(true)
    // 方法1：尝试使用fetch下载
    const response = await fetch(fileUrl)
    if (!response.ok) {
      throw new Error('网络请求失败')
    }

    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)

    // 创建下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理URL对象
    window.URL.revokeObjectURL(url)
    changeLoading(false)
    message.success('文件下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    changeLoading(false)

    // 备用方案：直接打开链接
    try {
      window.open(fileUrl, '_blank')
      message.info('已在新窗口打开文件，请手动保存')
    } catch (fallbackError) {
      message.error('下载失败，请联系管理员')
    }
  }
}
</script>
