<template>
  <BasicDrawer @register="registerDrawer" width="50%" title="申请列表" :show-footer="hasPermission(523)">
    <template #footer>
      <Button type="primary" class="m-2" @click="handleok" v-if="hasPermission(523) && buttonshow" :loading="buttonlodaing"
        >特批通过</Button
      >
    </template>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'approved_files'">
          <TableImg :imgList="record.approved_files" :simpleShow="true" />
        </template>
      </template>
    </BasicTable>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { BasicTable, useTable, TableImg } from '/@/components/Table'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { getApplyApprovedList, setApprovedStatus } from '/@/api/erpFlow/packings'
import { ref } from 'vue'
import { Button } from 'ant-design-vue'
import { usePermission } from '/@/hooks/web/usePermission'

const packing_id = ref()
const buttonlodaing = ref(false)
const buttonshow = ref()
const { hasPermission } = usePermission()
const emit = defineEmits(['success'])

const [registerDrawer, { closeDrawer }] = useDrawerInner(async (data) => {
  packing_id.value = data.id
  const { items } = await getApplyApprovedList({ packing_id: data.id })
  buttonshow.value = data.approved_status == 1
  setTableData(items)
})
const [registerTable, { setTableData }] = useTable({
  showIndexColumn: false,
  showTableSetting: false,
  columns: [
    {
      title: '装箱单号',
      dataIndex: 'picking_strid',
      width: 120
    },
    {
      title: '申请人名称',
      dataIndex: 'creator_name',
      width: 120
    },
    {
      title: '申请时间',
      dataIndex: 'created_at'
    },
    {
      title: '申请备注',
      dataIndex: 'remark'
    },
    {
      title: '申请附件',
      dataIndex: 'approved_files'
    }
  ]
})
function handleok() {
  try {
    buttonlodaing.value = true
    setApprovedStatus({ ids: [{ id: packing_id.value, approved_status: 2 }] })
    closeDrawer()
    emit('success')
    buttonlodaing.value = false
  } catch (e) {
    console.log(e)
  }
}
</script>
