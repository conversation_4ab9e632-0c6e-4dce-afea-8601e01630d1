<template>
  <BasicModal @register="registerModal" title="特批申请" :width="1000" :min-height="300" @ok="handleOk">
    <BasicForm @register="registerForm">
      <template #upload>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { ref, watch } from 'vue'
import { message, UploadFile, Upload } from 'ant-design-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { PlusOutlined } from '@ant-design/icons-vue'
import { applyApproved } from '/@/api/erpFlow/packings'
const emit = defineEmits(['success'])

const [registerModal, { changeOkLoading, closeModal }] = useModalInner((data) => {
  resetFields()
  setFieldsValue({ id: data.id })
})
//
const [registerForm, { setFieldsValue, getFieldsValue, resetFields }] = useForm({
  labelAlign: 'left',
  colon: true,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelWidth: 110,
  schemas: [
    {
      field: 'id',
      label: 'id',
      component: 'Input',
      show: false
    },
    {
      field: 'remark',
      label: '申请备注',
      component: 'InputTextArea',
      required: true,
      componentProps: {
        type: 'textarea',
        rows: 4,
        placeholder: '请输入申请备注'
      }
    },
    {
      field: 'approved_files',
      label: '申请附件',
      component: 'Upload',
      slot: 'upload'
    }
  ]
})

//附件
const filesList = ref<UploadFile[]>([])

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ approved_files: val.map((item) => item.url) })
  }
)

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    console.log(file)
    changeOkLoading(true)
    const curFile = filesList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({
      approved_files: filesList.value.map((item) => item.url)
    })
    // 判断是否所有的文件都已经上传成功，如果有uploading状态的就不修改按钮状态
    const isAllDone = filesList.value.every((item) => item.url)
    if (isAllDone) {
      changeOkLoading(false)
    }
  } catch (err) {
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}

async function handleOk() {
  try {
    await changeOkLoading(true)
    const formdata = await getFieldsValue()
    await applyApproved(formdata)

    emit('success')
    await closeModal()
    filesList.value = []
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  } finally {
    changeOkLoading(false)
  }
}
</script>
