<template>
  <div v-loading="loading" class="m-4 bg-white p-4 basic-info">
    <!--    <div v-if="operateType === PackagePageOperateTypeEnum.ADD" class="p-4">-->
    <!--      <Steps :current="stepValue">-->
    <!--        <Step title="上传装箱文件" />-->
    <!--        <Step title="确定装箱信息" />-->
    <!--      </Steps>-->
    <!--    </div>-->
    <!--    <div v-if="stepValue === 0">-->
    <!--      <div class="mb-2">-->
    <!--        <Alert-->
    <!--          type="warning"-->
    <!--          message="上传的文件请确保同一个包裹的产品使用 “合并单元格” 方式，否则默认会将产品分拆成不同包裹的产品"-->
    <!--          show-icon-->
    <!--        />-->
    <!--      </div>-->
    <!--      <UploadDragger :beforeUpload="handleImport" :showUploadList="false">-->
    <!--        <p class="ant-upload-drag-icon">-->
    <!--          <InboxOutlined />-->
    <!--        </p>-->
    <!--        <p class="ant-upload-text">上传装箱文件</p>-->
    <!--      </UploadDragger>-->
    <!--    </div>-->
    <!--    <div v-if="stepValue === 1">-->
    <Tabs :key="stepValue" v-model:activeKey="activeKey" class="min-h-60vh" type="card">
      <template #rightExtra>
        <Popover placement="leftTop" trigger="click">
          <template #content>
            <PagingApiSelect
              v-model:value="selectPackagesList"
              placeholder="请选择包裹，输入销售单号可查出对应的未装箱包裹"
              mode="multiple"
              resultField="items"
              :api="
                (params) =>
                  getPackageList({
                    ...params,
                    is_old: 0,
                    is_packing: 0,
                    is_out: 0,
                    is_cancel: 0,
                    is_retreat: 0,
                    is_stock: 0,
                    is_scrap: 0,
                    project_number: details.project_number
                  })
              "
              :searchMode="true"
              :pagingMode="true"
              :pagingSize="20"
              searchParamField="source_uniqid"
              :selectProps="{
                class: 'w-[300px]',
                allowClear: true,
                fieldNames: { value: 'id', label: 'strid' },
                showSearch: true,
                placeholder: '请选择'
              }"
            />
            <a-button class="ml-2" type="primary" @click="handleAddPackage">确定</a-button>
          </template>
          <!--          <a-button type="primary">添加包裹</a-button>-->
        </Popover>
      </template>
      <TabPane :key="0" tab="基础信息" :forceRender="true">
        <BasicForm @register="registerForm">
          <template #StockImgs="{}">
            <!-- <Badge :count="model.stock_imgs && model.stock_imgs.length">
              <Image
                v-if="model.stock_imgs && model.stock_imgs.length > 0"
                :preview="{ visible: false }"
                :width="200"
                :src="model.stock_imgs && model.stock_imgs[0]"
                @click="visiblestocking = true"
              />
              <Image v-else :width="200" src="https://img.gbuilderchina.com/erp/purchase/20241018/17296844576711fb7d3ba2d260864962.gif" />
              <div style="display: none">
                <ImagePreviewGroup :preview="{ visible: visiblestocking, onVisibleChange: (vis) => (visiblestocking = vis) }">
                  <Image v-for="item in model.stock_imgs" :key="item" :src="item" width="100" />
                </ImagePreviewGroup>
              </div>
            </Badge> -->
            <Upload
              v-model:file-list="StockImgs"
              action="/api/oss/putImg2Stocking"
              :custom-request="handleFileRequest"
              list-type="picture-card"
              :multiple="true"
              :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
            >
              <div v-if="operateType !== 'detail'">
                <plus-outlined />
                <div style="margin-top: 8px">Upload</div>
              </div>
            </Upload>
          </template>
          <template #Imgs="{}">
            <!-- <Badge :count="model.imgs && model.imgs.length">
              <Image
                v-if="model.imgs && model.imgs.length > 0"
                :preview="{ visible: false }"
                :width="200"
                :src="model.imgs && model.imgs[0]"
                @click="visibleimgs = true"
              />
              <Image v-else :width="200" src="https://img.gbuilderchina.com/erp/purchase/20241018/17296844576711fb7d3ba2d260864962.gif" />
              <div style="display: none">
                <ImagePreviewGroup :preview="{ visible: visibleimgs, onVisibleChange: (vis) => (visibleimgs = vis) }">
                  <Image v-for="item in model.imgs" :key="item" :src="item" width="100" />
                </ImagePreviewGroup>
              </div>
            </Badge> -->
            <Upload
              v-model:file-list="Imgs"
              action="/api/oss/putImg2Stocking"
              :custom-request="handleFileRequestimgs"
              list-type="picture-card"
              :multiple="true"
              :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
            >
              <div v-if="operateType !== 'detail'">
                <plus-outlined />
                <div style="margin-top: 8px">Upload</div>
              </div>
            </Upload>
          </template>
        </BasicForm>
      </TabPane>
    </Tabs>
    <!--    </div>-->
    <div class="text-right mt-4">
      <Button
        v-if="stepValue === 0"
        type="primary"
        href="https://img.gbuilderchina.com/erp/packages/20240104/170525901665965b7191f28584656694.xlsx"
        target="_blank"
        >下载模板文件</Button
      >
      <Button
        v-if="[PackagePageOperateTypeEnum.ADD, PackagePageOperateTypeEnum.EDIT].includes(operateType) && stepValue === 1"
        type="primary"
        @click="handleSubmit"
        >提交</Button
      >
      <Button v-if="operateType === PackagePageOperateTypeEnum.ADD && stepValue === 1" type="default" class="ml-4" @click="handlePrev"
        >重新上传</Button
      >
      <Button type="default" class="ml-4" @click="handleBack(router)">返回</Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { BasicForm, PagingApiSelect, useForm } from '/@/components/Form'
// import { BasicTable, useTable } from '/@/components/Table'
import { Tabs, TabPane, Button, Popover, Upload, UploadFile } from 'ant-design-vue'
// import { InboxOutlined } from '@ant-design/icons-vue'
import { getFormSchemas, handleBack } from '../datas/operate.datas'
import { omit, difference } from 'lodash-es'
import { pageEventBus } from '/@/views/erpFlow/packings/datas/datas'
import { useMessage } from '/@/hooks/web/useMessage'
// import * as XLSX from 'xlsx'
// import { WorkSheet } from 'xlsx'
import { useRoute, useRouter } from 'vue-router'
import { PackagePageOperateTitleEnum, PackagePageOperateTypeEnum } from '/@/enums/packageEnum'
import { getPackingDetail, updatePackage } from '/@/api/erpFlow/packings'
import { useTabs } from '/@/hooks/web/useTabs'
import { getPackageDetail } from '/@/api/erpFlow/packages'
import { PlusOutlined } from '@ant-design/icons-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'

const router = useRouter()
// const UploadDragger = Upload.Dragger
const loading = ref<boolean>(false)
const operateType = computed<PackagePageOperateTypeEnum>(() => route.params.type as PackagePageOperateTypeEnum)
const operateId = computed(() => route.query.id)
const selectPackagesList = ref([])
const details = ref({})
// const visiblestocking = ref(false)
// const visibleimgs = ref(false)
//备货图
const StockImgs = ref<UploadFile[]>([])
//装修图
const Imgs = ref<UploadFile[]>([])
//附件
watch(
  () => StockImgs.value,
  async (val) => {
    await setFieldsValue({ stock_imgs: val.map((item) => item.url) })
  }
)

watch(
  () => Imgs.value,
  async (val) => {
    await setFieldsValue({ imgs: val.map((item) => item.url) })
  }
)
onMounted(async () => {
  const { setTitle } = useTabs(router)
  setTitle(PackagePageOperateTitleEnum[operateType.value])
  try {
    if (!operateId.value) {
      return
    }
    if ([PackagePageOperateTypeEnum.EDIT, PackagePageOperateTypeEnum.DETAIL].includes(operateType.value)) {
      loading.value = true
      stepValue.value = 1
      const { items } = await getPackingDetail({ id: operateId.value })
      console.log(items)
      details.value = items
      approved_status.value = items.approved_status
      // jsonData.value = omit({ ...items, pkgList: items.packages, shipmentAddr: items.shipment_addr, shipmentAt: items.shipment_at }, [
      jsonData.value = omit({ ...items, shipmentAddr: items.shipment_addr, shipmentAt: items.shipment_at }, ['packages'])
      await nextTick(async () => {
        await setFieldsValue(omit(jsonData.value, ['pkgList']))
        // for (const item of jsonData.value.pkgList) {
        //   pkgList.value[pkgTabKey.value] = Object.assign(item, {
        //     items: item.items.map((product) =>
        //       isArray(product.imgs)
        //         ? Object.assign(product, { value: random(1, 100000000), itemOutId: product.item_out_id, requestId: product.request_id })
        //         : Object.assign(product, {
        //             imgs: [],
        //             value: random(1, 100000000),
        //             itemOutId: product.item_out_id,
        //             requestId: product.request_id
        //           })
        //     )
        //   })
        //   pkgTabKey.value += 1
        // }
        console.log(jsonData.value)

        StockImgs.value = jsonData.value?.stock_imgs?.map((file) => ({ name: file, url: file, uid: Math.random() * 100000 })) ?? []
        Imgs.value = jsonData.value?.imgs?.map((file) => ({ name: file, url: file, uid: Math.random() * 100000 })) ?? []
        // activePkgKey.value = Object.keys(pkgList.value)[0]
      })
    }
    loading.value = false
  } catch (e) {
    loading.value = false
    throw new Error(`${e}`)
  }
})

const route = useRoute()
const stepValue = ref(0)
const { createMessage } = useMessage()
const pkgTabKey = ref(1)
const activeKey = ref(0)
const jsonData = ref({})
// const removePkg = ref<any[]>([])
const approved_status = ref(0)
// const pkgList = ref({
//   [pkgTabKey.value]: {
//     ...commonPackingItem,
//     items: []
//   }
// })

const pkgList = ref({})
// const activePkgKey = ref<string>(Object.keys(pkgList.value)[0])

const [registerForm, { validate, setFieldsValue }] = useForm({
  labelWidth: 80,
  baseColProps: { span: 12 },
  actionColOptions: { span: 24 },
  schemas: getFormSchemas(operateType.value),
  showActionButtonGroup: false
})

// function handleAddPackage() {
//   pkgTabKey.value += 1
//   pkgList.value[pkgTabKey.value] = {
//     ...commonPackingItem,
//     items: []
//   }
//   activePkgKey.value = pkgTabKey.value.toString()
// }

async function handleAddPackage() {
  try {
    loading.value = true
    if (selectPackagesList.value.length === 0) return createMessage.error('请选择需要添加的包裹！')
    const intersectionItem = Object.values(pkgList.value).find((pkg) => {
      return selectPackagesList.value.includes(pkg.id)
    })

    if (intersectionItem) {
      return createMessage.error(`包裹号：${intersectionItem.strid}已存在包裹中，请移除后重新点击`)
    }

    const { items } = await getPackageDetail({ ids: selectPackagesList.value })
    for (const packages of items) {
      pkgList.value[pkgTabKey.value] = {
        ...packages
        // items: []
      }
      pkgTabKey.value += 1
    }
    selectPackagesList.value = []
  } catch (err) {
    createMessage.error('获取包裹详情失败')
  } finally {
    loading.value = false
  }
}

// const onEdit = (targetKey: number, action: string) => {
//   action === 'add' ? handleAddPackage() : remove(targetKey as unknown as string)
// }

// function remove(targetKey: string) {
//   if (Object.keys(pkgList.value).length <= 1) return createMessage.error('至少要有一个包裹')
//   // 有包裹id并且是编辑操作
//   if (operateType.value === PackagePageOperateTypeEnum.EDIT && pkgList.value[targetKey]['id']) {
//     // 将删除的包裹放进删除数组中，等提交时一起提交过去
//     removePkg.value.push(
//       Object.assign(pkgList.value[targetKey], {
//         type: PackageOperateTypeEnum.remove,
//         items: pkgList.value[targetKey]['items'].map((item) => ({ ...item, type: 3 }))
//       })
//     )
//   }
//   pkgList.value = omit(pkgList.value, targetKey)
//
//   nextTick(() => {
//     activePkgKey.value = Object.keys(pkgList.value)[0]
//   })
// }

async function handleSubmit() {
  let packageInfo = {}
  loading.value = true
  try {
    packageInfo = await validate()
    // if (isEmpty(pkgList.value)) return createMessage.error('包裹详情缺失，请重新上传！')
    // for (const pkgItem of pkgTabEl.value) {
    //   await pkgItem.validate()
    // }
    const params = {
      ...packageInfo
      // packingList: Object.values(pkgList.value)
      //   .map((item) => ({
      //     ...item,
      //     type: PackageOperateTypeEnum[route.params.type as 'add' | 'edit'],
      //     items: item.items.map((product) => ({
      //       ...product,
      //       type:
      //         product.type === PackageOperateTypeEnum.remove ? product.type : PackageOperateTypeEnum[route.params.type as 'add' | 'edit'],
      //       size: { height: product.height, length: product.height, width: product.width },
      //       remark: isString(product.remark) ? product.remark : `${product.remark}`
      //     }))
      //   }))
      //   .concat(removePkg.value)
    }
    // if (operateType.value === PackagePageOperateTypeEnum.EDIT && operateId.value) {
    //   params['id'] = +operateId.value
    // }
    // console.log(params)
    const { msg } = await updatePackage(params)
    if (msg === 'success') {
      pageEventBus.emit('package-table-reload')
      createMessage.success('装箱单更新成功')
      // go({ path: '/erp/packages', isReplace: true })
      await handleBack(router)
    }
    loading.value = false
  } catch (e) {
    console.log(e)
    if (e?.errorFields) {
      const errFields = e.errorFields.map((item) => item.name[0])
      if (difference(['buyer', 'country', 'shipmentAddr', 'shipmentAt', 'supplier'], errFields).length === 0) {
        createMessage.error('请填写装箱单的基础信息')
      } else if (difference(['method', 'quantity', 'size', 'volume', 'weight'], errFields).length === 0) {
        createMessage.error('请填写装箱单的包裹信息')
      } else if (errFields.includes('items')) {
        createMessage.error('请填写装箱单的包裹产品明细')
      }
    }
    loading.value = false
    throw new Error(`${e}`)
  }
}

// async function handleImport(file: File) {
//   try {
//     const isXlsx = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
//     if (!isXlsx) {
//       createMessage.error('请上传Excel格式的文件')
//       return false
//     }
//     const data = await file.arrayBuffer()
//
//     const wb = XLSX.read(data)
//     const ws: WorkSheet = wb.Sheets[wb.SheetNames[0]]
//
//     // 处理单元格信息
//     const fileData = generateExcelInfo(ws)
//     rawCellData.value = fileData
//     // 单元格数据转成json对象
//     jsonData.value = genderPackingInfo(fileData)
//     console.log(jsonData.value)
//     if (jsonData.value.pkgList.length === 0) {
//       jsonData.value = {}
//       return createMessage.error('请上传装箱单的包裹信息')
//     }
//     stepValue.value += 1
//     await nextTick(async () => {
//       await setFieldsValue(omit(jsonData.value, ['pkgList']))
//       for (const item of jsonData.value.pkgList) {
//         pkgList.value[pkgTabKey.value] = Object.assign(item, {
//           items: item.items.map((product) =>
//             isArray(product.imgs)
//               ? Object.assign(product, { value: random(1, 100000000) })
//               : Object.assign(product, { imgs: [], value: random(1, 100000000) })
//           )
//         })
//         pkgTabKey.value += 1
//       }
//       activePkgKey.value = Object.keys(pkgList.value)[0]
//     })
//
//     console.log(pkgList.value)
//   } catch (error) {
//     throw new Error(`${error}`)
//   }
//   return false
// }

function handlePrev() {
  stepValue.value -= 1
  pkgTabKey.value = 1
  jsonData.value = {}
  activeKey.value = 0
  pkgList.value = []
  selectPackagesList.value = []
  loading.value = false
}

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  StockImgs.value = StockImgs.value!.map((item) => {
    return item.url || (item.status === 'done' && item.response)
      ? {
          url: (item.url as string) || (item.response as string),
          uid: item.uid,
          name: item.name
        }
      : item
  })
  await setFieldsValue({
    stock_imgs: StockImgs.value.map((item) => item.url)
  })
}
async function handleFileRequestimgs({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  Imgs.value = Imgs.value!.map((item) => {
    return item.url || (item.status === 'done' && item.response)
      ? {
          url: (item.url as string) || (item.response as string),
          uid: item.uid,
          name: item.name
        }
      : item
  })
  await setFieldsValue({
    imgs: Imgs.value.map((item) => item.url)
  })
}
</script>

<style lang="less" scoped>
.basic-info {
  ::v-deep(.ant-picker.ant-picker-default) {
    width: 100%;
  }
}
</style>
