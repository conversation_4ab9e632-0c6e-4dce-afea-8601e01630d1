import * as XLSX from 'xlsx'
import { ref } from 'vue'
import { cloneDeep, range, set, isNumber, isString } from 'lodash-es'
import { commonCellStyle, exportCellStyle, mergeInfo } from './excel.template'
import { WorkSheet } from 'xlsx'
import { CellInfoType, PackageList, PackageOrder, ProductItem } from '/@/views/erpFlow/packings/datas/types'
import { BasicColumn, FormSchema } from '/@/components/Table'
import mitt from '/@/utils/mitt'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { columns as packageColumns } from '/@/views/erpFlow/packages/datas/datas'
import { useI18n } from '/@/hooks/web/useI18n'
import { GET_STATUS_SCHEMA } from '/@/const/status'
import ExcelJS from 'exceljs'
import { getStaffList } from '/@/api/erp/systemInfo'

const { tm } = useI18n()

export const pageEventBus = mitt()

export const mapStatus: { [key: number]: { value: string; color: 'error' | 'warning' | 'processing' | 'success' } } = {
  0: { value: '待装箱', color: 'error' },
  1: { value: '已装箱', color: 'processing' },
  2: { value: '已出货', color: 'success' }
}
export const approvedstatus: { [key: number]: { value: string; color: 'error' | 'warning' | 'processing' | 'success' | 'orange' } } = {
  0: { value: '未申请', color: 'processing' },
  1: { value: '未审批', color: 'orange' },
  2: { value: '已审批', color: 'success' },
  3: { value: '审批驳回', color: 'error' }
}
const iscancel = {
  0: { value: '否', color: 'orange' },
  1: { value: '是', color: 'error' }
}
export function getPath(type: 'add' | 'edit' | 'detail') {
  return `packings/operate/${type}`
}

const colPackingKeys = [
  {
    colId: 'A',
    field: 'no'
  },
  {
    colId: 'J',
    field: 'quantity'
  },
  {
    colId: 'K',
    field: 'method'
  },
  {
    colId: 'L',
    field: 'size'
  },
  {
    colId: 'M',
    field: 'weight'
  },
  {
    colId: 'N',
    field: 'volume'
  }
]

const colProductKeys = [
  {
    colId: 'B',
    field: 'name'
  },
  {
    colId: 'C',
    field: 'puid'
  },
  {
    colId: 'D',
    field: 'material'
  },
  {
    colId: 'E',
    field: 'length'
  },
  {
    colId: 'F',
    field: 'width'
  },
  {
    colId: 'G',
    field: 'height'
  },
  {
    colId: 'H',
    field: 'unit'
  },
  {
    colId: 'I',
    field: 'quantity'
  },
  {
    colId: 'O',
    field: 'code'
  },
  {
    colId: 'P',
    field: 'remark'
  }
]
export const rawCellData = ref<any>({})
export const columns = [
  {
    title: '装箱单号',
    dataIndex: 'strid',
    width: 250,
    resizable: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 150,
    resizable: true,
    customRender: ({ text }) => (mapStatus[text] ? useRender.renderTag(mapStatus[text]?.value, mapStatus[text]?.color) : text)
  },
  {
    title: '紧急程度',
    dataIndex: 'urgent_level',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      const mapLevel = tm(`tag.mapUrgentLevel.${text}`)
      return mapLevel ? useRender.renderTag(mapLevel?.alias, mapLevel?.color) : text
    }
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 150,
    resizable: true
  },
  // {
  //   title: '是否已出库',
  //   dataIndex: 'is_out',
  //   width: 150,
  //   resizable: true,
  //   customRender: ({ text }) => {
  //     const mapIsOut = tm(`tag.tagColor`)
  //     return mapIsOut?.[text] ? useRender.renderTag(mapIsOut?.[text]?.label, mapIsOut?.[text]?.color) : text
  //   }
  // },
  {
    title: '出库特批',
    dataIndex: 'approved_status',
    width: 150,
    resizable: true,
    customRender: ({ text }) =>
      approvedstatus[text] ? useRender.renderTag(approvedstatus[text]?.value, approvedstatus[text]?.color) : text
  },
  {
    title: '是否作废',
    dataIndex: 'is_cancel',
    width: 150,
    resizable: true,
    customRender: ({ text }) => (approvedstatus[text] ? useRender.renderTag(iscancel[text]?.value, iscancel[text]?.color) : text)
  },
  {
    title: '项目ID',
    dataIndex: 'project_number',
    width: 150,
    resizable: true
  },
  {
    title: '项目名称',
    dataIndex: 'project_name',
    width: 200,
    resizable: true
  },
  {
    title: '装箱日期',
    dataIndex: 'shipment_at',
    width: 150,
    resizable: true
  },
  {
    title: '买方国家',
    dataIndex: 'country',
    width: 150,
    resizable: true
  },
  {
    title: '唛头',
    dataIndex: 'buyer',
    width: 100,
    resizable: true
  },
  {
    title: '柜号',
    dataIndex: 'cabinet_number',
    width: 100,
    resizable: true
  },
  {
    title: '车牌号',
    dataIndex: 'plate_number',
    width: 100,
    resizable: true
  },
  {
    title: '出货地址',
    dataIndex: 'shipment_addr',
    width: 200,
    resizable: true
  },
  {
    title: '作废日期',
    dataIndex: 'cancel_at',
    width: 200,
    resizable: true
  },
  {
    title: '作废备注',
    dataIndex: 'cancel_remark',
    width: 200,
    resizable: true
  },
  {
    title: '作废人',
    dataIndex: 'cancel_inCharge_name',
    width: 200,
    resizable: true
  }
]
export const childColumns: BasicColumn[] = packageColumns.filter(
  (item) =>
    !['project_name', 'packing_strid', 'warehouse_name', 'warehouse_item_name', 'inCharge_name', 'creator_name'].includes(item.dataIndex)
)

const status_schema = GET_STATUS_SCHEMA(
  [
    { label: '待装箱', value: 0 },
    { label: '已装箱', value: 1 }
  ],
  {
    defaultValue: 0
  }
)
export const searchFromSchemas: FormSchema[] = [
  status_schema,
  {
    field: 'buyer',
    label: '唛头',
    component: 'Input'
  },
  {
    field: 'country',
    label: '国家',
    component: 'Input'
  },
  {
    field: 'strid',
    label: '单号',
    helpMessage: '可搜索装箱单号、销售单号、采购单号',
    component: 'Input'
  },
  // {
  //   field: 'status',
  //   label: '装箱状态',
  //   defaultValue: 0,
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: '待装箱', value: 0 },
  //       { label: '已装箱', value: 1 }
  //     ],
  //     allowClear: true,
  //     optionFilterProp: 'label'
  //   },
  //
  // },
  {
    field: 'shipment_at',
    label: '装箱日期',
    // defaultValue: [dayjs().format('YYYY-MM-DD 00:00:00'), dayjs().add(14, 'day').format('YYYY-MM-DD 23:59:59')],
    component: 'SingleRangeDate'
  },
  {
    field: 'created_at',
    label: '创建日期',
    component: 'SingleRangeDate',
    // defaultValue: [dayjs().add(-7, 'day').format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')],

    // slot: 'createdAt'
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'project_number',
    label: '项目ID',
    component: 'Input'
  },
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input'
    // component: 'PagingApiSelect',
    // componentProps: {
    //   api: getProjectList,
    //   resultField: 'items',
    //   searchMode: true,
    //   pagingMode: true,
    //   returnParamsField: 'id',
    //   searchParamField: 'title',
    //   selectProps: {
    //     fieldNames: { key: 'id', value: 'id', label: 'title' },
    //     showSearch: true,
    //     placeholder: '请选择',
    //     optionFilterProp: 'title'
    //   },
    //   itemProps: {
    //     validateTrigger: 'blur'
    //   }
    // },
  },
  {
    field: 'urgent_level',
    label: '紧急程度',
    component: 'Select',
    componentProps: {
      options: tm('tag.urgentLevelList'),
      fieldNames: { key: 'value', value: 'value', label: 'alias' }
    }
  },
  {
    field: 'is_out',
    label: '是否已出库',
    component: 'Select',
    componentProps: {
      options: tm('tag.isOutOpt')
    }
  },
  {
    field: 'is_have_out',
    label: '是否已生成出库单',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  },
  {
    field: 'is_cancel',
    label: '是否作废',
    defaultValue: 0,
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  },
  {
    field: 'is_packing_file',
    label: '是否上传箱单',
    defaultValue: 0,
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  },
  {
    field: 'creator',
    label: '创建人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  }
]

export const commonPackingInfo = {
  buyer: '',
  // order_no: '',
  country: '',
  supplier: '',
  shipmentAt: '',
  shipmentAddr: '',
  pkgList: []
}

export const commonPackingItem = {
  no: 0,
  quantity: 0, // 包裹数量
  method: '', // 打包方式
  size: '',
  weight: '', // 重量
  volume: '', // 体积
  items: [] // 商品明细
}

export const commonProductDetail = {
  name: '',
  puid: '',
  material: '',
  quantity: 0, // 产品数量
  unit: '',
  width: 0,
  height: 0,
  length: 0,
  code: '', // 海关编码
  remark: ''
}

// 将获取到的excel转为单元格信息
export function generateExcelInfo(ws: WorkSheet) {
  // 二维数组，第一层是行数据，第二层是列数据
  const jsonData = XLSX.utils.sheet_to_json<(number | string | null)[]>(ws, { header: 1 })
  const merges = ws['!merges'] || []
  const newData: CellInfoType[] = []
  console.log(jsonData)

  for (let r = 0; r < jsonData.length; r++) {
    for (let c = 0; c < jsonData[r].length; c++) {
      // 两次遍历到这里，获取到的是每个单元格的数据
      const cellInfo: CellInfoType = {
        rowId: XLSX.utils.encode_row(r),
        colId: XLSX.utils.encode_col(c),
        cell: XLSX.utils.encode_cell({ r: r, c: c }),
        content: jsonData[r][c],
        rowspan: 1, // 合并的列数
        colspan: 1, // 合并的行数
        rowMergeDetail: [], // 合并列单元格信息
        colMergeDetail: [] // 合并行单元格信息
      }
      // 这里遍历xlsx返回的合并信息；s：合并开始的单元格（r：行，c：列）；e：合并结束的单元格（r：行，c：列）
      // { s: { r: 0, c: 0 }, e: { r: 2, c: 2 } }，表示从第 0 行、第 0 列到第 2 行、第 2 列的单元格被合并成一个大的单元格
      for (let i = 0; i < merges.length; i++) {
        const mergeRange = merges[i]
        // 在合并单元格的范围之内
        if (r >= mergeRange.s.r && r <= mergeRange.e.r && c >= mergeRange.s.c && c <= mergeRange.e.c) {
          // 合并单元格起始单元格
          if (
            XLSX.utils.encode_cell({
              r: mergeRange.s.r,
              c: mergeRange.s.c
            }) === cellInfo.cell
          ) {
            cellInfo.rowspan = mergeRange.e.r - mergeRange.s.r + 1
            cellInfo.colspan = mergeRange.e.c - mergeRange.s.c + 1
            // 大于1表示合并行单元格
            if (mergeRange.e.r - mergeRange.s.r + 1 > 1) {
              // 具体合并了第几行
              cellInfo.colMergeDetail = range(mergeRange.s.r + 1, mergeRange.s.r + (mergeRange.e.r - mergeRange.s.r + 1) + 1).map((item) =>
                item.toString()
              )
            }
            // 大于1表示合并列单元格
            if (mergeRange.e.c - mergeRange.s.c + 1 > 1) {
              // 具体合并了第几列
              cellInfo.rowMergeDetail = range(mergeRange.s.c + 1, mergeRange.s.c + (mergeRange.e.c - mergeRange.s.c + 1) + 1).map((item) =>
                item.toString()
              )
            }
            break
          } else {
            // 被合并的单元格rowspan和colspan为0
            cellInfo.rowspan = 0
            cellInfo.colspan = 0
            break
          }
        }
      }

      newData.push(cellInfo)
    }
  }
  console.log(newData)

  return newData
}

// 根据导入数据获取装箱单的信息
export function genderPackingInfo(data: CellInfoType[]): PackageOrder {
  const packingKeys = [
    { field: 'buyer', cell: 'C2' },
    { field: 'sale_strid', cell: 'M2' },
    { field: 'country', cell: 'C3' },
    { field: 'supplier', cell: 'M3' },
    { field: 'shipmentAt', cell: 'C4' },
    { field: 'shipmentAddr', cell: 'M4' }
  ]
  const packingInfo = {
    ...commonPackingInfo
  }
  for (const item of packingKeys) {
    if (item.field === 'shipmentAt') {
      // 无效日期处理
      let date: Date | string = new Date(formatDate(getCellInfo(item.cell).content))
      if (isNaN(date.getTime())) {
        date = ''
      }
      packingInfo[item.field] = date
      continue
    }
    packingInfo[item.field] = getCellInfo(item.cell).content
  }
  return handlePackingList(packingInfo, data)
}

function formatDate(cellData: number) {
  const dateVal = XLSX.SSF.parse_date_code(cellData)
  if (isNumber(cellData)) {
    return `${dateVal.y}/${dateVal.m}/${dateVal.d}`
  }
  return cellData
}

function handlePackingList(data: PackageOrder, val: CellInfoType[]): PackageOrder {
  const packingList = val.filter(
    (item) => item.colId === 'A' && +item.rowId >= 8 && item.rowspan >= 1 && /^\d+$/.test(item.content?.toString())
  )
  const pkgList: PackageList[] = packingList.map((item) => {
    const packingRow = getRowInfo(item.rowId, colPackingKeys)
    packingRow.method = packingRow.method?.toString?.()
    packingRow.size = packingRow.size?.toString?.()
    return {
      ...commonPackingItem,
      ...packingRow,
      items: handleProductDetail(item)?.map((item) => ({
        ...item,
        name: item?.name?.toString?.(),
        code: item?.code?.toString?.(),
        material: item?.material?.toString?.(),
        unit: item?.unit?.toString?.()
      }))
      // no: packingInfo.no
    }
  })
  return {
    ...data,
    pkgList
  }
}

// 处理商品明细
function handleProductDetail(cellData: CellInfoType): ProductItem[] {
  // const data = { ...commonProductDetail }
  if (cellData.colMergeDetail.length > 0) {
    return cellData.colMergeDetail.map((item) => ({ ...commonProductDetail, ...getRowInfo(item, colProductKeys) }))
  }
  return [{ ...commonProductDetail, ...getRowInfo(cellData.rowId, colProductKeys) }]
}

// 获取单元格信息
export function getCellInfo(cell: string) {
  return rawCellData.value.find((item: CellInfoType) => item.cell === cell) || {}
}

type ColProductKeys = (typeof colProductKeys)[number]['field']

type ColProductKeysType = {
  [key in ColProductKeys]: number | string
}

// 获取行信息
export function getRowInfo(rowId: string, colKeys: Array<{ field: string; colId: string }>) {
  const rowDetail = rawCellData.value.filter((item: CellInfoType) => item.rowId === rowId)
  const rowInfo: ColProductKeysType = {}
  for (const item of colKeys) {
    set(rowInfo, item.field, rowDetail.find((row: CellInfoType) => row.colId === item.colId)?.content ?? '')
  }
  return {
    ...rowInfo,
    remark: isString(rowInfo.remark) ? rowInfo.remark : rowInfo.remark + '',
    puid: isString(rowInfo.puid) ? rowInfo.puid : rowInfo.puid + ''
  }
}

/**
 * 设置导出excel的样式
 * @param rawSheet 源数据
 * @param dataLength 数据长度
 * @returns sheet 带样式的单元格
 * @example setExportSheetStyle（sheet, excel.length）
 */
export const setExportSheetStyle = (rawSheet: WorkSheet, dataLength: number) => {
  const sheet = cloneDeep(rawSheet)
  // 设置导出excel的合并信息
  sheet['!merges'] = mergeInfo
  // 设置sheetjs的行高
  sheet['!rows'] = [{ hpt: 80 }, ...Array.from({ length: dataLength - 1 }, (_item, idx) => ({ hpt: idx > 3 ? 40 : 30 }))]
  // 设置sheetjs的列宽
  sheet['!cols'] = [...Array.from({ length: 16 }, () => ({ wpx: 100 }))]
  // 设置特定的单元格样式
  for (const key of Object.keys(exportCellStyle)) {
    // 判断key中有冒号的是单元格范围
    if (key.includes(':')) {
      const range = XLSX.utils.decode_range(key)
      // 给特定的单元格添加样式
      for (let row = range.s.r; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col })
          // 原本有样式的就合并，没有单元格的就创建单元格并添加样式
          if (sheet[cellAddress]) {
            sheet[cellAddress].s = {
              ...sheet[cellAddress].s,
              ...exportCellStyle[key]
            }
          } else {
            sheet[cellAddress] = {
              s: exportCellStyle[key],
              v: '',
              t: 's'
            }
          }
        }
      }
      continue
    }
    sheet[key].s = exportCellStyle[key]
  }
  // 设置公共的单元格样式
  for (const key of Object.keys(sheet)) {
    if (!/[A-Z]\d/.test(key)) continue
    sheet[key].s = { ...sheet[key].s, ...commonCellStyle }
  }
  return sheet
}

//面板tablecolumns
export const tableColumns = [
  // {
  //   title: '名称',
  //   dataIndex: 'name',
  //   key: 'name',
  //   width: 100
  // },
  {
    title: '包装产品数量',
    dataIndex: 'quantity',
    key: 'quantity',
    width: 120
  },
  {
    title: '打包方式',
    dataIndex: 'method',
    key: 'method',
    width: 100
  },
  {
    title: '长',
    dataIndex: 'length',
    key: 'length',
    width: 100
  },
  {
    title: '宽',
    dataIndex: 'width',
    key: 'width',
    width: 100
  },
  {
    title: '高',
    dataIndex: 'height',
    key: 'height',
    width: 100
  },
  // {
  //   title: '包装尺寸',
  //   dataIndex: 'size',
  //   key: 'size',
  //   width: 100
  // },
  {
    title: '重量',
    dataIndex: 'weight',
    key: 'weight',
    width: 100
  },
  {
    title: '体积',
    dataIndex: 'volume',
    key: 'volume',
    width: 100
  }
]

//仓库导出

const defaultStyle = {
  font: { size: 11, bold: false }, // 显式设置不加粗
  alignment: { vertical: 'middle', horizontal: 'center' as const, wrapText: true },
  border: {
    top: { style: 'thin' as const, color: { argb: 'FF000000' } },
    left: { style: 'thin' as const, color: { argb: 'FF000000' } },
    bottom: { style: 'thin' as const, color: { argb: 'FF000000' } },
    right: { style: 'thin' as const, color: { argb: 'FF000000' } }
  }
}

const headerStyle = {
  ...defaultStyle,
  font: { bold: true, size: 11 },
  fill: {
    type: 'pattern' as const,
    pattern: 'solid' as const,
    fgColor: { argb: 'FFFFA500' }
  }
}
// 创建Excel工作表的函数
export async function createExcelWorksheet(workbook: ExcelJS.Workbook, item: any) {
  const worksheet = workbook.addWorksheet(`装箱单${item.strid || '未知'}`)

  // 设置列宽
  worksheet.columns = [
    { key: 'A', width: 6 },
    { key: 'B', width: 12 },
    { key: 'C', width: 12 },
    { key: 'D', width: 15 },
    { key: 'E', width: 15 },
    { key: 'F', width: 30 },
    { key: 'G', width: 10 },
    { key: 'H', width: 8 },
    { key: 'I', width: 12 },
    { key: 'J', width: 10 },
    { key: 'K', width: 20 },
    { key: 'L', width: 10 },
    { key: 'M', width: 10 },
    { key: 'N', width: 15 },
    { key: 'O', width: 15 }
  ]

  // 合并单元格
  worksheet.mergeCells('A1:C1')
  worksheet.mergeCells('D1:H1')
  worksheet.mergeCells('I1:K1')
  worksheet.mergeCells('L1:O1')

  worksheet.mergeCells('A2:C2')
  worksheet.mergeCells('D2:H2')
  worksheet.mergeCells('I2:K2')
  worksheet.mergeCells('L2:O2')

  worksheet.mergeCells('A3:C3')
  worksheet.mergeCells('D3:H3')
  worksheet.mergeCells('I3:K3')
  worksheet.mergeCells('L3:O3')

  // 设置表头数据
  const row1 = worksheet.getRow(1)
  row1.getCell('A').value = '唛头\nshipping mark：'
  row1.getCell('D').value = item?.buyer || '/'
  row1.getCell('I').value = '柜号\nCabinet Number:'
  row1.getCell('L').value = item?.cabinet_number || ''

  const row2 = worksheet.getRow(2)
  row2.getCell('A').value = '国家\nCountry:'
  row2.getCell('D').value = item?.country || ''
  row2.getCell('I').value = '供应商\nSupplier:'
  row2.getCell('L').value = item?.supplier || ''

  const row3 = worksheet.getRow(3)
  row3.getCell('A').value = '出货日期\nShipment date:'
  row3.getCell('D').value = item?.shipment_at || ''
  row3.getCell('I').value = '出货地址\nShipping address:'
  row3.getCell('L').value = item?.shipment_addr || ''

  // 设置表头
  const headerRow = worksheet.getRow(4)
  headerRow.values = [
    '序号',
    '所在部门',
    '所在仓位',
    '采购单号',
    '包裹号',
    '产品中英文名称',
    '产品图片',
    '材质',
    '单位',
    '包装产品数量',
    '打包方式',
    `包装尺寸\n(长*宽*高cm)`,
    '重量\n(KG)',
    '体积\n(CBM)',
    '备注'
  ]

  // 设置行高
  worksheet.getRow(1).height = 30
  worksheet.getRow(2).height = 30
  worksheet.getRow(3).height = 30
  worksheet.getRow(4).height = 60

  // 为前三行设置背景色
  for (let i = 1; i <= 3; i++) {
    const row = worksheet.getRow(i)
    row.eachCell((cell: any) => {
      cell.style = {
        ...headerStyle,
        fill: {
          type: 'pattern',
          pattern: 'solid'
        }
      }
    })
  }

  // 为第四行设置样式
  const row4 = worksheet.getRow(4)
  row4.eachCell((cell: any) => {
    cell.style = headerStyle
  })

  return worksheet
}

// 添加图片到工作表的函数
export async function addImageToWorksheet(worksheet: ExcelJS.Worksheet, workbook: ExcelJS.Workbook, imageUrl: string, rowIndex: number) {
  try {
    const response = await fetch(imageUrl)
    const imageBuffer = await response.arrayBuffer()
    const imageId = workbook.addImage({
      buffer: imageBuffer,
      extension: 'png'
    })

    // 设置图片的最小尺寸
    const MIN_WIDTH = 0.9 // 最小宽度（列单位）
    const MIN_HEIGHT = 0.5 // 最小高度（行单位）

    // 计算图片尺寸，确保不小于最小值
    const width = Math.max(MIN_WIDTH, 0.9) // 默认宽度为0.9列
    const height = Math.max(MIN_HEIGHT, 0.5) // 默认高度为0.5行

    // 使用绝对定位模式，调整位置使图片不占满单元格
    // 通过调整tl和br的位置，在单元格周围留出空白
    worksheet.addImage(imageId, {
      tl: { col: 6.1, row: rowIndex - 0.5 }, // 左上角位置调整，留出上方和左侧空白 (移动到下一列)
      br: { col: 6.1 + width, row: rowIndex - 0.5 + height }, // 右下角位置根据最小尺寸计算
      editAs: 'oneCell',
      scale: 0.3
    })
  } catch (error) {
    console.error('插入图片失败:', error)
  }
}

// 添加数据行到工作表的函数
export async function addDataRows(worksheet: ExcelJS.Worksheet, workbook: ExcelJS.Workbook, item: any) {
  let rowIndex = 5

  // 计算行高的函数，根据多个内容长度动态计算，最小行高50
  const calculateRowHeight = (...contents: string[]): number => {
    const MIN_HEIGHT = 50 // 最小行高
    const BASE_CHARS = 50 // 基准字符数，当内容少于这个数量时使用最小行高
    const HEIGHT_FACTOR = 0.5 // 每超过BASE_CHARS的字符增加的高度因子

    // 合并所有内容的长度，考虑多个字段的内容
    const totalLength = contents.reduce((total, content) => {
      return total + (content ? content.length : 0)
    }, 0)

    if (totalLength <= BASE_CHARS) {
      return MIN_HEIGHT
    }

    // 计算额外高度，每多出一定字符数增加一定高度
    const extraHeight = Math.ceil((totalLength - BASE_CHARS) / BASE_CHARS) * HEIGHT_FACTOR * MIN_HEIGHT
    return MIN_HEIGHT + extraHeight
  }

  if (item.packages && Array.isArray(item.packages)) {
    for (const pkg of item.packages) {
      if (pkg.item && pkg.item.length === 1) {
        const product = pkg.item[0]
        const row = worksheet.getRow(rowIndex)

        // 计算并设置行高，根据商品名称和采购单号长度动态调整
        const productName = product.name || ''
        const purchaseStrids = pkg.purchase_strids || ''

        // 同时考虑商品名称和采购单号的长度
        row.height = calculateRowHeight(productName, purchaseStrids)

        row.values = [
          rowIndex - 4,
          pkg.departmentsList || '',
          pkg.warehouse_item_name || '',
          purchaseStrids,
          pkg.strid || '',
          productName,
          '', // 图片列留空
          product.material || '',
          product.unit || '',
          pkg.quantity || '',
          pkg.method || '',
          `${pkg.width}*${pkg.height}*${pkg.length}` || '',
          pkg.weight || '',
          pkg.volume || '',
          pkg.remark || ''
        ]

        // 确保图片URL存在且有效
        if (product.imgs && product.imgs.length > 0 && product.imgs[0]) {
          try {
            await addImageToWorksheet(worksheet, workbook, product.imgs[0], rowIndex)
          } catch (error) {
            console.error('添加图片失败:', error)
          }
        }

        row.eachCell((cell: any) => {
          cell.style = {
            ...defaultStyle,
            fill: {
              type: 'pattern' as const,
              pattern: 'solid' as const
            }
          }
        })
        rowIndex++
      } else if (pkg.item && pkg.item.length > 1) {
        const productNames = pkg.item.map((p: any) => p.name).join('、')
        const row = worksheet.getRow(rowIndex)

        // 计算并设置行高，根据商品名称和采购单号长度动态调整
        const purchaseStrids = pkg.purchase_strids || ''
        row.height = calculateRowHeight(productNames, purchaseStrids)

        row.values = [
          rowIndex - 4,
          pkg.departmentsList || '',
          pkg.warehouse_item_name || '',
          purchaseStrids,
          pkg.strid || '',
          productNames,
          '',
          pkg.item[0]?.material || '',
          pkg.item[0]?.unit || '',
          pkg.quantity || '',
          pkg.method || '',
          pkg.size || '',
          pkg.weight || '',
          pkg.volume || '',
          pkg.remark || ''
        ]
        row.eachCell((cell: any) => {
          cell.style = {
            ...defaultStyle,
            fill: {
              type: 'pattern' as const,
              pattern: 'solid' as const
            }
          }
        })
        rowIndex++
      }
    }
  }
}

// 导出Excel文件的函数
export async function exportExcelFile(workbook: ExcelJS.Workbook) {
  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `仓库装箱单-${+new Date()}.xlsx`
  link.click()
  window.URL.revokeObjectURL(url)
}
