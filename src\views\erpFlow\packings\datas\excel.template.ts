import { PackageOrder } from '/@/views/erpFlow/packings/datas/types'

/**
 * 生成导出的单元格模板
 * @param jsonData 获取到的表格数据
 * @returns
 * @example generateExportExportTemplate(jsonData)
 */
export const generateExportExcelTemplate = (jsonData: PackageOrder) => {
  const { buyer, order_no, country, supplier, shipment_date, shipment_address } = jsonData
  return [
    [
      `     George Decoration Material Outlet Mall
      总部地址：佛山市季华四路意美家陶瓷卫浴城24栋敬城建材国际商城
      Add.:Block No.24, Casa Ceramic&Sanitary Ware Market,
      Jihua No.Road, Changcheng District , Foshan City.
      Tel.:0757-82727445  82721926  82727925`
    ],
    ['买方 Buyer：', null, buyer, null, null, null, null, null, '订单号 Order No.:', null, null, null, order_no],
    ['国家 Country：', null, country, null, null, null, null, null, '供应商 Supplier:', null, null, null, supplier],
    [
      '出货日期 Shipment date:',
      null,
      shipment_date,
      null,
      null,
      null,
      null,
      null,
      '出货地址 Shipping address:',
      null,
      null,
      null,
      shipment_address
    ],
    ['Packing List'],
    [
      '*No.',
      '*产品中英文品名                          Product name',
      '*产品图片                        Product picture',
      '*材质 \r\nMaterial',
      '*产品尺寸Size\r\nL×W×H （cm）',
      null,
      null,
      '*单位\r\nUnit',
      '*产品数量\r\nQuantity',
      '*包装产品数量\r\nPacking quantity',
      '*打包方式\r\nPacking method',
      '包装尺寸\r\nPackage dimensions \r\n（长*宽*高m）',
      '*毛重\r\nG.W.(KG)',
      '*体积                    Measurement\r\n(CBM)',
      '海关编码\r\nHS CODE',
      '备注                                                  Remarks'
    ],
    [null, null, null, null, '*长Length（cm）', '*宽Width（cm）', '*高或厚度Heith or Thickness（cm）'],
    ...jsonData.packing_list.reduce((arr, item) => {
      return [
        ...arr,
        ...item.items.map((product) => [
          item.no,
          product.product_name,
          product.imgs,
          product.material,
          product.product_size.length,
          product.product_size.width,
          product.product_size.height,
          product.unit,
          product.quantity,
          item.packing_quantity,
          item.packing_method,
          item.packing_dimensions,
          item.packing_kg,
          item.packing_measurement,
          product.hs_code,
          product.remark
        ])
      ]
    }, [])
  ] as (null | number | string)[][]
}

export const mergeInfo = [
  { s: { c: 14, r: 5 }, e: { c: 14, r: 6 } },
  { s: { c: 15, r: 5 }, e: { c: 15, r: 6 } },
  { s: { c: 12, r: 5 }, e: { c: 12, r: 6 } },
  { s: { c: 13, r: 5 }, e: { c: 13, r: 6 } },
  { s: { c: 10, r: 5 }, e: { c: 10, r: 6 } },
  { s: { c: 11, r: 5 }, e: { c: 11, r: 6 } },
  { s: { c: 0, r: 4 }, e: { c: 15, r: 4 } },
  { s: { c: 4, r: 5 }, e: { c: 6, r: 5 } },
  { s: { c: 0, r: 14 }, e: { c: 1, r: 14 } },
  { s: { c: 0, r: 5 }, e: { c: 0, r: 6 } },
  { s: { c: 1, r: 5 }, e: { c: 1, r: 6 } },
  { s: { c: 2, r: 5 }, e: { c: 2, r: 6 } },
  { s: { c: 3, r: 5 }, e: { c: 3, r: 6 } },
  { s: { c: 7, r: 5 }, e: { c: 7, r: 6 } },
  { s: { c: 8, r: 5 }, e: { c: 8, r: 6 } },
  { s: { c: 9, r: 5 }, e: { c: 9, r: 6 } },
  { s: { c: 0, r: 2 }, e: { c: 1, r: 2 } },
  { s: { c: 2, r: 2 }, e: { c: 7, r: 2 } },
  { s: { c: 8, r: 2 }, e: { c: 11, r: 2 } },
  { s: { c: 12, r: 2 }, e: { c: 15, r: 2 } },
  { s: { c: 0, r: 3 }, e: { c: 1, r: 3 } },
  { s: { c: 2, r: 3 }, e: { c: 7, r: 3 } },
  { s: { c: 8, r: 3 }, e: { c: 11, r: 3 } },
  { s: { c: 12, r: 3 }, e: { c: 15, r: 3 } },
  { s: { c: 0, r: 0 }, e: { c: 15, r: 0 } },
  { s: { c: 0, r: 1 }, e: { c: 1, r: 1 } },
  { s: { c: 2, r: 1 }, e: { c: 7, r: 1 } },
  { s: { c: 8, r: 1 }, e: { c: 11, r: 1 } },
  { s: { c: 12, r: 1 }, e: { c: 15, r: 1 } }
]

export const exportCellStyle = {
  // A1: {
  //   alignment: { horizontal: 'center', vertical: 'center', wrapText: true }
  // },
  A5: {
    fill: { patternType: 'solid', fgColor: { rgb: 'ffff00' } }
  },
  'A6:P7': {
    fill: { patternType: 'solid', fgColor: { rgb: 'fabf8f' } }
  },
  'A1:P7': {
    border: {
      top: { style: 'thin', color: { rgb: '000000' } },
      bottom: { style: 'thin', color: { rgb: '000000' } },
      left: { style: 'thin', color: { rgb: '000000' } },
      right: { style: 'thin', color: { rgb: '000000' } }
    }
  }
}

export const commonCellStyle = {
  alignment: { horizontal: 'center', vertical: 'center', wrapText: true },
  border: {
    top: { style: 'thin', color: { rgb: '000000' } },
    bottom: { style: 'thin', color: { rgb: '000000' } },
    left: { style: 'thin', color: { rgb: '000000' } },
    right: { style: 'thin', color: { rgb: '000000' } }
  }
}
