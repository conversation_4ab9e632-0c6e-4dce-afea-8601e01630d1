import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
// import { getDocOutWarehouse } from '/@/api/erp/package'
import { computed, nextTick, ref } from 'vue'
import { PackageListItem } from '/@/api/erp/modle/packageTypes'
// import { omit, random } from 'lodash-es'
import { useTabs } from '/@/hooks/web/useTabs'
import { PackagePageOperateTypeEnum } from '/@/enums/packageEnum'
import { useGo } from '/@/hooks/web/usePage'
import { getStaffList } from '/@/api/baseData/staff'

// 移除了出库id的下拉列表
export const outWarehouseItemOptions = ref<
  Array<{ label: string; value: number; children: (Omit<PackageListItem, 'id'> & { value: number })[] }>
>([])

// 出库产品的列表
export const outWarehouseItemList = computed(() => outWarehouseItemOptions.value.reduce((arr, item) => [...arr, ...item.children], []))

// 出库商品的map，可以通过value获取到对应的出库产品
export const mapOutWarehouseItem = computed<{ [key: number]: Omit<PackageListItem, 'id'> & { value: number } }>(() => {
  const mapItem = {}
  for (const item of outWarehouseItemList.value) {
    mapItem[item.itemOutId] = item
  }
  return mapItem
})

export const getFormSchemas = (type: PackagePageOperateTypeEnum): FormSchema[] => [
  {
    field: 'id',
    label: 'ID',
    component: 'InputNumber',
    ifShow: type === PackagePageOperateTypeEnum.EDIT,
    show: false
  },
  {
    field: 'strid',
    label: '装箱单号',
    component: 'Input',
    render: ({ model }) => model.strid
  },
  {
    field: 'urgent_level',
    label: '紧急程度',
    required: true,
    component: 'Select',
    componentProps: {
      options: [
        { label: '一般', value: 1 },
        { label: '紧急', value: 2 },
        { label: '非常紧急', value: 3 }
      ],
      placeholder: '请选择',
      showSearch: true
    }
  },
  {
    field: 'pkg_quantitys',
    label: '全部包裹总数',
    component: 'InputNumber',
    required: true,
    labelWidth: 120,
    dynamicDisabled: true
  },
  {
    field: 'volumes',
    label: '全部包裹总体积',
    component: 'InputNumber',
    required: true,
    labelWidth: 120,
    dynamicDisabled: true
  },
  {
    field: 'buyer',
    label: '唛头',
    component: 'Input',
    required: true,
    dynamicDisabled: type === PackagePageOperateTypeEnum.DETAIL
  },
  // {
  //   field: 'sale_strid',
  //   label: '销售订单号',
  //   component: 'Input',
  //   required: true
  // },
  {
    field: 'country',
    label: '国家',
    component: 'Input',
    required: true,
    dynamicDisabled: type === PackagePageOperateTypeEnum.DETAIL
  },
  {
    field: 'supplier',
    label: '供应商',
    component: 'Input',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'cabinet_number',
    label: '柜号',
    component: 'Input',
    colProps: { span: 12 },
    dynamicDisabled: type === PackagePageOperateTypeEnum.DETAIL
  },
  {
    field: 'plate_number',
    label: '车牌号',
    component: 'Input',
    colProps: { span: 12 },
    dynamicDisabled: type === PackagePageOperateTypeEnum.DETAIL
  },
  {
    field: 'shipmentAt',
    label: '出货日期',
    component: 'DatePicker',
    componentProps: { valueFormat: 'YYYY-MM-DD' },
    required: true,
    dynamicDisabled: type === PackagePageOperateTypeEnum.DETAIL
  },
  {
    field: 'shipmentAddr',
    label: '出货地址',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    dynamicDisabled: type === PackagePageOperateTypeEnum.DETAIL
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
    colProps: { span: 24 }
  },
  {
    label: '备货图',
    field: 'stock_imgs',
    component: 'Input',
    slot: 'StockImgs',
    colProps: { span: 12 }
  },
  {
    label: '备货图最后上传时间',
    field: 'stock_imgs_at',
    component: 'DatePicker',
    componentProps: { valueFormat: 'YYYY-MM-DD' },
    colProps: { span: 12 },
    labelWidth: 150,
    dynamicDisabled: true
  },
  {
    label: '装箱图',
    field: 'imgs',
    component: 'Input',
    slot: 'Imgs',
    colProps: { span: 12 }
  },
  {
    label: '装箱图最后上传时间',
    field: 'imgs_at',
    component: 'DatePicker',
    colProps: { span: 12 },
    componentProps: { valueFormat: 'YYYY-MM-DD' },
    labelWidth: 150,
    dynamicDisabled: true
  }
]

export const getPkgFormSchemas = (type: PackagePageOperateTypeEnum): FormSchema[] => [
  {
    field: 'id',
    label: 'ID',
    component: 'InputNumber',
    ifShow: type === PackagePageOperateTypeEnum.EDIT,
    show: false
  },
  {
    field: 'batch_code',
    label: '批次',
    component: 'Input',
    dynamicDisabled: true
  },
  {
    field: 'quantity',
    label: '包装产品数量',
    component: 'InputNumber',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'method',
    label: '打包方式',
    component: 'Input',
    required: true,
    dynamicDisabled: true
  },
  // {
  //   field: 'size',
  //   label: '包装尺寸',
  //   component: 'Input',
  //   required: true
  // },
  {
    field: 'length',
    label: '长',
    component: 'InputNumber',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'width',
    label: '宽',
    component: 'InputNumber',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'height',
    label: '高',
    component: 'InputNumber',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'weight',
    label: '重量',
    component: 'InputNumber',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'volume',
    label: '体积',
    component: 'InputNumber',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'PagingApiSelect',
    itemProps: {
      validateTrigger: 'blur'
    },
    dynamicDisabled: true,
    // required: true,
    componentProps() {
      return {
        api: getStaffList,
        resultField: 'items',
        selectProps: {
          allowClear: true,
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
          // disabled: type === 'detail'
          // disabled: true
        }
      }
    }
  },
  // {
  //   field: 'type',
  //   label: '类型',
  //   component: 'Input',
  //   required: true
  // },
  {
    field: 'items',
    label: '包裹产品明细',
    component: 'Input',
    slot: 'Items',
    // required: true,
    colProps: { span: 24 },
    rules: [{ required: true, validator: validItems }]
  }
]

function validItems(_rule: any, value: any[]) {
  if (!value || value.length === 0) return Promise.reject('请选择包裹内的商品并填写对应参数')
  // 必须校验的key
  const keys = modalFormSchemas.filter((item: FormSchema) => item.required).map((item) => item.field)
  // 必须验证的numberInput的key
  const numberKeys = modalFormSchemas
    .filter((item: FormSchema) => item.required && item.component === 'InputNumber')
    .map((item) => item.field)
  // 验证所有字段是否有值
  const validRes = value.every((product) => keys.every((key) => product[key]))
  // 验证所有numberInput是否大于0
  const validNumberRes = value.every((product) => numberKeys.every((key) => product[key] > 0))
  // 验证是否已经关联了库存
  // const validStockRes = value.every((product) => ['requestId', 'itemOutId'].every((key) => product[key]))
  // return validRes && validNumberRes && validStockRes
  return validRes && validNumberRes ? Promise.resolve() : Promise.reject('请填写正确的包裹明细信息！')
}

export const productColumns: BasicColumn[] = [
  {
    title: '产品名称',
    dataIndex: 'name',
    width: 200,
    resizable: true
  },
  {
    title: '产品编码',
    dataIndex: 'puid',
    width: 200,
    resizable: true
  },
  // {
  //   title: '关联出库产品',
  //   dataIndex: 'relateStocking',
  //   width: 400,
  //   resizable: true
  // },
  // {
  //   title: '产品图',
  //   dataIndex: 'imgs',
  //   width: 200,
  //   resizable: true
  // },
  {
    title: '产品材质',
    dataIndex: 'material',
    width: 200,
    resizable: true
  },
  {
    title: '产品数量',
    dataIndex: 'quantity',
    width: 100,
    resizable: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 100,
    resizable: true
  },
  {
    title: '产品宽度',
    dataIndex: 'width',
    width: 100,
    resizable: true
  },
  {
    title: '产品高度',
    dataIndex: 'height',
    width: 100,
    resizable: true
  },
  {
    title: '产品长度',
    dataIndex: 'length',
    width: 100,
    resizable: true
  },
  {
    title: '海关编码',
    dataIndex: 'code',
    width: 200,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 250,
    resizable: true
  }
]

export const modalFormSchemas: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    // required: true,
    show: false
  },
  {
    field: 'name',
    label: '商品名称',
    component: 'Input',
    required: true
    // render: ({ model }) => model.name
  },
  {
    label: '产品编码',
    field: 'puid',
    component: 'Input',
    required: true
  },
  {
    label: '产品材质',
    field: 'material',
    component: 'Input',
    required: true
  },
  {
    label: '产品数量',
    field: 'quantity',
    component: 'InputNumber',
    required: true
  },
  {
    label: '单位',
    field: 'unit',
    component: 'Input',
    required: true
  },
  {
    label: '产品宽度',
    field: 'width',
    component: 'InputNumber',
    componentProps: {
      addonAfter: 'cm',
      min: 1
    },
    rules: [
      {
        required: true,
        validator: (_, value) => (value > 0 ? Promise.resolve() : Promise.reject('产品宽度必须大于0'))
      }
    ]
  },
  {
    label: '产品高度',
    field: 'height',
    component: 'InputNumber',
    componentProps: {
      addonAfter: 'cm',
      min: 1
    },
    rules: [
      {
        required: true,
        validator: (_, value) => (value > 0 ? Promise.resolve() : Promise.reject('产品高度必须大于0'))
      }
    ]
  },
  {
    label: '产品长度',
    field: 'length',
    component: 'InputNumber',
    componentProps: {
      addonAfter: 'cm',
      min: 1
    },
    rules: [
      {
        required: true,
        validator: (_, value) => (value > 0 ? Promise.resolve() : Promise.reject('产品长度必须大于0'))
      }
    ]
  },
  {
    label: '海关编码',
    field: 'code',
    component: 'Input',
    required: true
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea'
    // required: true
  }
  // {
  //   label: '产品图',
  //   field: 'imgs',
  //   component: 'Upload',
  //   // required: true,
  //   slot: 'Imgs',
  //   colProps: { span: 24 }
  // }
]

// export async function getOutWarehouseItems() {
//   return new Promise(async (resolve) => {
//     const { items } = await getDocOutWarehouse()
//     outWarehouseItemOptions.value = items.map((item) => ({
//       label: item.strid,
//       value: item.id,
//       itemOutId: item.id,
//       selectable: false,
//       children: item.items.map((val) => {
//         const data = { ...val, label: val.name, value: random(1, 1000000000), itemOutId: val.id }
//         return omit(data, 'id')
//       })
//     }))
//     resolve(outWarehouseItemOptions.value)
//   })
// }

export async function handleBack(router) {
  const { closeCurrent } = useTabs(router)
  await closeCurrent()
  await nextTick(() => {
    const go = useGo(router)
    go('/erpFlow/packings', true)
  })
}
