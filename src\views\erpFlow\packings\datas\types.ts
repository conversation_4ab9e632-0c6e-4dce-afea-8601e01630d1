export interface CellInfoType {
  rowId: string
  colId: string
  cell: string
  content: number | string | null
  rowspan: number
  colspan: number
  rowMergeDetail: string[]
  colMergeDetail: string[]
}

export type PackageOrder = {
  buyer: string
  order_no: string
  country: string
  supplier: string
  shipment_date: string
  shipment_address: string
  packing_list: PackageList[]
}

export type PackageList = {
  no: number
  packing_quantity: number
  packing_method: string
  packing_dimensions: string
  packing_kg: string
  packing_measurement: string
  items: ProductItem[]
}

export type ProductItem = {
  product_name: string
  imgs: string
  material: string
  quantity: number
  unit: string
  product_size: {
    width: number
    height: number
    length: number
  }
  hs_code: string
  remark: string
}

export interface IData {
  date: number | string
  return_order_no: number | string | Array
}
export interface PropsType {
  isUpdate: boolean
  cardItem?: Recordable
  type: string
}
