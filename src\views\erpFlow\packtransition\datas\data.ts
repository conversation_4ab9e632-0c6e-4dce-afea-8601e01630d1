import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullAndUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'
import { getStaffList } from '/@/api/baseData/staff'

export const columns: BasicColumn[] = [
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 150,
    resizable: true
  },
  {
    title: '包裹号',
    dataIndex: 'strid',
    width: 150,
    resizable: true
  },
  {
    title: '原包裹号',
    dataIndex: 'strid_origin',
    width: 150,
    resizable: true
  },
  {
    title: '原仓库',
    dataIndex: 'warehouse_name_origin',
    width: 150,
    resizable: true
  },
  {
    title: '原仓库仓位',
    dataIndex: 'warehouse_item_name_origin',
    width: 150,
    resizable: true
  },
  {
    title: '现仓库',
    dataIndex: 'warehouse_name',
    width: 150,
    resizable: true
  },
  {
    title: '现仓库仓位',
    dataIndex: 'warehouse_item_name',
    width: 150,
    resizable: true
  },
  {
    title: '包裹类型',
    dataIndex: 'type',
    width: 150,
    resizable: true,
    customRender: ({ value }) => {
      return !isNullAndUnDef(value) ? (value == 1 ? '合包' : '拆包') : '-'
    }
  },
  {
    title: '是否跟进',
    dataIndex: 'is_follow',
    width: 150,
    resizable: true,
    customRender: ({ value }) => {
      return !isNullAndUnDef(value) ? h(Tag, { color: value ? 'green' : 'red' }, value ? '是' : '否') : '-'
    }
  },
  {
    title: '跟进时间',
    dataIndex: 'follow_at',
    width: 150,
    resizable: true
  },
  {
    title: '跟进人名称',
    dataIndex: 'follow_inCharge_name',
    width: 150,
    resizable: true
  }
]

export const searchFromSchemas: FormSchema[] = [
  {
    field: 'is_follow',
    label: '是否跟进',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    },
    colProps: { span: 8 }
  },
  {
    field: 'packing_package_strid',
    label: '包裹号',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'packing_package_strid_org',
    label: '原包裹号',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'follow_at',
    label: '跟进时间',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    colProps: { span: 8 }
  },
  {
    field: 'follow_inCharge',
    label: '创建人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: { span: 8 }
  },
  {
    field: 'follow_inCharge',
    label: '创建人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: { span: 8 }
  }
]
