<template>
  <div class="p-4">
    <BasicTable :data-cachekey="routePath" @register="registerTable">
      <template #toolbar>
        <a-button v-if="hasPermission(502)" type="primary" @click="handlefollow">设置跟进</a-button>
      </template>
    </BasicTable>
  </div>
</template>
<script setup lang="ts">
import { BasicTable, useTable } from '/@/components/Table'
import { getconvertList, setIsFollow } from '/@/api/erpFlow/packages'
import { columns, searchFromSchemas } from './datas/data'
import { message } from 'ant-design-vue'
import { usePermission } from '/@/hooks/web/usePermission'
import { useRoute } from 'vue-router'

const route = useRoute()
const { path: routePath } = route
const { hasPermission } = usePermission()

const [registerTable, { getSelectRowKeys, reload }] = useTable({
  title: '包裹转换列表',
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  api: getconvertList,
  columns,
  formConfig: {
    schemas: searchFromSchemas,
    labelWidth: 100,
    fieldMapToTime: [['follow_at', ['follow_at_start', 'follow_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  },
  rowKey: 'id',
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: (record) => {
      if (record.is_follow === 1) {
        return { disabled: true }
      } else {
        return { disabled: false }
      }
    }
  }
})

async function handlefollow() {
  try {
    const tablekey = await getSelectRowKeys()
    if (tablekey.length === 0) return message.error('请选择要跟进的包裹')
    setIsFollow({ ids: tablekey })
    reload()
  } catch (e) {
    console.log(e)
  }
}
</script>
