import { BasicColumn, FormSchema } from '/@/components/Table'
import { getDeptTree } from '/@/api/admin/dept'
import { h } from 'vue'
import { Tag } from 'ant-design-vue'
import { mapAudit } from '/@/views/erp/purchaseOrder/datas/datas'
import { mapAudit as mapSaleAudit } from '/@/views/erp/purchaseOrder/datas/datas'
import { isNullOrUnDef } from '/@/utils/is'

export const columns: BasicColumn[] = [
  {
    title: '采购单号',
    dataIndex: 'strid',
    width: 250,
    resizable: true
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    width: 150,
    resizable: true
  },
  {
    title: 'Gbuilder供应商',
    dataIndex: 'is_gbuilder',
    width: 150,
    resizable: true,
    customRender({ text }) {
      if (text === 0) {
        return h(Tag, { color: 'error' }, () => '否')
      } else if (text === 1) {
        return h(Tag, { color: 'success' }, () => '是')
      }
    }
  },
  {
    title: 'Gbuilder服务费',
    dataIndex: 'gbuilder_percentage',
    width: 150,
    resizable: true
  },
  {
    title: 'Gbuilder提成',
    dataIndex: 'gbuilder_commission',
    width: 150,
    resizable: true
  },
  {
    title: '应付金额',
    dataIndex: 'cost_left',
    width: 150,
    resizable: true
  },
  {
    title: '实付金额',
    dataIndex: 'paid_actual',
    width: 150,
    resizable: true,
    customRender: ({ record }) => {
      return record.work.paid_actual ?? '0.0000'
    }
  },
  {
    title: '销售结算状态',
    dataIndex: 'sale_is_audit',
    width: 150,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : h(Tag, { color: mapSaleAudit[value]?.color }, () => mapSaleAudit[value]?.label)
    }
  },
  {
    title: '采购结算状态',
    dataIndex: 'is_audit',
    width: 150,
    resizable: true,
    customRender: ({ record }) => {
      return h(Tag, { color: mapAudit[record.is_audit].color }, () => mapAudit[record.is_audit]?.label)
    }
  },
  {
    title: '销售结算日期',
    dataIndex: 'sale_audit_at',
    width: 120,
    resizable: true
  },
  {
    title: '采购结算日期',
    dataIndex: 'audit_at',
    width: 120,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'dept_name',
    width: 150,
    resizable: true
  }
]
export const searchFormSchema: FormSchema[] = [
  {
    field: 'dept_ids',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      multiple: true,
      showCheckedStrategy: 'TreeSelect.SHOW_ALL',
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        optionFilterProp: 'name',
        treeDefaultExpandAll: true,
        showSearch: true,
        placeholder: '请选择',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  {
    field: 'is_audit',
    label: '采购结算状态',
    component: 'Select',
    componentProps: {
      options: Object.keys(mapAudit).map((key) => ({ label: mapAudit[key].label, value: Number(key) }))
    }
  },
  {
    field: 'audit_at',
    label: '采购结算日期',
    component: 'SingleRangeDate',
    componentProps: {
      allowEmpty: [true, true],
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  },
  {
    field: 'sale_audit_at',
    label: '销售结算日期',
    component: 'SingleRangeDate',
    componentProps: {
      allowEmpty: [true, true],
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  }
]
