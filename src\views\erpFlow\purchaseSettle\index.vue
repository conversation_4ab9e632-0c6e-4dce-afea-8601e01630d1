<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Tooltip>
          <template #title>选中销售订单批量结算</template>
          <a-button @click="onAccount" :disabled="exporting" :loading="exporting" type="primary" v-if="hasPermission(512)"
            >订单结算</a-button
          >
        </Tooltip>
        <a-button type="primary" @click="handleExport" :loading="exporting" :disabled="exporting" v-if="hasPermission(511)">导出</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :stopa-buttonPropagation="true" :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <AddModel @register="registerModel" @success="handleSuccess" @close="clearSelectedRowKeys" />
    <PurchaseDrawer @register="registerPurchaseDrawer" @success="handleSuccess" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Tooltip } from 'ant-design-vue'
import { ActionItem, BasicTable, useTable, TableAction } from '/@/components/Table'
import { getPurchaseOrderList } from '/@/api/erp/purchaseOrder'
import { useMessage } from '/@/hooks/web/useMessage'
import { usePermission } from '/@/hooks/web/usePermission'
import { useModal } from '/@/components/Modal'
import AddModel from '/@/views/erp/purchaseOrder/components/AddModel.vue'
import PurchaseDrawer from '/@/views/erp/purchaseOrder/components/purchaseDrawer.vue'
import { columns, searchFormSchema } from './datas/datas'
import { IRecord } from '../../erp/purchaseOrder/datas/types'
import { useDrawer } from '/@/components/Drawer'
import { cloneDeep } from 'lodash-es'
import { downloadByData } from '/@/utils/file/download'

const exporting = ref(false)
const { createMessage } = useMessage()
const { hasPermission } = usePermission()
const [registerPurchaseDrawer, { openDrawer, setDrawerProps }] = useDrawer()

const [registerTable, { getForm, getSelectRows, clearSelectedRowKeys, reload, setLoading }] = useTable({
  title: '采购订单结算查询',
  api: getPurchaseOrderList,
  showIndexColumn: false,
  columns,
  rowKey: 'id',
  useSearchForm: true,
  searchInfo: { is_commission: 1 }, //is_commission是用来区分是否是采购订单结算查询页面的标识,传1代表是
  formConfig: {
    labelWidth: 150,
    schemas: searchFormSchema,
    baseColProps: {
      span: 6
    },
    fieldMapToTime: [
      ['audit_at', ['audit_at_start', 'audit_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['sale_audit_at', ['sale_audit_at_start', 'sale_audit_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  },
  actionColumn: {
    width: 100,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: (record) => ({
      disabled: record.is_audit !== 0
    })
  }
})

function createActions(record: IRecord): ActionItem[] {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission([514])
    }
  ]
}

//批量结算日期
const [registerModel, { openModal: openAddModal }] = useModal()
function onAccount() {
  try {
    exporting.value = true
    const selectData = getSelectRows()
    if (selectData.length < 1) return
    const show = ref(false)
    for (let item of selectData) {
      if (item.is_audit == 1) {
        createMessage.warning('请选择未结算的采购单')
        show.value = true
        break
      }
    }
    const data = selectData.map((item) => {
      return { id: item.work_id }
    })
    if (!show.value) {
      openAddModal(true, data)
    }
  } catch (err) {
    console.error(err)
    createMessage.error('操作失败')
  } finally {
    exporting.value = false
  }
}

async function handleExport() {
  try {
    exporting.value = true
    setLoading(true)
    const params = getForm()?.getFieldsValue()
    const response = await getPurchaseOrderList({ ...params, is_excel: 2, is_commission: 1, pageSize: ******** }, true)

    //将二进制流转xlsx文件并下载
    downloadByData(response as any, `采购订单结算查询-${+new Date()}.xlsx`)
    createMessage.success('导出成功')
  } catch (err) {
    createMessage.error('导出失败')
    throw new Error(err)
  } finally {
    setLoading(false)
    exporting.value = false
  }
}

function handleSuccess() {
  clearSelectedRowKeys()
  reload()
}

// 详情
function handleDetail(record: IRecord) {
  console.log(record, 'record')
  openDrawer(true, { record: cloneDeep(record), isUpdate: false, type: 'detail' })
  setDrawerProps({ title: '查看采购订单详情' })
}
</script>
