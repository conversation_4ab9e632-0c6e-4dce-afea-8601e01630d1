<template>
  <BasicDrawer @register="registerDrawer" title="查看任务队列" width="60%" @visible-change="change">
    <jobgetlist :reloads="reloads" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import jobgetlist from '/@/views/fileexport/jobgetlist/index.vue'

const reloads = ref(false)

const [registerDrawer] = useDrawerInner((data) => {
  console.log(data)
})

function change(visible) {
  reloads.value = visible
}
</script>
