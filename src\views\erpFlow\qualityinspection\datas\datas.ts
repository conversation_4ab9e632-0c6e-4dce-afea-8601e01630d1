import { isNull } from 'lodash-es'
import { BasicColumn, FormSchema, TableImg } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { mul } from '/@/utils/math'
import { useI18n } from '/@/hooks/web/useI18n'
import { usePermission } from '/@/hooks/web/usePermission'
import { h } from 'vue'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'
import { getDeptTree } from '/@/api/admin/dept'
import { getRelatePurchaseList } from '/@/api/erp/inWarehouse'
import { getWarehouse } from '/@/api/baseData/warehouse'
import { getErpSupplier } from '/@/api/commonUtils'

export const statusMap = {
  0: { color: '', text: '未生产' },
  1: { color: 'green', text: '生产中' },
  2: { color: 'skyblue', text: '生产完成' }
}

const { t } = useI18n()
const { hasPermission } = usePermission()

export const purchaseColumns: BasicColumn[] = [
  {
    title: '采购单号',
    dataIndex: 'strid',
    width: 250,
    resizable: true
  },
  {
    title: '商品名称',
    dataIndex: 'name',
    width: 250,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 100,
    resizable: true
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    width: 100,
    resizable: true
  },
  {
    title: '图片',
    dataIndex: 'imgs',
    width: 100,
    resizable: true
  },
  {
    title: '产品编码',
    dataIndex: 'puid',
    width: 100,
    resizable: true
  },
  {
    title: '已采购数量',
    dataIndex: 'qty_purchased',
    width: 100,
    resizable: true
  },
  {
    title: '未质检数量',
    dataIndex: 'qr_num_left',
    width: 150
  },
  {
    title: '未装包裹数',
    dataIndex: 'qty_pkg_left',
    width: 100,
    resizable: true
  },
  {
    title: '未入库数量',
    dataIndex: 'qty_wait_received',
    width: 100,
    resizable: true
  },
  {
    title: '未入库金额',
    dataIndex: 'unreceivedAmount',
    width: 100,
    resizable: true,
    customRender: ({ record }) => {
      return mul(record.qty_wait_received, record.unit_price) ?? '0.00'
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    resizable: true,
    customRender: ({ text }) => useRender.renderTag(statusMap[text].text, statusMap[text].color)
  },
  {
    title: '质检状态',
    dataIndex: 'qc_status',
    width: 100,
    resizable: true,
    customRender: ({ text }) =>
      !isNull(text) ? useRender.renderTag(t(`tag.qcStatusTag.${text}.label`), t(`tag.qcStatusTag.${text}.color`)) : text
  },
  {
    title: '生产完成日期',
    dataIndex: 'production_finish_at',
    width: 150,
    resizable: true
  },
  {
    title: '需求生产完成日期',
    dataIndex: 'request_status_at',
    width: 150,
    resizable: true
  }
]

export const inventOptions = {
  4: { value: 4, label: '采购入库' },
  2: { value: 2, label: '库存转换入库' },
  3: { value: 3, label: '盘点入库' }
}

export const inventColumns: BasicColumn[] = [
  {
    title: '入库来源',
    dataIndex: 'src',
    width: 200,
    resizable: true,
    customRender: ({ text }) => inventOptions[text]?.label ?? text
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    width: 150,
    resizable: true
  },
  {
    title: '库存名称',
    dataIndex: 'name',
    width: 150,
    resizable: true
  },
  {
    title: '图片',
    dataIndex: 'imgs',
    key: 'imgs',
    customRender: ({ text }) => {
      if (text?.length < 1) {
        return h('div', {}, '-')
      }
      return h(TableImg, { imgList: text, simpleShow: true })
    },
    width: 100
  },
  {
    title: '产品编码',
    dataIndex: 'puid',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: 'UN',
    dataIndex: 'uniqid',
    width: 150,
    resizable: true,
    customRender: ({ value }) => {
      return !isNullOrUnDef(value) ? value : '-'
    }
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '所属销售单号',
    dataIndex: 'source_uniqid',
    key: 'source_uniqid',
    width: 250,
    sorter: true
  },
  {
    title: '所属部门',
    dataIndex: 'department',
    width: 250
  },
  {
    title: '库存状态',
    dataIndex: 'status',
    width: 150,
    customRender: ({ record }) => {
      const statusLabel = {
        0: { val: '未入库', color: 'warning' },
        1: { val: '已入库', color: 'processing' },
        2: { val: '已清点', color: 'success' }
      }
      return h(Tag, { color: statusLabel[record.status] ? statusLabel[record.status].color : '' }, () =>
        statusLabel[record.status] ? statusLabel[record.status].val : record.status
      )
    }
  },
  {
    title: '质检状态',
    dataIndex: 'qc_status',
    width: 100,
    customRender: ({ text }) =>
      !isNull(text) ? useRender.renderTag(t(`tag.qcStockTag.${text}.label`), t(`tag.qcStockTag.${text}.color`)) : '-'
  },
  {
    title: '交付日期',
    dataIndex: 'deliver_at',
    width: 150,
    resizable: true
  },
  {
    title: '实际入库数量',
    dataIndex: 'qty_received',
    width: 150,
    resizable: true
  },
  {
    title: '现有库存数量',
    dataIndex: 'qty_stocking',
    width: 150
  },
  {
    title: '未质检数量',
    dataIndex: 'qr_num_left',
    width: 150
  },
  {
    title: '库存单位',
    dataIndex: 'unit',
    width: 150
  },
  {
    title: '成本',
    dataIndex: 'cost_price',
    width: 150,
    ifShow: () => {
      // const userStore = useUserStore()
      // // 当前人角色
      // const roleValue = userStore.getUserInfo?.roleValue
      // // 质检不显示单价
      // return !['inspection', 'warehouse'].includes(roleValue)
      return hasPermission(324)
    }
  },

  {
    title: '所在仓库',
    dataIndex: 'warehouse_name',
    width: 100
  },

  // {
  //   title: '产品图片',
  //   dataIndex: 'imgs',
  //   customRender: ({ text }) => {
  //     if (text?.length < 1) {
  //       return h('div', {}, '-')
  //     }
  //     return h(TableImg, { imgList: text, simpleShow: true })
  //   },
  //   width: 150
  // },
  {
    title: '生产货期',
    dataIndex: 'updated_at',
    width: 150
  },
  {
    title: '入库日期',
    dataIndex: 'created_at',
    width: 200
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    resizable: true,
    customRender: ({ record }) => {
      if (record.remark) {
        return record.remark
      } else {
        return '-'
      }
    }
  }
]

export const purchaseSchemas: FormSchema[] = [
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    },
    colProps: { span: 8 }
  },
  {
    label: '采购单号',
    field: 'strids',
    component: 'PagingApiSelect',
    componentProps: {
      resultField: 'items',
      api: getRelatePurchaseList,
      searchMode: true,
      pagingMode: true,
      searchParamField: 'strid',
      mode: 'multiple',
      selectProps: {
        disabled: true,
        fieldNames: { key: 'id', value: 'strid', label: 'strid' },
        showSearch: true,
        placeholder: '请选择'
      },
      pagingSize: 20,
      returnParamsField: 'id'
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: { span: 8 }
  }
]

export const inventSchmeas: FormSchema[] = [
  {
    field: 'src',
    label: '入库来源',
    component: 'Select',
    componentProps: {
      options: Object.keys(inventOptions).map((key) => {
        return { label: inventOptions[key].label, value: key }
      })
    }
  },
  {
    field: 'source_uniqid',
    label: '所属销售单号',
    component: 'Input'
  },
  {
    field: 'name',
    label: '名称',
    component: 'Input'
  },
  {
    field: 'warehouse_id',
    label: '所在仓库',
    component: 'PagingApiSelect',
    componentProps: {
      api: getWarehouse,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      // params: { pageSize: 100 },
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'supplier_id',
    label: '供应商',
    component: 'PagingApiSelect',
    componentProps: () => {
      return {
        api: getErpSupplier,
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        },
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        resultField: 'items'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'puid',
    label: '产品编码',
    component: 'Input'
  }
]
