<template>
  <div>
    <PageWrapper>
      <Tabs default-active-key="1" type="card">
        <TabPane key="1" tab="采购未质检商品">
          <BasicTable @register="registerpurchaseTable" @fetch-success="onPurchaseFetchSuccess">
            <template #toolbar>
              <Button type="primary" @click="handlepurhaseExport('purchase')" :loading="exporting"> 导出搜索结果 </Button>
              <Button type="primary" @click="handlepurhaseExportAll" :loading="exporting">查看任务队列 </Button>
            </template>
          </BasicTable>
        </TabPane>
        <TabPane key="2" tab="库存未质检商品">
          <BasicTable @register="registerinventTable" @fetch-success="onInventFetchSuccess">
            <template #toolbar>
              <Button type="primary" @click="handlepurhaseExport('inventory')" :loading="exporting"> 导出搜索结果 </Button>
              <Button type="primary" @click="handlepurhaseExportAll" :loading="exporting">查看任务队列 </Button>
            </template>
          </BasicTable>
        </TabPane>
      </Tabs>
      <exportDrawer @register="registerExportDrawer" />
    </PageWrapper>
  </div>
</template>
<script setup lang="ts">
import { BasicTable, useTable } from '/@/components/Table'
import { Tabs, TabPane, Button, message } from 'ant-design-vue'
import { PageWrapper } from '/@/components/Page'
import { purchaseColumns, inventColumns, purchaseSchemas, inventSchmeas } from './datas/datas'
import { getItemsList } from '/@/api/erp/PurchaseTracking'
import { getstockList } from '/@/api/erp/inventory'
import { jobexportNoQrList } from '/@/api/fileexport/jobgetlist'
import exportDrawer from './components/exportDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { ref } from 'vue'

const [registerExportDrawer, { openDrawer }] = useDrawer()
const exporting = ref(false)

const [registerpurchaseTable, { getForm }] = useTable({
  columns: purchaseColumns,
  api: getItemsList,
  showIndexColumn: false,
  showTableSetting: true,
  useSearchForm: true,
  beforeFetch: async (params) => {
    return {
      ...params,
      qc_status: 0
    }
  },
  formConfig: {
    schemas: purchaseSchemas
  }
})

const [registerinventTable, { getForm: getinventForm }] = useTable({
  columns: inventColumns,
  api: getstockList,
  showIndexColumn: false,
  showTableSetting: true,
  useSearchForm: true,
  beforeFetch: async (params) => {
    return {
      ...params,
      qc_status: 0
    }
  },
  formConfig: {
    labelWidth: 150,
    schemas: inventSchmeas,
    showAdvancedButton: true,
    autoAdvancedLine: 1,
    actionColOptions: {
      span: 24
    },
    baseColProps: { span: 6 }
  }
})

async function handlepurhaseExport(type) {
  try {
    exporting.value = true
    const params = getForm()?.getFieldsValue()
    const params2 = getinventForm()?.getFieldsValue()
    await jobexportNoQrList({ qc_status: 0, ...params, ...params2, type: type === 'purchase' ? 1 : 2, pageSize: totals.value })
    message.success('导出任务已添加，请前往文件导出查看下载')
    exporting.value = false
  } catch (e) {
    message.error('下载失败')
    exporting.value = false
  }
}

function handlepurhaseExportAll() {
  openDrawer(true)
}
const totals = ref(0)
function onPurchaseFetchSuccess({ items, total }) {
  console.log(items, total)
  totals.value = total
}
function onInventFetchSuccess({ items, total }) {
  console.log(items, total)
  totals.value = total
}
</script>
