import { BasicColumn, FormSchema } from '/@/components/Table'
import { getDeptTree } from '/@/api/admin/dept'
import { useI18n } from '/@/hooks/web/useI18n'
import { isNullOrUnDef } from '/@/utils/is'
import { h } from 'vue'
import { Tag } from 'ant-design-vue'
import { mapAudit } from '/@/views/erp/saleOrder/datas/datas'

const { tm } = useI18n()

// export const columns: BasicColumn[] = [
//   {
//     title: '订单号',
//     dataIndex: 'source_uniqid',
//     width: 170,
//     resizable: true
//   },
//   {
//     title: '客户名',
//     dataIndex: 'client_name',
//     width: 150,
//     resizable: true
//   },
//   {
//     title: '项目名称',
//     dataIndex: 'project_name',
//     width: 250,
//     resizable: true
//   },
//   {
//     title: '部门',
//     dataIndex: 'dept_name',
//     width: 170,
//     resizable: true,
//     customRender: ({ record }) => {
//       console.log(record.type1.end_at)
//       return (
//         <div>
//           {record.department ? <div>产品部：{record.department ?? '-'}</div> : null}
//           {record.operation_name ? <div>业务部门：{record.operation_name ?? '-'}</div> : null}
//         </div>
//       )
//     }
//   },
//   {
//     title: '结算状态',
//     dataIndex: 'is_audit',
//     width: 150,
//     resizable: true,
//     customRender: ({ value }) => {
//       return isNullOrUnDef(value) ? '-' : h(Tag, { color: mapAudit[value]?.color }, () => mapAudit[value]?.label)
//       // return mapStatus[record.status]?.label
//     }
//   },
//   {
//     title: '结算日期',
//     dataIndex: 'audit_at',
//     width: 150,
//     resizable: true
//   },
//   {
//     title: '应收金额',
//     dataIndex: 'receivable',
//     width: 150,
//     resizable: true
//   },
//   {
//     title: '备货日期',
//     dataIndex: 'type3',
//     width: 150,
//     resizable: true,
//     customRender: ({ record }) =>
//       record?.type3?.start_at ? (
//         <span style={record?.type3?.is_overdue ? 'color: red' : ''}>{record?.type3?.start_at}</span>
//       ) : (
//         <span>-</span>
//       )
//   },
//   {
//     title: '生产完成日期',
//     dataIndex: 'type4',
//     width: 150,
//     resizable: true,
//     customRender: ({ record }) =>
//       record?.type4?.start_at ? (
//         <span style={record?.type4?.is_overdue ? 'color: red' : ''}>{record?.type4?.start_at}</span>
//       ) : (
//         <span>-</span>
//       )
//   },
//   {
//     title: '装箱完成日期',
//     dataIndex: 'type6',
//     width: 150,
//     resizable: true,
//     customRender: ({ record }) =>
//       record?.type6?.start_at ? (
//         <span style={record?.type6?.is_overdue ? 'color: red' : ''}>{record?.type6?.start_at}</span>
//       ) : (
//         <span>-</span>
//       )
//   },
//   {
//     title: '交付日期',
//     dataIndex: 'deliver_at',
//     width: 150,
//     resizable: true
//   },
//   {
//     title: '出库日期',
//     dataIndex: 'est_finished_at',
//     width: 150,
//     resizable: true
//   },
//   {
//     title: '最初逾期责任人',
//     dataIndex: 'iability_type',
//     width: 150,
//     resizable: true,
//     customRender: ({ text }) => tm('limitation.mapInitiallyType')?.[text] ?? text
//   },
//   {
//     title: '提成金额',
//     dataIndex: 'achievement_commission',
//     width: 170,
//     resizable: true
//   }
// ]
export const columns: BasicColumn[] = [
  {
    title: '订单号',
    dataIndex: 'source_uniqid',
    width: 170,
    resizable: true
  },
  {
    title: '项目',
    dataIndex: 'project',
    width: 250,
    resizable: true,
    customRender: ({ record }) =>
      h('div', { style: 'text-align:left' }, [
        h('div', {}, `项目ID: ${record.project_number ?? ''}`),
        h('div', {}, `项目名称: ${record.project_name ?? ''}`)
      ])
  },

  {
    title: '客户名',
    dataIndex: 'client_name',
    width: 150,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'dept_name',
    width: 170,
    resizable: true,
    customRender: ({ record }) => {
      console.log(record.type1.end_at)
      return (
        <div>
          {record.department ? <div>产品部：{record.department ?? '-'}</div> : null}
          {record.operation_name ? <div>业务部门：{record.operation_name ?? '-'}</div> : null}
        </div>
      )
    }
  },
  {
    title: '结算日期',
    dataIndex: 'audit_at',
    width: 150,
    resizable: true
  },
  {
    title: '应收金额',
    dataIndex: 'receivable',
    width: 150,
    resizable: true
  },
  {
    title: '备货日期',
    dataIndex: 'type3',
    width: 150,
    resizable: true,
    customRender: ({ record }) =>
      record?.type3?.start_at ? (
        <span style={record?.type3?.is_overdue ? 'color: red' : ''}>{record?.type3?.start_at}</span>
      ) : (
        <span>-</span>
      )
  },
  {
    title: '生产完成日期',
    dataIndex: 'type4',
    width: 150,
    resizable: true,
    customRender: ({ record }) =>
      record?.type4?.start_at ? (
        <span style={record?.type4?.is_overdue ? 'color: red' : ''}>{record?.type4?.start_at}</span>
      ) : (
        <span>-</span>
      )
  },
  {
    title: '装箱完成日期',
    dataIndex: 'type6',
    width: 150,
    resizable: true,
    customRender: ({ record }) =>
      record?.type6?.start_at ? (
        <span style={record?.type6?.is_overdue ? 'color: red' : ''}>{record?.type6?.start_at}</span>
      ) : (
        <span>-</span>
      )
  },
  {
    title: '交付日期',
    dataIndex: 'deliver_at',
    width: 150,
    resizable: true
  },
  {
    title: '出库日期',
    dataIndex: 'est_finished_at',
    width: 150,
    resizable: true
  },
  {
    title: '最初逾期责任人',
    dataIndex: 'iability_type',
    width: 150,
    resizable: true,
    customRender: ({ text }) => tm('limitation.mapInitiallyType')?.[text] ?? text
  },
  {
    title: '提成金额',
    dataIndex: 'achievement_commission',
    width: 170,
    resizable: true
  },
  {
    title: '结算状态',
    dataIndex: 'is_audit',
    width: 150,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : h(Tag, { color: mapAudit[value]?.color }, () => mapAudit[value]?.label)
      // return mapStatus[record.status]?.label
    }
  },

  {
    title: '人员',
    dataIndex: 'person',
    width: 170,
    resizable: true,
    customRender: ({ record }) => (
      <div>
        <div>方案经理：{record.inCharge_name}</div>
        <div>项目经理：{record.program_incharge_name}</div>
        <div>交付经理：{record.delivery_incharge_name}</div>
      </div>
    )
  },
  {
    title: '可备货日期',
    dataIndex: 'type1',
    width: 150,
    resizable: true,
    customRender: ({ record }) =>
      record?.type1?.start_at ? (
        <span style={record?.type1?.is_overdue ? 'color: red' : ''}>{record?.type1?.start_at}</span>
      ) : (
        <span>-</span>
      )
  },
  {
    title: '拆单日期',
    dataIndex: 'type2',
    width: 150,
    resizable: true,
    customRender: ({ record }) =>
      record?.type2?.start_at ? (
        <span style={record?.type2?.is_overdue ? 'color: red' : ''}>{record?.type2?.start_at}</span>
      ) : (
        <span>-</span>
      )
  },
  {
    title: '主管审核日期',
    dataIndex: 'type7',
    width: 150,
    resizable: true,
    customRender: ({ record }) =>
      record?.type7?.start_at ? (
        <span style={record?.type7?.is_overdue ? 'color: red' : ''}>{record?.type7?.start_at}</span>
      ) : (
        <span>-</span>
      )
  },

  {
    title: '质检完成日期',
    dataIndex: 'type5',
    width: 150,
    resizable: true,
    customRender: ({ record }) =>
      record?.type5?.start_at ? (
        <span style={record?.type5?.is_overdue ? 'color: red' : ''}>{record?.type5?.start_at}</span>
      ) : (
        <span>-</span>
      )
  },
  {
    title: '质检审核日期',
    dataIndex: 'type8',
    width: 150,
    resizable: true,
    customRender: ({ record }) =>
      record?.type8?.start_at ? (
        <span style={record?.type8?.is_overdue ? 'color: red' : ''}>{record?.type8?.start_at}</span>
      ) : (
        <span>-</span>
      )
  }
]

export const searchFormSchema: FormSchema[] = [
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        placeholder: '请选择',
        showSearch: true,
        optionFilterProp: 'name',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  {
    field: 'audit_at',
    label: '结算日期',
    component: 'SingleRangeDate',
    componentProps: {
      allowEmpty: [true, true],
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  },
  {
    field: 'est_finished_at',
    label: '出库日期',
    component: 'SingleRangeDate',
    componentProps: {
      allowEmpty: [true, true],
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  }
]
