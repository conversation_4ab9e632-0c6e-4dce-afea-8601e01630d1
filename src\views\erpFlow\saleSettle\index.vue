<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button v-if="hasPermission(510)" :loading="exporting" :disabled="exporting" type="primary" @click="handleExport">导出</a-button>
      </template>
    </BasicTable>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { BasicTable, useTable } from '/@/components/Table'
import { getLimitationList } from '/@/api/erp/sales'
import { usePermission } from '/@/hooks/web/usePermission'
import { useMessage } from '/@/hooks/web/useMessage'
import { columns, searchFormSchema } from './datas/datas'
import { downloadByData } from '/@/utils/file/download'

const { createMessage } = useMessage()

const { hasPermission } = usePermission()

const exporting = ref(false)

const [registerTable, { getForm, setLoading }] = useTable({
  title: '销售订单结算查询',
  api: getLimitationList,
  showIndexColumn: false,
  columns,
  searchInfo: { is_commission: 1 }, //is_commission是用来区分是否是销售订单结算查询页面的标识,传1代表是
  rowKey: 'id',
  useSearchForm: true,
  formConfig: {
    labelWidth: 150,
    schemas: searchFormSchema,
    baseColProps: {
      span: 6
    },
    fieldMapToTime: [
      ['audit_at', ['audit_at_start', 'audit_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['est_finished_at', ['est_finished_at_start', 'est_finished_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  }
})
async function handleExport() {
  try {
    exporting.value = true
    setLoading(true)
    const params = getForm()?.getFieldsValue()
    const response = await getLimitationList({ ...params, is_excel: 1, is_commission: 1, pageSize: 10000 }, true)

    //将二进制流转xlsx文件并下载
    downloadByData(response as any, `销售订单结算查询-${+new Date()}.xlsx`)

    createMessage.success('导出成功')
  } catch (err) {
    createMessage.error('导出失败')
    throw new Error(err)
  } finally {
    setLoading(false)
    exporting.value = false
  }
}
</script>
