<template>
  <div>
    <BasicModal v-bind="$attrs" @register="registerModal" title="库存成本增加" width="600px" @ok="addSubmit" okText="提交" destroyOnClose>
      <BasicForm @register="registerForm">
        <template #StockingItems="{ model }">
          <Tag v-for="item in model.stocking_items" color="processing" :key="item.id">{{ item.name }}</Tag>
        </template>
      </BasicForm>
    </BasicModal>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { Tag } from 'ant-design-vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { ModalMethods } from '/@/components/Modal'
import { IRecord } from '/@/views/erp/Inventory/datas/types'
import { setBatchCost } from '/@/api/erp/inventory'
import { useMessage } from '/@/hooks/web/useMessage'

import { schemas } from '../datas/costSortModal'

const propsData = ref<{ selectedRow: IRecord[] }>({ selectedRow: [] })
const { createMessage } = useMessage()

const emits = defineEmits<{ (e: 'success'): void; (e: 'register', $event: ModalMethods): void }>()

const [registerModal, { closeModal, changeOkLoading }] = useModalInner((data: { selectedRow: IRecord[] }) => {
  propsData.value = data
  setFieldsValue({
    stocking_ids: data.selectedRow.map((item) => item.id),
    stocking_items: data.selectedRow,
    score: 0
  })
})

const [registerForm, { validateFields, setFieldsValue }] = useForm({
  schemas,
  showActionButtonGroup: false,
  colon: true,
  labelWidth: 110,
  baseColProps: {
    span: 24
  }
})
async function addSubmit() {
  try {
    await changeOkLoading(true)
    const values = await validateFields()
    const res = await setBatchCost({
      unit_price: values.unit_price,
      account_name: values.account.label,
      account_code: values.account.value,
      ids: values.stocking_ids.map((item: number) => ({ id: item }))
    })
    if (res.msg === 'success') {
      createMessage.success('修改成功！')
      await closeModal()
      emits('success')
    }
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  }
}
</script>
