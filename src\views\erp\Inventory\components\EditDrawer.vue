<template>
  <div>
    <BasicDrawer title="编辑仓库信息" @register="registerDrawer" v-bind="$attrs" width="90%" show-footer ok-text="确定" @ok="handleSubmit">
      <BasicForm @register="registerFrom" />
    </BasicDrawer>
  </div>
</template>

<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { useForm, BasicForm } from '/@/components/Form'
import { schemas } from '../datas/drawer'
import { editStocking } from '/@/api/erp/inventory'
// import { ModalMethods } from '/@/components/Modal'
import { useMessage } from '/@/hooks/web/useMessage'

const { createMessage } = useMessage()
const emits = defineEmits(['success', 'register'])

const [registerDrawer, { closeDrawer, changeOkLoading }] = useDrawerInner(async ({ record }) => {
  await setFieldsValue(record)
})

const [registerFrom, { setFieldsValue, validate }] = useForm({
  baseColProps: { span: 24 },
  showActionButtonGroup: false,
  colon: true,
  labelAlign: 'right',
  labelWidth: 100,
  schemas
})

async function handleSubmit() {
  try {
    await changeOkLoading(true)
    const values = await validate()
    console.log(values)
    const res = await editStocking(values)
    if (res.msg === 'success') {
      createMessage.success('修改成功！')
      emits('success')
      await closeDrawer()
    }
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  }
}
</script>
