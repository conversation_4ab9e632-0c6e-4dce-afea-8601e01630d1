<template>
  <div>
    <BasicDrawer @register="registerdrawer" v-bind="$attrs" width="90%" show-footer ok-text="确定" @ok="submintshipment" @close="close">
      <Card title="基本信息" :bordered="false">
        <Description @register="registerdescription" :bordered="false" :column="4" />
      </Card>
      <Card title="关联订单" :bordered="false">
        <Alert message="仅提供部分可查询数据进行展示,详情请前往相关页面" type="info" show-icon />
        <BasicTable :title="unref(title)" @register="register1" :columns="column" :data-source="datasucose">
          <template #bodyCell="{ column: curColumn, record }">
            <template v-if="curColumn.dataIndex === 'inCharge'"
              ><div v-for="(item, index) in statusdata" :key="index">
                <div v-if="item.id == record.inCharge">
                  {{ item.name }}
                </div>
              </div>
            </template>
          </template>
        </BasicTable>
      </Card>
    </BasicDrawer>
  </div>
</template>
<script lang="ts" setup>
import { getStaffList } from '/@/api/baseData/staff'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { Description, useDescription } from '/@/components/Description'
import { Card, Alert } from 'ant-design-vue'
import { BasicTable, useTable } from '/@/components/Table'
import { schema, columns1, columns2, columns3, columns4 } from '../datas/drawer'
import { ref } from 'vue'
import { getWarehouse } from '/@/api/baseData/warehouse'
import { unref } from 'vue'
import { getPurchaseOrderList } from '/@/api/erp/purchaseOrder'
import { getstockgetDoc } from '/@/api/erp/outWarehouse'
import { getInventoryListst, getInventoryList } from '/@/api/erp/inventory'
const showtabel = ref()
const title = ref()
const column = ref<any>([])
const datasucose = ref()
const [registerdrawer, { closeDrawer }] = useDrawerInner(async (val) => {
  showtabel.value = val.record.src
  for (let key in val.record) {
    stadata.value[key] = val.record[key]
    if (key == 'warehouse_id') {
      const warehouseid: any = await getWarehouse({ pageSize: 100 })
      const warehouse = warehouseid.items.find((item) => item.id === val.record['warehouse_id'])
      if (warehouse) {
        stadata.value['warehouse_id'] = warehouse.name
      }
    }
  }
  switch (val.record.src) {
    case 1:
      title.value = '采购单'
      column.value = columns1
      const purchasedata = await getPurchaseOrderList({ id: stadata.value['id'] })
      datasucose.value = purchasedata.items
      break
    case 2:
      title.value = '转换单'
      column.value = columns2
      const transitiondata = await getInventoryListst({ id: stadata.value['doc_id'] })
      datasucose.value = transitiondata.items
      break
    case 3:
      title.value = '盘点单'
      StaffListFn()
      column.value = columns3
      const checkdata = await getInventoryList({ id: stadata.value['doc_id'] })
      datasucose.value = checkdata.items
      break
    case 4:
      title.value = '入库单'
      column.value = columns4
      const warehousingdata = await getstockgetDoc({ id: stadata.value['doc_in_id'] })
      datasucose.value = warehousingdata.items
      break
  }
})
//option
const stadata = ref({})
const [registerdescription] = useDescription({
  schema,
  data: stadata
})
//table
const [register1] = useTable({
  showIndexColumn: true,
  maxHeight: 300
  // columns: column,
  // dataSource: datasucose
})

async function submintshipment() {
  closeDrawer()
}
function close() {
  datasucose.value = []
}
//员工
const statusdata = ref()
async function StaffListFn() {
  const { items } = await getStaffList()
  statusdata.value = items
}
</script>
