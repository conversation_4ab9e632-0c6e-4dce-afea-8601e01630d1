<template>
  <BasicModal @register="registerModal" title="库存备货设置" @ok="handleConfirm">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'qty_reserve'">
          <InputNumber v-model:value="record.qty_reserve" :max="+record.qty_stocking" :min="0.01" :precision="2" />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicTable, useTable } from '/@/components/Table'
import { columns } from '../datas/StockUp.datas'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { InputNumber } from 'ant-design-vue'

const emits = defineEmits(['submit', 'register'])

const [registerTable, { setTableData, getDataSource }] = useTable({
  showIndexColumn: false,
  columns,
  pagination: false,
  rowKey: 'id',
  scroll: { y: 400 }
})

const [registerModal, { setModalProps }] = useModalInner(({ data }) => {
  setTableData(data.map((item) => ({ ...item, qty_reserve: item.qty_stocking })))
})

function handleConfirm() {
  setModalProps({ confirmLoading: true })
  const params = getDataSource()
  emits('submit', params)
}
</script>
