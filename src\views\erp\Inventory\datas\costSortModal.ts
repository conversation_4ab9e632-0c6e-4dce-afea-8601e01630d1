import { getCategory } from '/@/api/financialDocuments/otherIncome'
import { FormSchema } from '/@/components/Form'
export const schemas: FormSchema[] = [
  {
    field: 'stocking_ids',
    component: 'Select',
    componentProps: {
      mode: 'multiple'
    },
    label: '选中的库存商品',
    required: true,
    show: false
  },
  {
    field: 'stocking_items',
    component: 'Input',
    label: '选中的库存商品',
    slot: 'StockingItems'
  },
  {
    field: 'account',
    label: '科目',
    component: 'ApiSelect',
    componentProps: () => {
      return {
        api: getCategory,
        resultField: 'items',
        selectProps: {
          labelInValue: true,
          fieldNames: {
            key: 'key',
            value: 'account_code',
            label: 'account_name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'account_name',
          allowClear: true
        }
        // onChange: (val, shall) => {
        //   handFn(shall)
        // }
      }
    },
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'unit_price',
    label: '分摊金额',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      precision: 4
    },
    required: true
  }
]
