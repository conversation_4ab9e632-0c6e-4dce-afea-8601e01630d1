import { DescItem } from '/@/components/Description'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { getWarehouse } from '/@/api/baseData/warehouse'
// import { useUserStore } from '/@/store/modules/user'
import { usePermission } from '/@/hooks/web/usePermission'

const { hasPermission } = usePermission()
export const schema: DescItem[] = [
  //   {
  //     field: 'puid',
  //     label: '单号'
  //   },
  {
    field: 'id',
    label: '产品编号'
  },
  {
    field: 'name',
    label: '库存名称'
  },
  {
    field: 'src_name',
    label: '分类'
  },
  {
    field: 'created_at',
    label: '入库时间'
  },
  {
    field: 'qty_stocking',
    label: '数量'
  },
  {
    field: 'unit_price',
    label: '成本',
    render(val) {
      return `${val} ￥`
    },
    show: () => {
      return hasPermission(325)
      // const userStore = useUserStore()
      // // 当前人角色
      // const roleValue = userStore.getUserInfo?.roleValue
      // // 质检不显示单价
      // return !['inspection'].includes(roleValue)
    }
  },
  {
    field: 'status',
    label: '库存状态',
    render: (val) => {
      const statuslable = {
        0: '生成入库单',
        1: '生成采购单',
        2: '完成生产',
        3: '已入库'
      }
      if (statuslable[val]) {
        return statuslable[val]
      } else {
        return val
      }
    }
  },
  {
    field: 'updated_at',
    label: '生产货期'
  },
  {
    field: 'warehouse_id',
    label: '所在仓库'
  },
  {
    field: 'unit',
    label: '单位'
  },
  {
    field: 'desc',
    label: '描述',
    render(val) {
      if (val) {
        return val
      } else {
        return '----'
      }
    }
  },
  {
    field: 'remark',
    label: '备注',
    render(val) {
      if (val) {
        return val
      } else {
        return '----'
      }
    }
  }
]

export const columns1: BasicColumn[] = [
  {
    dataIndex: 'dept_id',
    title: '采购部门'
  },
  {
    dataIndex: 'supplier_id',
    title: '供应商'
  },
  {
    dataIndex: 'created_at',
    title: '采购日期'
  }
]
export const columns2: BasicColumn[] = [
  {
    dataIndex: 'id',
    title: '转换单ID'
  },
  {
    dataIndex: 'strid',
    title: '转换单号'
  },
  {
    dataIndex: 'created_at',
    title: '转换日期'
  },
  {
    dataIndex: 'creator_name',
    title: '转换人'
  }
]
export const columns3: BasicColumn[] = [
  {
    dataIndex: 'strid',
    title: '盘点单号'
  },
  {
    dataIndex: 'inCharge',
    title: '负责人'
  },
  {
    dataIndex: 'created_at',
    title: '盘点创建时间'
  },
  {
    dataIndex: 'desc',
    title: '盘点备注'
  }
]
export const columns4: BasicColumn[] = [
  {
    dataIndex: 'strid',
    title: '入库单号'
  },
  {
    dataIndex: 'department_name',
    title: '入库部门'
  },
  {
    dataIndex: 'created_at',
    title: '入库时间'
  },
  {
    dataIndex: 'updated_at',
    title: '更新时间'
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
    required: true
  },
  {
    field: 'name',
    label: '库存名称',
    component: 'Input',
    required: true,
    componentProps: {
      disabled: true
    }
  },
  {
    field: 'unit_price',
    label: '单价',
    component: 'InputNumber',
    dynamicDisabled: true,
    componentProps: {
      min: 0,
      precision: 2
    },
    required: true
  },
  {
    field: 'warehouse_id',
    label: '所在仓库',
    component: 'ApiSelect',
    componentProps: {
      api: getWarehouse,
      params: { is_disabled: 0 },
      resultField: 'items',
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'desc',
    label: '描述',
    component: 'InputTextArea'
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea'
  }
]
