export interface IRecord {
  id: number
  src: number
  origin_stocking_id: null | number
  doc_id: number
  doc_in_id: number
  work_id: null | number
  request_id: number
  purchase_id: number
  name: string
  puid: null | number
  imgs: string[]
  warehouse_id: number
  pkg_num: number
  pkg_received: null | number
  unit: string
  unit_price: null | number
  qty_total: number
  qty_received: number
  qty_defective: number
  qty_stocking: number
  desc: null | string
  remark: null | string
  status: number
  updated_at: string
  created_at: string
  received_at: string
  item_out: any[]
  src_name: string
  status_name: string
  qty_out: number
  qty_available: number
  work_strid: null | number | string
  key: string
  reserve_num: number
  qty_residue: number
}
