<template>
  <BasicDrawer @register="registerDrawer">
    <Descriptions bordered :column="3">
      <DescriptionsItem label="采购单号">{{ init_record?.pur_strid }}</DescriptionsItem>
      <DescriptionsItem label="转换类型">{{ init_record?.change_type == 2 ? '公转私' : '私转公' }}</DescriptionsItem>
      <DescriptionsItem label="原款项是否退回">{{ init_record?.re_clause == 1 ? '是' : '否' }}</DescriptionsItem>
      <DescriptionsItem label="新发票类型">{{ invoicetype[init_record?.invoice_type]?.label }}</DescriptionsItem>
      <DescriptionsItem label="我司签约主体">{{ init_record?.contracting_party }}</DescriptionsItem>
      <DescriptionsItem label="是否Gbuilder">{{ init_record?.is_gbuilder == 1 ? '是' : '否' }}</DescriptionsItem>
      <DescriptionsItem label="含税金额">{{ init_record?.cost }}</DescriptionsItem>
      <DescriptionsItem label="开票税点">{{ init_record?.tax_rate }}</DescriptionsItem>
      <DescriptionsItem label="税金">{{ init_record?.tax_amount }}</DescriptionsItem>
      <DescriptionsItem label="成本总价">{{ init_record?.amount }}</DescriptionsItem>
      <DescriptionsItem label="开票加收税点">{{ init_record?.add_point }}</DescriptionsItem>
      <DescriptionsItem label="开票加收税点金额">{{ init_record?.add_point_amount }}</DescriptionsItem>
      <DescriptionsItem label="旧货款付款账号">{{ init_record?.account || '-' }}</DescriptionsItem>
      <DescriptionsItem label="旧货款收款开户行">{{ init_record?.bank || '-' }}</DescriptionsItem>
      <DescriptionsItem label="旧货款收款账户名称">{{ init_record?.account_name || '-' }}</DescriptionsItem>
      <DescriptionsItem label="付款账号名称">{{ init_record?.from_account_name || '-' }}</DescriptionsItem>
      <DescriptionsItem label="付款账号">{{ init_record?.from_account || '-' }}</DescriptionsItem>
      <DescriptionsItem label="付款账号开户行">{{ init_record?.from_bank || '-' }}</DescriptionsItem>
      <DescriptionsItem label="本次输入金额">{{ init_record?.amount_cost || '-' }}</DescriptionsItem>
    </Descriptions>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.key === 'imgs'">
          <TableImg :imgList="record.imgs ?? []" :simpleShow="true" />
        </template>
      </template>
    </BasicTable>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable, TableImg } from '/@/components/Table'
import { Descriptions, DescriptionsItem } from 'ant-design-vue'
import { invoicetype } from '../../purchaseOrder/datas/public.data'
import { columns } from '../datas/edit.data'

const init_record = ref<any>()

const [registerDrawer] = useDrawerInner(async (data) => {
  init_record.value = data.record
  setTableData(data.record?.items || [])
})
const [registerTable, { setTableData }] = useTable({
  title: '商品明细',
  columns
})
</script>
