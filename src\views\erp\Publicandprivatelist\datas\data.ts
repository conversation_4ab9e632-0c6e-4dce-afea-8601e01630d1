import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'
import { invoicetype } from '../../purchaseOrder/datas/public.data'

export const columns: BasicColumn[] = [
  {
    title: '公私单号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '采购单号',
    dataIndex: 'pur_strid',
    width: 200,
    resizable: true
  },
  {
    title: '转换类型',
    dataIndex: 'change_type',
    width: 100,
    resizable: true,
    customRender({ text }) {
      return !isNullOrUnDef(text) ? h(Tag, { color: text === 1 ? 'green' : 'orange' }, text === 2 ? '公转私' : '私转公') : ''
    }
  },
  {
    title: '原款项是否退回',
    dataIndex: 're_clause',
    width: 150,
    resizable: true,
    customRender({ text }) {
      return !isNullOrUnDef(text) ? h(Tag, { color: text === 1 ? 'green' : 'red' }, text === 1 ? '是' : '否') : ''
    }
  },
  {
    title: '新发票类型',
    dataIndex: 'invoice_type',
    width: 100,
    resizable: true,
    customRender({ text }) {
      return !isNullOrUnDef(text) ? invoicetype[text].label : ''
    }
  },
  {
    title: '我司新签约主体',
    dataIndex: 'contracting_party',
    width: 200,
    resizable: true
  },
  {
    title: '含税金额',
    dataIndex: 'cost',
    width: 100,
    resizable: true
  },
  {
    title: '开票税点',
    dataIndex: 'tax_rate',
    width: 100,
    resizable: true
  },
  {
    title: '税金',
    dataIndex: 'tax_amount',
    width: 100,
    resizable: true
  },
  {
    title: '成本总价',
    dataIndex: 'amount',
    width: 100,
    resizable: true
  },
  {
    title: '开票加收税点',
    dataIndex: 'add_point',
    width: 100,
    resizable: true
  },
  {
    title: '开票加收税点金额',
    dataIndex: 'add_point_amount',
    width: 150,
    resizable: true
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 100,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'strid',
    label: '公私单号',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'pur_strid',
    label: '采购单号',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'change_type',
    label: '转换类型',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      options: [
        {
          label: '公转私',
          value: 2
        },
        {
          label: '私转公',
          value: 1
        }
      ]
    }
  },
  {
    field: 're_clause',
    label: '原款项是否退回',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 2
        }
      ]
    }
  }
]
