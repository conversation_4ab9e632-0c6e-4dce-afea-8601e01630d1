import { BasicColumn } from '/@/components/Table'

export const columns: BasicColumn[] = [
  {
    title: '商品名称',
    dataIndex: 'name',
    width: 150,
    resizable: true
  },
  {
    title: '产品图片',
    dataIndex: 'imgs',
    width: 100,
    resizable: true
  },
  {
    title: '产品编码',
    dataIndex: 'puid',
    width: 150,
    resizable: true
  },
  {
    title: '采购数量',
    dataIndex: 'qty_purchased',
    width: 100,
    resizable: true
  },
  {
    title: '原采购单价',
    dataIndex: 'unit_price',
    width: 150,
    resizable: true
  },
  {
    title: '原采购金额',
    dataIndex: 'unit_price_new',
    width: 150,
    resizable: true
  },
  {
    title: '新采购单价',
    dataIndex: 'unit_amount',
    width: 150,
    resizable: true
  },
  {
    title: '新采购金额',
    dataIndex: 'unit_amount_new',
    width: 150,
    resizable: true
  },
  {
    title: 'purchase_id',
    dataIndex: 'purchase_id',
    width: 150,
    resizable: true,
    ifShow: false
  }
]
