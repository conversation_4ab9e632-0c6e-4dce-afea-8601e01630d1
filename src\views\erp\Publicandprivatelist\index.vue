<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <editDrawer @register="registereditDrawer" />
  </div>
</template>
<script setup lang="ts">
import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table'
import { columns, schemas } from './datas/data'
import { purchasegetMaleSelfDoc } from '/@/api/erp/purchaseOrder'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'

const [registereditDrawer, { openDrawer, setDrawerProps }] = useDrawer()

const [registerTable] = useTable({
  useSearchForm: true,
  showTableSetting: true,
  columns,
  api: purchasegetMaleSelfDoc,
  formConfig: {
    schemas,
    labelWidth: 120
  },
  actionColumn: {
    width: 100,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  }
})

function createActions(record): ActionItem[] {
  return [
    {
      label: '详情',
      onClick: handleDetail.bind(null, record)
    }
  ]
}

function handleDetail(record) {
  openDrawer(true, {
    record
  })
  setDrawerProps({ title: '详情', width: '90%', showFooter: false })
}
</script>
