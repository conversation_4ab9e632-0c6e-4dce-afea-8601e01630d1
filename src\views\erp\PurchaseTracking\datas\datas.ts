import { cloneDeep } from 'lodash-es'
import type { BasicColumn, FormSchema } from '/@/components/Table'
import { getColumns, searchFromSchemas } from '/@/views/erp/purchaseOrder/datas/datas'
import { isNull, isUndefined } from 'lodash-es'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { mul } from '/@/utils/math'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { useI18n } from '/@/hooks/web/useI18n'
import { h } from 'vue'

const { t, tm } = useI18n()
export const statusMap = {
  0: { color: '', text: '未生产' },
  1: { color: 'green', text: '生产中' },
  2: { color: 'skyblue', text: '生产完成' }
}
/** 引用采购订单页面表格进行过滤 */
export const columnsFn = (): BasicColumn[] => {
  const columns = cloneDeep(getColumns()).filter((item) => {
    const arr: any = ['created_at', 'strid', 'status', 'supplier_name', 'dept_name']
    if (arr.includes(item.dataIndex)) {
      if (item.dataIndex === 'created_at') {
        item.sorter = {
          multiple: 3
        }
      }
      return item
    }
  })
  columns.splice(
    4,
    0,
    {
      title: '需求生产完成日期',
      dataIndex: 'request_status_at',
      width: 150,
      resizable: true,
      sorter: {
        multiple: 1
      }
    },
    {
      title: '实际生产完成日期',
      dataIndex: 'production_finish_at',
      width: 150,
      resizable: true,
      customRender: ({ record }) => {
        const requestat = Math.floor(new Date(record.request_status_at).getTime() / 1000)
        const production = Math.floor(new Date(record.production_finish_at).getTime() / 1000)
        return h('span', { style: { color: production > requestat ? 'red' : '' } }, record.production_finish_at)
      }
    },
    { title: '交付日期', dataIndex: 'deliver_at', width: 150, resizable: true }
  )
  const other = [
    {
      title: '紧急状态',
      dataIndex: 'urgent_level',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        const mapUrgentLevel = tm('tag.mapUrgentLevel')
        const curUrgentLevel = mapUrgentLevel[text]
        if (curUrgentLevel) return useRender.renderTag(curUrgentLevel.alias, curUrgentLevel.color)
        return '-'
      },
      sorter: {
        multiple: 2
      }
    },
    {
      title: '质检状态',
      dataIndex: 'qc_status',
      width: 100,
      resizable: true,
      customRender: ({ text }) =>
        !isNull(text) ? useRender.renderTag(t(`tag.qcStatusTag.${text}.label`), t(`tag.qcStatusTag.${text}.color`)) : text
    },
    {
      title: '未入库金额',
      dataIndex: 'noInWarehouseAmount',
      width: 100,
      resizable: true,
      customRender: ({ value }) => {
        return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
      }
    },
    {
      title: '生产完成',
      dataIndex: 'is_production_finish',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        const map = {
          0: { color: 'error', label: '否' },
          1: { color: 'success', label: '是' }
        }
        return !isUndefined(text) && !isNull(text) ? useRender.renderTag(map[text].label, map[text].color) : '-'
      }
    }
  ]
  // columns.push({
  //   title: '未入库金额',
  //   dataIndex: 'noInWarehouseAmount',
  //   width: 100,
  //   resizable: true,
  //   customRender: ({ value }) => {
  //     return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
  //   }
  // })
  return [...columns, ...other]
}

/** 子表格 */
export const childRenColumns: BasicColumn[] = [
  {
    title: 'id',
    dataIndex: 'id',
    width: 100,
    resizable: true
  },
  {
    title: '采购单号',
    dataIndex: 'strid',
    width: 100,
    resizable: true
  },
  {
    title: '商品名称',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 100,
    resizable: true
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    width: 100,
    resizable: true
  },
  {
    title: '图片',
    dataIndex: 'imgs',
    width: 100,
    resizable: true
  },
  {
    title: '产品编码',
    dataIndex: 'puid',
    width: 100,
    resizable: true
  },
  {
    title: '单价',
    dataIndex: 'unit_price',
    width: 100,
    resizable: true
  },

  {
    title: '已采购数量',
    dataIndex: 'qty_purchased',
    width: 100,
    resizable: true
  },
  {
    title: '未质检数量',
    dataIndex: 'qr_num_left',
    width: 150
  },
  {
    title: '未装包裹数',
    dataIndex: 'qty_pkg_left',
    width: 100,
    resizable: true
  },
  {
    title: '未入库数量',
    dataIndex: 'qty_wait_received',
    width: 100,
    resizable: true
  },
  {
    title: '未入库金额',
    dataIndex: 'unreceivedAmount',
    width: 100,
    resizable: true,
    customRender: ({ record }) => {
      return mul(record.qty_wait_received, record.unit_price) ?? '0.00'
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    resizable: true,
    customRender: ({ text }) => useRender.renderTag(statusMap[text].text, statusMap[text].color)
  },
  {
    title: '质检状态',
    dataIndex: 'qc_status',
    width: 100,
    resizable: true,
    customRender: ({ text }) =>
      !isNull(text) ? useRender.renderTag(t(`tag.qcStatusTag.${text}.label`), t(`tag.qcStatusTag.${text}.color`)) : text
  },
  {
    title: '生产完成日期',
    dataIndex: 'production_finish_at',
    width: 150,
    resizable: true
  },
  {
    title: '需求生产完成日期',
    dataIndex: 'request_status_at',
    width: 150,
    resizable: true
  }
]

//子产品tablecolum
export const tablecolum = (type?: string): BasicColumn[] => [
  {
    title: 'id',
    dataIndex: 'id',
    width: 100,
    resizable: true,
    defaultHidden: true
  },
  {
    title: 'request_id',
    dataIndex: 'request_id',
    width: 100,
    resizable: true,
    defaultHidden: true
  },
  {
    title: 'work_id',
    dataIndex: 'work_id',
    width: 100,
    resizable: true,
    defaultHidden: true
  },
  {
    title: '产品名称',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  {
    title: '产品数量',
    dataIndex: 'quantity',
    width: 100,
    resizable: true
  },
  {
    title: '种类占比',
    dataIndex: 'proportion_org',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text + '%' : '-'
    }
  },
  {
    title: '单个sku占比',
    dataIndex: 'proportion',
    width: 100,
    resizable: true
    // customRender: ({ text }) => {
    //   return text ? text + '%' : '-'
    // }
    // defaultHidden: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 100,
    resizable: true
  },
  {
    title: type == 'retreat' ? '剩余可退货数量' : '剩余数量',
    dataIndex: 'quantity_left',
    width: 100,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 100,
    resizable: true
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 100,
    resizable: true
  },
  {
    title: '图片',
    dataIndex: 'imgs',
    width: 100,
    resizable: true
  },
  {
    title: '附件',
    dataIndex: 'files',
    width: 250,
    resizable: true
  },
  {
    title: 'type',
    dataIndex: 'type',
    width: 100,
    resizable: true,
    defaultHidden: true
  }
]

/** 引用采购订单页面筛选进行过滤 */
export const searchFromSchema: FormSchema[] = cloneDeep(searchFromSchemas).filter((item) => {
  const arr: any = ['created_at', 'strid', 'status', 'supplier_id', 'dept_ids']
  if (arr.includes(item.field)) {
    if (item.field === 'status') {
      item.defaultValue = 1
    }
    return item
  }
})

export const otherSchema: FormSchema[] = [
  {
    field: 'qc_status',
    label: '质检状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未质检', value: 0 },
        { label: '待质检', value: 2 },
        { label: '已质检', value: 1 },
        { label: '质检审核中', value: 3 },
        { label: '质检中', value: 4 }
      ]
    }
  },
  {
    field: 'request_status_at',
    label: '需求生产完成日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'deliver_at',
    label: '交付日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'urgent_level',
    label: '紧急状态',
    component: 'Select',
    componentProps: {
      options: tm(`tag.urgentLevelList`),
      fieldNames: { key: 'value', value: 'value', label: 'alias' }
    }
  },
  {
    field: 'is_production_finish',
    label: '是否生产完成',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'production_finish_at',
    label: '实际生产完成日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'request_status_at_not_null',
    label: '需求生产完成日期为空',
    component: 'Select',
    defaultValue: 1,
    componentProps: {
      options: [
        { label: '否', value: 1 },
        { label: '是', value: 0 }
      ]
    }
  },
  {
    field: 'is_overdue',
    label: '是否逾期',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  }
]
