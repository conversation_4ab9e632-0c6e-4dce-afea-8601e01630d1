<template>
  <div>
    <BasicTable :data-cachekey="routePath" class="p-4" @register="registerTable">
      <template #toolbar>
        <Button v-if="hasPermission(339)" type="primary" @click="handleExport(1)" :loading="exporting"> 导出搜索结果 </Button>
        <Tooltip>
          <template #title> 按搜索条件，导出包裹模板！ </template>
          <Button type="primary" @click="handleExport(2)" :loading="exporting"> 包裹模板导出 </Button>
        </Tooltip>
      </template>
      <template #expandedRowRender="{ record }">
        <BasicTable
          :key="record.status"
          :ref="(el) => (expandedRowRefs[record.id] = el)"
          class="p-4"
          @register="registerChildrenTable"
          :api="getItemsList.bind(null, { pageSize: 9999, strid: record.strid })"
          v-model:expandedRowKeys="childrenexpandedRowKeys"
        >
          <template #toolbar>
            <p style="color: red; font-size: 24px; position: absolute; left: 0; height: 20px"
              >注意：产品多的时候可能存在多页，请仔细检查清楚产品是否都已生产完成，否则会影响创建质检单和创建包裹</p
            >
            <Button :disabled="record.status === 0 || !isDisable[record.id] || pushing" @click="endProduction(record.id)"
              >批量设置完成生产</Button
            >
          </template>
          <template #bodyCell="{ text, column }">
            <template v-if="column.key === 'imgs'">
              <TableImg :imgList="text" :simpleShow="true" />
            </template>
            <!--            <template v-if="column.key === 'status'">-->
            <!--              <Tag :color="statusMap[record.status]?.color"> {{ statusMap[record.status]?.text }}</Tag>-->
            <!--            </template>-->
          </template>
          <template #expandedRowRender="{ record: cellRecord }">
            <BasicTable :columns="tablecolum()" :can-resize="false" :data-source="cellRecord.items_sub" :show-index-column="false">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'imgs'">
                  <TableImg :imgList="record.imgs" :simpleShow="true" />
                </template>
                <template v-if="column.key === 'files'">
                  <div v-for="(newVal, index) in record.files" :key="index">
                    <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
                  >
                </template>
              </template>
            </BasicTable>
          </template>
        </BasicTable>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <QcDrawer @register="registerQcDrawer" @success="qcReload" />
    <UpdateAtModal @register="registerUpdateAtModal" @success="purchaseOrderReload" />
    <PreviewFile @register="registerModal" />
  </div>
</template>

<script setup lang="ts" name="/wms/purchaseReceipt">
import { getItemsList, setStatus, exportFile } from '/@/api/erp/PurchaseTracking'
import { childRenColumns, columnsFn, otherSchema, searchFromSchema, tablecolum } from './datas/datas'
import QcDrawer from '/@/views/erp/newqc/components/editDrawer.vue'
import { BasicTable, useTable, TableImg, TableActionType, TableAction, ActionItem } from '/@/components/Table'
import { Button, message, Tooltip } from 'ant-design-vue'
import { ref, unref } from 'vue'
import { getPurchaseOrderList } from '/@/api/erp/purchaseOrder'
import { cloneDeep, isArray, isEmpty } from 'lodash-es'
import { useDrawer } from '/@/components/Drawer'
import { usePermission } from '/@/hooks/web/usePermission'
import UpdateAtModal from '/@/views/revisitManage/revisitLog/components/UpdateAtModal.vue'
import * as propertyConst from '/@/views/revisitManage/revisitLog/datas/const'
import { useModal } from '/@/components/Modal'
import { useMessage } from '/@/hooks/web/useMessage'
import { createImgPreview } from '/@/components/Preview'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { useRoute } from 'vue-router'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'

const route = useRoute()
const { path: routePath } = route
const pageSearchInfo = ref({})

pageSearchInfo.value = {}
if (window.history.state?.searchParams) {
  pageSearchInfo.value = window.history.state.searchParams
}
const { createMessage } = useMessage()
const exporting = ref<boolean>(false)
const { hasPermission } = usePermission()
const expandedRowRefs = ref<{ [key: string]: TableActionType | Nullable<any> }>({})
const isDisable: any = ref(true)
const childrenexpandedRowKeys = ref<number[]>([])
let tableAction = ref()
const pushing = ref<boolean>(false)
const [registerQcDrawer, { openDrawer: openQcDrawer, setDrawerProps: setQcDrawerProps }] = useDrawer()
const [registerUpdateAtModal, { openModal, setModalProps }] = useModal()
const [registerTable, { reload: purchaseOrderReload, getForm, setLoading }] = useTable({
  title: '采购商品跟踪',
  api: getPurchaseOrderList,
  columns: columnsFn(),
  searchInfo: {
    is_purchase: 1,
    from: 2
  },
  sortFn: (sortInfo) => {
    // mapRequestSort.value = sortInfo.order
    const map = {
      ascend: 'asc',
      descend: 'desc'
    }
    if (isArray(sortInfo)) {
      return { orderByList: JSON.stringify(sortInfo.map((item) => ({ orderBy: item.field, sort: map[item.order] }))) }
    }

    return sortInfo.order
      ? {
          orderByList: JSON.stringify([
            {
              sort: map[sortInfo.order],
              orderBy: sortInfo.field
            }
          ])
        }
      : {}
  },
  showIndexColumn: false,
  showTableSetting: true,
  useSearchForm: true,
  rowKey: 'id',
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas: [...searchFromSchema, ...otherSchema],
    fieldMapToTime: [
      ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['request_status_at', ['request_status_at_start', 'request_status_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['deliver_at', ['deliver_at_start', 'deliver_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['production_finish_at', ['production_finish_at_start', 'production_finish_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  },
  pagination: {
    // size: 'small',
    pageSize: 10,
    pageSizeOptions: ['10', '20', '100']
  },
  actionColumn: {
    width: 250,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  beforeFetch: async (params) => {
    let pageParams = {}
    if (!isEmpty(pageSearchInfo.value)) {
      const form = getForm()
      pageParams = {
        ...pageSearchInfo.value
      }
      await form.setFieldsValue(pageParams)
      pageSearchInfo.value = {}
    }
    return {
      ...params,
      ...pageParams
    }
  }
})

/** 注册子表格 */
const [registerChildrenTable, {}] = useTable({
  showIndexColumn: false,
  columns: childRenColumns,
  showTableSetting: false,
  useSearchForm: false,
  isTreeTable: false,
  rowKey: 'id',
  canResize: false,
  rowSelection: {
    onChange: handleChange,
    getCheckboxProps: (record) => {
      if (record.status == 2) {
        return { disabled: true }
      } else {
        return { disabled: false }
      }
    }
  },

  pagination: {
    pageSize: 10,
    pageSizeOptions: ['10', '20', '100']
  }
})

/** 点击checkbox时 */
function handleChange() {
  isDisable.value = cloneDeep(unref(expandedRowRefs))
  for (let i in isDisable.value) {
    tableAction.value = cloneDeep(unref(expandedRowRefs)[i]?.tableAction)
    if (tableAction.value?.getSelectRows().length == 0) {
      delete isDisable.value[i]
    }
  }
}

/** 点击批量设置完成状态 */
async function endProduction(parentId) {
  try {
    pushing.value = true
    tableAction.value = cloneDeep(unref(expandedRowRefs)[parentId]?.tableAction)
    const selectedRow = tableAction.value?.getSelectRows().map((item) => {
      return {
        id: item.id
      }
    })
    await setStatus({ ids: selectedRow, status: 2 })
    tableAction.value?.reload()
    tableAction.value?.clearSelectedRowKeys()
    message.success('操作成功！')
    purchaseOrderReload()
  } catch (error) {
    console.log(error)
    message.error('操作失败！')
  } finally {
    pushing.value = false
  }
}

function createActions(record): ActionItem[] {
  return [
    {
      icon: 'clarity:contract-line',
      label: '生成采购质检单',
      onClick: handleAddQc.bind(null, record),
      ifShow: hasPermission(267),
      disabled: [0, 1].includes(record.qc_status)
    },
    {
      label: '申请延期',
      onClick: () => {
        setModalProps({ title: '申请延期' })
        openModal(true, { work_id: record.work_id, type: propertyConst.PURCHASEAPPLYDELAYLABLE, id: record.sale_work_id })
      },
      tooltip: '需求生产完成日期默认会在填写的交付日期上减五天',
      ifShow: hasPermission([287])
      // disabled: record.follow_up_at ? new Date(record.follow_up_at).getTime() > new Date().getTime() : true
      // color: record.follow_up_at ? (new Date(record.follow_up_at).getTime() > new Date().getTime() ? 'success' : 'error') : 'warning'
    }
  ]
}

function handleAddQc(record) {
  setQcDrawerProps({ title: '新增采购质检', showFooter: true })
  openQcDrawer(true, { type: 'add', qcType: 1, purchaseRecord: record })
}

function qcReload() {
  purchaseOrderReload()
}

async function handleExport(type: number) {
  try {
    exporting.value = true
    setLoading(true)
    const params = getForm()?.getFieldsValue()
    const response = await exportFile({ ...params, is_purchase: 1, is_excel: type })
    // const blob = new Blob([response.data], { type: response.headers['content-type'] })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    downloadLink.download = `采购商品跟踪-${+new Date()}.xlsx`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    createMessage.success('导出成功')
  } catch (err) {
    createMessage.error('导出失败')
    throw new Error(err)
  } finally {
    setLoading(false)
    exporting.value = false
  }
}

//展示
const [registerModal, { openModal: openchildrenModal }] = useModal()
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  console.log(prefix)
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openchildrenModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>
