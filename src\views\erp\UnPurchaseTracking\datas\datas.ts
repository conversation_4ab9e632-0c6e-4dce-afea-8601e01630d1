import type { BasicColumn, FormSchema } from '/@/components/Table'
import { columns as saleOrderColumns, searchFormSchema } from '/@/views/erp/saleOrder/datas/datas'
import {} from '/@/views/erp/saleOrder/datas/datas'
import { isNull, isUndefined } from 'lodash-es'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'

import { cloneDeep } from 'lodash-es'
import { getStaffList } from '/@/api/erp/systemInfo'
import { mul } from '/@/utils/math'
import { useI18n } from '/@/hooks/web/useI18n'
import { useRender } from '/@/components/Table/src/hooks/useRender'

const { tm } = useI18n()
export const statusMap = {
  0: { color: '', text: '未生产' },
  1: { color: 'green', text: '生产中' },
  2: { color: 'skyblue', text: '生产完成' }
}

/** 引用销售订单页面表格进行过滤 */
export const columnsFn = (): BasicColumn[] => {
  const columns = cloneDeep(saleOrderColumns).filter((item) => {
    const arr: any = ['created_at', 'source_uniqid', 'strid', 'status', 'department', 'program_incharge_name', 'inCharge_name', 'received']
    if (arr.includes(item.dataIndex)) {
      return item
    }
  })
  columns.splice(2, 0, {
    title: '紧急状态',
    dataIndex: 'urgent_level',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      const mapUrgentLevel = tm('tag.mapUrgentLevel')
      const curUrgentLevel = mapUrgentLevel[text]
      if (curUrgentLevel) return useRender.renderTag(curUrgentLevel.alias, curUrgentLevel.color)
      return '-'
    }
  })
  columns.splice(3, 0, {
    title: '采购是否逾期',
    dataIndex: 'purchase_is_overdue',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      if (!text || text === '0') {
        return useRender.renderTag('否', 'green')
      } else {
        return useRender.renderTag('是', 'red')
      }
    }
  })
  columns.splice(4, 0, {
    title: '是否完成产品拆分',
    dataIndex: 'is_finish_split',
    width: 130,
    resizable: true,
    customRender: ({ text }) => {
      if (!text || text === '0') {
        return useRender.renderTag('否', '')
      } else {
        return useRender.renderTag('是', 'green')
      }
    }
  })
  columns.splice(7, 0, {
    title: '采购需求日期人员',
    dataIndex: 'purchase_est_finish_creator_name',
    width: 150,
    resizable: true
  })
  columns.splice(8, 0, {
    title: '采购需求完成日期',
    dataIndex: 'purchase_est_finish_at',
    width: 150,
    resizable: true
  })
  columns.push(
    {
      title: '未采购金额',
      dataIndex: 'noPurchaseAmount',
      width: 100,
      resizable: true,
      customRender: ({ value }) => {
        return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
      }
    },
    {
      title: '可备货日期',
      dataIndex: 'stock_at',
      width: 150,
      resizable: true
    },
    {
      title: '创建日期',
      dataIndex: 'created_at',
      width: 150,
      resizable: true
    }
  )
  return columns
}

/** 子表格 */
export const childRenColumns: BasicColumn[] = [
  {
    title: 'id',
    dataIndex: 'id',
    width: 100,
    resizable: true
  },
  {
    title: '销售单号',
    dataIndex: 'source_uniqid',
    width: 200,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 100,
    resizable: true
  },
  {
    title: '商品名称',
    dataIndex: 'name',
    width: 200,
    resizable: true
  },
  {
    title: '图片',
    dataIndex: 'imgs',
    width: 100,
    resizable: true
  },
  {
    title: '产品编码',
    dataIndex: 'puid',
    width: 200,
    resizable: true
  },
  {
    title: '原单价',
    dataIndex: 'unit_price_org',
    width: 100,
    resizable: true
  },
  {
    title: '单价',
    dataIndex: 'unit_price',
    width: 100,
    resizable: true
  },
  {
    title: '需求原数量',
    dataIndex: 'qty_request_org',
    width: 100,
    resizable: true
  },
  {
    title: '需求数量',
    dataIndex: 'qty_request',
    width: 100,
    resizable: true
  },
  {
    title: '需求实际数量',
    dataIndex: 'qty_request_actual',
    width: 100,
    resizable: true
  },
  {
    title: '未采购数量',
    dataIndex: 'qty_request_left',
    width: 100,
    resizable: true
  },
  {
    title: '总价',
    dataIndex: 'total_amount',
    width: 100,
    resizable: true
  },
  {
    title: '未采购金额',
    dataIndex: 'amountNotPurchased',
    width: 100,
    resizable: true,
    customRender: ({ record }) => {
      return mul(record.qty_request_left, record.unit_price) ?? '0.00'
    }
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 100,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 100,
    resizable: true
  }
]

//子产品tablecolum
export const tablecolum = (type?: string): BasicColumn[] => [
  {
    title: 'id',
    dataIndex: 'id',
    width: 100,
    resizable: true,
    defaultHidden: true
  },
  {
    title: 'request_id',
    dataIndex: 'request_id',
    width: 100,
    resizable: true,
    defaultHidden: true
  },
  {
    title: 'work_id',
    dataIndex: 'work_id',
    width: 100,
    resizable: true,
    defaultHidden: true
  },
  {
    title: '产品名称',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  {
    title: '产品数量',
    dataIndex: 'quantity',
    width: 100,
    resizable: true
  },
  {
    title: '种类占比',
    dataIndex: 'proportion_org',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text + '%' : '-'
    }
  },
  {
    title: '单个sku占比',
    dataIndex: 'proportion',
    width: 100,
    resizable: true
    // customRender: ({ text }) => {
    //   return text ? text + '%' : '-'
    // }
    // defaultHidden: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 100,
    resizable: true
  },
  {
    title: type == 'retreat' ? '剩余可退货数量' : '剩余数量',
    dataIndex: 'quantity_left',
    width: 100,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 100,
    resizable: true
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 100,
    resizable: true
  },
  {
    title: '图片',
    dataIndex: 'imgs',
    width: 100,
    resizable: true
  },
  {
    title: '附件',
    dataIndex: 'files',
    width: 250,
    resizable: true
  },
  {
    title: 'type',
    dataIndex: 'type',
    width: 100,
    resizable: true,
    defaultHidden: true
  }
]
/** 引用销售订单界面的筛选进行过滤 */
export const searchFromSchemas: (routeName) => FormSchema[] = (routeName) =>
  cloneDeep(searchFormSchema(undefined, routeName))
    .filter((item) => {
      const arr = ['source_uniqid', 'submited_at', 'status', 'dept_id']
      if (arr.includes(item.field)) {
        return item
      }
    })
    .concat(otherFormSchema)

export const otherFormSchema: FormSchema[] = [
  {
    field: 'program_incharge',
    label: '方案负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'inCharge',
    label: '项目负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'urgent_level',
    label: '紧急状态',
    component: 'Select',
    componentProps: {
      options: tm(`tag.urgentLevelList`),
      fieldNames: { key: 'value', value: 'value', label: 'alias' }
    }
  },
  {
    field: 'stock_at',
    label: '可备货日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD'
    }
  },
  {
    field: 'created_at',
    label: '创建日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD'
    }
  }
]
