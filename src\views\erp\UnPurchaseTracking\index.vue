<template>
  <div class="p-4">
    <BasicTable :data-cachekey="routePath" @register="registerTable" v-model:expandedRowKeys="expandedRowKeys">
      <template #bodyCell="{ column: parentColumn, record: parentRecord }">
        <template v-if="parentColumn.dataIndex === 'action'">
          <TableAction :actions="createActions(parentRecord)" :drop-down-actions="createDropActions(parentRecord)" />
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <BasicTable
          class="p-4"
          @register="registerChildrenTable"
          :api="getItemsList.bind(null, { pageSize: 9999, source_uniqid: record.source_uniqid })"
          v-model:expandedRowKeys="childrenexpandedRowKeys"
        >
          <template #bodyCell="{ text, column }">
            <template v-if="column.key === 'imgs'">
              <TableImg :imgList="text" :simpleShow="true" />
            </template>
            <template v-if="column.key === 'status'">
              <Tag :color="statusMap[record.status]?.color"> {{ statusMap[record.status]?.text }}</Tag>
            </template>
          </template>
          <template #expandedRowRender="{ record: cellRecord }">
            <BasicTable :columns="tablecolum()" :can-resize="false" :data-source="cellRecord.items_sub" :show-index-column="false">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'imgs'">
                  <TableImg :imgList="record.imgs" :simpleShow="true" />
                </template>
                <template v-if="column.key === 'files'">
                  <div v-for="(newVal, index) in record.files" :key="index">
                    <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
                  >
                </template>
              </template>
            </BasicTable>
          </template>
        </BasicTable>
      </template>
      <template #toolbar>
        <Button v-if="hasPermission(338)" type="primary" @click="handleExport" :loading="exporting"> 导出搜索结果 </Button>
      </template>
    </BasicTable>
    <RetreatDrawer @register="registerRetreatDrawer" @success="reload" />
    <PreviewFile @register="registerModal" />
  </div>
</template>

<script setup lang="tsx" name="/wms/purchaseReceipt">
import { getSalesOrderList, setCencleReady, setIsAudit, setPurchaseEstFinishAt } from '/@/api/erp/sales'
import { columnsFn, childRenColumns, statusMap } from './datas/datas'
import { BasicTable, useTable, TableImg, TableAction, ActionItem, EditRecordRow } from '/@/components/Table'
import { Tag, Button, DatePicker } from 'ant-design-vue'
import { getItemsList, exportFile } from '/@/api/erp/UnPurchaseTracking'
import { searchFromSchemas, tablecolum } from './datas/datas'
import { onMounted, ref } from 'vue'
import { useMessage } from '/@/hooks/web/useMessage'
import { usePermission } from '/@/hooks/web/usePermission'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { getWorkList } from '/@/api/commonUtils'
import { useDrawer } from '/@/components/Drawer'
import RetreatDrawer from '/@/views/erp/saleOrder/components/CreateRetreatDrawer.vue'
import { createImgPreview } from '/@/components/Preview'
import { useModal } from '/@/components/Modal'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { useRoute } from 'vue-router'
import dayjs, { Dayjs } from 'dayjs'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'

const route = useRoute()
const { name: routeName, path: routePath } = route
const { hasPermission } = usePermission()
const { createMessage } = useMessage()
const exporting = ref<boolean>(false)
const expandedRowKeys = ref<number[]>([])
const childrenexpandedRowKeys = ref<number[]>([])

const [registerTable, { getForm, reload, setLoading, setProps }] = useTable({
  title: '未采购商品跟踪',
  api: getSalesOrderList,
  showIndexColumn: false,
  searchInfo: {
    is_purchase: 0,
    from: 3,
    is_finish_split: 1
  },
  columns: columnsFn(),
  showTableSetting: true,
  useSearchForm: true,
  isTreeTable: true,
  rowKey: 'id',
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    alwaysShowLines: 2,
    schemas: searchFromSchemas(undefined),
    fieldMapToTime: [
      ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['stock_at', ['stock_at_start', 'stock_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['submited_at', ['submited_at_start', 'submited_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  },
  actionColumn: {
    width: 350,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  pagination: {
    // size: 'small',
    pageSize: 10,
    pageSizeOptions: ['10', '20', '100']
  }
})

onMounted(() => {
  setProps({
    formConfig: {
      ...NEW_STATUS_FORMCONFIG,
      alwaysShowLines: 2,
      schemas: searchFromSchemas(routeName),
      fieldMapToTime: [
        ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
        ['stock_at', ['stock_at_start', 'stock_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
        ['submited_at', ['submited_at_start', 'submited_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
      ]
    }
  })
})

/** 注册子表格 */
const [registerChildrenTable, {}] = useTable({
  showIndexColumn: false,
  columns: childRenColumns,
  showTableSetting: false,
  useSearchForm: false,
  isTreeTable: true,
  rowKey: 'id',
  canResize: false,
  pagination: {
    pageSize: 10,
    pageSizeOptions: ['10', '20', '100']
  }
})

const [registerRetreatDrawer, { openDrawer: openRetreatDrawer, setDrawerProps: setRetreatDrawerProps }] = useDrawer()

async function handleExport() {
  try {
    exporting.value = true
    setLoading(true)
    const params = getForm()?.getFieldsValue()
    const response = await exportFile({ ...params, is_purchase: 0 })
    // const blob = new Blob([response.data], { type: response.headers['content-type'] })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    downloadLink.download = `未采购商品跟踪-${+new Date()}.xlsx`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    createMessage.success('导出成功')
  } catch (err) {
    createMessage.error('导出失败')
    throw new Error(err)
  } finally {
    setLoading(false)
    exporting.value = false
  }
}

/** 操作按钮 */
function createActions(record: EditRecordRow): Recordable[] {
  const saleStore = useSaleOrderStore()
  let editButtonList: ActionItem[] = [
    {
      label: '退货',
      // color: 'error',
      disabled: !saleStore.retreatSaleStatus.includes(record.status),
      onClick: handleRetreat.bind(null, record, 'retreat'),
      ifShow: hasPermission(381)
    },
    {
      label: '退货再建单',
      // color: 'error',
      disabled: !saleStore.retreatSaleStatus.includes(record.status),
      onClick: handleRetreat.bind(null, record, 'new'),
      ifShow: hasPermission(382)
    },
    {
      label: '取消可备货',
      // disabled: record.is_audit !== 2 || record.parent_id !== null,
      popConfirm: {
        okText: '确定',
        title: '确定要取消可备货吗？',
        cancelText: '取消',
        placement: 'topLeft',
        confirm: handleIsAudit.bind(null, record, 'stock'),
        disabled: ![2].includes(record.status)
      },
      ifShow: hasPermission(384)
    }
  ]

  return editButtonList
}

function createDropActions(record) {
  return [
    {
      label: '设置采购需求日期',
      ifShow: hasPermission(569),
      popConfirm: {
        placement: 'topRight',
        confirm: handleSetPurchaseAt.bind(null, record),
        title: (
          <>
            <span class="mr-2">设置采购需求日期</span>
            <DatePicker
              v-model:value={record.purchaseAt}
              popupStyle={{ 'z-index': 9999 }}
              value-format="YYYY-MM-DD 00:00:00"
              disabled-date={handleDisabledDate}
            />
          </>
        )
      }
    }
  ]
}

function handleDisabledDate(current: Dayjs) {
  return current && current < dayjs().startOf('day')
}

async function handleSetPurchaseAt(record) {
  try {
    const { news } = await setPurchaseEstFinishAt({ id: record.id, purchase_est_finish_at: record.purchaseAt })
    if (news === 'success') {
      createMessage.success('修改成功')
      return
    }
    createMessage.error('修改失败')
  } catch (err) {
    createMessage.error('修改失败')
    console.log(err)
  } finally {
    reload()
  }
}

//退货
async function handleRetreat(record, way: string) {
  const { items } = await getWorkList({ id: record.id, type: 3, item_left: 1 })

  if (items.length < 1 && way !== 'afterSale') return createMessage.error('没有可退货的商品')

  setRetreatDrawerProps({ title: '销售退货' })
  openRetreatDrawer(true, { record, type: 1, way })
}

async function handleIsAudit(record, type) {
  try {
    const { news } =
      type == 'is_audit' ? await setIsAudit({ id: record.id, is_audit: 0 }) : await setCencleReady({ id: record.id, status: 1 })
    if (news === 'success') {
      type == 'is_audit' ? createMessage.success('反结算成功') : createMessage.success('取消备货成功')
      await reload()
    }
  } catch (err) {
    console.error(err)
    type == 'is_audit' ? createMessage.error('反结算失败') : createMessage.error('取消备货失败')
  }
}

//展示
const [registerModal, { openModal }] = useModal()
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  console.log(prefix)
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>
