<template>
  <div>
    <BasicTable :data-cachekey="routePath" @register="registerTable" class="p-4" @fetch-success="handleFetch" @expand="onExpand">
      <template #toolbar>
        <!-- <Tooltip>
          <template #title>选中销售订单生成收款单</template>
          <a-button v-if="hasPermission([186])" @click="debouncedGenerate" :disabled="generateBtnStatus" class="mr-8px"
            >生成付款单</a-button
          >
        </Tooltip> -->
        <!-- <a-button @click="handleVerify" v-if="hasPermission([191])" :disabled="generateBtnStatus">收付款结账审核</a-button> -->
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
      </template>
      <template #form-receivable="{ model }">
        <FormItemRest>
          <div style="display: flex; align-items: center">
            <InputNumber v-model:value="model.receivable1" placeholder="应收金额初始值" style="height: 35px" :precision="4" />
            <span class="iconify" data-icon="ant-design:swap-outlined" style="width: 32px; padding: 0 3px 0 3px"></span>
            <InputNumber v-model:value="model.receivable2" placeholder="应收金额最大值" style="height: 35px" :precision="4" />
          </div>
        </FormItemRest>
      </template>
      <template #form-received="{ model }">
        <FormItemRest>
          <div style="display: flex; align-items: center">
            <InputNumber v-model:value="model.received_start" placeholder="已收金额初始值" style="height: 35px" :precision="4" />
            <span class="iconify" data-icon="ant-design:swap-outlined" style="width: 32px; padding: 0 3px 0 3px"></span>
            <InputNumber v-model:value="model.received_end" placeholder="已收金额最大值" style="height: 35px" :precision="4" />
          </div>
        </FormItemRest>
      </template>
    </BasicTable>
    <VerifyDrawer @register="registerDrawer" />
    <EditDrawer @register="registerEditDrawer" @success="reload" />
    <!-- <PurchaseModal @register="registerModal" @success="handleSuccess" /> -->
    <RetreatDrawer @register="registerRetreatDrawer" @success="reload" />
    <!-- <AddSaleorderDrawer @register="registerSaleorderDrawer" @success="reload" /> -->
    <BackOrderNumModal @register="registerBackOrderNumModal" @success="reload" />
  </div>
</template>

<script setup lang="ts">
import { nextTick, ref, onMounted } from 'vue'
// import { cloneDeep, debounce } from 'lodash-es'
import { Form, InputNumber, message } from 'ant-design-vue'
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import { useModal } from '/@/components/Modal'
import type { ActionItem } from '/@/components/Table'
import { useDrawer } from '/@/components/Drawer'
// import AddSaleorderDrawer from '../saleOrder/components/AddSaleorderDrawer.vue'
import { getSalesOrderList, setSalesStatus, getSalesOrderChildren } from '/@/api/erp/sales'
// import { addBatch } from '/@/api/financialDocuments/receiptOrder'
import { usePermission } from '/@/hooks/web/usePermission'
// import { useMessage } from '/@/hooks/web/useMessage'
import mitt from '/@/utils/mitt'
import VerifyDrawer from '../saleOrder/components/VerifyDrawer.vue'
import EditDrawer from '../saleOrder/components/EditDrawer.vue'
import type { IRecord } from '../saleOrder/datas/types'
import { columns, formConfigFn } from '../saleOrder/datas/datas'
// import { useModal } from '/@/components/Modal'
// import GenerateModal from '../saleOrder/components/GenerateModal.vue'
// import PurchaseModal from './components/PurchaseModal.vue'
import RetreatDrawer from '../saleOrder/components/CreateRetreatDrawer.vue'
import BackOrderNumModal from '../saleOrder/components/BackOrderNumModal.vue'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { getWorkList } from '/@/api/commonUtils'
import { useRoute } from 'vue-router'
const { hasPermission } = usePermission()
// import { useModal } from '/@/components/Modal'
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerEditDrawer, { openDrawer: openEditDrawer, setDrawerProps: setEditDrawerProps }] = useDrawer()
const [registerRetreatDrawer, { openDrawer: openRetreatDrawer, setDrawerProps: setRetreatDrawerProps }] = useDrawer()
const [registerBackOrderNumModal, { openModal: openBackOrderNumModal }] = useModal()
const route = useRoute()
const { path: routePath, name: routeName } = route

const FormItemRest = Form.ItemRest

const rootMenuEmitter = mitt()
const saleStore = useSaleOrderStore()
const [registerTable, { reload, setTableData, setLoading, getSelectRows, setProps, clearSelectedRowKeys }] = useTable({
  title: '备货订单列表',
  showIndexColumn: false,
  columns,
  api: getSalesOrderList,
  pagination: {
    pageSize: 10,
    position: ['bottomRight']
  },
  actionColumn: {
    width: 220,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  rowKey: 'id',
  useSearchForm: true,
  searchInfo: { type: 2 },
  formConfig: formConfigFn(),
  showTableSetting: true,
  rowSelection: {
    type: 'checkbox',
    onChange: handleChange,
    getCheckboxProps: (record) => {
      if (record.status === 0 || record.status === 16 || record.is_audit == 1 || record.parent_id !== null) {
        return { disabled: true }
      } else {
        return { disabled: false }
      }
    }
  }
})

onMounted(() => {
  rootMenuEmitter.emit('sale-order-reload', reload)
  setProps({ formConfig: formConfigFn(clearSelectedRowKeys, reload, undefined, routeName) })
})

/** 注册modal */
// const [registerModal, { openModal }] = useModal()

function createActions(record: IRecord): ActionItem[] {
  return [
    {
      icon: 'clarity:success-line',
      label: '生效',
      popConfirm: {
        okText: '确定',
        title: '确定将订单状态设置成生效状态吗',
        cancelText: '取消',
        placement: 'left',
        confirm: handleChangeStatus.bind(null, record, 1)
      },
      disabled: record.status !== 0 || record.parent_id !== null,
      ifShow: hasPermission([187])
    },
    {
      icon: 'clarity:success-line',
      label: '可备货',
      // popConfirm: {
      //   okText: '确定',
      //   title: '确定将订单状态设置成可备货状态吗',
      //   cancelText: '取消',
      //   placement: 'left',
      //   confirm: handleChangeStatus.bind(null, record, 2)
      // },
      onClick: handleSetCankao.bind(null, record),
      disabled: record.status !== 1 || record.parent_id !== null,
      ifShow: hasPermission([212])
    }
  ]
}

function createDropDownActions(record: IRecord): ActionItem[] {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      disabled: record.type !== 2,
      ifShow: hasPermission([184])
    },
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      disabled: ![0, 1].includes(record.status) || record.parent_id !== null,
      // disabled: record.status !== 0 && record.is_reject == 0,
      ifShow: hasPermission([190])
    },
    {
      icon: 'clarity:close-line',
      label: '取消订单',
      popConfirm: {
        okText: '确定',
        title: '确定将订单状态设置成取消状态吗',
        cancelText: '取消',
        placement: 'topLeft',
        confirm: handleChangeStatus.bind(null, record, 16),
        disabled: record.status !== 0 || record.parent_id !== null
      },
      // disabled: record.status !== 0 || record.parent_id !== null,
      ifShow: hasPermission([188])
    },
    {
      label: '退货',
      color: 'error',
      disabled: !saleStore.retreatSaleStatus.includes(record.status) || record.parent_id !== null,
      onClick: handleRetreat.bind(null, record),
      ifShow: hasPermission([185])
    }
  ]
}

function handleSetCankao(record: IRecord) {
  openBackOrderNumModal(true, record)
}

async function handleChangeStatus(record: IRecord, status: number) {
  console.log(status)

  try {
    const result = await setSalesStatus({ id: record.id, status })
    console.log(result)
    reload()
  } catch (err) {
    throw new Error(`${err}`)
  }
}

function handleDetail(record: IRecord): void {
  setDrawerProps({ title: '备货详情' })
  openDrawer(true, record)
}

function handleEdit(record: IRecord) {
  setEditDrawerProps({ title: '编辑备货订单' })
  openEditDrawer(true, { record, type: 'edit' })
}

//退货
async function handleRetreat(record: IRecord) {
  const { items } = await getWorkList({ id: record.id, item_left: 1 })
  if (items.length < 1) return message.error('没有可退货的商品')
  setRetreatDrawerProps({ title: '退货' })
  openRetreatDrawer(true, { record, type: 1 })
}

function handleFetch({ items }: { items: IRecord[] }): void {
  nextTick(() => {
    setTableData(items.map((item) => ({ ...item, children: [] })))
  })
}
// function handleAddOrder(record: IRecord): void {
//   // openModal(true, record)
//   openSaleorderDrawer(true, { record })
// }

async function onExpand(expanded: boolean, record: IRecord): Promise<void> {
  console.log(record, 'record', expanded)
  if (!expanded || record.children.length > 0) return
  setLoading(true)
  try {
    const childrenData = await getSalesOrderChildren(record.id, record.basic_work_id)
    Object.assign(
      record.children,
      childrenData.items.map((item) => ({ ...item, children: [] }))
    )
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    setLoading(false)
  }
}

/** 选中 */
const generateBtnStatus = ref(true)
function handleChange() {
  if (getSelectRows().length == 0) {
    generateBtnStatus.value = true
  } else {
    generateBtnStatus.value = false
  }
}

/** 生成付款单 */
// const debouncedGenerate = debounce(handleGenerate, 200)
// async function handleGenerate() {
//   let clientId: Array<number> = []
//   let recordData: Array<object> = []

//   try {
//     getSelectRows().forEach((item) => {
//       recordData.push(item)
//       clientId.push(item.client_id)
//     })

//     if (new Set(clientId).size == 1) {
//       openModal(true, { record: recordData, selectRowsData: cloneDeep(clientId[0]) })
//     } else {
//       message.error('不同客户不能生成同一张收款单！')
//     }
//   } catch (error) {
//     message.success('生成付款单失败!')
//   }
// }

// const { createConfirm } = useMessage()
//结账核对
// function handleVerify() {
//   const selectData = getSelectRows()
//   if (selectData.length < 1) return
//   createConfirm({
//     iconType: 'warning',
//     title: () => h('span', '结账核对'),
//     content: () => h('span', '只有状态为执行中的订单才可以进行结账核对,确定继续结账核对吗?'),
//     onOk: async () => {
//       const data = selectData.map((item) => {
//         return item.id
//       })

//       await verifyOrder({
//         is_audit: 1,
//         work_ids: data
//       })
//       handleSuccess()
//     }
//   })
// }

// function handleSuccess() {
//   clearSelectedRowKeys()
//   reload()
// }
//modal
// const [registerModel, { openModal }] = useModal()
</script>
<style lang="less" scoped>
:deep(.ant-input-number) {
  width: 235px !important;
}
</style>
