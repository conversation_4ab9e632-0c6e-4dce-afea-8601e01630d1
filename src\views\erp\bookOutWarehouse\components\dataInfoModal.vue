<template>
  <BasicModal @register="registerModal" title="信息更改" :minHeight="400" @ok="handleOk">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { schemas } from '../datas/modal'
import { message } from 'ant-design-vue'
import { updateOutWarehouse } from '/@/api/erp/bookOutWarehouse'
import { ref } from 'vue'
const id = ref()
const [registerModal, { closeModal }] = useModalInner(async (data) => {
  console.log(data)
  id.value = data.record.id
  await resetFields()
})
const [registerForm, { getFieldsValue, resetFields }] = useForm({
  labelWidth: 120,
  schemas,
  showActionButtonGroup: false
})
const emit = defineEmits(['success', 'registerModal'])
function handleOk() {
  const formdata = getFieldsValue()
  console.log(formdata)

  if (Object.keys(formdata).length === 0 || Object.values(formdata).every((value) => value === undefined)) {
    message.error('请至少填写一项信息')
  } else {
    const params = {
      ...formdata,
      id: id.value
    }
    console.log(formdata)
    updateOutWarehouse(params)
    closeModal()
    emit('success')
  }
}
</script>
