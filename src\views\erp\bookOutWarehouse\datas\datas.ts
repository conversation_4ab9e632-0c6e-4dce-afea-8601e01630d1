import { BasicColumn, FormSchema } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { isNumber } from 'lodash-es'
import { useI18n } from '/@/hooks/web/useI18n'
const { t, tm } = useI18n()

export const columns: BasicColumn[] = [
  {
    title: '单号',
    dataIndex: 'strid'
  },
  {
    title: '明细订单',
    dataIndex: 'item',
    customRender: ({ text }) => {
      const nameArr = text.map((item) => item.source_uniqid)
      return text ? useRender.renderTags(nameArr) : '-'
    }
  },
  {
    title: '预约出库日期',
    dataIndex: 'plan_out_at'
  },
  {
    title: '责任人',
    dataIndex: 'inCharge_name'
  },
  {
    title: '部门',
    dataIndex: 'department'
  },
  {
    title: '备注',
    dataIndex: 'remark'
  },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) =>
      isNumber(text) ? useRender.renderTag(t(`tag.bookingStatus.${text}.label`), t(`tag.bookingStatus.${text}.color`)) : '-'
  },
  {
    title: '紧急程度',
    dataIndex: 'urgent_level',
    customRender: ({ text }) =>
      isNumber(text) ? useRender.renderTag(t(`tag.mapUrgentLevel.${text}.alias`), t(`tag.mapUrgentLevel.${text}.color`)) : '-'
  }
]

export const searchFormSchema: FormSchema[] = [
  {
    field: 'source_uniqid',
    label: '销售订单',
    component: 'Input'
  },
  {
    field: 'urgent_level',
    label: '紧急程度',
    component: 'Select',
    componentProps: {
      options: tm(`tag.urgentLevelList`),
      fieldNames: { key: 'value', value: 'value', label: 'alias' }
    }
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: tm(`tag.bookingStatusList`)
    }
  }
]
