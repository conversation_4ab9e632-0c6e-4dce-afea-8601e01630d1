import { FormSchema } from '/@/components/Form'
// import { getWorkList } from '/@/api/commonUtils'
import { getStaffList } from '/@/api/baseData/staff'
import { getDeptTree } from '/@/api/admin/dept'
import { getProjectList } from '/@/api/projectOverview'
import { reactive, ref } from 'vue'
import { BasicTableProps } from '@/components/VxeTable'
import { getWorkList } from '/@/api/commonUtils'
import { WorkItem } from '/@/api/commonUtils/modle/types'

export const loading = ref<boolean>(false)
export const salesList = ref<WorkItem[]>([])
const columns = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    title: '销售单号',
    field: 'source_uniqid'
  },
  {
    title: '客户',
    field: 'client_name'
  },
  {
    title: '来源',
    field: 'source'
  },
  {
    title: '部门',
    field: 'department_name'
  },
  {
    title: '负责人',
    field: 'inCharge_name'
  },
  {
    title: '创建人',
    field: 'creator_name'
  }
]
export const gridOptions = reactive<BasicTableProps>({
  id: 'VxeTable',
  loading: loading,
  keepSource: true,
  editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
  columns,
  height: 700,
  proxyConfig: null,
  toolbarConfig: null,
  checkboxConfig: {
    trigger: 'row'
  }
})
export const getSchemas = (type, tableRef, record?: any, formEvent?: any): FormSchema[] => [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false
  },
  {
    field: 'work_id',
    label: 'work_id',
    component: 'Input',
    show: false
  },
  // {
  //   field: 'urgent_level',
  //   label: '紧急级别',
  //   component: 'RadioButtonGroup',
  //   componentProps: {
  //     options: [
  //       { label: '不紧急', value: 1 },
  //       { label: '比较紧急', value: 2 },
  //       { label: '非常紧急', value: 3 }
  //     ]
  //   },
  //   required: true
  // },
  {
    field: 'urgent_level',
    label: '紧急级别',
    required: true,
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        { label: '低', value: 1 },
        { label: '中', value: 2 },
        { label: '高', value: 3 }
      ]
    }
  },
  {
    field: 'project_number',
    label: '项目',
    component: 'PagingApiSelect',
    required: true,
    // dynamicDisabled: false,
    componentProps: {
      api: getProjectList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      returnParamsField: 'id',
      searchParamField: 'title',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'title' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'title'
      },
      itemProps: {
        validateTrigger: 'blur'
      },
      onChange: async (val) => {
        try {
          loading.value = true
          let { items } = await getWorkList({ project_number: val, pageSize: 1000 })
          items = items.filter((item) => [2, 3, 4, 5].includes(item.status))
          salesList.value = items
          if (type !== 'add') {
            Promise.resolve().then(() => {
              const { item } = record
              const selectedId = item.map((item) => item.work_id)
              tableRef.value.setCheckboxRow(
                items.filter((item) => selectedId.includes(item.id)),
                true
              )
              formEvent?.setFieldsValue({ items: items.filter((item) => selectedId.includes(item.id)) })
              // setFieldsValue({ items: items.filter((item) => selectedId.includes(item.id)) })
              if (type === 'detail') formEvent?.disabledForm()
            })
          }
        } catch (err) {
          throw new Error(err)
        } finally {
          loading.value = false
        }
      }
    }
  },
  {
    field: 'splicing_strid',
    label: '拼货单号',
    component: 'Input'
  },
  // {
  //   field: 'sale_work_id',
  //   label: '销售单号',
  //   component: 'PagingApiSelect',
  //   componentProps: {
  //     resultField: 'items',
  //     api: getWorkList,
  //     params: {
  //       type: 3
  //     },
  //     searchMode: true,
  //     pagingMode: true,
  //     searchParamField: 'source_uniqid',
  //     // immediate: true,
  //     selectProps: {
  //       allowClear: true,
  //       fieldNames: { key: 'id', value: 'id', label: 'source_uniqid' },
  //       showSearch: true,
  //       placeholder: '请选择',
  //       optionFilterProp: 'source_uniqid',
  //       disabled: type === 'detail'
  //     }
  //   },
  //   required: true,
  //   itemProps: {
  //     validateTrigger: 'blur'
  //   },
  //   dynamicDisabled: type === 'detail'
  // },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'PagingApiSelect',
    itemProps: {
      validateTrigger: 'blur'
    },
    dynamicDisabled: true,
    required: true,
    componentProps() {
      return {
        api: getStaffList,
        resultField: 'items',
        selectProps: {
          allowClear: true,
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
          // disabled: type === 'detail'
          // disabled: true
        }
      }
    }
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: true,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        placeholder: '请选择',
        treeDefaultExpandAll: true,
        showSearch: true,
        optionFilterProp: 'name',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    },
    required: true
  },
  {
    field: 'plan_out_at',
    label: '预约出库时间',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      class: 'w-full'
    },
    required: true,
    dynamicDisabled: type === 'detail'
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
    dynamicDisabled: type === 'detail'
  },
  // {
  //   label: '审核状态',
  //   field: 'status',
  //   component: 'RadioButtonGroup',
  //   defaultValue: 0,
  //   componentProps: {
  //     options: [
  //       { label: '已审核', value: 1 },
  //       { label: '未审核', value: 0 }
  //     ]
  //   },
  //   required: true,
  //   show: false
  // },
  {
    label: '预约明细',
    field: 'items',
    component: 'Select',
    required: true,
    slot: 'Items'
  }
]
