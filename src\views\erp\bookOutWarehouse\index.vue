<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button v-if="hasPermission(253)" type="primary" @click="handleCreate"> 新建预约 </a-button>

        <PopConfirmButton v-if="hasPermission(320)" title="确定作废选中的订单吗" placement="left" @confirm="handleNullify" type="error">
          作废
        </PopConfirmButton>
        <!--        <a-button v-if="hasPermission(257)" type="primary" @click="handleBatchApprove"> 批量审核 </a-button>-->
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :stop-button-propagation="true"
            :actions="createActions(record)"
            :drop-down-actions="createDropDownActions(record)"
          />
        </template>
      </template>
    </BasicTable>
    <BookingDrawer @register="registerDrawer" @success="reload" />
    <OutWarehouseDrawer @register="registerOutWarehouseDrawer" @success="reload" />
    <dataInfoModal @register="registerInfoModal" @success="reload" />
  </div>
</template>

<script setup lang="ts">
import { ActionItem, BasicTable, TableAction, useTable } from '/@/components/Table'
import { approveBooking, getBookingList } from '/@/api/erp/bookOutWarehouse'
import { columns, searchFormSchema } from './datas/datas'
import { useDrawer } from '/@/components/Drawer'
import BookingDrawer from './components/bookingDrawer.vue'
import { usePermission } from '/@/hooks/web/usePermission'
import OutWarehouseDrawer from '/@/views/erp/outWarehouse/components/OutWarehouseDrawer.vue'
import { PopConfirmButton } from '/@/components/Button'
import { useMessage } from '/@/hooks/web/useMessage'
import dataInfoModal from './components/dataInfoModal.vue'
import { useModal } from '/@/components/Modal'

const { createMessage } = useMessage()
const { hasPermission } = usePermission()
const [registerTable, { reload, setLoading, getSelectRows, clearSelectedRowKeys }] = useTable({
  title: '预约出库',
  api: getBookingList,
  searchInfo: {
    order_by: 'id',
    sort: 'desc'
  },
  showIndexColumn: false,
  columns,
  showTableSetting: true,
  rowKey: 'id',
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  useSearchForm: true,
  formConfig: {
    name: 'searchForm',
    labelWidth: 120,
    baseColProps: { span: 8 },
    schemas: searchFormSchema,
    alwaysShowLines: 1,
    fieldMapToTime: [
      ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['check_at', ['check_at_start', 'check_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  },
  afterFetch: (data) => {
    clearSelectedRowKeys()
    return data
  },
  rowSelection: {
    getCheckboxProps: (record) => {
      return { disabled: record.status === 16 }
    }
  }
})

const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()

const [registerOutWarehouseDrawer, { openDrawer: openOutWarehouseDrawer, setDrawerProps: setOutWarehouseDrawerProps }] = useDrawer()
const [registerInfoModal, { openModal }] = useModal()

function handleCreate() {
  setDrawerProps({ title: '新建预约', showFooter: true })
  openDrawer(true, { type: 'add' })
}

function createActions(record): ActionItem[] {
  return [
    {
      disabled: record.status !== 0,
      label: '编辑',
      onClick: () => {
        setDrawerProps({ title: '编辑预约', showFooter: true })
        openDrawer(true, { record, type: 'edit' })
      },
      ifShow: hasPermission(254)
    },
    {
      // disabled: record.status === 1,
      label: '确定预约',
      popConfirm: {
        okText: '确定',
        title: '确定修改状态为确定吗？',
        cancelText: '取消',
        placement: 'left',
        disabled: record.status !== 0,
        confirm: handleApprove.bind(null, record, 1)
      },
      // onClick: handleApprove.bind(null, record, 1),
      ifShow: hasPermission(258)
    }
  ]
}

function createDropDownActions(record): ActionItem[] {
  return [
    {
      label: '生成出库单',
      disabled: record.status !== 1,
      onClick: () => {
        setOutWarehouseDrawerProps({ showFooter: true })
        openOutWarehouseDrawer(true, { type: 'add', bookOutWarehouse: record })
      },
      ifShow: hasPermission(259)
    },
    {
      label: '详情',
      onClick: () => {
        setDrawerProps({ title: '预约详情', showFooter: false })
        openDrawer(true, { record, type: 'detail' })
      },
      ifShow: hasPermission(255)
    },
    {
      label: '信息更改',
      onClick: () => {
        openModal(true, { record })
      },
      ifShow: hasPermission(446)
    }
    // {
    //   disabled: record.status === 0,
    //   label: '取消审核',
    //   onClick: handleApprove.bind(null, record, 0),
    //   ifShow: hasPermission(259)
    // },
    // {
    //   ifShow: hasPermission(256),
    //   label: '删除',
    //   popConfirm: {
    //     okText: '确定',
    //     title: '确定删除此数据吗？',
    //     cancelText: '取消',
    //     placement: 'left',
    //     disabled: record.status !== 0,
    //     confirm: async () => {
    //       try {
    //         setLoading(true)
    //         const { msg } = await delBooking({ id: record.id })
    //         if (msg === 'success') {
    //           await reload()
    //         }
    //       } catch (e) {
    //         throw new Error(e)
    //       } finally {
    //         setLoading(false)
    //       }
    //     }
    //   }
    // }
  ]
}

async function handleApprove(record, status: number) {
  try {
    setLoading(true)
    const { msg } = await approveBooking({ ids: [{ id: record.id, status }] })
    if (msg === 'success') {
      await reload()
    }
    setLoading(false)
  } catch (e) {
    setLoading(false)
    throw new Error(e)
  }
}

async function handleNullify() {
  const selectRow = getSelectRows()
  if (selectRow.length === 0) {
    return createMessage.error('请勾选需要作废的数据')
  }
  try {
    setLoading(true)
    const { msg } = await approveBooking({ ids: selectRow.map((item) => ({ id: item.id, status: 16 })) })
    if (msg === 'success') {
      clearSelectedRowKeys()
      await reload()
    }
    setLoading(false)
  } catch (err) {
    setLoading(false)
    throw new Error(err)
  }
}

// async function handleBatchApprove() {
//   try {
//     setLoading(true)
//     const keys = getSelectRowKeys()
//     const data = keys.map((key) => ({ id: key, status: 1 }))
//     const { msg } = await approveBooking({ ids: data })
//     if (msg === 'success') {
//       await reload()
//     }
//     setLoading(false)
//   } catch (e) {
//     setLoading(false)
//     throw new Error(e)
//   }
// }
</script>
