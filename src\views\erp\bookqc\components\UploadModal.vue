<template>
  <BasicModal @register="register" title="附件上传" @ok="handleOk" width="800px">
    <BasicForm @register="registerform">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          :multiple="true"
          @remove="handleRemove"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template></BasicForm
    ></BasicModal
  >
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { UploadFile, Upload, message } from 'ant-design-vue'
import { ref, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { qrpqsetFiles } from '/@/api/erp/bookqc'

//id
const propsFiles = ref()
const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
  console.log(data)
  setFieldsValue({ id: data?.id })
  propsFiles.value = data?.files
  filesList.value = data?.files?.map((file) => ({ name: file, url: file, uid: Math.random() * 100000 })) ?? []
})
const [registerform, { setFieldsValue, getFieldsValue }] = useForm({
  schemas: [
    {
      field: 'id',
      label: 'id',
      component: 'Input',
      show: false
    },
    {
      field: 'files',
      label: '附件上传',
      component: 'Upload',
      slot: 'Files',
      required: true,
      rules: [
        {
          required: true,
          validator: async (_rule: Rule, value: string) => {
            if (!value || value.length === 0) return Promise.reject('请上传附件')
            return Promise.resolve()
          }
          // trigger: 'change'
        }
      ],
      colProps: {
        span: 24
      }
    }
  ],
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelCol: { span: 5 }
})

//附件
const filesList = ref<UploadFile[]>([])

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val.map((item) => item.url) })
  }
)

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    console.log(file)
    changeOkLoading(true)
    const curFile = filesList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({
      files: filesList.value.map((item) => item.url)
    })
    // 判断是否所有的文件都已经上传成功，如果有uploading状态的就不修改按钮状态
    const isAllDone = filesList.value.every((item) => item.url)
    if (isAllDone) {
      changeOkLoading(false)
    }
  } catch (err) {
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}

async function handleRemove(file: UploadFile) {
  if (propsFiles.value.length > 0) {
    for (const item of propsFiles.value) {
      if (item == file.url) {
        message.error('不能删除已存在的文件')
        return false
      }
    }
  }
}

const emit = defineEmits(['relaod', 'register'])
// 提交
async function handleOk() {
  try {
    await changeOkLoading(true)
    const formdata = await getFieldsValue()
    for (const items of formdata.files) {
      if (items == '' || items == undefined || items == null) {
        return message.error('文件上次失败,请检查文件上次后,再提交')
      }
    }
    await qrpqsetFiles(formdata)

    emit('relaod')
    await closeModal()
    message.success('附件上传成功')
    filesList.value = []
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  } finally {
    changeOkLoading(false)
  }
}
</script>
