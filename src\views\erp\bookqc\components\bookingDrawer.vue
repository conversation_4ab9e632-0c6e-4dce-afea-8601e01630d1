<template>
  <div>
    <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="70%" destroyOnClose @ok="handleSubmit" @close="handleClose">
      <BasicForm @register="registerForm">
        <template #Items>
          <FormItemRest>
            <Alert message="勾选订单作为预约明细" type="info" show-icon />
            <VxeBasicTable
              ref="tableRef"
              class="!p-0 mt-1"
              :data="salesList"
              v-bind="gridOptions"
              @checkbox-change="handleSelectChange"
              @checkbox-all="handleSelectChange"
            />
          </FormItemRest>
        </template>
        <template #files>
          <Upload
            v-model:file-list="filesList"
            action="/api/oss/putImg"
            list-type="picture-card"
            :custom-request="handleFileRequest"
            :multiple="true"
            :disabled="types === 'detail'"
          >
            <div>
              <plus-outlined />
              <div style="margin-top: 8px">Upload</div>
            </div>
          </Upload>
        </template>
      </BasicForm>
    </BasicDrawer>
  </div>
</template>

<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { getSchemas, gridOptions, salesList } from '../datas/drawer.datas'
import { AddqcUpdateBooking } from '/@/api/erp/bookqc'
import defaultUser from '/@/utils/erp/defaultUser'
import { VxeBasicTable } from '/@/components/VxeTable'
import { Form, Alert, UploadFile, message, Upload } from 'ant-design-vue'
import { ref, computed, watch } from 'vue'
import { useMessage } from '/@/hooks/web/useMessage'
import { pick } from 'lodash-es'
import { transformFieldDisabledStatus } from '/@/utils/formSchema'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { PlusOutlined } from '@ant-design/icons-vue'

const tableRef = ref<HTMLTableElement | null>(null)
const { createMessage } = useMessage()
const FormItemRest = Form.ItemRest
const propsData = ref({})
// 数据库返回的表格选中数据
const originSelectedIds = computed(() => {
  const originSelected = propsData.value?.record?.item || []
  return originSelected.map((item) => item.work_id)
})
const types = ref()
const emits = defineEmits(['success', 'register'])
const [registerDrawer, { closeDrawer, changeOkLoading, changeLoading }] = useDrawerInner(async (data: { type: string; record?: any }) => {
  try {
    const { type, record } = data
    changeLoading(true)
    types.value = type
    // await resetSchema(getSchemas(type, tableRef, record))
    propsData.value = data
    handleInit(type, record)
  } catch (err) {
    console.log(err)
    throw new Error(err)
  } finally {
    changeLoading(false)
  }
})
const [registerForm, { validate, setFieldsValue, resetSchema, updateSchema }] = useForm({
  baseColProps: { span: 24 },
  showActionButtonGroup: false,
  labelCol: { span: 2 },
  colon: true
  // schemas: getSchemas(type)
})

const mapSubmit = {
  add: handleAddSubmit,
  edit: handleEditSubmit
}

function handleAddSubmit(formItems) {
  return formItems.map((item) => ({ work_id: item.id, project_number: item.project_number, type: 1 }))
}

// 编辑提交的数据items数据 = 数据库明细表的明细（新增和删除） + 新增的明细
function handleEditSubmit(formItems) {
  const originData = propsData.value?.record?.item || []
  const originItems = formItems.map((item) => item.id)
  // 原有的items
  const items = originData
    .filter((item) => originItems.includes(item.work_id))
    .map((item) => pick({ ...item, type: 2 }, ['project_number', 'type', 'work_id', 'id']))

  // 删除的items
  const delItems = originData
    .filter((item) => !originItems.includes(item.work_id))
    .map((item) => pick({ ...item, type: 3 }, ['project_number', 'type', 'work_id', 'id']))

  // 新增的items
  const addItems = handleAddSubmit(formItems.filter((item) => !originSelectedIds.value.includes(+item.id)))

  return [...items, ...delItems, ...addItems]
}

async function handleSubmit() {
  try {
    changeOkLoading(true)
    const values = await validate()

    const params = {
      ...values,
      items: mapSubmit[propsData.value.type](values.items)
    }
    const { msg } = await AddqcUpdateBooking(params)
    if (msg === 'success') {
      closeDrawer()
      emits('success')
    }
    changeOkLoading(false)
  } catch (err) {
    console.log(err)
    changeOkLoading(false)
    throw new Error(err)
  }
}

function handleInit(type, record) {
  const mapHandle = {
    detail: handleDetail,
    edit: handleEdit,
    add: handleAdd
  }

  mapHandle[type](record)
  // 控制form的disabled状态
  updateSchema(transformFieldDisabledStatus(getSchemas('edit', tableRef, record), ['detail'].includes(type), ['inCharge'], 'field'))
}

function handleDetail(record) {
  handleEdit(record)
}

function handleEdit(record) {
  // 假设record.item为记录的字段
  const { item, project_number: project_num } = record
  const project_number = project_num || [...new Set(item.map((item) => item.project_number))]?.[0]
  if (!project_number) {
    return createMessage.error('没有项目号')
  }
  resetSchema(getSchemas('edit', tableRef, record, { setFieldsValue }))
  setFieldsValue({ ...record, project_number })
}

function handleAdd() {
  resetSchema(getSchemas('add', tableRef))
  setFieldsValue({ inCharge: defaultUser!.userId })
}

async function handleSelectChange() {
  const select = tableRef.value?.getCheckboxRecords(true)
  await setFieldsValue({ items: select || [] })
}

function handleClose() {
  salesList.value = []
}

//附件
const filesList = ref<UploadFile[]>([])

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val.map((item) => item.url) })
  }
)

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    console.log(file)
    changeOkLoading(true)
    const curFile = filesList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({
      files: filesList.value.map((item) => item.url)
    })
    // 判断是否所有的文件都已经上传成功，如果有uploading状态的就不修改按钮状态
    const isAllDone = filesList.value.every((item) => item.url)
    if (isAllDone) {
      changeOkLoading(false)
    }
  } catch (err) {
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}
</script>
