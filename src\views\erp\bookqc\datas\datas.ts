import { BasicColumn, FormSchema } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { isNumber } from 'lodash-es'
import { useI18n } from '/@/hooks/web/useI18n'
const { t, tm } = useI18n()

export const columns: BasicColumn[] = [
  {
    title: '单号',
    dataIndex: 'strid',
    resizable: true,
    width: 250
  },
  {
    title: '明细订单',
    dataIndex: 'item',
    customRender: ({ text }) => {
      const nameArr = text.map((item) => item.source_uniqid)
      return text ? useRender.renderTags(nameArr) : '-'
    },
    resizable: true,
    width: 250
  },
  {
    title: '预约质检日期',
    dataIndex: 'plan_qc_at',
    resizable: true
  },
  {
    title: '责任人',
    dataIndex: 'inCharge_name',
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'department',
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    resizable: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    resizable: true,
    customRender: ({ text }) =>
      isNumber(text) ? useRender.renderTag(t(`tag.bookingStatus.${text}.label`), t(`tag.bookingStatus.${text}.color`)) : '-'
  },
  {
    title: '紧急程度',
    dataIndex: 'urgent_level',
    resizable: true,
    customRender: ({ text }) =>
      isNumber(text) ? useRender.renderTag(t(`tag.mapUrgentLevel.${text}.alias`), t(`tag.mapUrgentLevel.${text}.color`)) : '-'
  }
]

export const searchFormSchema: FormSchema[] = [
  {
    field: 'source_uniqid',
    label: '销售订单',
    component: 'Input'
  },
  {
    field: 'urgent_level',
    label: '紧急程度',
    component: 'Select',
    componentProps: {
      options: tm(`tag.urgentLevelList`),
      fieldNames: { key: 'value', value: 'value', label: 'alias' }
    }
  }
]
