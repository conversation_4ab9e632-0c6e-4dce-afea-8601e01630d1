<template>
  <div>
    <BasicTable :data-cachekey="routePath" @register="registerTable">
      <template #toolbar>
        <a-button v-if="hasPermission(253)" type="primary" @click="handleCreate"> 新建预约 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :stop-button-propagation="true"
            :actions="createActions(record)"
            :drop-down-actions="createDropDownActions(record)"
          />
        </template>
      </template>
    </BasicTable>
    <BookingDrawer @register="registerDrawer" @success="reload" />
    <UploadModal @register="registerUploadModal" @relaod="reload" />
  </div>
</template>

<script setup lang="ts">
import { ActionItem, BasicTable, TableAction, useTable } from '/@/components/Table'
import { ApproveqcBooking, DelqcBooking, GetBookingqcList } from '/@/api/erp/bookqc'
import { columns, searchFormSchema } from './datas/datas'
import { useDrawer } from '/@/components/Drawer'
import BookingDrawer from './components/bookingDrawer.vue'
import { usePermission } from '/@/hooks/web/usePermission'
import { useRoute } from 'vue-router'
import UploadModal from './components/UploadModal.vue'
import { useModal } from '/@/components/Modal'

const route = useRoute()
const { path: routePath } = route
const { hasPermission } = usePermission()
const [registerUploadModal, { openModal: openModalUplad }] = useModal()

const [registerTable, { reload, setLoading }] = useTable({
  title: '预约质检',
  api: GetBookingqcList,
  showIndexColumn: false,
  columns,
  showTableSetting: true,
  rowKey: 'id',
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  useSearchForm: true,
  formConfig: {
    name: 'searchForm',
    labelWidth: 120,
    baseColProps: { span: 8 },
    schemas: searchFormSchema,
    alwaysShowLines: 1,
    fieldMapToTime: [
      ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['check_at', ['check_at_start', 'check_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  },
  rowSelection: {
    getCheckboxProps: (record) => {
      return { disabled: record.status === 16 }
    }
  }
})

const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()

function handleCreate() {
  setDrawerProps({ title: '新建预约', showFooter: true })
  openDrawer(true, { type: 'add' })
}

function createActions(record): ActionItem[] {
  return [
    {
      disabled: record.status !== 0,
      label: '编辑',
      onClick: () => {
        setDrawerProps({ title: '编辑预约', showFooter: true })
        openDrawer(true, { record, type: 'edit' })
      },
      ifShow: hasPermission(254)
    },
    {
      label: '确定预约',
      popConfirm: {
        okText: '确定',
        title: '确定修改状态为确定吗？',
        cancelText: '取消',
        placement: 'left',
        disabled: record.status !== 0,
        confirm: handleApprove.bind(null, record, 1)
      },
      ifShow: hasPermission(258)
    }
  ]
}

function createDropDownActions(record): ActionItem[] {
  return [
    {
      label: '详情',
      onClick: () => {
        setDrawerProps({ title: '预约详情', showFooter: false })
        openDrawer(true, { record, type: 'detail' })
      },
      ifShow: hasPermission(255)
    },
    {
      ifShow: hasPermission(256),
      label: '删除',
      popConfirm: {
        okText: '确定',
        title: '确定删除此数据吗？',
        cancelText: '取消',
        placement: 'left',
        disabled: record.status !== 0,
        confirm: async () => {
          try {
            setLoading(true)
            const { msg } = await DelqcBooking({ id: record.id })
            if (msg === 'success') {
              await reload()
            }
          } catch (e) {
            throw new Error(e)
          } finally {
            setLoading(false)
          }
        }
      }
    },
    {
      color: 'error',
      label: '更改附件',
      onClick: handleUppload.bind(null, record)
    }
  ]
}

async function handleApprove(record, status: number) {
  try {
    setLoading(true)
    const { msg } = await ApproveqcBooking({ ids: [{ id: record.id, status }] })
    if (msg === 'success') {
      await reload()
    }
    setLoading(false)
  } catch (e) {
    setLoading(false)
    throw new Error(e)
  }
}

function handleUppload(record) {
  openModalUplad(true, record)
}
</script>
