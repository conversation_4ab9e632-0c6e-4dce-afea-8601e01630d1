<template>
  <BasicModal @register="register" title="订单结算日期" @ok="addsubmit" :min-height="400" @close="close">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
//updateWorksAudit

import { updateWorksAudit } from '/@/api/erp/sales'
import { WorksAuditschemas } from '../datas/Modal'
import { ref } from 'vue'
//From

const [registerForm, { validate, resetFields }] = useForm({
  showActionButtonGroup: false,
  schemas: WorksAuditschemas,
  baseColProps: {
    span: 24
  }
})
//work_ids
const work_ids = ref<any>([])

//modal
const [register, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  changeOkLoading(false)
  resetFields()

  work_ids.value = data
})
const emit = defineEmits(['success', 'register', 'close'])
async function addsubmit() {
  try {
    changeOkLoading(true)

    const values = await validate()

    await updateWorksAudit({ ...values, work_ids: work_ids.value, is_audit: 1 })

    emit('success')
    await closeModal()
    work_ids.value = []
  } catch (err) {
    changeOkLoading(false)
    console.error(err)
  }
}
function close() {
  work_ids.value = []
  emit('close')
}
//提交
</script>
