<template>
  <div>
    <BasicModal
      centered
      @register="registerModal"
      v-bind="$attrs"
      width="100%"
      title="选择图片"
      destroyOnClose
      defaultFullscreen
      :closeFunc="handleClose"
      @ok="handleOk"
    >
      <Row style="height: 100%" align="top" justify="center" :wrap="false">
        <Col span="5">
          <Descriptions title="本地上传列表" />
          <scroll-container style="height: 77vh; padding-bottom: 15px; overflow-x: hidden">
            <Upload action="/api/oss/putImg" :custom-request="handleFileRequest" :multiple="true" v-model:file-list="uploadFileList">
              <div class="flex bg-gray-100 flex-col justify-center" style="width: 20vw; height: 19vh">
                <plus-outlined />
                <div class="text-center" style="margin-top: 8px">Upload</div>
              </div>
              <template #itemRender="{ file }">
                <div
                  v-if="file.uid"
                  class="flex p-2 rounded-md mt-5"
                  style="border: 1px solid #dddddd; width: 19.5vw; box-sizing: border-box"
                >
                  <Checkbox
                    :checked="selectImg?.uid === file.uid"
                    @change="handleCheckboxChange"
                    class="self-center w-full"
                    :value="file.uid"
                    :disabled="!file.url"
                  >
                    <div class="flex">
                      <div class="ml-2 mr-2 overflow-hidden flex flex-wrap items-center" style="width: 150px; height: 150px">
                        <img class="object-cover w-full" style="height: 150px" :src="file.url" :alt="file.url" />
                      </div>
                      <div class="flex-1" style="min-width: 5vw">
                        <div>{{ file.name }}</div>
                        <Progress v-if="file.percent" :percent="file.percent" />
                      </div>
                    </div>
                  </Checkbox>
                </div>
              </template>
            </Upload>
          </scroll-container>
        </Col>
        <Col>
          <Divider type="vertical" class="!h-185" />
        </Col>
        <Col>
          <Descriptions title="相关订单商品图片" />
          <BasicForm @register="registerForm" />
          <scroll-container style="height: 64.5vh; padding-bottom: 15px; overflow-x: hidden">
            <!--            <CheckboxGroup v-model:value="selectImgs">-->
            <Row :gutter="[20, 15]" style="height: 60vh">
              <template v-if="compSalesImgList.length > 0">
                <Col v-for="item in compSalesImgList" :key="item.uid" :span="6">
                  <div class="flex p-2 rounded-md" style="border: 1px solid #dddddd">
                    <Checkbox
                      class="self-center w-full"
                      :value="item.uid"
                      :disabled="!item.url"
                      :checked="selectImg?.uid === item.uid"
                      @change="handleCheckboxChange"
                    >
                      <div class="flex">
                        <div class="ml-2 mr-2 overflow-hidden flex flex-wrap items-center" style="width: 150px; height: 150px">
                          <img class="object-cover w-full" style="height: 150px" :src="item.url" :alt="item.url" />
                        </div>
                        <div class="flex-1" style="min-width: 5vw">
                          <div>{{ item.name }}</div>
                          <div>{{ item.puid }}</div>
                          <Progress v-if="item.percent" :percent="item.percent" />
                          <!--                      <Progress :percent="50" />-->
                        </div>
                      </div>
                    </Checkbox>
                  </div>
                </Col>
              </template>
              <Col v-else :span="24">
                <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
              </Col>
            </Row>
            <!--            </CheckboxGroup>-->
          </scroll-container>
          <div class="p-4 text-right">
            <Pagination
              v-model:current="paging.page"
              :total="compSalesImgList.length"
              v-model:pageSize="paging.pageSize"
              show-size-changer
            />
          </div>
        </Col>
      </Row>
    </BasicModal>
  </div>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { computed, reactive, ref, watch } from 'vue'
import { Row, Col, Descriptions, Divider, Upload, message, UploadFile, Pagination, Checkbox, Progress, Empty } from 'ant-design-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { PlusOutlined } from '@ant-design/icons-vue'
import { ScrollContainer } from '/@/components/Container'
// import { BasicForm, FormSchema, useForm } from '/@/components/Form'
import { getRequestList } from '/@/api/erp/sales'
import { useMessage } from '/@/hooks/web/useMessage'
import { cloneDeep } from 'lodash-es'
import { BasicForm, useForm } from '/@/components/Form'
// import type { UploadProps } from 'ant-design-vue'

const selectImg = ref(null)
const { createMessage } = useMessage()
const salesImgList = ref<any[]>([])
const loading = ref(false)
const paging = reactive<{ page: number; pageSize: number }>({ page: 1, pageSize: 10 })
const pagingTotal = ref<number>(0)

const compSalesImgList = computed(() => {
  return salesImgList.value
    .filter((item) => item.imgs && item.imgs.length > 0)
    .reduce((arr, item) => {
      const itemImgs = item.imgs.map((img) => ({ ...item, url: img, uid: item.id }))
      return [...arr, ...itemImgs]
    }, [])
})

const [registerForm, { validate }] = useForm({
  // layout: 'inline',
  showAdvancedButton: false,
  autoSubmitOnEnter: true,
  schemas: [
    {
      field: 'name',
      label: '商品名称',
      component: 'Input'
      // required: true
    },
    {
      field: 'puid',
      label: '产品编码',
      component: 'Input'
      // required: true
    }
  ],
  baseColProps: { span: 8 },
  labelCol: { span: 5 },
  actionColOptions: { span: 4 },
  labelAlign: 'left',
  colon: true,
  submitButtonOptions: {
    loading
  },
  submitFunc: async () => {
    paging.page = 1
    selectImg.value = null
    await initData()
  }
})
const emits = defineEmits(['change', 'register'])

const orderId = ref<null | number>(null)

const uploadFileList = ref<UploadFile[]>([])

const imgList = ref([])

const record = ref(null)

const [registerModal, { changeOkLoading, changeLoading, closeModal }] = useModalInner((data) => {
  console.log(data)
  imgList.value = data.imgList || []
  orderId.value = data.id
  record.value = data.record
  initData()
})

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  // console.log(file)
  try {
    const isImage = file.type.includes('image')
    if (!isImage) {
      uploadFileList.value = uploadFileList.value!.filter((item) => item.url)
      message.error(`只允许上传图片文件`)
      return
    }
    changeOkLoading(true)
    const curFile = uploadFileList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      uploadFileList.value = uploadFileList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    uploadFileList.value = uploadFileList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    // await setFieldsValue({
    //   files: uploadFileList.value.filter((item) => item.url).map((item) => item.url)
    // })
    // 判断是否所有的文件都已经上传成功，如果有uploading状态的就不修改按钮状态
    const isAllDone = uploadFileList.value.every((item) => item.url)
    if (isAllDone) {
      changeOkLoading(false)
    }
    // changeOkLoading(false)
  } catch (err) {
    // console.log(uploadFileList.value)
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') uploadFileList.value = uploadFileList.value!.filter((item) => item.status === 'done' || item.url)
    else uploadFileList.value = uploadFileList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}

async function initData() {
  try {
    changeLoading(true)
    loading.value = true
    const formData = await validate()
    const formParams = {}
    for (const key of Object.keys(formData)) {
      if (formData[key]) {
        formParams[key] = formData[key]
      }
    }
    // const { items, total } = await getRequestList({ ...paging, ...formData })
    if (!orderId.value) return createMessage.error('遇到未知问题，请联系管理员！')
    const { items, total } = await getRequestList({ ...paging, id: orderId.value, ...formParams })
    pagingTotal.value = total
    salesImgList.value = items
  } catch (e) {
    createMessage.error('出现未知错误！')
    throw new Error(JSON.stringify(e))
  } finally {
    loading.value = false
    changeLoading(false)
  }
}

watch([() => paging.page, () => paging.pageSize], (data, oldData) => {
  const [page, pageSize] = data
  const [oldPage, oldPageSize] = oldData
  if (page !== oldPage || pageSize !== oldPageSize) {
    initData()
  }
})

// function handleBeforeUpload(file): UploadProps['beforeUpload'] {
//   const isImage = file.type.includes('image')
//   if (!isImage) {
//     uploadFileList.value = uploadFileList.value!.filter((item) => item.url)
//     message.error(`只允许上传图片文件`)
//   }
//   return isImage
// }

function handleClose() {
  orderId.value = null
  uploadFileList.value = []
  imgList.value = []
  salesImgList.value = []
  selectImg.value = null
  return true
}

function handleCheckboxChange(val) {
  selectImg.value = [...compSalesImgList.value, ...uploadFileList.value].find((item) => item.uid === val.target.value)
}

function handleOk() {
  if (!selectImg.value?.url) return createMessage.error('请选择图片！')
  emits('change', record.value, cloneDeep(selectImg.value))
  closeModal()
  handleClose()
}
</script>
