<template>
  <BasicDrawer @register="registerDrawer" @ok="debounceSubmit">
    <BasicForm @register="registerForm">
      <template #FilesSlot>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg2Stocking"
          :custom-request="handleFileRequest"
          :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
        >
          <a-button type="primary">
            <upload-outlined />
            Upload
          </a-button>
        </Upload>
      </template>
      <template #saleGoodsSlot>
        <FormItemRest>
          <BasicTable @register="registerTable">
            <template #toolbar>
              <div class="btn-list">
                <a-button type="primary" @click="handleAdd" class="mr-8px"> <plus-outlined />添加产品 </a-button>

                <Dropdown class="mr-8px">
                  <a class="ant-dropdown-link" @click.prevent>
                    <a-button> 文件导入 <CloudUploadOutlined /></a-button>
                  </a>
                  <template #overlay>
                    <Menu @click="handleMenuClick">
                      <MenuItem key="upload"><UploadOutlined /> 导入文件</MenuItem>
                      <MenuItem key="export"><DownloadOutlined /> 导出模板</MenuItem>
                    </Menu>
                  </template>
                </Dropdown>
                <Popover title="批量修改已选择产品的单价" trigger="click" v-model:visible="visibleUnitPrice">
                  <template #content>
                    <InputNumber v-model:value="batchUnitPrice" :precision="2" :min="0.01" :max="99999999" />
                    <div class="flex mt-4px justify-end">
                      <a-button type="primary" class="mr-4px" @click="handleBatchUpdate('unitPrice')" size="small">确定 </a-button>
                      <a-button size="small" @click="visibleUnitPrice = false">取消 </a-button>
                    </div>
                  </template>
                  <a-button class="mr-8px"><BarsOutlined />批量修改单价</a-button>
                </Popover>
                <Popover title="批量修改已选择产品的需求数量" trigger="click" v-model:visible="visibleQuantity">
                  <template #content>
                    <InputNumber v-model:value="batchQuantity" :precision="2" :min="0.01" />
                    <div class="flex mt-4px justify-end">
                      <a-button type="primary" class="mr-4px" @click="handleBatchUpdate('quantity')" size="small">确定 </a-button>
                      <a-button size="small" @click="visibleQuantity = false">取消 </a-button>
                    </div>
                  </template>
                  <a-button><BarsOutlined />批量修改需求数量</a-button>
                </Popover>
              </div>
            </template>
            <template #bodyCell="{ text, column, record }">
              <template v-if="column && column.dataIndex === 'imgs'">
                <TableImg :size="60" :simpleShow="true" :imgList="text" v-if="text.length > 0" />
              </template>
              <template v-if="column && column.dataIndex === 'is_logistics_follow'">
                {{ record.is_logistics_follow == 1 ? '是' : '否' }}
              </template>
              <template v-if="column.key === 'action'">
                <TableAction :actions="createActions(record)" :stop-button-propagation="true" />
              </template>
            </template>
          </BasicTable>
        </FormItemRest>
      </template>
    </BasicForm>
    <ImpExcelModal @register="registerUploadModal" :dataCallBackFn="handleUploadData" ref="ImpExcelModalRef" />
    <ProductModal @register="registerModal" @add-success="handleAddSuccess" @update-success="handleUpdateProduct" />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { UploadFile } from 'ant-design-vue'
import { ref, watch } from 'vue'
import { schemas, columns } from '../datas/craeadrawer'
import { useForm, BasicForm } from '/@/components/Form'
import { useTable, BasicTable, TableAction, TableImg, EditRecordRow, ActionItem } from '/@/components/Table'
import { PlusOutlined, BarsOutlined, UploadOutlined, CloudUploadOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import { Form, Popover, InputNumber, Upload, Dropdown, Menu, MenuItem } from 'ant-design-vue'
import { updateOrCreateSalesOrder } from '/@/api/extrasPage/createSaleOrder'
import { throttle } from 'lodash-es'
import { storeToRefs } from 'pinia'
import { useUserStore } from '/@/store/modules/user'
import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { useModal } from '/@/components/Modal'
import ProductModal from '/@/views/extrasPage/createSaleOrder/components/ProductModal.vue'
import { onExpExcelTemplate } from '/@/utils/exportTemplate'
import { ImpExcelModal } from '/@/components/Excel'
import { fieldMap, transformData2Import } from '/@/views/extrasPage/createSaleOrder/datas/fn'
import { add, mul } from '/@/utils/math'
import { useDrawerInner, BasicDrawer } from '/@/components/Drawer'

const emit = defineEmits(['success', 'register'])
const userStore = useUserStore()
const filesList = ref<UploadFile[]>([])
const { userInfo } = storeToRefs(userStore)
const FormItemRest = Form.ItemRest
//批量修改
const visibleUnitPrice = ref<boolean>(false)
const visibleQuantity = ref<boolean>(false)
const batchUnitPrice = ref(0.01)
const batchQuantity = ref(0.01)
//创建类型便于商品金额判断
const submitLoading = ref(false)
const [registerModal, { openModal, setModalProps }] = useModal()
const [registerUploadModal, { openModal: openUploadModal }] = useModal()
const ImpExcelModalRef = ref<InstanceType<typeof ImpExcelModal>>()

const [registerDrawer, { closeDrawer, changeOkLoading }] = useDrawerInner(async (data) => {
  console.log(data)
  await resetSchema(schemas(handleShowDeposit))
  updateSchema([
    {
      field: 'program_incharge',
      componentProps: {
        defaultOptions: [
          {
            wxworkId: userInfo.value!.wxworkId,
            name: userInfo.value!.realName
          }
        ]
      }
    },
    {
      field: 'creator',
      componentProps: {
        defaultOptions: [
          {
            id: userInfo.value!.userId,
            name: userInfo.value!.realName
          }
        ]
      }
    },
    {
      field: 'inCharge',
      componentProps: {
        defaultOptions: [
          {
            wxworkId: userInfo.value!.wxworkId,
            name: userInfo.value!.realName
          }
        ]
      }
    }
  ])

  setFieldsValue({
    // inCharge: shall.user_wxworkId,
    program_incharge: userInfo.value!.wxworkId,
    wxworkId: userInfo.value!.wxworkId,
    creator: userInfo.value!.userId
  })
})

const [registerForm, { setFieldsValue, validate, getFieldsValue, updateSchema, resetSchema }] = useForm({
  //   schemas: schemas(handleShowDeposit, handleproject),
  compact: false,
  actionColOptions: { span: 24 },
  labelWidth: 130,
  showActionButtonGroup: false,
  colon: true,
  baseColProps: { span: 8 }
})

const [
  registerTable,
  { getDataSource, setTableData, getSelectRowKeys, updateTableDataRecord, insertTableDataRecord, deleteTableDataRecord }
] = useTable({
  showIndexColumn: true,
  columns,
  dataSource: [],
  pagination: false,
  striped: true,
  canResize: false,
  rowKey: 'key',
  actionColumn: {
    width: 230,
    title: '操作',
    dataIndex: 'action'
  },
  rowSelection: {
    type: 'checkbox'
  }
})

async function handleSubmit() {
  submitLoading.value = true
  changeOkLoading(true)
  try {
    validate()
    const formData = await getFieldsValue()
    const {
      client_name,
      client_contact,
      client_location,
      show_deposit,
      deposit_name,
      deposit_contact,
      deposit_account,
      sales_goods,
      type
    } = formData
    const params = {
      ...formData,
      files: formData?.files?.map((item: UploadFile) => item.url) ?? [],
      sales_goods: undefined,
      type: type,
      receivable: sales_goods.reduce((accumulator, currentValue) => add(accumulator, currentValue.totalAmount), 0), //所有总价之和
      client: {
        name: client_name,
        contact: client_contact ?? null,
        location: client_location
      },
      deposit: show_deposit
        ? {
            name: deposit_name,
            contact: deposit_contact,
            account: deposit_account
          }
        : undefined,
      items: sales_goods.map((item) => ({
        ...item,
        puid: item.puid ?? '',
        totalAmount: item.totalAmount,
        desc: item.desc ?? '',
        remark: item.remark ?? ''
      }))
    }
    //备货与消费订单不填销售单号,单需要给个固定值,后端替换生成新单号
    if (!params.tpuid) {
      params.tpuid = 'YTTY-***************'
    }
    //消费订单销售渠道不填默认erp
    if (params.type == 27 && !params.src) {
      params.src = 'erp'
    }

    await updateOrCreateSalesOrder(params)
    setTimeout(() => {
      emit('success')
      closeDrawer()
      submitLoading.value = false
      changeOkLoading(true)
    }, 1000)
  } catch (err) {
    submitLoading.value = false
    changeOkLoading(true)
    throw new Error(`${err}`)
  }
}
const debounceSubmit = throttle(handleSubmit, 500)

watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val })
  }
)

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'saleOrder')
  onSuccess!(result.path)
  filesList.value = filesList.value!.map((item, idx) => {
    const url = item.url || item.response
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: url ? url.match(/[^\/]+$/)[0] : idx
    }
  })
  await setFieldsValue({ files: filesList.value.map((item) => item.url) })
}

async function handleAdd() {
  setModalProps({ title: '添加产品' })
  openModal(true, { isUpdate: false, type: 27 })
}

const handleMenuClick = ({ key }) => {
  if (key === 'export') {
    onExpExcelTemplate(Object.values(fieldMap), false)
  } else if (key === 'upload') {
    openUploadModal(true, {
      sheetName: 'Sheet1',
      headerRow: 1,
      startCell: 'A2',
      endCell: `I200000`
    })
  }
}

async function handleBatchUpdate(type = 'unitPrice') {
  const selectKeys = getSelectRowKeys()
  if (selectKeys.length === 0) return
  const { sales_goods } = getFieldsValue()
  for (const item of sales_goods) {
    if (selectKeys.includes(item.key)) {
      if (type === 'unitPrice') {
        item.unitPrice = batchUnitPrice.value
      } else {
        item.quantity = batchQuantity.value
      }
      item.totalAmount = mul(item.unitPrice, item.quantity, 2)
      updateTableDataRecord(item.key, item)
    }
  }
  await setFieldsValue({ sales_goods })
  if (type === 'unitPrice') {
    visibleUnitPrice.value = false
    batchUnitPrice.value = 0
  } else {
    visibleQuantity.value = false
    batchQuantity.value = 0
  }
}

function createActions(record: EditRecordRow): ActionItem[] {
  return [
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record)
    },
    {
      icon: 'ant-design:delete-outlined',
      color: 'error',
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        placement: 'left',
        confirm: handleDelete.bind(null, record)
      }
    }
  ]
}

async function handleShowDeposit(isShow: number) {
  await updateSchema([
    {
      field: 'deposit_name',
      ifShow: isShow == 1
    },
    {
      field: 'deposit_contact',
      ifShow: isShow == 1
    },
    {
      field: 'commission',
      ifShow: isShow == 1
    },
    {
      field: 'deposit_account',
      ifShow: isShow == 1
    }
  ])
}

async function handleEdit(record: EditRecordRow) {
  setModalProps({ title: '编辑产品' })
  openModal(true, { isUpdate: true, record, type: 27 })
}

async function handleDelete(record: EditRecordRow) {
  deleteTableDataRecord(record.key)
  await setFieldsValue({ sales_goods: getDataSource() })
}

function handleAddSuccess(params) {
  insertTableDataRecord(params)
  setFieldsValue({ sales_goods: getDataSource() })
}

function handleUpdateProduct(params) {
  updateTableDataRecord(params.key, params)
  setFieldsValue({ sales_goods: getDataSource() })
}

//上传
async function handleUploadData(data) {
  try {
    ImpExcelModalRef.value?.changeLoading(true)
    const step1Data = transformData2Import(data)
    //计算总价和key
    const tableData = await getDataSource()

    const lastKey = tableData.length > 0 ? tableData[tableData.length - 1].key : 0

    //插入表格
    const sales_goods = [
      ...step1Data.map((item, index) => ({
        ...item,
        key: lastKey + index + 1,
        imgs: [],
        totalAmount: mul(item.unitPrice, item.quantity, 2)
      })),
      ...tableData
    ]
    setTableData(sales_goods)

    await setFieldsValue({ sales_goods })
    //关闭弹窗

    return Promise.resolve('OK')
  } catch (err) {
    console.log(err)
    return Promise.reject('Fail')
  } finally {
    ImpExcelModalRef.value?.changeLoading(false)
  }
}
</script>
