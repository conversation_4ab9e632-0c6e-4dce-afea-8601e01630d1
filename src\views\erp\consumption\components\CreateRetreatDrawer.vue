<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="新增" show-footer @ok="handleSubmit" width="90%">
    <BasicForm @register="registerForm">
      <template #ProductSlot="{ model }">
        <FormItemRest>
          <Alert message="勾选订单商品作为退货商品" type="info" />
          <BasicTable @register="registerTable" v-model:expandedRowKeys="expandedRowKeys" :expandIconColumnIndex="-1">
            <template #bodyCell="{ record, column, index }">
              <template v-if="column && column.dataIndex === 'quantity' && model.product_info.length > 0">
                <FormItemRest>
                  <InputNumber
                    v-if="model.product_info[index]"
                    v-model:value="model.product_info[index].quantity"
                    :min="0"
                    :precision="2"
                    :default-value="1"
                    :max="record.maxQuantity"
                  />
                </FormItemRest>
              </template>
              <template v-if="column && column.dataIndex === 'remark' && model.product_info.length > 0">
                <FormItemRest>
                  <Textarea v-if="model.product_info[index]" v-model:value="model.product_info[index].remark" />
                </FormItemRest>
              </template>
              <template v-if="column && column.dataIndex === 'desc' && model.product_info.length > 0">
                <FormItemRest>
                  <Textarea v-if="model.product_info[index]" v-model:value="model.product_info[index].desc" />
                </FormItemRest>
              </template>
            </template>
          </BasicTable>
        </FormItemRest>
      </template>
    </BasicForm>
  </BasicDrawer>
  <EditDrawer @register="registerEditDrawer" />
  <PurchaseDrawer @register="registerPurchaseDrawer" @success="Pursuccess" />
</template>

<script lang="ts" setup>
import { ref, unref } from 'vue'
import { Form, InputNumber, message, Textarea, Alert } from 'ant-design-vue'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicDrawer, useDrawer, useDrawerInner } from '/@/components/Drawer'
import { retreatAdd } from '/@/api/erp/retreat'
import { BasicTable, TableActionType, useTable } from '/@/components/Table'
import { getItemRequest } from '/@/api/commonUtils'
import defaultUser from '/@/utils/erp/defaultUser'
import { columns, createSchemas, selectRowKeys } from '../datas/createRetreatDrawer'
import type { IRecord } from '../datas/types'
import { add, mul } from '/@/utils/math'
import EditDrawer from './EditDrawer.vue'
import PurchaseDrawer from '../../purchaseOrder/components/purchaseDrawer.vue'

const emit = defineEmits(['success', 'register'])
const FormItemRest = Form.ItemRest

const propsData = ref<{ type: 1; record?: IRecord; items?: { doc_id: number }; way?: string }>({ type: 4 })

const expandedRowKeys = ref<number[]>([])
const expandedRowRefs = ref<{ [key: string]: TableActionType | Nullable<any> }>({})
const quantitymax = ref(false)

//采购退货生成新采购订单
const [registerPurchaseDrawer] = useDrawer()

const [registerTable, { setTableData, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
  showIndexColumn: false,
  columns,
  dataSource: [],
  pagination: false,
  striped: true,
  rowKey: 'id',
  rowSelection: {
    type: 'checkbox',
    onChange: () => {
      selectRowKeys.value = getSelectRowKeys()
    },
    getCheckboxProps: (record) => {
      return { disabled: record.quantity === 0 }
    }
  },
  clickToRowSelect: false
})

const [registerDrawer, { changeLoading, changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  propsData.value = data
  expandedRowKeys.value = []
  try {
    changeLoading(true)
    await resetFields()
    await clearSelectedRowKeys()
    await updateSchema(createSchemas(handleOrderChange))
    await setFieldsValue({ product_info: [] })
    setTableData([])
    await updateSchema([
      {
        field: 'source_uniqid',
        ifShow: data.type === 1
      },
      {
        field: 'strid',
        ifShow: data.type === 2
      }
    ])
    setFieldsValue({
      type: data.type,
      source_uniqid: data.record.source_uniqid,
      dept_id: data.record.dept_id,
      applicant: defaultUser!.userId,
      inCharge: defaultUser!.userId
    })
    await clearValidate('dept_id')
  } catch (e) {
    console.log('加载抽屉出错', e)
    throw new Error(`${e}`)
  } finally {
    changeLoading(false)
  }
})

const [registerForm, { resetFields, setFieldsValue, validate, getFieldsValue, updateSchema, clearValidate }] = useForm({
  schemas: createSchemas(handleOrderChange),
  baseColProps: { span: 24 },
  colon: true,
  showActionButtonGroup: false,
  labelWidth: 120
})
//退货再建但是跳转到编辑
const [registerEditDrawer, { openDrawer: openEditDrawer }] = useDrawer()

async function handleOrderChange() {
  changeLoading(true)

  clearSelectedRowKeys()

  try {
    const saleOrderResult = await getItemRequest({ work_id: propsData.value.record?.id, pageSize: 999 })
    await setFieldsValue({
      product_info: saleOrderResult.items
        .filter((item) => item.qty_request_left > 0)
        .map((item) => ({ ...item, quantity: item.qty_request_left, remark: '', desc: '' }))
    })
    setTableData(
      saleOrderResult.items
        .filter((item) => item.qty_request_left > 0)
        .map((item) => ({
          ...item,
          maxQuantity: item.qty_request_left,
          quantity: item.qty_request_left,
          remark: '',
          desc: ''
        }))
    )
  } catch (err) {
    console.error(err, 'OrderChange时错误')
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
}

function mapProductInfo(item) {
  const items_sub = ref([])
  if (item.items_sub?.length > 0) {
    const tableAction = unref(expandedRowRefs)[item.id]?.tableAction
    const items = tableAction?.getSelectRows()
    if (!items || items.length == 0) {
      message.error('请勾选子产品商品')
      throw new Error('请勾选子产品退货商品')
    }

    if (item.quantity == item.qty_request_left) {
      quantitymax.value = true
    } else {
      quantitymax.value = false
    }
    items_sub.value = items?.map((val) => {
      return {
        work_id: unref(propsData).record!['id'],
        request_id: val.request_id,
        request_sub_id: val.id,
        purchase_id: undefined,
        purchase_sub_id: undefined,
        quantity: quantitymax.value ? Number(val.quantity_left) : Number(val.quantity),
        proportion: val.proportion,
        proportion_org: val.proportion_org,
        unit: val.unit,
        remark: val.remark,
        desc: val.desc,
        name: val.name,
        imgs: val.imgs ?? [],
        files: val.files ?? []
      }
    })
  }
  // const items_addAfterSale

  const commonProperties = {
    remark: item.remark ?? '',
    desc: item.desc ?? '',
    unit: item.unit,
    unit_price: item.unit_price,
    imgs: item.imgs ?? []
  }

  const saleOrderItem = {
    name: item.name,
    work_id: item.work_id,
    request_id: item.id,
    quantity: item.quantity,
    items_sub: items_sub.value,
    ...commonProperties
  }

  const purchaseOrderItem = {
    purchase_id: item.id,
    name: item.name,
    work_id: item.work_id,
    request_id: item.request_id,
    quantity: item.quantity,
    warehouse_id: item.warehouse_id,
    stocking_id: item.stocking_id,
    items_sub: items_sub.value,
    ...commonProperties
  }

  return { saleOrderItem, purchaseOrderItem }
}

async function handleSubmit() {
  try {
    await changeOkLoading(true)
    await validate()
    const formData = getFieldsValue()
    const selectRow = getSelectRowKeys()

    if (selectRow.length === 0) {
      message.error('至少需要选择一个商品!')
      changeOkLoading(false)
      return
    }
    const { applicant, inCharge, dept_id, remark, product_info, type } = formData

    //商品信息

    const params: Recordable = {
      work_id: unref(propsData).record!['id'],
      doc: {
        type,
        applicant,
        inCharge,
        dept_id,
        remark: remark ?? '',
        total_price: product_info
          .filter((item) => selectRow.includes(item.id))
          .reduce((prev, curr) => add(prev, mul(curr.unit_price, curr.quantity)), 0)
      },
      is_change_sale: 0
    }

    params.items = product_info.filter((item) => selectRow.includes(item.id)).map((item) => mapProductInfo(item).saleOrderItem)

    console.log(params)

    const res = await retreatAdd(params)
    if (res.work_id) {
      await closeDrawer()
      emit('success')
      openEditDrawer(true, { record: { id: res.work_id } })
    }

    await closeDrawer()
    setTimeout(() => {
      changeOkLoading(false)
    }, 2000)
    emit('success')
  } catch (err) {
    console.log('提交退货单出错:', err)
    changeOkLoading(false)
    throw new Error(`${err}`)
  }
}

//采购退货后采购订单生成完成退出
async function Pursuccess() {
  await closeDrawer()
  setTimeout(() => {
    changeOkLoading(false)
  }, 2000)
  emit('success')
}
</script>
