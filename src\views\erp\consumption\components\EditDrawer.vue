<template>
  <div>
    <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="编辑销售订单" show-footer @ok="handleSubmit" width="90%">
      <BasicForm @register="registerForm">
        <template #filesSlot>
          <Upload
            v-model:file-list="filesList"
            action="/api/oss/putImg2Stocking"
            :custom-request="handleFileRequest"
            :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
          >
            <a-button type="primary">
              <upload-outlined />
              Upload
            </a-button>
          </Upload>
        </template>
        <template #relateOrderSlot="{ model }">
          <BasicTable @register="registerDrawerTable" v-model:expandedRowKeys="expandedRowKeys" :expandIconColumnIndex="-1">
            <template #headerCell="{ column }">
              <template v-if="column.dataIndex === 'action'">
                <span>{{ column.customTitle }}</span>
                <div>
                  <a-button type="primary" size="small" @click="handleBatchDel">批量删除选中产品</a-button>
                </div>
              </template>
              <template v-else>{{ column.customTitle }}</template>
            </template>
            <template #bodyCell="{ column, index, record }">
              <template v-if="column.key === 'imgs'">
                <div class="flex items-center justify-center">
                  <TableImg :imgList="record.imgs" :simpleShow="true" />
                </div>
              </template>
              <template v-if="column.dataIndex === 'name' && model.relate_order[index]">
                <FormItemRest>
                  <Input v-model:value="model.relate_order[index].name" />
                </FormItemRest>
              </template>
              <template v-if="column.dataIndex === 'unit' && model.relate_order[index]">
                <FormItemRest>
                  <Input v-model:value="model.relate_order[index].unit" />
                </FormItemRest>
              </template>
              <template v-if="column.dataIndex === 'unit_price' && model.relate_order[index]">
                <FormItemRest>
                  <InputNumber
                    v-model:value="model.relate_order[index].unit_price"
                    :precision="2"
                    :min="ordertype == 2 ? 0 : 0.01"
                    :max="ordertype == 2 ? 0 : 999999999999"
                    @change="handleReChange(record)"
                  />
                </FormItemRest>
              </template>
              <template v-if="column.dataIndex === 'qty_request' && model.relate_order[index]">
                <FormItemRest>
                  <InputNumber
                    v-model:value="model.relate_order[index].qty_request"
                    :precision="2"
                    :min="0.01"
                    @change="handleReChange(record, model.relate_order[index].qty_request)"
                  />
                </FormItemRest>
              </template>
              <template v-if="column.dataIndex === 'desc' && model.relate_order[index]">
                <FormItemRest>
                  <Input v-model:value="model.relate_order[index].desc" />
                </FormItemRest>
              </template>
              <template v-if="column.dataIndex === 'remark' && model.relate_order[index]">
                <FormItemRest>
                  <Input v-model:value="model.relate_order[index].remark" />
                </FormItemRest>
              </template>
              <!--              <template v-if="column.dataIndex === 'puid' && model.relate_order[index]">-->
              <!--                <Input v-model:value="model.relate_order[index].puid" />-->
              <!--              </template>-->
              <!--              <template v-if="column.dataIndex === 'batch_code' && model.relate_order[index]">-->
              <!--                <Input v-model:value="model.relate_order[index].batch_code" />-->
              <!--              </template>-->
              <template v-if="column.dataIndex === 'action'">
                <TableAction :actions="createActions(record)" />
              </template>
            </template>
          </BasicTable>
        </template>
      </BasicForm>
    </BasicDrawer>
    <ChooseImg @register="registerChooseImgModal" @change="handleSelected" />
  </div>
</template>

<script setup lang="ts">
import { ref, unref, watch } from 'vue'
import { Form, Input, InputNumber, Upload, UploadFile } from 'ant-design-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { UploadOutlined } from '@ant-design/icons-vue'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable, TableImg, TableAction, TableActionType } from '/@/components/Table'
import { editSalesOrder, getSalesOrderDetail, getSalesOrderListReq } from '/@/api/erp/sales'
import { getDrawerTableColumns, getSchemasList } from '../datas/drawer.data'
import { div, mul } from '/@/utils/math'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import ChooseImg from './ChooseImg.vue'
import { useModal } from '/@/components/Modal'

const filesList = ref<UploadFile[]>([])
const expandedRowKeys = ref<number[]>([])
const expandedRowRefs = ref<{ [key: string]: TableActionType | Nullable<any> }>({})
const emit = defineEmits(['register', 'success'])

const [registerChooseImgModal, { openModal: openChooseImgModal }] = useModal()

const FormItemRest = Form.ItemRest

const id = ref()
const totalSplittable = ref<any>([])
const type = ref('')
//订单类型,决定商品金额
const ordertype = ref()

const [registerDrawer, { changeLoading, changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  changeLoading(true)
  totalSplittable.value = []
  expandedRowKeys.value = []
  type.value = data.type

  try {
    await resetFields()
    setTableData([])
    id.value = data.record.id
    ordertype.value = data.record.type

    const detailData = await getSalesOrderDetail({ work_id: data.record.id })

    const { items } = await getSalesOrderListReq({ work_id: data.record.id, pageSize: 999 })
    filesList.value = data.record.files?.map((item) => ({ url: item, name: item, uid: item }))
    resetSchema(getSchemasList(updateSchema, data.type))
    await setFieldsValue({
      ...detailData,
      relate_order: items,
      client: {
        value: detailData.client_id,
        label: detailData.client_name
      }
    })
    setTableData(items)
    items.forEach((item: any) => {
      if (item.items_sub?.length > 0) {
        totalSplittable.value.push(...item.items_sub)
      }
    })
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
})

const [registerForm, { setFieldsValue, resetFields, validate, getFieldsValue, resetSchema, updateSchema }] = useForm({
  schemas: getSchemasList(),
  baseColProps: { span: 12 },
  colon: true,
  showActionButtonGroup: false,
  labelWidth: 120
})

const [registerDrawerTable, { setTableData, updateTableDataRecord, deleteTableDataRecord, getSelectRowKeys, clearSelectedRowKeys }] =
  useTable({
    showIndexColumn: false,
    columns: getDrawerTableColumns(),
    dataSource: [],
    pagination: false,
    canResize: false,
    rowKey: 'id',
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
      fixed: 'right'
      // ifShow: false
    },
    rowSelection: {}
  })

watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val })
  }
)
async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  filesList.value = filesList.value!.map((item, idx) => {
    const url = item.url || item.response
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: url ? url.match(/[^\/]+$/)[0] : idx
    }
  })
  await setFieldsValue({ files: filesList.value.map((item) => item.url) })
}

async function handleReChange(record, qty_request?) {
  const formData = await getFieldsValue()
  //这里应该还要判断每行的单价和数量是不是有值
  const total_amount = formData.relate_order.reduce((pre, cur) => {
    return pre + mul(cur.unit_price, cur.qty_request)
  }, 0)
  setFieldsValue({ receivable: total_amount })
  updateTableDataRecord(record.id, { total_amount })
  if (qty_request) {
    //sku
    const tableAction = unref(expandedRowRefs)[record.id]?.tableAction
    console.log(totalSplittable.value)
    const table = totalSplittable.value.filter((item) => item.request_id === record.id)
    console.log(table)

    table.forEach((item) => {
      console.log(item)
      item.proportion = div(mul(div(qty_request, item.quantity, 6), item.proportion_org, 6), 100, 6)
    })
    tableAction?.setTableData(table)
    console.log(tableAction)
  }
}

async function handleSubmit() {
  try {
    await changeOkLoading(true)

    const formData = await validate()
    const { client } = formData

    const params = {
      ...formData,
      files: formData.files?.map((item: UploadFile) => item.url),
      relate_order: undefined,
      id: unref(id),
      client_id: client.value,
      client_name: client.label,
      items: formData.relate_order.map((item) => ({
        name: item.name ?? '',
        id: item.id,
        unit: item.unit ?? '',
        unit_price: item.unit_price ?? '',
        qty_request: item.qty_request ?? '',
        desc: item.desc ?? '',
        remark: item.remark ?? '',
        imgs: item.imgs ?? []
        // puid: item.puid ?? '',
        // batch_code: item.batch_code ?? ''
      }))
    }

    await editSalesOrder(params)
    await closeDrawer()
    changeOkLoading(false)
    emit('success')
  } catch (err) {
    changeOkLoading(false)
    throw new Error(`${err}`)
  }
}

function createActions(record) {
  return [
    {
      // icon: 'ant-design:file-add-outlined',
      // color: 'success',
      label: '编辑图片',
      ifShow: type.value !== 'split',
      onClick: handleChooseImg.bind(null, record)
    },

    {
      label: '删除',
      ifShow: type.value !== 'split',
      onClick: handleDel.bind(null, record)
    }
  ]
}

function handleChooseImg(record) {
  openChooseImgModal(true, { imgList: record.imgs, id: id.value, record })
}

function handleSelected(record, selected) {
  updateTableDataRecord(record.id, { imgs: [selected.url] })
}

//展示

async function handleDel(record) {
  deleteTableDataRecord(record.id)
}

function handleBatchDel() {
  deleteTableDataRecord(getSelectRowKeys())
  clearSelectedRowKeys()
}
</script>
