<!-- eslint-disable max-len -->
<template>
  <BasicModal @register="registerModal" width="70%" defaultFullscreen @ok="handleOk">
    <template #title>
      <div style="display: flex">
        <div style="margin-right: 10px"> 请填写以下信息 : </div>
        <a
          style="color: red"
          href="https://img.gbuilderchina.com/erp/%E6%94%B6%E6%AC%BE%E6%96%B9%E5%BC%8F%E5%AF%B9%E5%BA%94%E5%BD%95%E5%85%A5%E6%94%B6%E6%AC%BE%E5%8D%95%E7%9A%84%E5%A1%AB%E5%86%99%E8%A6%81%E6%B1%82.docx"
          >收款方式对应录入收款单的填写要求说明文档（点击下载）</a
        >
      </div>
    </template>
    <BasicForm @register="registerForm">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          :multiple="true"
        >
          <Tooltip placement="top">
            <template #title>
              <span>附件必须放水单/单据，否则财务无法识别对应哪一笔流水，较大概率重填</span>
            </template>
            <div>
              <plus-outlined />
              <div style="margin-top: 8px">Upload</div>
            </div>
          </Tooltip>
        </Upload>
      </template>
    </BasicForm>
    <Alert message="多条单据生成付款单时,请往下滑动滚轮" show-icon description="明细填入佣金是会自动生成其他收入单" />
    <Card title="本次应收金额">
      <template #extra>
        <div class="text-base w-180">
          当前输入本次应收金额汇总：
          <span class="text-blue-500 font-bold text-xl">{{ compRowPrice }}</span>
        </div>
      </template>
      <template v-for="item in cloneDeepSelectRowsData" :key="item.strid">
        <Descriptions class="mb-5">
          <DescriptionsItem :label="`销售单号 : ${item.source_uniqid}`" :labelStyle="{ color: '#909399', width: '40%' }">
            <Form :model="item" ref="formRef" :rules="formRulesFn()" layout="inline">
              <FormItem has-feedback name="no_amount" label="去佣货款金额">
                <Tooltip placement="top">
                  <template #title>
                    <span>本次收款分配去佣货款金额，不是填写总金额，填的是本次收款分配金额</span>
                  </template>
                  <InputNumber :formatter="(value) => `¥${value}`" v-model:value="item.no_amount" :min="0.01" :precision="2" />
                </Tooltip>
              </FormItem>
              <FormItem has-feedback name="commission" label="佣金">
                <Tooltip placement="top">
                  <template #title>
                    <span>
                      若本次收款中包含中间商自己的佣金(需要我们二次转出)则需要填写，若不包含佣金全部是我们的货款直接入账即不用填写
                    </span>
                  </template>
                  <InputNumber :formatter="(value) => `¥${value}`" v-model:value="item.commission" :precision="2" />
                </Tooltip>
              </FormItem>
              <FormItem has-feedback name="commission" label="含佣金额">
                <div>{{ add(item.no_amount, item.commission, 2) }}</div>
              </FormItem>
              <FormItem name="payment_type" label="款项类型">
                <RadioGroup v-model:value="item.payment_type" :options="radioOpt" />
              </FormItem>
              <Tooltip placement="top">
                <template #title>
                  <span>本张订单的收款总金额不对等的原因</span>
                </template>
                <FormItem has-feedback name="amout_remark" label="备注" v-if="item.amout_remark_show">
                  <Textarea :formatter="(value) => `¥${value}`" v-model:value="item.amout_remark" />
                </FormItem>
              </Tooltip>
            </Form>
          </DescriptionsItem>
        </Descriptions>
      </template>
    </Card>
  </BasicModal>
</template>

<script setup lang="ts">
import { addBatch } from '/@/api/financialDocuments/receiptOrder'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, Rule, useForm } from '/@/components/Form'
import {
  Card,
  Descriptions,
  DescriptionsItem,
  InputNumber,
  message,
  Form,
  FormItem,
  UploadFile,
  Upload,
  RadioGroup,
  Tooltip,
  Alert,
  Textarea
} from 'ant-design-vue'
import { ref, watch, computed } from 'vue'
import { cloneDeep, isNull, isUndefined } from 'lodash-es'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { PlusOutlined } from '@ant-design/icons-vue'
import { add, sub } from '/@/utils/math'
import { useMessage } from '/@/hooks/web/useMessage'
import { rejectList } from '/@/api/erp/sales'
import { generschemas } from '../datas/Modal'
import { useUserStore } from '/@/store/modules/user'
const userStore = useUserStore()

const { createMessage } = useMessage()
const emit = defineEmits(['register', 'handleSubmit', 'success'])

const cloneDeepSelectRowsData = ref<Array<any>>([])
const compRowPrice = computed(() => {
  return cloneDeepSelectRowsData.value.reduce((total, work) => add(add(work.no_amount, work.commission || 0), total), 0)
})
// const selectRowsData = ref<Array<any>>([])
const formRef = ref()
const radioOpt = [
  {
    label: '定金',
    value: 1
  },
  {
    label: '最后一笔款',
    value: 2
  },
  {
    label: '全款',
    value: 3
  }
]
/** 校验 */
const formRulesFn = (): any => {
  return {
    no_amount: [
      {
        required: true,
        trigger: 'change',
        validator: async (_rule: Rule, value: string) => {
          // Number(value) > Number(selectRowsData.value[index].no_amount) ||
          if (Number(value) <= 0) {
            return Promise.reject('输入的收款金额必须大于0！')
          } else {
            return Promise.resolve()
          }
        }
      }
    ],
    payment_type: [
      {
        required: true,
        trigger: 'change'
      }
    ],
    amout_remark: [
      {
        required: true,
        trigger: 'change',
        validator: async (_rule: Rule, value: string) => {
          // Number(value) > Number(selectRowsData.value[index].no_amount) ||
          if (!value) {
            return Promise.reject('请输入金额不对等的原因！')
          } else {
            return Promise.resolve()
          }
        }
      }
    ]
  }
}

/** 注册from */
const [registerForm, { validate, resetFields, setFieldsValue, resetSchema }] = useForm({
  labelWidth: 150,
  // schemas: generschemas
  showSubmitButton: false,
  showResetButton: false,
  actionColOptions: {
    span: 24
  }
})

/** 注册Modal */
const [registerModal, { closeModal }] = useModalInner(async (data) => {
  console.log(data)
  const hasType27 = data.selectRowsData.some((item) => item.type === 27)
  resetSchema(generschemas(hasType27))
  resetFields()
  await setFieldsValue({ dept_id: userStore.getUserInfo?.deptId })
  // selectRowsData.value = data.selectRowsData

  const { items } = await rejectList({ work_ids: data.work_ids })
  if (items) {
    const imgsdata = typeof items.files === 'string' ? JSON.parse(items.files) : items.files
    filesList.value = (
      typeof imgsdata === 'string'
        ? [{ url: imgsdata, uid: +new Date().toString(), name: imgsdata }]
        : (imgsdata as string[])?.map((item: string, idx: number) => ({ url: item, uid: idx.toString(), name: item }))
    ) as UploadFile[]
    cloneDeepSelectRowsData.value = cloneDeep(data.selectRowsData).map((item) => ({
      ...item,
      no_amount: null,
      log_no_amount: item.no_amount,
      commission: null
    }))
    cloneDeepSelectRowsData.value.forEach((item) => {
      items.work?.forEach((work) => {
        if (work.work_id === item.id) {
          item.no_amount = Number(work.amount)
          item.payment_type = work.payment_type
          item.commission = work.commission || 0
        }
      })
    })
    await setFieldsValue({ ...items, notes_account: items.from_account, notes_bank: items.from_bank })
  } else {
    filesList.value = []
    // 将no_amount的默认值去除，使用另外的字段记录之前的no_amount
    cloneDeepSelectRowsData.value = cloneDeep(data.selectRowsData).map((item) => ({
      ...item,
      no_amount: null,
      log_no_amount: item.no_amount,
      commission: null
    }))
  }
  console.log(cloneDeepSelectRowsData.value)
})

/** 点击确认 */
const handleOk = async () => {
  try {
    let data
    try {
      data = await validate()
    } catch (_e) {
      createMessage.error('请填写生成内容')
      return
    }
    const isEmptyFile = data.files.some((item) => isUndefined(item) || isNull(item))
    if (isEmptyFile) {
      return createMessage.error('请等待文件上传完成，再提交！')
    }
    let works: Array<{ work_id: number; amount: number; commission: number; payment_type: number; amout_remark: string }> = []

    // 校验
    try {
      for (let item of formRef.value) {
        await item.validate()
      }
    } catch (_e) {
      createMessage.error('请完善本次应收的 货款金额 和 款项类型 以及 备注')
      return
    }

    cloneDeepSelectRowsData.value.forEach((item) => {
      works.push({
        work_id: item.id,
        amount: item.no_amount,
        payment_type: item.payment_type,
        commission: item.commission,
        amout_remark: item.amout_remark
      })
      data.client_id = item.client_id
      works.forEach((item: any) => {
        if (!item.commission) {
          delete item.commission
        }
      })
    })

    // 判断汇总金额和填写的总金额是否相等
    const worksPrice = works.reduce((total, work) => add(add(work.amount, work.commission || 0), total), 0)
    if (data.total_price !== worksPrice) {
      return createMessage.error('填入的金额与汇总总金额不一致！请检查金额！')
    }
    const res = await addBatch({ ...data, works, clause: 1 })
    if (res.code == 0) {
      closeModal()
      emit('success')
      message.success('成功生成收款单！')
    } else {
      throw new Error(`${res.message}`)
    }
    console.log(data, works)
  } catch (error: any) {
    console.log(error)
    message.error(error.message)
    throw new Error(`${error}`)
  }
}

//附件
const filesList = ref<UploadFile[]>([])

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val?.map((item) => item.url) })
  }
)

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    console.log(file)
    const curFile = filesList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({
      files: filesList.value.map((item) => item.url)
    })
  } catch (err) {
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}

//款项类型更改
watch(
  () => cloneDeepSelectRowsData.value,
  (newval) => {
    console.log(newval[0].payment_type)
    newval.forEach((item) => {
      if (sub(item.receivable_left, item.received_actual, 2) !== add(item.no_amount, item.commission, 2) && item.payment_type > 1) {
        Reflect.set(item, 'amout_remark_show', true)
      } else {
        Reflect.set(item, 'amout_remark_show', false)
        item.amout_remark = ''
      }
    })
  },
  {
    deep: true
  }
)
</script>
<style lang="less" scoped>
:deep(.ant-picker) {
  width: 100%;
}

:deep(.ant-descriptions-item-content) {
  display: inline-block;
}

:deep(.ant-descriptions-item) {
  padding: 0;
}
</style>
