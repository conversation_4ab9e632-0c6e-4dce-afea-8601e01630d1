<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="查看详情" width="90%" destroy-on-close>
    <BasicForm @register="registerForm">
      <template #FilesSlot>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg2Stocking"
          :custom-request="handleFileRequest"
          :disabled="true"
          :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
        >
          <!-- <a-button type="primary">
            <upload-outlined />
            Upload
          </a-button> -->
        </Upload>
      </template>
      <template #relateOrder="{ model }">
        <!--        <BasicTable :dataSource="model.relate_order" :columns="drawerTableColumns" bordered :pagination="false">-->
        <BasicTable
          v-if="model.work_id"
          :api="(params) => getSalesOrderListReq({ ...params, work_id: model.work_id })"
          :columns="drawerTableColumns"
          bordered
          :showIndexColumn="false"
          :canResize="false"
          v-model:expandedRowKeys="expandedRowKeys"
          :expandIconColumnIndex="-1"
          rowKey="id"
          :actionColumn="{
            width: 150,
            title: '操作',
            dataIndex: 'action'
          }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'imgs'">
              <TableImg :imgList="record.imgs" :simpleShow="true" />
            </template>
            <template v-if="column.dataIndex === 'action'">
              <TableAction :actions="createActions(record)" />
            </template>
          </template>
          <template #expandedRowRender="{ record: cellRecord }">
            <BasicTable :columns="tablecolum()" :can-resize="false" :data-source="cellRecord.items_sub">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'imgs'">
                  <TableImg :imgList="record.imgs" :simpleShow="true" />
                </template>
                <template v-if="column.key === 'files'">
                  <div v-for="(newVal, index) in record.files" :key="index">
                    <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
                  >
                </template>
              </template>
            </BasicTable>
          </template>
        </BasicTable>
      </template>
    </BasicForm>
    <PreviewFile @register="registerModal" />
  </BasicDrawer>
</template>

<script lang="ts" setup name="VerifyDrawer">
import { ref } from 'vue'
import { Upload, UploadFile } from 'ant-design-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { ActionItem, BasicTable, TableImg, TableAction } from '/@/components/Table'

import { getSalesOrderDetail, getSalesOrderListReq } from '/@/api/erp/sales'
import { schemas, drawerTableColumns, tablecolum } from '../datas/drawer.data'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { useMessage } from '/@/hooks/web/useMessage'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { useModal } from '/@/components/Modal'
import { createImgPreview } from '/@/components/Preview'

const expandedRowKeys = ref<number[]>([])
const filesList = ref<UploadFile[]>([])

const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

const [registerDrawer, { changeLoading }] = useDrawerInner(async (data) => {
  changeLoading(true)
  try {
    filesList.value = data?.files?.map((item) => ({ url: item, name: item, uid: item }))
    const detailData = await getSalesOrderDetail({ work_id: data.id })
    // const subOrderList = await getSalesOrderListReq({ work_id: data.id })
    await setFieldsValue({ ...detailData, work_id: data.id })
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
})

const [registerForm, { setFieldsValue }] = useForm({
  schemas: [
    {
      field: 'work_id',
      label: 'work_id',
      component: 'Input',
      show: false
    },
    ...schemas
  ],
  actionColOptions: { span: 24 },
  baseColProps: { span: 24 },
  labelAlign: 'left',
  colon: true,
  showActionButtonGroup: false
})

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  filesList.value = filesList.value!.map((item, idx) => {
    const url = item.url || item.response
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: url ? url.match(/[^\/]+$/)[0] : idx
    }
  })
  await setFieldsValue({ files: filesList.value.map((item) => item.url) })
}

function createActions(record: any): ActionItem[] {
  return [
    {
      label: '查看子产品',
      onClick: handleViewRelate.bind(null, record),
      disabled: record.items_sub?.length == 0
    }
  ]
}

//ctions
function handleViewRelate(record) {
  expandedRowKeys.value.includes(record.id)
    ? (expandedRowKeys.value = expandedRowKeys.value.filter((item) => item !== record.id))
    : expandedRowKeys.value.push(record.id)
}

//展示
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  console.log(prefix)
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>

<style lang="less" scoped>
.pay-order {
  cursor: pointer;
  color: rgb(141, 141, 255);
  display: inline-block;
  line-height: 32px;
}

.img-popup-text {
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  color: rgb(176, 176, 255);
  cursor: pointer;
}
:deep(.ant-col.ant-form-item-label.ant-form-item-label-left) {
  font-weight: 600;
  > label {
    font-size: 15px;
  }
}
</style>
