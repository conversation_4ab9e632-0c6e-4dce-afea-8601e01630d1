<template>
  <BasicModal @register="register" title="商品添加" @ok="handleOk" width="800px">
    <BasicForm @register="registerform">
      <template #files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          @change="change('files')"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
      <template #imgs>
        <Upload
          v-model:file-list="imgsList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          @change="change('imgs')"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
    </BasicForm></BasicModal
  >
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { schemassplit } from '../datas/Modal'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { UploadFile, Upload } from 'ant-design-vue'
import { ref, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { div, mul } from '/@/utils/math'

//id
const init_record = ref()
const init_key = ref()
const type = ref()
const qty_request = ref()
const [register, { closeModal }] = useModalInner((data) => {
  resetFields()
  filesList.value = []
  imgsList.value = []
  init_key.value = ''
  init_record.value = data.record
  type.value = data.type
  qty_request.value = data.qty_request
  if (data.type == 'add') {
    setFieldsValue({
      sname: data.record.name
    })
  } else {
    init_key.value = data.record.key
    setFieldsValue({
      ...data.record
    })
    filesList.value = data.record.files?.map((item) => ({ url: item, name: item, uid: item })) ?? []
    imgsList.value = data.record.imgs?.map((item) => ({ url: item, name: item, uid: item })) ?? []
  }
})
const [registerform, { setFieldsValue, validate, resetFields }] = useForm({
  schemas: schemassplit,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelCol: { span: 5 }
})

//附件
const filesList = ref<UploadFile[]>([])
//图片组
const imgsList = ref<UploadFile[]>([])

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val.map((item) => item.url) })
  }
)
watch(
  () => imgsList.value,
  async (val) => {
    await setFieldsValue({ imgs: val.map((item) => item.url) })
  }
)

const types = ref('')
function change(type) {
  types.value = type
}

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  types.value == 'files'
    ? (filesList.value = filesList.value!.map((item) => {
        return {
          url: (item.url as string) || (item.response as string),
          uid: item.uid,
          name: item.name
        }
      }))
    : (imgsList.value = imgsList.value!.map((item) => {
        return {
          url: (item.url as string) || (item.response as string),
          uid: item.uid,
          name: item.name
        }
      }))
  types.value == 'files'
    ? await setFieldsValue({
        files: filesList.value.map((item) => item.url)
      })
    : await setFieldsValue({
        imgs: imgsList.value.map((item) => item.url)
      })
}
const emit = defineEmits(['register', 'handleAdd'])
// 提交
async function handleOk() {
  const formdata = await validate()

  console.log(formdata.quantity)
  console.log(init_record.value)

  const params = {
    ...formdata,
    request_id: type.value == 'add' ? init_record.value.id : init_record.value.request_id,
    work_id: init_record.value.work_id,
    type: type.value == 'add' ? 1 : 2,
    key: init_key.value,
    id: type.value == 'add' ? undefined : init_record.value.id,
    proportion: div(mul(div(qty_request.value, formdata.quantity, 6), formdata.proportion_org, 6), 100, 6)
  }
  emit('handleAdd', params)
  closeModal()
}
</script>
