import { getStaffList } from '/@/api/baseData/staff'
import { getListByDate } from '/@/api/erp/outWarehouse'
import { getRmbquot } from '/@/api/erp/sales'
import { getDept } from '/@/api/erp/systemInfo'
import { FormSchema } from '/@/components/Form'
import { mul } from '/@/utils/math'

export const WorksAuditschemas: FormSchema[] = [
  {
    field: 'audit_at',
    label: '结算日期',
    component: 'DatePicker',
    required: true,
    componentProps: {
      style: {
        width: '100%'
      },
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'finance_remark',
    label: '财务备注',
    component: 'InputTextArea'
  }
]

export const generschemas = (warehouse?): FormSchema[] => [
  // {
  //   field: 'collection_at',
  //   label: '收款日期',
  //   component: 'DatePicker',
  //   itemHelpMessage: '银行流水到账日期',
  //   componentProps: {
  //     valueFormat: 'YYYY-MM-DD'
  //   },
  //   colProps: {
  //     span: 7
  //   },
  //   required: true
  // },
  {
    field: 'fund_at',
    label: '水单日期',
    component: 'DatePicker',
    itemHelpMessage:
      '水单日期是核算业绩的时间，是客户汇款截图上的水单日期，请如实填写，如录入收款单日期是12月2日，水单日期为11月30日收款，即填写11月30日',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    colProps: {
      span: 7
    },
    required: true
  },
  {
    field: 'notes',
    label: '对方付款人',
    itemHelpMessage: '这笔打款账户名字',
    component: 'Input',
    colProps: {
      span: 7
    },
    required: true
  },
  {
    field: 'notes_account',
    label: '对方付款人账号',
    itemHelpMessage: ['只需要填写打款账号后4位数字', '收现金的直接填写“现金”'],
    component: 'Input',
    colProps: {
      span: 7
    },
    required: true
  },
  {
    field: 'notes_bank',
    label: '对方付款银行/平台',
    itemHelpMessage: ['填写对方打款账户所在银行', '信保填写对应平台', '收现金的直接填写“现金”'],
    component: 'Input',
    colProps: {
      span: 7
    },
    required: true
  },
  {
    field: 'account',
    label: '收款账号',
    component: 'Input',
    itemHelpMessage: ['我司收款账号后4位数字', '收现金的直接填写“现金”'],
    colProps: {
      span: 7
    },
    required: true
  },
  {
    field: 'account_name',
    label: '收款银行',
    component: 'Input',
    itemHelpMessage: ['我司收款账号所在银行', '收现金的直接填写“现金” '],
    colProps: {
      span: 7
    },
    required: true
  },
  // {
  //   field: 'payment_type',
  //   label: '款项类型',
  //   component: 'RadioGroup',
  //   // defaultValue: 3,去除默认值，让用户手选
  //   componentProps: {
  //     options: [
  //       {
  //         label: '定金',
  //         value: 1
  //       },
  //       {
  //         label: '最后一笔款',
  //         value: 2
  //       },
  //       {
  //         label: '全款',
  //         value: 3
  //       }
  //     ]
  //   },
  //   colProps: {
  //     span: 11
  //   },
  //   required: true
  // },
  {
    field: 'g_remark',
    label: '携带备注',
    component: 'InputTextArea',
    itemHelpMessage: '外币收款需备注收到总外币金额，及其他注释',
    // componentProps: {
    //   autosize: { minRows: 3, maxRows: 6 }
    // },
    colProps: {
      span: 7
    }
  },
  {
    label: '本次应收汇总金额',
    field: 'total_price',
    component: 'InputNumber',
    componentProps: ({ formModel }) => {
      return {
        min: 0,
        disabled: formModel.rate !== '1.000000',
        precision: 2
      }
    },
    defaultValue: 0,
    required: true,
    itemHelpMessage: ['这笔流水金额。', '注意：必须填写与下方填写的本次应收金额总金额（货款+佣金）对应', '否则无法生成'],
    colProps: {
      span: 7
    }
  },
  {
    field: 'is_collection',
    label: '是否软装代收',
    required: true,
    itemHelpMessage: '如果当前所收款项包含门窗软件产品款项请选择是',
    component: 'Select',
    colProps: {
      span: 7
    },
    componentProps: {
      options: [
        {
          label: '不是',
          value: 0
        },
        {
          label: '是',
          value: 1
        }
      ]
    }
  },
  {
    field: 'fg_amount',
    label: '外币金额',
    component: 'InputNumber',
    itemHelpMessage: '当汇率不为人人民币时填写外币金额算出汇总总金额',
    componentProps: ({ formModel }) => {
      return {
        precision: 4,
        min: 0,
        disabled: formModel.rate == '1.000000',
        onChange(val) {
          formModel.total_price = mul(val, formModel.rate, 2)
        }
      }
    },
    colProps: {
      span: 7
    }
  },

  {
    field: 'exchange_rate',
    label: '汇率',
    component: 'ApiSelect',
    defaultValue: '1.000000',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        api: getRmbquot,
        resultField: 'items',
        selectProps: {
          fieldNames: { value: 'fBuyPri', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          onChange(val, shall) {
            if (val == '1.000000') {
              formModel.fg_amount = undefined
              formModel.total_price = undefined
            }
            formModel.rate = val
            formModel.total_price = mul(val, formModel.fg_amount || 0, 2)
            formModel.currency = shall.name.split('-')[0]
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 7
    }
  },
  {
    field: 'rate',
    label: '汇率比例',
    component: 'InputNumber',
    componentProps: ({ formModel }) => {
      return {
        precision: 4,
        min: 0,
        max: 100,
        onChange(val) {
          formModel.total_price = mul(val, formModel.fg_amount || 0, 2)
        }
      }
    },
    colProps: {
      span: 7
    },
    defaultValue: '1.000000'
  },
  {
    field: 'dept_id',
    label: '带单部门',
    component: 'PagingApiSelect',
    required: true,
    componentProps: {
      api: getDept,
      resultField: 'items',
      labelField: 'name',
      valueField: 'id',
      searchMode: true,
      pagingMode: true,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择'
      }
    },
    colProps: {
      span: 7
    }
  },
  {
    field: 'files',
    label: '水单/收据上传',
    required: true,
    component: 'Upload',
    // helpMessage: '附件必须放水单，否则财务无法识别对应哪一笔流水，较大概率重填',
    slot: 'Files',
    colProps: {
      span: 7
    }
  },
  {
    field: 'doc_out_warehouse_id',
    label: '出库单单号',
    component: 'PagingApiSelect',
    componentProps: {
      api: getListByDate,
      resultField: 'items',
      labelField: 'name',
      valueField: 'id',
      searchMode: true,
      pagingMode: true,
      searchParamField: 'strid',
      selectProps: {
        // mode: 'multiple',
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'strid'
        },
        showSearch: true,
        placeholder: '请选择'
      }
    },
    show: warehouse,
    ifShow: warehouse,
    colProps: {
      span: 7
    }
  },
  {
    field: 'currency',
    label: '货币',
    component: 'Input',
    show: false,
    defaultValue: '人民币'
  }
]

//查看子产品
export const schemassplit: FormSchema[] = [
  {
    field: 'sname',
    label: '主产品名称',
    component: 'Input',
    colProps: {
      span: 12
    },
    componentProps: {
      disabled: true
    }
  },
  {
    field: 'name',
    label: '子产品名称',
    component: 'Input',
    colProps: {
      span: 12
    },
    required: true
  },
  {
    field: 'quantity',
    label: '产品数量',
    component: 'InputNumber',
    itemHelpMessage: '产品数量只能填整数',
    colProps: {
      span: 12
    },
    componentProps: {
      min: 0,
      precision: 0
    },
    required: true
  },
  {
    field: 'proportion_org',
    label: '产品占比',
    component: 'InputNumber',
    itemHelpMessage: '所有子产品占比总和为100%',
    componentProps: {
      min: 0,
      max: 100,
      precision: 2,
      // formatter: (val) => `${val}%`,
      addonAfter: '%',
      parser: (val) => val.replace('%', '')
    },
    colProps: {
      span: 12
    },
    required: true
  },
  {
    field: 'unit',
    label: '单位',
    required: true,
    component: 'Input',
    colProps: {
      span: 12
    }
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    colProps: {
      span: 12
    }
  },
  {
    field: 'desc',
    label: '描述',
    component: 'InputTextArea',
    colProps: {
      span: 12
    }
  },
  {
    field: 'imgs',
    label: '图片组',
    component: 'Upload',
    slot: 'imgs',
    colProps: {
      span: 12
    },
    required: true
  },
  {
    field: 'files',
    label: '附件',
    component: 'Upload',
    slot: 'files',
    colProps: {
      span: 12
    }
  }
]

export const schemasPerson: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false
  },
  {
    field: 'design',
    label: '设计师',
    component: 'PagingApiSelect',
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      mode: 'multiple',
      returnParamsField: 'id',
      selectProps: {
        maxTagCount: 3,
        allowClear: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    colProps: {
      span: 24
    }
  },
  {
    field: 'design2d',
    label: '2D设计师',
    component: 'PagingApiSelect',
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: {
      api: getStaffList,
      mode: 'multiple',
      maxTagCount: 3,
      resultField: 'items',
      searchMode: true,
      returnParamsField: 'id',
      pagingMode: true,
      selectProps: {
        allowClear: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    colProps: {
      span: 24
    }
  },
  {
    field: 'design3d',
    label: '3D设计师',
    component: 'PagingApiSelect',
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      mode: 'multiple',
      maxTagCount: 3,
      returnParamsField: 'id',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        allowClear: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    colProps: {
      span: 24
    }
  }
]
