import { DefaultOptionType } from 'ant-design-vue/es/vc-tree-select/TreeSelect'
import { getDeptSelectTree } from '/@/api/admin/dept'
// import { getClientList } from '/@/api/commonUtils'
import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
// import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { getStaffList } from '/@/api/baseData/staff'
import { Rule } from 'ant-design-vue/lib/form'
import { getRmbquot } from '/@/api/erp/sales'
import { getcustomerList } from '/@/api/financialDocuments/otherIncome'
import { crmgetProjectByCrm } from '/@/api/extrasPage/createSaleOrder'
import { getProjectList } from '/@/api/projectOverview'
const pathname = window.location.pathname

export const schemas: (handleShowDeposit: (isShow: number) => void) => FormSchema[] = (handleShowDeposit) => [
  {
    field: 'type',
    label: '类型',
    component: 'RadioButtonGroup',
    defaultValue: 27,
    componentProps: {
      options: [{ label: '费用订单', value: 27 }]
    },
    colProps: { span: 24 },
    ifShow: false
  },
  {
    field: 'project_name',
    label: '项目名称',
    component: 'PagingApiSelect',
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.type !== 2 ? true : false
    },
    componentProps: {
      disabled: true
    }
  },
  {
    field: 'project_number',
    label: '项目ID',
    component: 'PagingApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: pathname === '/sp/' ? getProjectList : crmgetProjectByCrm,
        resultField: 'items',
        labelField: 'title',
        valueField: 'id',
        searchMode: true,
        pagingMode: true,
        searchParamField: pathname === '/sp/' ? 'id' : 'project_number',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'id',
            label: 'id'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'id',
          allowClear: true,
          onChange(_, shall) {
            console.log(shall)
            if (!shall) {
              formModel.project_name = undefined
              formModel.client_name = undefined
              formModel.client_contact = undefined
              formModel.client_location = undefined
            }
            formModel.project_name = shall.title
            formModel.client_name = shall.customer_name
            formModel.client_contact = shall.customer_contact
            formModel.client_location = shall.customer_location
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    required() {
      return window.location.pathname === '/car/' ? false : true
    },
    ifShow() {
      return window.location.pathname === '/car/' ? false : true
    }
  },
  {
    field: 'tpuid',
    component: 'Input',
    label: '销售订单号',
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.type !== 2 ? true : false
    },
    show(renderCallbackParams) {
      return renderCallbackParams.model.type !== 2 ? true : false
    },
    required(renderCallbackParams) {
      return renderCallbackParams.model.type !== 27 ? true : false
    },
    dynamicDisabled(renderCallbackParams) {
      return renderCallbackParams.model.type == 27 ? true : false
    },
    dynamicRules(renderCallbackParams) {
      return renderCallbackParams.model.type !== 27
        ? [
            {
              required: true,
              message: '请填写销售订单号'
            },
            {
              validator(_, value) {
                return new Promise((resolve, reject) => {
                  if (value.includes(' ') || value.includes('+') || value.includes('　')) return reject('销售订单号不能有空格或加号')
                  resolve()
                })
              },
              trigger: 'blur'
            }
          ]
        : []
    }
  },
  {
    field: 'src',
    component: 'Input',
    label: '信息来源渠道',
    required(renderCallbackParams) {
      return renderCallbackParams.model.type !== 27 ? true : false
    }
  },
  // {
  //   field: 'inCharge',
  //   label: '项目负责人',
  //   // required: true,
  //   component: 'Select',
  //   slot: 'inChargeSlot'
  // },
  {
    field: 'inCharge',
    label: '项目负责人',
    component: 'PagingApiSelect',
    required: true,
    componentProps: {
      api: getStaffList,
      searchMode: true,
      pagingMode: true,
      resultField: 'items',
      pagingSize: 20,
      returnParamsField: 'name',
      selectProps: {
        fieldNames: { key: 'id', value: 'wxworkId', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'program_incharge',
    label: '方案负责人',
    component: 'PagingApiSelect',
    required: true,
    componentProps: {
      api: getStaffList,
      searchMode: true,
      pagingMode: true,
      resultField: 'items',
      pagingSize: 20,
      returnParamsField: 'name',
      selectProps: {
        fieldNames: { key: 'id', value: 'wxworkId', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'delivery_incharge',
    label: '交付负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'wxworkId', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    }
  },
  {
    field: 'creator',
    label: '创建人',
    required: true,
    component: 'PagingApiSelect',
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        allowClear: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    dynamicDisabled(renderCallbackParams) {
      return renderCallbackParams.model.type == 27 ? true : false
    }
  },
  {
    field: 'wxworkId',
    label: '创建用户wxWorkId',
    component: 'Input',
    show: false
  },
  {
    field: 'exchangeRate',
    component: 'ApiSelect',
    label: '汇率',
    componentProps: {
      api: getRmbquot,
      resultField: 'items',
      selectProps: {
        fieldNames: { value: 'fBuyPri', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    required: true
  },

  {
    field: 'client_name',
    component: 'PagingApiSelect',
    label: '客户名',
    componentProps: ({ formModel }) => {
      return {
        api: getcustomerList,
        resultField: 'items',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'name',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          onChange(_, shall) {
            formModel.client_contact = shall.contact
            formModel.client_location = shall.location
          }
        }
      }
    },
    dynamicDisabled(renderCallbackParams) {
      console.log(window.location.pathname)

      return (window.location.pathname !== '/car/' && renderCallbackParams.model.type !== 2) ||
        (window.location.pathname === '/car/' && renderCallbackParams.model.type == 27)
        ? true
        : false
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'client_contact',
    component: 'Input',
    label: '客户联系方式',
    dynamicDisabled(renderCallbackParams) {
      return renderCallbackParams.model.type !== 2 ? true : false
    }
  },
  {
    field: 'client_location',
    component: 'Input',
    label: '客户地址',
    dynamicDisabled(renderCallbackParams) {
      return renderCallbackParams.model.type !== 2 ? true : false
    }
  },
  {
    field: 'currency',
    component: 'Input',
    defaultValue: '人民币',
    label: '货币'
  },
  {
    field: 'deptId',
    label: '部门',
    required: true,
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptSelectTree,
      treeSelectProps: {
        treeDefaultExpandAll: true,
        fieldNames: { children: 'children', key: 'id', value: 'id', label: 'name' },
        placeholder: '请选择',
        showSearch: true,
        filterTreeNode: (search: string, item: DefaultOptionType) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  // {
  //   field: 'receivable',
  //   required: true,
  //   component: 'InputNumber',
  //   label: '应收金额'
  // },
  {
    field: 'submitedAt',
    component: 'DatePicker',
    label: '开单日期',
    componentProps: {
      showTime: true,
      style: {
        width: '100%'
      }
    }
  },

  {
    field: 'trade_methods',
    component: 'Select',
    label: '贸易方式',
    required: true,
    defaultValue: 'EXW',
    componentProps: {
      showSearch: true,
      options: [
        {
          label: 'FOB',
          value: 'FOB'
        },
        {
          label: 'CFR',
          value: 'CFR'
        },
        {
          label: 'EXW',
          value: 'EXW'
        },
        {
          label: 'FCA',
          value: 'FCA'
        },
        {
          label: 'DAP',
          value: 'DAP'
        }
      ]
    }
  },
  {
    field: 'country',
    component: 'Input',
    label: '国家',
    required: true
  },
  {
    field: 'show_deposit',
    component: 'RadioButtonGroup',
    label: '是否有佣金',
    defaultValue: 0,
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ],
      onChange: (isShow) => {
        handleShowDeposit(isShow)
      }
    }
  },
  {
    field: 'deposit_name',
    component: 'Input',
    label: '佣金所属人',
    ifShow: false,
    required: true
  },
  {
    field: 'deposit_contact',
    component: 'Input',
    label: '佣金所属人联系方式',
    ifShow: false,
    required: true
  },
  {
    field: 'commission',
    component: 'InputNumber',
    label: '佣金',
    ifShow: false,
    required: true
    // required: true
  },
  {
    field: 'deposit_account',
    component: 'Input',
    label: '佣金账户',
    ifShow: false,
    required: true
  },
  {
    field: 'files',
    component: 'Upload',
    label: '附件',
    slot: 'FilesSlot',
    colProps: { span: 24 }
  },
  {
    field: 'sales_goods',
    component: 'Input',
    defaultValue: [],
    label: '订单产品',
    colProps: { span: 24 },
    slot: 'saleGoodsSlot',
    rules: [{ required: true, validator: validateEditGoods }]
  }
  // {
  //   field: 'project_number',
  //   label: '项目编号',
  //   component: 'Input',
  //   ifShow: false
  // }
]

function validateEditGoods(_rule: Rule, value: Recordable[]) {
  if (!value || value.length === 0) return Promise.reject('请先添加至少一条订单产品')
  const validResult = value.every((item) => item.name && item.unit && item.unitPrice !== undefined && item.quantity)
  if (!validResult) return Promise.reject('请完善订单产品的信息')
  return Promise.resolve()
}

export const columns: BasicColumn[] = [
  {
    dataIndex: 'name',
    title: '产品名称',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'is_logistics_follow',
    title: '是否物流部跟进',
    width: 120,
    resizable: true,
    defaultHidden: true
  },
  {
    dataIndex: 'unit',
    title: '单位',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'unitPrice',
    title: '单价(RMB)',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'quantity',
    title: '需求数量',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'totalAmount',
    title: '总价',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'puid',
    title: '产品编码',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'batch_code',
    title: '批号',
    width: 150,
    resizable: true
  },
  {
    dataIndex: 'uniqid',
    title: '产品唯一码',
    width: 150,
    resizable: true
  },
  {
    dataIndex: 'desc',
    title: '描述',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'remark',
    title: '备注',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'imgs',
    title: '产品图片',
    width: 120,
    resizable: true
  }
]
