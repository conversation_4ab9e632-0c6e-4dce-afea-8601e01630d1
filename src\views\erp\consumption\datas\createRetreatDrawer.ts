import { Rule } from 'ant-design-vue/lib/form'
import type { DefaultOptionType } from 'ant-design-vue/es/vc-tree-select/TreeSelect'
import type { FormSchema, BasicColumn } from '/@/components/Table'
import { getDeptSelectTree } from '/@/api/admin/dept'
import { getStaffList } from '/@/api/baseData/staff'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { message } from 'ant-design-vue'
import { ref } from 'vue'

//已选择的商品
export const selectRowKeys = ref<any[]>([])

export const createSchemas = (handleOrderChange?: Function, way?: string): FormSchema[] => [
  {
    field: 'type',
    label: way == 'afterSale' ? '单据类型' : '退货类型',
    component: 'Select',
    required: true,
    dynamicDisabled: true,
    componentProps: () => {
      return {
        options: [
          {
            value: 1,
            label: '销售退货'
          },
          {
            value: 2,
            label: '采购退货'
          }
        ]
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'source_uniqid',
    label: '销售单号',
    component: 'Input',
    required: true,
    dynamicDisabled: true,
    componentProps: {
      onChange: async (val: string) => {
        console.log('销售单change', val)
        if (!val) return
        // id是work_id
        try {
          handleOrderChange!()
        } catch (e) {
          throw new Error(`${e}`)
        }
      }
    }
  },
  {
    field: 'strid',
    label: '采购单号',
    component: 'Input',
    dynamicDisabled: true,
    componentProps: {
      onChange: async (val: string) => {
        console.log('采购单change', val)
        if (!val) return
        // id是work_id
        try {
          handleOrderChange!()
        } catch (e) {
          throw new Error(`${e}`)
        }
      }
    }
  },
  {
    field: 'applicant',
    label: '申请人',
    required: true,
    component: 'ApiSelect',
    componentProps({ formActionType }) {
      return {
        api: getStaffList,
        resultField: 'items',
        selectProps: {
          disabled: true,
          allowClear: true,
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        },
        params: {
          pageSize: 9999
        },
        onChange: async () => {
          try {
            await formActionType?.validateFields!(['applicant'])
          } catch (e) {
            throw new Error(`${e}`)
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'inCharge',
    label: '负责人',
    required: true,
    component: 'ApiSelect',
    componentProps: ({ formActionType }) => ({
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        disabled: true,
        allowClear: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      },
      params: {
        pageSize: 9999
      },
      onChange: async () => {
        try {
          await formActionType?.validateFields!(['inCharge'])
        } catch (e) {
          throw new Error(`${e}`)
        }
      }
    }),
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    required: true,
    componentProps: {
      // disabled: ['detail'].includes(type),
      disabled: true,
      api: getDeptSelectTree,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'id', value: 'id', label: 'name' },
        placeholder: '请选择',
        filterTreeNode: (search: string, item: DefaultOptionType) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注'
  },
  {
    field: 'product_info',
    label: '商品信息',
    component: 'Input',
    required: true,
    slot: 'ProductSlot',
    rules: [
      {
        required: true,
        validator: (_rule: Rule, value: any[]) => {
          if (!value || value.length === 0) return Promise.reject('请先选择关联的入库单')
          const validResult = value.filter((item) => selectRowKeys.value.includes(item.id)).every((item) => item.quantity > 0)
          console.log(way)

          if (!validResult && way !== 'afterSale') {
            message.error('退货数量必须大于0')
            return Promise.reject('退货数量必须大于0')
          }
          return Promise.resolve()
        }
      }
    ]
  }
]

export const columns: BasicColumn[] = [
  {
    dataIndex: 'name',
    title: '产品名称',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'imgs',
    title: '产品图片',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return useRender.renderImg(text)
    }
  },
  {
    dataIndex: 'unit',
    title: '单位',
    width: 100,
    resizable: true
  },
  {
    dataIndex: 'unit_price',
    title: '单价',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'maxQuantity',
    title: '剩余可退货数量',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'quantity',
    title: '退货数量',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'remark',
    title: '退货备注',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'desc',
    title: '退货描述',
    width: 120,
    resizable: true
  }
]
