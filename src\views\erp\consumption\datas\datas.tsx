import { h, nextTick, ref } from 'vue'
import { BasicColumn, FormSchema, TableActionType } from '/@/components/Table'
import { Tag } from 'ant-design-vue'
import { isNullOrUnDef } from '/@/utils/is'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { useI18n } from '/@/hooks/web/useI18n'
import { isArray, isNull, isString, isUndefined } from 'lodash-es'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { getDeptTree } from '/@/api/admin/dept'
import { getDept } from '/@/api/erp/systemInfo'
import { getStaffList } from '/@/api/baseData/staff'
import type { FormProps } from '/@/components/Form'

const { t, tm } = useI18n()
//订单类型和状态
const saleStore = useSaleOrderStore()

export const tableRef = ref<Nullable<TableActionType>>(null)

/** 结算状态 */
export const mapAudit = {
  0: { label: '未结算', color: '' },
  1: { label: '已结算', color: 'green' },
  2: { label: '待结算', color: 'skyblue' }
}

const isticket = {
  0: { label: '否', color: '' },
  1: { label: '是', color: 'green' }
}
const isfinishdisburse = {
  0: { label: '否', color: '' },
  1: { label: '是', color: 'green' }
}

export const columns: BasicColumn[] = [
  {
    title: '开单时间',
    dataIndex: 'submited_at',
    width: 120,
    resizable: true
  },
  {
    title: '交货日期',
    dataIndex: 'delivery_at',
    width: 120,
    resizable: true
  },
  {
    title: '可备货日期',
    dataIndex: 'stock_at',
    width: 120,
    resizable: true
  },
  {
    title: '收款次数',
    dataIndex: 'receipt_num',
    width: 100,
    resizable: true
  },
  {
    title: '项目id',
    dataIndex: 'project_number',
    width: 120,
    resizable: true
  },
  {
    title: '项目名称',
    dataIndex: 'project_name',
    width: 120,
    resizable: true
  },
  {
    title: '是否开票',
    dataIndex: 'is_ticket',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : h(Tag, { color: isticket[value].color }, () => isticket[value].label)
    }
  },
  // {
  //   title: '是否完成产品拆分',
  //   dataIndex: 'is_finish_split',
  //   width: 150,
  //   helpMessage: '完成产品拆分过后,才能进行采购',
  //   resizable: true,
  //   customRender: ({ record }) => {
  //     return h(Tag, { color: isticket[record.is_finish_split].color }, () => isticket[record.is_finish_split].label)
  //   }
  // },
  {
    title: '开票类型',
    dataIndex: 'ticket',
    width: 100,
    resizable: true
  },
  {
    title: '销售订单号',
    dataIndex: 'source_uniqid',
    width: 200,
    resizable: true
  },
  {
    title: '订单类型',
    dataIndex: 'type',
    width: 120,
    resizable: true,
    customRender: ({ value }) => (isNullOrUnDef(value) ? '-' : saleStore.saleType[value])
  },
  {
    title: '紧急程度',
    dataIndex: 'urgent_level',
    width: 100,
    resizable: true,
    customRender: ({ text }) =>
      text ? useRender.renderTag(t(`tag.mapUrgentLevel.${text}.alias`), t(`tag.mapUrgentLevel.${text}.color`)) : '-'
  },
  {
    title: '订单状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ value }) => {
      // return h(Tag, { color: saleStore.statusColor[record.status] }, () => saleStore.saleStatus[record.status])
      return isNullOrUnDef(value) ? '-' : useRender.renderTag(saleStore.saleStatus[value], saleStore.statusColor[value])
    }
  },
  {
    title: '结算状态',
    dataIndex: 'is_audit',
    width: 150,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : h(Tag, { color: mapAudit[value]?.color }, () => mapAudit[value]?.label)
      // return mapStatus[record.status]?.label
    }
  },
  {
    title: '是否销售改单',
    dataIndex: 'is_change_sale',
    width: 150,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : h(Tag, { color: isticket[value]?.color }, () => isticket[value]?.label)
      // return mapStatus[record.status]?.label
    }
  },
  {
    title: '是否售后单',
    dataIndex: 'is_after_sale',
    width: 150,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : h(Tag, { color: isticket[value]?.color }, () => isticket[value]?.label)
      // return mapStatus[record.status]?.label
    }
  },
  {
    title: '是否最后一笔支出',
    dataIndex: 'is_finish_disburse',
    width: 150,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : h(Tag, { color: isfinishdisburse[value]?.color }, () => isfinishdisburse[value]?.label)
      // return mapStatus[record.status]?.label
    }
  },
  {
    title: '驳回备注',
    dataIndex: 'reject_remark',
    width: 150,
    customRender({ text }) {
      return text || '-'
    }
  },
  {
    title: '驳回日期',
    dataIndex: 'reject_at',
    width: 150,
    customRender({ text }) {
      return text || '-'
    }
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 150
  },
  {
    title: '业务部门',
    dataIndex: 'operation_department',
    width: 150,
    resizable: true
  },
  {
    title: '客户名称',
    dataIndex: 'client_name',
    width: 100,
    resizable: true
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 100,
    resizable: true
  },
  {
    title: '交付负责人',
    dataIndex: 'delivery_incharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '方案负责人',
    dataIndex: 'program_incharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '项目负责人',
    dataIndex: 'inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '审核人',
    dataIndex: 'auditor_name',
    width: 100,
    resizable: true
  },

  // {
  //   title: '入库包裹数',
  //   dataIndex: 'pkg_num',
  //   width: 100,
  //   resizable: true
  // },
  {
    title: '销售渠道',
    dataIndex: 'source',
    width: 100,
    resizable: true,
    customRender: ({ record }) => record.source || '-'
  },
  {
    title: '订单金额',
    dataIndex: 'total_price',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  // {
  //   title: '原总应收金额',
  //   dataIndex: 'receivable_org',
  //   width: 100,
  //   resizable: true,
  //   customRender: ({ value }) => {
  //     return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
  //   }
  // },
  // {
  //   title: '总应收金额',
  //   dataIndex: 'receivable',
  //   width: 100,
  //   resizable: true,
  //   customRender: ({ value }) => {
  //     return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
  //   }
  // },

  {
    title: '已收金额',
    dataIndex: 'received',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value ? value : 0)
    }
  },
  {
    title: '实收金额',
    dataIndex: 'received_actual',
    width: 100,
    resizable: true,
    helpMessage: '实收金额与已收金额不对等,应是本张销售单存在退货或退款金额',
    customRender: ({ value }) => {
      return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  {
    title: '改单后订单总额',
    dataIndex: 'receivable_left',
    width: 150,
    resizable: true,
    customRender: ({ value }) => {
      return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  {
    title: '结算金额',
    dataIndex: 'audit_amount',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  // {
  //   title: '带单部门',
  //   dataIndex: 'take_department',
  //   width: 100,
  //   resizable: true
  // },
  // {
  //   title: '运营中心',
  //   dataIndex: 'operation_name',
  //   width: 100,
  //   resizable: true
  // },
  {
    title: '翻译人员名称',
    dataIndex: 'translate',
    width: 100,
    resizable: true
  },
  {
    title: '设计人员名称',
    dataIndex: 'design',
    width: 100,
    customRender: ({ text }) => {
      const textarr = text.map((item) => item.name)
      return text ? useRender.renderTags(textarr) : '-'
    },
    resizable: true
  },
  {
    title: '单品设计师',
    dataIndex: 'one_case_designer_name',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return text ? useRender.renderTags(text) : '-'
    }
  },
  {
    title: '全案设计师',
    dataIndex: 'full_case_designer_name',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      // return text ? useRender.renderTags(text) : '-'
      switch (text) {
        case isArray(text):
          return useRender.renderTags(text)
        case isString(text):
          return useRender.renderTag(text)
        default:
          return '-'
      }
    }
  },
  {
    title: '全案设计部门',
    dataIndex: 'dept_designer_department',
    width: 150,
    resizable: true
  },
  {
    title: '单品设计部门',
    dataIndex: 'one_dept_designer_department',
    width: 150,
    resizable: true
  },
  {
    title: '2d设计师',
    dataIndex: 'design2d',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      const textarr = text.map((item) => item.name)
      return text ? useRender.renderTags(textarr) : '-'
    }
  },
  {
    title: '3d设计师',
    dataIndex: 'design3d',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      const textarr = text.map((item) => item.name)
      return text ? useRender.renderTags(textarr) : '-'
    }
  },
  {
    title: '凤凰计划',
    dataIndex: 'phoenix_plan',
    width: 100,
    resizable: true
  },
  {
    title: '渠道来源',
    dataIndex: 'source2',
    width: 100,
    resizable: true
  },
  // {
  //   title: '退货金额',
  //   dataIndex: 'retreat_amount',
  //   width: 100,
  //   resizable: true,
  //   customRender: ({ value }) => {
  //     return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
  //   }
  // },
  {
    title: '佣金',
    dataIndex: 'commission',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  {
    title: '贸易方式',
    dataIndex: 'trade_methods',
    width: 120,
    resizable: true
  },
  {
    title: '国家',
    dataIndex: 'country',
    width: 120,
    resizable: true
  },
  {
    title: '结束时间',
    dataIndex: 'est_finished_at',
    width: 120,
    resizable: true
  },
  {
    title: '结算日期',
    dataIndex: 'audit_at',
    width: 120,
    resizable: true
  },
  {
    title: '业绩核定日期',
    dataIndex: 'ach_app_at',
    width: 120,
    resizable: true
  },
  {
    title: '财务特批',
    dataIndex: 'is_finance_approved',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '' : text ? '是' : '否'
    }
  }
]

export const warehouseColumns: BasicColumn[] = [
  {
    title: '开单时间',
    dataIndex: 'submited_at',
    width: 120,
    resizable: true
  },
  {
    title: '销售订单号',
    dataIndex: 'source_uniqid',
    width: 200,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 150
  }
]

export const searchFormSchema: FormSchema[] = [
  {
    field: 'status',
    label: '',
    // ifShow: ['/erp/saleOrder'].includes(routeName),
    component: 'RadioButtonGroup',
    defaultValue: undefined,
    componentProps: ({ formActionType }) => ({
      options: saleStore.mapOrderStatusConcatAll,
      onChange: () => {
        nextTick(() => formActionType.submit())
      }
    }),
    colProps: {
      span: 24
    }
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        placeholder: '请选择',
        showSearch: true,
        optionFilterProp: 'name',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  // {
  //   field: 'is_audit_dept',
  //   label: '核算部门',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: '是', value: 1 },
  //       { label: '否', value: 0 }
  //     ]
  //   }
  // },
  {
    field: 'operation',
    label: '业务部门',
    component: 'PagingApiSelect',
    componentProps: {
      api: getDept,
      // params: { status: 1, is_audit: 1, is_operate: 1 },
      params: { status: 1, is_audit: 1 },
      resultField: 'items',
      labelField: 'name',
      valueField: 'id',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        style: {
          width: '100%'
        }
      }
    }
  },
  // {
  //   field: 'take_dept_id',
  //   label: '带单部门',
  //   component: 'PagingApiSelect',
  //   componentProps: {
  //     api: getDept,
  //     params: { status: 1 },
  //     // params: { status: 1, is_audit: 1 },
  //     resultField: 'items',
  //     labelField: 'name',
  //     valueField: 'id',
  //     searchMode: true,
  //     pagingMode: true,
  //     selectProps: {
  //       fieldNames: {
  //         key: 'key',
  //         value: 'id',
  //         label: 'name'
  //       },
  //       optionFilterProp: 'name',
  //       showSearch: true,
  //       placeholder: '请选择',
  //       allowClear: true,
  //       style: {
  //         width: '100%'
  //       }
  //     }
  //   }
  // },
  // {
  //   field: 'status',
  //   label: '状态',
  //   component: 'Select',
  //   componentProps: {
  //     options: saleStore.mapOrderStatusOptions
  //   },
  //   colProps: { span: 8 }
  // },
  {
    field: 'source_uniqid',
    label: '销售订单号',
    component: 'Input'
  },
  {
    field: 'delivery_incharge',
    label: '交付负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'program_incharge',
    label: '方案负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'inCharge',
    label: '项目负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'is_audit',
    label: '结算状态',
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        { label: '未完成结算', value: 0 },
        { label: '已完成结算', value: 1 },
        { label: '待结算', value: 2 }
      ]
    }
  },
  {
    field: 'is_change_sale',
    label: '是否销售改单',
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ]
    }
  },
  {
    field: 'is_after_sale',
    label: '是否售后单',
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ]
    }
  },
  {
    field: 'is_finish_split',
    label: '是否完成拆单',
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ]
    }
  },

  {
    field: 'client_name',
    label: '客户',
    component: 'Input'
    // componentProps: () => {
    //   return {
    //     api: getcustomerList,
    //     resultField: 'items',
    //     selectProps: {
    //       fieldNames: {
    //         key: 'key',
    //         value: 'id',
    //         label: 'name'
    //       },
    //       showSearch: true,
    //       placeholder: '请选择',
    //       optionFilterProp: 'name',
    //       allowClear: true
    //     }
    //   }
    // }
  },
  {
    field: 'creator',
    label: '创建人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'receivable',
    label: '应收金额',
    component: 'Input',
    slot: 'receivable'
  },
  {
    field: 'submited_at',
    label: '开单日期',
    component: 'SingleRangeDate',
    // defaultValue: [dayjs().add(-7, 'day').format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')],

    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'project_number',
    label: '项目id',
    component: 'Input'
  },
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input'
  },
  {
    field: 'est_finished_at',
    label: '项目结束时间',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'urgent_level',
    label: '紧急程度',
    component: 'Select',
    componentProps: {
      options: tm('tag.urgentLevelList'),
      fieldNames: { key: 'value', value: 'value', label: 'alias' }
    }
  },
  {
    field: 'ach_app_at',
    label: '业绩核定日期',
    component: 'SingleRangeDate',
    // defaultValue: [dayjs().add(-7, 'day').format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')],

    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'is_ach_app_at_null',
    label: '业绩核定日期是否为空',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  }
]

export const formConfigFn: (clearSelectedRowKeys?: any, reload?: any) => Partial<FormProps> = (clearSelectedRowKeys?, reload?) => ({
  labelAlign: 'left',
  colon: true,
  labelWidth: 110,
  wrapperCol: { span: 24 },
  schemas: searchFormSchema,
  alwaysShowLines: 2,
  fieldMapToTime: [
    ['receivable', ['receivable1', 'receivable2']],
    ['submited_at', ['submited_at_start', 'submited_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
    ['est_finished_at', ['est_finished_at_start', 'est_finished_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
    ['ach_app_at', ['ach_app_at_start', 'ach_app_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
  ],
  autoSubmitOnEnter: true,
  actionColOptions: { span: 24 },
  resetFunc: (): any => {
    clearSelectedRowKeys?.()
    reload?.()
  },
  submitFunc: (): any => {
    clearSelectedRowKeys?.()
    reload?.()
  },
  baseColProps: {
    span: 6
  }
})
