import { message } from 'ant-design-vue'

export const checkboxErrMessage = '状态为已取消或未执行或结算状态为已结算的订单不可以执行本次操作'
export const checkoutIncomeErrorMessage = '状态为已取消或未执行的订单不可以执行本次操作'
export function isCheckboxDisabled(type, selectRows) {
  if (selectRows.length === 0) {
    message.error('请勾选订单')
    return true
  }
  if (['generateReceipt', 'closingAudit'].includes(type)) {
    if (selectRows.some((item) => item.status === 0 || item.status === 16 || item.is_audit == 1)) {
      message.error(checkboxErrMessage)
      return true
    }
  } else if (['createOtherIncome'].includes(type)) {
    if (selectRows.some((item) => item.status === 0 || item.status === 16)) {
      message.error(checkoutIncomeErrorMessage)
      return true
    }
  } else {
    return true
  }
}

export function validTableData(data) {
  if (data.length === 0) {
    message.error('收入明细不能为空')
    return true
  }
  if (data.some((item) => !item.account_name || !item.account_code || !item.amount || !item.department || !item.currency?.trim())) {
    message.error('收入明细数据必填数据不完整')
    return true
  }
}
