export interface IRecord {
  id: number
  is_audit: number
  parent_id: number
  basic_work_id: number
  name: string
  strid: string
  type: number
  source: string
  source_uniqid: string
  client_id: number
  client_name: string
  status: number
  dept_id: number
  creator: number
  auditor: null | string | number
  inCharge: null | string | number
  currency: string
  receivable: string
  received: null | number
  exchange_rate: string
  received_actual: number
  cost: string
  paid: null | number
  remark: null | string
  created_at: string
  updated_at: string
  submited_at: string
  department: string
  creator_name: string
  auditor_name: null | string
  inCharge_name: null | string
  qty_defective: null | number
  children: any[] //这里不知道是什么类型的数组
  work?: Recordable
  total_price: number
  is_withdraw?: number
  is_finance_approved: 0 | 1 | null
}
