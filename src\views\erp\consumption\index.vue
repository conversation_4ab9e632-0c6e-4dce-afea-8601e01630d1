<template>
  <div>
    <BasicTable
      :data-cachekey="routePath"
      :ref="(el) => (tableRef = el)"
      @register="registerTable"
      class="p-4"
      @fetch-success="handleFetch"
      @expand="onExpand"
    >
      <template #toolbar>
        <Tooltip>
          <template #title>{{ checkboxErrMessage }}</template>
          <a-button v-if="hasPermission([452])" @click="debouncedGenerate" :disabled="generateBtnStatus" class="mr-8px"
            >生成收款单</a-button
          >
        </Tooltip>
        <Tooltip>
          <template #title>{{ checkboxErrMessage }}</template>
          <a-button @click="account" v-if="hasPermission([453])" :disabled="generateBtnStatus">收付款结账审核</a-button>
        </Tooltip>
        <Tooltip>
          <template #title>{{ checkoutIncomeErrorMessage }}</template>
          <a-button type="primary" @click="onCreateOtherIncome" v-if="hasPermission([454])">创建其他收入单</a-button>
        </Tooltip>
        <a-button type="primary" @click="onclickCreate" v-if="hasPermission([616])">创建费用订单</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
      </template>
      <template #form-receivable="{ model }">
        <FormItemRest>
          <div style="display: flex; align-items: center">
            <InputNumber
              v-model:value="model.receivable1"
              valueFormat="YYYY-MM-DD 00:00:00"
              placeholder="应收金额初始值"
              style="height: 35px"
              :precision="4"
            />
            <span class="iconify" data-icon="ant-design:swap-outlined" style="width: 32px; padding: 0 3px 0 3px"></span>
            <InputNumber
              v-model:value="model.receivable2"
              valueFormat="YYYY-MM-DD 23:59:59"
              placeholder="应收金额最大值"
              style="height: 35px"
              :precision="4"
            />
          </div>
        </FormItemRest>
      </template>
      <template #expandedRowRender="{ record: fatherrecord }" v-if="routeName == '/erpFlow/unpurchaseTrackingsplit'">
        <BasicTable
          class="p-4"
          @register="registerChildrenTable"
          :api="getItemsList.bind(null, { pageSize: 9999, source_uniqid: fatherrecord.source_uniqid })"
          v-model:expandedRowKeys="childrenexpandedRowKeys"
        >
          <template #bodyCell="{ text, column, record }">
            <template v-if="column.key === 'imgs'">
              <TableImg :imgList="text" :simpleShow="true" />
            </template>
            <template v-if="column.key === 'status'">
              <Tag :color="statusMap[record.status]?.color"> {{ statusMap[record.status]?.text }}</Tag>
            </template>
          </template>
          <template #expandedRowRender="{ record: cellRecord }">
            <BasicTable :columns="tablecolum()" :can-resize="false" :data-source="cellRecord.items_sub" :show-index-column="false">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'imgs'">
                  <TableImg :imgList="record.imgs" :simpleShow="true" />
                </template>
                <template v-if="column.key === 'files'">
                  <div v-for="(newVal, index) in record.files" :key="index">
                    <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
                  >
                </template>
              </template>
            </BasicTable>
          </template>
        </BasicTable>
      </template>
    </BasicTable>
    <VerifyDrawer @register="registerDrawer" />
    <GenerateModal @register="registerModal" @success="handleSuccess" />
    <RetreatDrawer @register="registerRetreatDrawer" @success="reload" />
    <AddModel @register="registerModel" @success="handleSuccess" @close="clearSelectedRowKeys" />
    <BatchOtherIncomDrawer @register="registerOtherIncomeDrawer" @success="reload" />
    <PreviewFile @register="registerpreModal" />
    <UploadModal @register="registerUploadModal" @success="handleSuccess" />
    <CreaDrawer @register="registerCreaDrawer" @success="handleSuccess" />
    <FlowtransferDrawer @register="registerFlowtransferDrawer" @success="handleSuccess" />
    <personeditModa @register="registerpersoneditModa" @success="reload" />
  </div>
</template>

<script setup lang="ts" name="/erp/saleOrder">
import { nextTick, ref, onMounted } from 'vue'
import { debounce } from 'lodash-es'
import { Tooltip, message, Form, InputNumber } from 'ant-design-vue'
import { BasicTable, useTable, TableAction } from '/@/components/Table'
// import { useModal } from '/@/components/Modal'
import type { ActionItem } from '/@/components/Table'
import { useDrawer } from '/@/components/Drawer'
import AddModel from './components/AddModel.vue'
import { getSalesOrderList, setSalesStatus, getSalesOrderChildren, setIsAudit } from '/@/api/erp/sales'
// import { addBatch } from '/@/api/financialDocuments/receiptOrder'
import { usePermission } from '/@/hooks/web/usePermission'
import { useMessage } from '/@/hooks/web/useMessage'
import mitt from '/@/utils/mitt'
import VerifyDrawer from './components/VerifyDrawer.vue'
import type { IRecord } from './datas/types'
import { columns, warehouseColumns, formConfigFn, tableRef } from './datas/datas'
import { useModal } from '/@/components/Modal'
import GenerateModal from './components/GenerateModal.vue'
import RetreatDrawer from './components/CreateRetreatDrawer.vue'
import BatchOtherIncomDrawer from './components/BatchOtherIncomDrawer.vue'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { getWorkList } from '/@/api/commonUtils'
import { useUserStore } from '/@/store/modules/user'
import { storeToRefs } from 'pinia'
import { useRoute } from 'vue-router'
import { checkboxErrMessage, isCheckboxDisabled, checkoutIncomeErrorMessage } from './datas/fn'
//订单拆分
import { statusMap, tablecolum, childRenColumns } from '../UnPurchaseTracking/datas/datas'
import { getItemsList } from '/@/api/erp/UnPurchaseTracking'
import { createImgPreview } from '/@/components/Preview'
import UploadModal from '../saleOrder/components/UploadModal.vue'
import CreaDrawer from './components/CreaDrawer.vue'
import FlowtransferDrawer from '../saleOrder/components/FlowtransferDrawer.vue'
import personeditModa from './components/personeditModa.vue'

const childrenexpandedRowKeys = ref<number[]>([])

const route = useRoute()
const { name: routeName, path: routePath } = route
console.log(route)

const { createMessage } = useMessage()
const { hasPermission } = usePermission()
// import { useModal } from '/@/components/Modal'
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerRetreatDrawer, { openDrawer: openRetreatDrawer, setDrawerProps: setRetreatDrawerProps }] = useDrawer()
// const [registerSaleorderDrawer, { openDrawer: openSaleorderDrawer }] = useDrawer()
const [registerpersoneditModa, { openModal: openpersoneditModa }] = useModal()
// const InputNumber = Input.Number
const [registerOtherIncomeDrawer, { openDrawer: openOtherIncomeDrawer, setDrawerProps: setOtherIncomeDrawerProps }] = useDrawer()
const [registerCreaDrawer, { openDrawer: openCreaDrawer, setDrawerProps: setCreaDrawerProps }] = useDrawer()
const FormItemRest = Form.ItemRest

const rootMenuEmitter = mitt()
const saleStore = useSaleOrderStore()
const { warehouseQualityStaffRoleValues } = storeToRefs(saleStore)

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const isWarehouseRole = ref(warehouseQualityStaffRoleValues.value.includes(userInfo.value?.roleValue))

const [registerTable, { reload, setTableData, setLoading, getSelectRows, clearSelectedRowKeys, setProps, getSelectRowKeys }] = useTable({
  title: '消费订单列表',
  showIndexColumn: false,
  columns: isWarehouseRole.value ? warehouseColumns : columns,
  api: getSalesOrderList,
  pagination: {
    pageSize: 10,
    position: ['bottomRight']
  },
  isTreeTable: true,
  beforeFetch: (params) => {
    return {
      ...params,
      type: 27
    }
  },
  actionColumn: {
    width: isWarehouseRole.value ? 100 : 220,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  rowKey: 'id',
  useSearchForm: true,
  formConfig: formConfigFn(),
  showTableSetting: true,
  rowSelection: {
    type: 'checkbox',
    onChange: handleChange,
    getCheckboxProps: (record) => {
      if (record.parent_id !== null) {
        return { disabled: true }
      } else {
        return { disabled: false }
      }
    }
  }
})

onMounted(() => {
  rootMenuEmitter.emit('sale-order-reload', reload)
  setProps({ formConfig: formConfigFn(clearSelectedRowKeys, reload) })
})

/** 注册modal */
const [registerModal, { openModal }] = useModal()

function createActions(record: IRecord): ActionItem[] {
  return [
    {
      icon: 'clarity:success-line',
      label: '生效',
      popConfirm: {
        okText: '确定',
        title: '确定将订单状态设置成生效状态吗',
        cancelText: '取消',
        placement: 'left',
        confirm: handleChangeStatus.bind(null, record, 1)
      },
      disabled: record.status !== 0 || record.parent_id !== null,
      ifShow: hasPermission([455])
    }
  ]
}

function createDropDownActions(record: IRecord): ActionItem[] {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission([457])
    },
    {
      label: '设计师编辑',
      onClick: handleEdit.bind(null, record),
      disabled: ![0].includes(record.is_audit),
      ifShow: hasPermission([725])
    },
    {
      label: '退货',
      color: 'error',
      disabled: record.status !== 1,
      onClick: handleRetreat.bind(null, record),
      ifShow: hasPermission([458])
    },
    {
      icon: 'clarity:close-line',
      label: '取消订单',
      popConfirm: {
        okText: '确定',
        title: '确定将订单状态设置成取消状态吗',
        cancelText: '取消',
        placement: 'topLeft',
        confirm: handleChangeStatus.bind(null, record, 16),
        disabled: record.status !== 0 || record.parent_id !== null
      },
      // disabled: record.status !== 0 || record.parent_id !== null,
      ifShow: hasPermission([474])
    },
    {
      icon: 'ant-design:upload-outlined',
      color: 'error',
      label: '附件',
      tooltip: '旧erp数据上传附件',
      onClick: handleUppload.bind(null, record)
    },
    {
      label: '反结算',
      // disabled: record.is_audit !== 2 || record.parent_id !== null,
      popConfirm: {
        okText: '确定',
        title: '确定要反结算吗？',
        cancelText: '取消',
        placement: 'topLeft',
        confirm: handleIsAudit.bind(null, record),
        disabled: ![1, 2].includes(record.is_audit) || record.parent_id !== null
      },
      ifShow: hasPermission([341])
    },
    {
      label: '流水调拨',
      onClick: handleFlowtransfer.bind(null, record),
      ifShow: hasPermission([298]),
      disabled: !(record.received > 0 && record.is_audit !== 1)
    }
  ]
}

//流水调拨
const [registerFlowtransferDrawer, { openDrawer: openFlowtransferDrawer }] = useDrawer()
function handleFlowtransfer(record) {
  openFlowtransferDrawer(true, { type: 1, record })
}

async function handleChangeStatus(record: IRecord, status: number) {
  console.log(status)

  try {
    const result = await setSalesStatus({ id: record.id, status: status })
    console.log(result)
    reload()
  } catch (err) {
    throw new Error(`${err}`)
  }
}

function handleDetail(record: IRecord): void {
  setDrawerProps({ title: '销售详情' })
  openDrawer(true, record)
}

function handleEdit(record: IRecord) {
  openpersoneditModa(true, record)
}

//退货
async function handleRetreat(record: IRecord) {
  const { items } = await getWorkList({ id: record.id, type: 27, item_left: 1 })

  if (items.length < 1) return message.error('没有可退货的商品')

  setRetreatDrawerProps({ title: '销售退货' })
  openRetreatDrawer(true, { record, type: 1 })
}

function handleFetch({ items }: { items: IRecord[] }): void {
  if (routeName == '/erpFlow/unpurchaseTrackingsplit') return
  nextTick(() => {
    setTableData(items.map((item) => ({ ...item, children: [] })))
  })
}
// function handleAddOrder(record: IRecord): void {
//   // openModal(true, record)
//   openSaleorderDrawer(true, { record })
// }

async function onExpand(expanded: boolean, record: IRecord): Promise<void> {
  console.log(record, 'record', expanded)
  if (routeName !== '/erpFlow/unpurchaseTrackingsplit') {
    if (!expanded || record.children.length > 0) return
    setLoading(true)
    try {
      const childrenData = await getSalesOrderChildren(record.id, record.basic_work_id)
      Object.assign(
        record.children,
        childrenData.items.map((item) => ({ ...item, children: [] }))
      )
    } catch (err) {
      throw new Error(`${err}`)
    } finally {
      setLoading(false)
    }
  }
}

/** 选中 */
const generateBtnStatus = ref(true)
function handleChange() {
  if (getSelectRows().length == 0) {
    generateBtnStatus.value = true
  } else {
    generateBtnStatus.value = false
  }
}

/** 生成收款单 */
const debouncedGenerate = debounce(handleGenerate, 200)
//函数名不能见名知意,generate是生成的意思而已,如果我是生成其他收入单呢
async function handleGenerate() {
  let clientId: Array<number> = []
  const show = ref(false)
  try {
    const selectRows = await getSelectRows()
    if (isCheckboxDisabled('generateReceipt', selectRows)) return

    selectRows.forEach((item) => {
      clientId.push(item.client_id)
      return item.id
    })
    for (let item of getSelectRows()) {
      if (item.payment_type && item.payment_type !== 1) {
        message.warning('该销售订单已完成付款单最后一笔款或全款生成')
        show.value = true
        break
      }
    }
    if (!show.value) {
      if (new Set(clientId).size == 1) {
        openModal(true, { selectRowsData: getSelectRows(), work_ids: getSelectRowKeys() })
      } else {
        const errMessage = '不同客户不能生成同一张收款单！'
        message.error(errMessage)
        throw new Error(errMessage)
      }
    }
  } catch (error) {
    // message.error('生成收款单失败!')
    throw new Error(error)
  }
}

//结账核对
// function handleVerify() {
//   const selectData = getSelectRows()
//   if (selectData.length < 1) return
//   createConfirm({
//     iconType: 'warning',
//     title: () => h('span', '结账核对'),
//     content: () => h('span', '只有状态为执行中的订单才可以进行结账核对,确定继续结账核对吗?'),
//     onOk: async () => {
//       const data = selectData.map((item) => {
//         return item.id
//       })

//       await verifyOrder({
//         is_audit: 1,
//         work_ids: data
//       })
//       handleSuccess()
//     }
//   })
// }

//批量结算日期
const [registerModel, { openModal: openAddModal }] = useModal()
function account() {
  const selectData = getSelectRows()
  if (isCheckboxDisabled('closingAudit', selectData)) return
  if (selectData.length < 1) return
  const data = selectData.map((item) => {
    return { id: item.id }
  })
  openAddModal(true, data)
}

async function onCreateOtherIncome() {
  const selectRows = await getSelectRows()
  if (isCheckboxDisabled('createOtherIncome', selectRows)) return
  setOtherIncomeDrawerProps({
    title: '创建其他收入单'
  })
  openOtherIncomeDrawer(true, selectRows)
}

function handleSuccess() {
  clearSelectedRowKeys()
  reload()
}

//订单拆分子表格

/** 注册子表格 */
const [registerChildrenTable, {}] = useTable({
  showIndexColumn: false,
  columns: childRenColumns,
  showTableSetting: false,
  useSearchForm: false,
  isTreeTable: true,
  rowKey: 'id',
  canResize: false,
  pagination: {
    pageSize: 10,
    pageSizeOptions: ['10', '20', '100']
  }
})
//展示
const [registerpreModal, { openModal: openpreModal }] = useModal()
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  console.log(prefix)
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openpreModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}

async function handleIsAudit(record) {
  try {
    const { news } = await setIsAudit({ id: record.id, is_audit: 0 })
    if (news === 'success') {
      createMessage.success('反结算成功')
      await reload()
    }
  } catch (err) {
    console.error(err)
    createMessage.error('反结算失败')
  }
}

// 上传附件
const [registerUploadModal, { openModal: openModalUplad }] = useModal()
function handleUppload(record) {
  openModalUplad(true, record)
}

function onclickCreate() {
  openCreaDrawer(true, {})
  setCreaDrawerProps({ title: '新增', width: '90%', showFooter: true })
}
</script>
<style lang="less" scoped>
:deep(.ant-input-number) {
  width: 235px !important;
}
</style>
