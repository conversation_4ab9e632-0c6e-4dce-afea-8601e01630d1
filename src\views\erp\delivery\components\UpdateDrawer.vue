<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" :show-footer="!['detail'].includes(props.type)" @ok="handleOk" width="100%">
    <BasicForm @register="registerForm">
      <template #PurchaseSlot="{}">
        <FormItemRest>
          <Alert
            message="需先选择供应商才可选择采购订单.如果有子产品,父产品的数量受已选择的子产品的数量影响. 提交前请勾选送货商品并确认送货数量"
            type="warning"
          />
          <VxeBasicTable
            :tree-config="{ rowField: 'id', children: 'items_sub' }"
            :column-config="{ resizable: true }"
            v-bind="gridOptionsParent"
            ref="tableRef"
            @checkbox-change="selectChangeEvent"
            @checkbox-all="selectChangeEvent"
          >
            <template #Action="{ row, column }">
              <template v-if="column.field === 'action' && row.hasOwnProperty('items_sub')">
                <a-button block type="link" @click="handleRemoveDetail(row)">删除</a-button>
              </template>
            </template>
            <template #QuantitySlot="{ row, column }">
              <template v-if="column.field === 'now_quantity'">
                <InputNumber
                  :disabled="['detail'].includes(props.type)"
                  :min="row.hasOwnProperty('items_sub') ? 0.01 : 1"
                  :max="row.delivery_quantity_left"
                  v-model:value="row.now_quantity"
                  :precision="row.hasOwnProperty('items_sub') ? 2 : 0"
                  @change="handleChangeQuantity(row)"
                />
              </template>
            </template>
            <template #ActionHeader="{ column }">
              <div>
                {{ column.title }}
              </div>
              <a-button size="small" type="primary" @click="handleRemoveSelected">删除选中项</a-button>
            </template>
          </VxeBasicTable>
        </FormItemRest>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script lang="ts" setup>
import { reactive, ref, unref } from 'vue'
import { Form, InputNumber, Alert, message } from 'ant-design-vue'
import type { DefaultOptionType } from 'ant-design-vue/es/vc-tree-select/TreeSelect'
import { BasicTableProps, VxeBasicTable, VxeGridInstance } from '/@/components/VxeTable'
import { cloneDeep } from 'lodash-es'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { updateDelivery, getDeliveryDetail } from '/@/api/erp/delivery'
import { getPurchaseDetail } from '/@/api/erp/purchaseOrder'
import { isNull } from '/@/utils/is'
import { add, mul } from '/@/utils/math'
import type { IProps } from '../datas/type'
import { getSchemasList, searchWorkList, docKeys, calcNewParams, getDrawerTableColumns } from '../datas/updateDrawer'

const FormItemRest = Form.ItemRest

const prevSelectWork = ref<DefaultOptionType[]>([])

const props = ref<IProps>({
  type: 'add'
})

const tableRef = ref<VxeGridInstance>()
const gridDataSource = ref<Recordable[]>([])
const vxeLoading = ref<boolean>(false)

const emit = defineEmits(['register', 'success'])

const [registerDrawer, { changeOkLoading, changeLoading, closeDrawer }] = useDrawerInner(async (data) => {
  try {
    await changeLoading(true)
    await changeOkLoading(false)
    await resetFields()
    props.value = data
    const { type } = data
    prevSelectWork.value = []
    gridDataSource.value = []

    await updateSchema(
      getSchemasList(data.type, { handlePurchaseOrderChange, validateFields, updateSchema, setFieldsValue, changeLoading })
    )

    gridOptionsParent.columns = getDrawerTableColumns(type)
    if (['edit', 'detail'].includes(type)) {
      const { items: deliveryDetails } = await getDeliveryDetail({ id: data.record.id })
      deliveryDetails.items = deliveryDetails.items.map((item) => ({
        ...item,
        now_quantity: item.quantity,
        items_sub: item.items_sub.map((subItem) => ({
          ...subItem,
          now_quantity: subItem.quantity,
          parentId: item.id
        }))
      }))
      await handleLoad(deliveryDetails)
    }
    changeLoading(false)
  } catch (err) {
    changeLoading(false)
    console.error(err)
  }
})

const [registerForm, { updateSchema, resetFields, getFieldsValue, setFieldsValue, validateFields, validate }] = useForm({
  schemas: getSchemasList(unref(props).type),
  colon: true,
  showActionButtonGroup: false,
  baseColProps: { span: 6 },
  labelAlign: 'left',
  labelCol: { style: { width: '120px' } }
})

const gridOptionsParent = reactive<BasicTableProps>({
  keepSource: true,
  editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
  rowConfig: {
    keyField: 'id'
  },
  columns: [],
  toolbarConfig: {
    enabled: false
  },
  tableStyle: { padding: '0 !important' },
  proxyConfig: null,
  headerAlign: 'center',
  'scroll-y': { enabled: true, gt: 20 },
  id: 'VxeTable',
  loading: vxeLoading,
  height: '500px',
  data: gridDataSource
})

// 回显表单数据
async function handleLoad(details) {
  await setFieldsValue({ ...calcNewParams(docKeys, details), purchase_list: details.items })
  //渲染表格
  gridDataSource.value = cloneDeep(details.items)
  await updateSchema([
    {
      field: 'inCharge',
      componentProps: { defaultOptions: [{ name: details?.inCharge_name, id: details?.inCharge }] }
    },
    {
      field: 'warehouse_id',
      componentProps: { defaultOptions: [{ name: details?.warehouse_name, id: details?.warehouse_id }] }
    },
    {
      field: 'supplier_id',
      componentProps: { defaultOptions: [{ name: details?.supplier_name, id: details?.supplier_id }] }
    }
  ])
}

//多选的情况
async function handlePurchaseOrderChange(workIds: DefaultOptionType[], loading) {
  try {
    const { type } = props.value
    if (type === 'add') {
      loading.value = true
      vxeLoading.value = true
      let tableData: Recordable[] = []
      // 判断是增加还是减少
      if (workIds.length > prevSelectWork.value.length) {
        // 增加了关联,判断增加了哪个，只请求新增的
        // mapPrevSelectWork: 获取上一次选中的work的关联采购订单条数
        const mapPrevSelectWork = prevSelectWork.value.map((item) => item.value)

        // pushTableData： 新增了的work
        const pushTableData: DefaultOptionType[] = workIds.filter((item) => !mapPrevSelectWork.includes(item.value))
        for (const workId of pushTableData) {
          searchWorkList.value.push(cloneDeep(workId))
          let { items } = await getPurchaseDetail({ doc_id: workId.option.doc_id, pageSize: 500 })

          //过滤掉qty_wait_received小于1的商品
          items = items.filter((item) => item.qty_wait_received > 0)
          if (items) tableData = [...(getFieldsValue().purchase_list || []), ...items]
        }
      } else if (workIds.length < prevSelectWork.value.length) {
        // 减少了关联,获取表单数据进行filter
        const mapWorks = workIds.map((item) => item.value)
        searchWorkList.value = searchWorkList.value.filter((item) => mapWorks.includes(item.value))
        tableData = (getFieldsValue().purchase_list || []).filter((item: Recordable) => mapWorks.includes(item.doc_id))
      }

      // 初始化一下送货数量
      tableData = tableData.map((item) => ({
        ...item,
        now_quantity: item.delivery_quantity_left,
        items_sub: item.items_sub.map((subItem) => ({ ...subItem, now_quantity: subItem.delivery_quantity_left, parentId: item.id }))
      }))
      //将子级产品的数组转到父级产品的数组中
      await setFieldsValue({
        purchase_list: cloneDeep(tableData)
      })
      //设置表格数据
      // gridDataSource.value = transformArray(tableData)
      gridDataSource.value = cloneDeep(tableData)

      prevSelectWork.value = workIds
    }
  } finally {
    vxeLoading.value = false
    loading.value = false
  }
}

//checkbox的事件
function selectChangeEvent({ $table }) {
  const newSelectRows = $table.getCheckboxRecords()

  if (newSelectRows.length === 0) return
  // 从所有子产品中拿到所有的父产品id
  const allSelectChildRows = newSelectRows.filter((item) => item.hasOwnProperty('parentId'))
  const parentIds = Array.from(new Set(allSelectChildRows.map((item) => item.parentId)))

  // 我们要改的只有父产品而已(我这里是将parentIds里所有父产品都重新计算数量,就不用判断这么多情况了,否则if else要写很多)
  for (const id of parentIds) {
    const curChildRows = allSelectChildRows.filter((item) => item.parentId === id)
    // 计算出父产品的数量(now_quantity)
    const sumQuantity = curChildRows.reduce((acc, cur) => add(acc, mul(cur.now_quantity, cur.proportion)), 0)

    const idx = gridDataSource.value.findIndex((item) => +item.id === +id)
    gridDataSource.value[idx].now_quantity = sumQuantity
  }
}

//改变数量
const handleChangeQuantity = (row: Recordable) => {
  // 诚哥说现在是只有子产品影响父产品数量
  // 表格选中数据
  const newSelectRows = tableRef.value?.getCheckboxRecords(true) || []
  // 拿到所有选中的子产品
  const allSelectChildRows = newSelectRows.filter((item) => item.hasOwnProperty('parentId'))

  // 判断是否是父产品
  if (row.hasOwnProperty('items_sub')) {
    if (row.items_sub.length === 0) return
    // 找到改父产品下,已选的子产品
    const curChildRows = allSelectChildRows.filter((item) => item.parentId === row.id)
    const curParentIdx = gridDataSource.value.findIndex((item) => +item.id === +row.id)
    if (curChildRows.length === 0) return
    // 计算出父产品的数量(now_quantity)
    const sumQuantity = curChildRows.reduce((acc, cur) => add(acc, mul(cur.now_quantity, cur.proportion)), 0)
    // 更新父产品的数量
    gridDataSource.value[curParentIdx].now_quantity = sumQuantity
  } else {
    // 是子产品
    // 判读是否已经选中该子产品
    if (allSelectChildRows.map((item) => item.id).includes(row.id)) {
      // 找到所有该子产品同级的所有选中的子产品
      const allCurChildRows = allSelectChildRows.filter((item) => row.parentId === item.parentId)
      // 计算出父产品的数量(now_quantity)
      const sumQuantity = allCurChildRows.reduce((acc, cur) => add(acc, mul(cur.now_quantity, cur.proportion)), 0)
      const idx = gridDataSource.value.findIndex((item) => +item.id === +row.parentId)
      gridDataSource.value[idx].now_quantity = sumQuantity
    }
  }
}

//没在编辑里做删除,如果做,就要判断类型
function handleRemoveDetail(record: Recordable) {
  const idx = gridDataSource.value.findIndex((item) => +item.id === +record.id)
  gridDataSource.value.splice(idx, 1)
  setFieldsValue({ purchase_list: gridDataSource.value })
  const residual = gridDataSource.value.filter((item) => item.work_id === record.work_id)
  // // 如果删除的这条数据是workid的最后一个数据，则删除关联采购订单这个id
  if (residual.length > 0) return
  const workList = getFieldsValue().work
  const newWorkList = workList.filter((item) => item.value !== record.doc_id)
  setFieldsValue({ work: newWorkList })
}

async function handleRemoveSelected() {
  const selectRow = tableRef.value?.getCheckboxRecords(true).filter((item) => isNull(item.parentId))
  // if (leftData.length > 0) {
  // 这里就是只删除一个
  const selectId = selectRow?.map((item) => item.id) || []
  gridDataSource.value = gridDataSource.value.filter((item) => !selectId.includes(item.id))
  await setFieldsValue({ purchase_list: gridDataSource.value })
  //   return
  // }
  // 判断批量删除的明细，是否下拉列表的doc中最后一个item
  const selectDocId = gridDataSource.value?.map((item) => item.doc_id) || []
  // const leftData = gridDataSource.value.filter((item) => selectDocId.includes(item.doc_id))
  const workList = getFieldsValue().work.filter((item) => selectDocId.includes(item.value))
  prevSelectWork.value = workList
  await setFieldsValue({ work: workList })
  await setFieldsValue({ purchase_list: gridDataSource.value })
}

// TODO: 当前父产品的数量是保留两位小数,如果子产品的占比保留小数超过三位,会导致计算出来的父产品数量不准确(设计是有缺陷的,这里是一个被决策者埋了个大雷)
async function handleOk() {
  try {
    await changeOkLoading(true)
    const values = await validate()
    const { type, record } = props.value
    const isCreate = ['add'].includes(type)
    // 处理表格选中数据
    const newSelectRows = tableRef.value?.getCheckboxRecords(true) || []
    if (newSelectRows.length === 0) {
      const errMsg = '请选择要送货的商品'
      message.error(errMsg)
      throw new Error(errMsg)
    }
    // 从所有子产品中拿到所有的父产品id
    //拿到所有选中的子产品
    const allSelectChildRows = newSelectRows.filter((item) => item.hasOwnProperty('parentId'))
    // 已经选上的父产品id(因为不全选完子产品的话,父产品是不会选上的)
    const selectParentIds = Array.from(new Set(newSelectRows.filter((item) => !item.hasOwnProperty('parentId')).map((item) => item.id)))
    const selectChildToparentIds = Array.from(new Set(allSelectChildRows.map((item) => item.parentId)))
    const allSelectParentIds = Array.from(new Set([...selectParentIds, ...selectChildToparentIds]))

    const calcItems = []
    for (const id of allSelectParentIds) {
      const curParentRow = gridDataSource.value.find((item) => +item.id === +id)
      // 找到改父产品下,已选的子产品
      const curChildRows = allSelectChildRows.filter((item) => item.parentId === id)
      // 如果父产品的数量大于剩余可送货数量
      if (curParentRow.now_quantity > curParentRow.delivery_quantity_left) {
        const errMsg = `父产品${curParentRow.name}的数量(${curParentRow.now_quantity})大于剩余可送货数量(${curParentRow.delivery_quantity_left})`
        message.error(errMsg)
        throw new Error(errMsg)
      }
      //从现在的逻辑来说,子产品的数量应该不会大于该子产品的送货剩余数量
      if (curChildRows.length > 0 && curChildRows.some((item) => item.delivery_quantity_left < item.now_quantity)) {
        const errMsg = `子产品的数量大于该子产品剩余可送货数量`
        message.error(errMsg)
        throw new Error(errMsg)
      }

      const newItem = {
        doc_purchase_id: isCreate ? curParentRow.doc_id : curParentRow.doc_purchase_id,
        dept_id: curParentRow.dept_id,
        request_id: curParentRow.request_id,
        purchase_id: curParentRow.purchase_id,
        quantity: Number(curParentRow.now_quantity),
        items_sub: curChildRows.map((subItem) => ({
          doc_id: isCreate ? undefined : curParentRow.doc_id,
          item_purchase_id: subItem.item_purchase_id,
          item_purchase_sub_id: isCreate ? subItem.id : subItem.item_purchase_sub_id,
          puid: subItem.puid,
          request_id: subItem.request_id,
          request_sub_id: subItem.request_sub_id,
          name: subItem.name,
          quantity: Number(subItem.now_quantity),
          proportion: subItem.proportion
        }))
      }

      // 再做一层判断,如果该父产品下所有子产品都选完了,父产品的数量应该为delivery_quantity_left(不对,如果我新增的时候选两个子产品,然后编辑选一个,再进编辑,就有问题了)
      // if (curChildRows.length === curParentRow.items_sub.length) {
      //   newItem.quantity = curParentRow.delivery_quantity_left
      // }
      calcItems.push(newItem)
    }
    const calcDoc = calcNewParams(docKeys, values)

    const params = {
      doc: {
        id: isCreate ? undefined : record!.id,
        ...calcDoc
      },
      items: calcItems
    }
    await updateDelivery(params)
    emit('success')
    closeDrawer()
  } catch (err) {
    await changeOkLoading(false)
    console.error(err)
  }
}
</script>
<style lang="less" scoped>
@import url(../css/updateDrawer.css);
</style>
