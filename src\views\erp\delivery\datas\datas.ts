import { getAccountList } from '/@/api/commonUtils'
import { BasicColumn, FormSchema } from '/@/components/Table'

export const columns: BasicColumn[] = [
  {
    title: '送货单号',
    dataIndex: 'strid',
    width: 120,
    resizable: true
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name',
    width: 120,
    resizable: true
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 120,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 120,
    resizable: true
  },
  {
    title: '仓库地址',
    dataIndex: 'warehouse_address',
    width: 120,
    resizable: true
  },
  {
    title: '仓库电话',
    dataIndex: 'warehouse_telphone',
    width: 120,
    resizable: true
  },
  {
    title: '仓库联系人',
    dataIndex: 'warehouse_contacts',
    width: 120,
    resizable: true
  },
  {
    title: '供应商电话',
    dataIndex: 'supplier_telphone',
    width: 120,
    resizable: true
  },
  {
    title: '供应商联系人',
    dataIndex: 'supplier_contacts',
    width: 120,
    resizable: true
  }
]

export const searchFormSchema: FormSchema[] = [
  {
    field: 'strid',
    label: '送货单号',
    component: 'Input'
  },
  {
    field: 'doc_purchase_strid',
    label: '采购单号',
    component: 'Input'
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    }
  },
  {
    field: 'creator',
    label: '创建人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    }
  }
  // {
  //   field: 'created_at',
  //   label: '创建日期',
  //   component: 'SingleRangeDate',
  //   componentProps: {
  //     allowEmpty: [true, true],
  //     showTime: false,
  //     valueFormat: 'YYYY-MM-DD',
  //     format: 'YYYY-MM-DD',
  //     style: {
  //       width: '100%'
  //     }
  //   }
  // }
]
