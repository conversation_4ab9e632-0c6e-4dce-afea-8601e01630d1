import { h, withDirectives, ref } from 'vue'
import { TType } from './type'
import { getWarehouse } from '/@/api/baseData/warehouse'
import { getAccountList, getErpSupplier } from '/@/api/commonUtils'
import { getRelatePurchaseList } from '/@/api/erp/inWarehouse'
import { FormSchema } from '/@/components/Table'
import loadingDirective from '/@/directives/loading'
import { VxeGridPropTypes } from 'vxe-table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { isNullOrUnDef } from '/@/utils/is'

export const searchWorkList = ref<any[]>([])
const loading = ref<boolean>(false)

export const getSchemasList: (type: TType, Fn?) => FormSchema[] = (type, Fn) => {
  const isDetail = type === 'detail'
  return [
    {
      field: 'inCharge',
      label: '负责人',
      component: 'PagingApiSelect',
      required: true,
      dynamicDisabled: isDetail,
      componentProps: {
        api: getAccountList,
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'supplier_id',
      label: '供应商',
      dynamicDisabled: isDetail,
      component: 'PagingApiSelect',
      required: true,
      componentProps: {
        api: getErpSupplier,
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true
        },
        onChange: async (value) => {
          const hasValue = !isNullOrUnDef(value)
          if (!hasValue) return
          try {
            // 新增的时候,选了供应商,然后带出关联的采购订单
            if (['add'].includes(type)) {
              Fn.changeLoading(true)
              const params = {
                status: 1,
                pageSize: 999999,
                is_wait: 1,
                supplier_id: value
              }
              await Fn!.updateSchema!({
                field: 'work',
                ifShow: hasValue,
                componentProps: {
                  params
                }
              })
              if (hasValue) {
                const { items } = await getRelatePurchaseList(params)
                const workStrids = items.map((item) => ({
                  disabled: undefined,
                  key: item.doc_id,
                  label: item.strid,
                  originLabel: item.strid,
                  value: item.doc_id,
                  option: item,
                  doc_id: item.doc_id
                }))

                Fn!.setFieldsValue({ work: workStrids })
              }
            }
            Fn.changeLoading(false)
          } catch (e) {
            Fn.changeLoading(false)
            throw new Error(`${e}`)
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'supplier_contacts',
      label: '供应商联系人',
      component: 'Input',
      required: true,
      dynamicDisabled: isDetail
    },
    {
      field: 'supplier_telphone',
      label: '供应商电话',
      component: 'Input',
      required: true,
      dynamicDisabled: isDetail
    },
    {
      field: 'warehouse_id',
      component: 'PagingApiSelect',
      label: '仓库',
      required: true,
      dynamicDisabled: isDetail,
      componentProps: {
        pagingMode: true,
        searchMode: true,
        api: (params) => getWarehouse({ ...params }),
        resultField: 'items',
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: false,
          placeholder: '请选择',
          optionFilterProp: 'name'
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'warehouse_contacts',
      label: '仓库联系人',
      component: 'Input',
      required: true,
      dynamicDisabled: isDetail
    },
    {
      field: 'warehouse_telphone',
      label: '仓库电话',
      component: 'Input',
      required: true,
      dynamicDisabled: isDetail
    },
    {
      field: 'warehouse_address',
      label: '仓库地址',
      component: 'Input',
      required: true,
      dynamicDisabled: isDetail
    },
    {
      field: 'remark',
      label: '备注',
      component: 'InputTextArea',
      required: true,
      dynamicDisabled: isDetail
    },
    {
      ifShow: false, //只有选择了供应商才显示
      field: 'work',
      label: '关联采购订单',
      component: 'ApiSelect',
      colProps: { span: 24 },
      required: true,
      itemProps: {
        validateTrigger: 'blur'
      },
      componentProps: {
        // disabled: type === 'detail',
        api: getRelatePurchaseList,
        params: {
          status: 1,
          // pageSize: 999999,
          is_wait: 1
        },
        searchParamField: 'strid',
        immediate: false,
        selectProps: {
          disabled: ['detail', 'edit'].includes(type),
          fieldNames: { key: 'doc_id', value: 'doc_id', label: 'strid' },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          mode: 'multiple',
          labelInValue: true,
          dropdownRender: ({ menuNode }) => {
            const vNode = h('div', {}, menuNode)
            return withDirectives(vNode, [[loadingDirective, loading.value]])
          }
        },
        searchMode: true,
        resultField: 'items',
        pagingMode: true,
        returnParamsField: 'doc_id',
        onChange: async (val: number) => {
          try {
            if (['add'].includes(type)) await Fn!.validateFields!(['work_id'])

            Fn!.handlePurchaseOrderChange(val, loading)
          } catch (e) {
            throw new Error(`${e}`)
          }
        }
      }
    },
    {
      field: 'purchase_list',
      label: '采购列表',
      component: 'Input',
      slot: 'PurchaseSlot',
      colProps: { span: 24 }
      // rules: [{ required: true, validator: validateGoods }]
    }
  ]
}

//校验
// function validateGoods(_rule: Rule, value: Recordable[]) {
//   console.log(value, 'value')
//   if (!value || value.length === 0) return Promise.reject('请先选择关联的采购订单')
//   const validResult = value.every(
//     (item) =>
//       // item.qty_total !== undefined &&
//       !isUndefined(item.qty_received) && !isUndefined(item.warehouse_id)
//     // item.pkg_num !== undefined
//   )
//   if (!validResult) return Promise.reject('请完善产品列表内的信息')
//   return Promise.resolve()
// }

// doc需要传递的字段
export const docKeys = [
  'supplier_id',
  'supplier_contacts',
  'supplier_telphone',
  'warehouse_id',
  'warehouse_contacts',
  'warehouse_telphone',
  'warehouse_address',
  'inCharge',
  'remark'
]

export const itemsKeys = ['doc_id', 'dept_id', 'request_id', 'purchase_id']

export function calcNewParams(mapKeys, oldObj) {
  return mapKeys.reduce((acc, key) => {
    if (oldObj.hasOwnProperty(key)) {
      acc[key] = oldObj[key]
    }
    return acc
  }, {})
}

export const getDrawerTableColumns: (type?: TType) => VxeGridPropTypes.Columns = (type) => [
  {
    width: 100,
    type: 'checkbox',
    // visible: ['add'].includes(type),
    treeNode: true
  },
  {
    title: '采购单号',
    field: 'purchase_strid',
    width: 150,
    visible: !['add'].includes(type)
  },
  {
    title: '数量',
    field: 'now_quantity',
    width: 150,
    slots: {
      default: 'QuantitySlot'
    }
  },
  {
    title: '送货剩余数量',
    field: 'delivery_quantity_left',
    width: 150
  },
  {
    title: '名称',
    field: 'name',
    showOverflow: 'tooltip',
    align: 'center'
  },
  {
    title: '图片',
    field: 'imgs',
    slots: {
      default: ({ row }) => {
        return [useRender.renderImg(row.imgs)]
      }
    }
  },
  {
    title: '产品编号',
    field: 'puid',
    align: 'center'
  },
  {
    title: '操作',
    field: 'action',
    width: 200,
    fixed: 'right',
    visible: ['add'].includes(type),
    slots: {
      default: 'Action',
      header: 'ActionHeader'
    }
  }
]
