<template>
  <div>
    <BasicTable :data-cachekey="routePath" @register="registerTable">
      <template #toolbar>
        <a-button preIcon="ant-design:plus-outlined" @click="handleUpdate('add')" type="primary" v-if="hasPermission([366])">新增</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
      </template>
    </BasicTable>
    <UpdateDrawer @register="registerUpdateDrawer" @success="reload" />
  </div>
</template>

<script lang="ts" setup>
import { getDeliveryList, exportDelivery, changeDeliveryStatus } from '/@/api/erp/delivery'
import { BasicTable, useTable, ActionItem, TableAction } from '/@/components/Table'
import { useDrawer } from '/@/components/Drawer'
import { downloadByData } from '/@/utils/file/download'
import { usePermission } from '/@/hooks/web/usePermission'
import UpdateDrawer from './components/UpdateDrawer.vue'
import { columns, searchFormSchema } from './datas/datas'
import type { TType } from './datas/type'
import { useRoute } from 'vue-router'

const route = useRoute()
const { path: routePath } = route
const { hasPermission } = usePermission()
const [registerTable, { reload, setLoading }] = useTable({
  title: '送货单',
  api: getDeliveryList,
  rowKey: 'id',
  showIndexColumn: false,
  columns,
  useSearchForm: true,
  showTableSetting: true,
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema,
    baseColProps: { span: 6 }
  },
  actionColumn: {
    width: 180,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  }
})

const [registerUpdateDrawer, { openDrawer: openUpdateDrawer, setDrawerProps: setUpdateDrawerProps }] = useDrawer()

function createActions(record: Recordable): ActionItem[] {
  return [
    {
      icon: 'ant-design:file-search-outlined',
      label: '审核',
      popConfirm: {
        title: '审核后将无法撤回，且无法编辑，是否确定审核？',
        placement: 'left',
        confirm: handleChangeStatus.bind(null, record)
      },
      disabled: record.status !== 0,
      ifShow: hasPermission([364])
    },
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleUpdate.bind(null, 'edit', record),
      disabled: record.status !== 0,
      ifShow: hasPermission([349])
    }
  ]
}

function createDropDownActions(record: Recordable): ActionItem[] {
  return [
    {
      label: '导出',
      icon: 'carbon:download',
      disabled: record.status !== 1,
      onClick: handleExport.bind(null, record),
      ifShow: hasPermission([350])
    },
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleUpdate.bind(null, 'detail', record),
      ifShow: hasPermission([365])
    }
  ]
}

async function handleChangeStatus(record: Recordable) {
  await changeDeliveryStatus({ id: record.id, status: 1 })
  reload()
}

function handleUpdate(type: TType, record?: Recordable) {
  const mapTitle = {
    add: '新增送货单',
    edit: '编辑送货单',
    detail: '送货单详情'
  }
  setUpdateDrawerProps({ title: mapTitle[type] })
  openUpdateDrawer(true, { type, record })
}

async function handleExport(record: Recordable) {
  try {
    setLoading(true)
    const res = await exportDelivery({ id: record.id })
    //将二进制流转csv文件并下载
    downloadByData(res as any, `送货单${record.strid}.csv`)
  } catch (err) {
    console.error(err)
  } finally {
    setLoading(false)
  }
}
</script>
