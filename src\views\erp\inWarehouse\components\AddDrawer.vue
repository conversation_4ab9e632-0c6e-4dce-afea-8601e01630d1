<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="新增入库单" width="90%" @ok="handleOk" show-footer>
    <ScrollContainer>
      <BasicForm @register="registerBasicinfoForm">
        <template #Purchase="{ model }">
          <FormItemRest>
            <BasicTable @register="registerTable">
              <template #headerCell="{ column }">
                <template v-if="column.dataIndex === 'warehouse_id'">
                  <Popover trigger="click" title="批量选择已勾选产品的所在仓库">
                    <template #content>
                      <Select
                        style="width: 188px"
                        :options="commonMap.storeList"
                        v-model:value="selectWarehouse"
                        :fieldNames="{ value: 'id', label: 'name' }"
                        optionFilterProp="name"
                        show-search
                        placeholder="请选择已勾选产品的所在仓库"
                      />
                      <Button style="margin-left: 5px" type="primary" @click="handleBatchEdit">确定</Button>
                    </template>
                    <span style="margin-right: 10px">
                      {{ column.customTitle }}
                      <EditOutlined />
                    </span>
                  </Popover>
                </template>
                <template v-else-if="column.dataIndex === 'received_at'">
                  <Popover trigger="click" title="批量选择已勾选产品的入库时间">
                    <template #content>
                      <Space direction="vertical" :size="12">
                        <DatePicker v-model:value="received_at" valueFormat="YYYY-MM-DD" format="YYYY-MM-DD" />
                      </Space>
                      <Button style="margin-left: 5px" type="primary" @click="handleBatchEdit">确定</Button>
                    </template>
                    <span style="margin-right: 10px">
                      {{ column.customTitle }}
                      <EditOutlined />
                    </span>
                  </Popover>
                </template>
                <template v-else>{{ column.customTitle }}</template>
              </template>
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.dataIndex === 'approve'">
                  <MinusCircleOutlined @click="handleRemoveDetail(record)" />
                </template>
                <template v-if="column.key === 'imgs'">
                  <TableImg :imgList="record.imgs" :simpleShow="true" @click="stopPropagation" />
                </template>
                <template v-else-if="column.dataIndex === 'warehouse_id' && model.purchase_list[index]">
                  <FormItemRest>
                    <Select v-model:value="model.purchase_list[index].warehouse_id" :options="warehouseFnData" />
                  </FormItemRest>
                </template>
                <template v-else-if="column.dataIndex === 'received_at' && model.purchase_list[index]">
                  <FormItemRest>
                    <DatePicker
                      v-model:value="model.purchase_list[index].received_at"
                      :disabled="!propsData.isUpdate"
                      valueFormat="YYYY-MM-DD"
                      format="YYYY-MM-DD"
                    />
                  </FormItemRest>
                </template>

                <template v-else-if="column.dataIndex === 'pkg_received' && model.purchase_list[index]">
                  <FormItemRest>
                    <InputNumber :min="0" v-model:value="model.purchase_list[index].pkg_received" :disabled="!propsData.isUpdate" />
                  </FormItemRest>
                </template>
                <template v-else-if="column.dataIndex === 'qty_total' && model.purchase_list[index]">
                  <FormItemRest>
                    <InputNumber
                      :min="0.01"
                      :max="record.qty_wait_received"
                      v-model:value="model.purchase_list[index].qty_total"
                      :disabled="!propsData.isUpdate"
                      :precision="2"
                    />
                  </FormItemRest>
                  <!-- {{ model.purchase_list[index].qty_total }} -->
                </template>
                <!-- <template v-else-if="column.dataIndex === 'pkg_num' && model.purchase_list[index]">
                <FormItemRest>
                  <InputNumber :min="0" v-model:value="model.purchase_list[index].pkg_num" :disabled="!propsData.isUpdate" />
                </FormItemRest>
              </template> -->
                <!-- <template v-else-if="column.dataIndex === 'qty_received' && model.purchase_list[index]">
                  <FormItemRest>
                    <InputNumber
                      :min="0.01"
                      :max="model.purchase_list[index].qty_total"
                      v-model:value="model.purchase_list[index].qty_received"
                      :disabled="!propsData.isUpdate"
                    />
                  </FormItemRest>
                </template> -->
                <template v-else-if="column.dataIndex === 'qty_stocking' && model.purchase_list[index]">
                  {{ model.purchase_list[index].qty_stocking }}
                </template>

                <template v-else-if="column.dataIndex === 'qty_defective' && model.purchase_list[index]">
                  <FormItemRest>
                    <InputNumber :min="0" v-model:value="model.purchase_list[index].qty_defective" :disabled="!propsData.isUpdate" />
                  </FormItemRest>
                </template>

                <!--              <template v-else-if="column.dataIndex === 'status' && model.purchase_list[index]">-->
                <!--                <FormItemRest>-->
                <!--                  <Select-->
                <!--                    :options="statusOptions.mapDetailStatus[model.status]"-->
                <!--                    v-model:value="model.purchase_list[index].status"-->
                <!--                    @change="handleStatusSelectChange"-->
                <!--                  />-->
                <!--                </FormItemRest>-->
                <!--              </template>-->
              </template>
            </BasicTable>
          </FormItemRest>
        </template>
      </BasicForm>
    </ScrollContainer>
  </BasicDrawer>
</template>

<script lang="ts" setup name="AddDrawer">
import { ref, unref } from 'vue'
import { Form, InputNumber, DatePicker, Select, Button, Popover, Space } from 'ant-design-vue'
import { EditOutlined, MinusCircleOutlined } from '@ant-design/icons-vue'
import { DefaultOptionType } from 'ant-design-vue/es/vc-tree-select/TreeSelect'
import { isUndefined, isNull, cloneDeep } from 'lodash-es'
import { BasicTable, useTable, TableImg } from '/@/components/Table'
import { ScrollContainer } from '/@/components/Container'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
// import { getPurchaseDetail } from '/@/api/erp/purchaseOrder'
import { useMessage } from '/@/hooks/web/useMessage'

import { editUpdateInWarehouse } from '/@/api/erp/inWarehouse'
import { getPurchaseDetail } from '/@/api/erp/purchaseOrder'
import { getWarehouse } from '/@/api/baseData/warehouse'
// import { getStoreList } from '/@/api/commonUtils'
import { useMapStoreWithOut } from '/@/store/modules/commonMap'
import type { PropsType } from '../datas/types'
import { getDrawerTableColumns, getSchemasList, itemsKeys, searchWorkList } from '../datas/AddDrawer'

import { useRoute } from 'vue-router'
// import { useLoading } from '/@/components/Loading'

const route = useRoute()
const routeName = unref(route).name
const commonMap = useMapStoreWithOut()
const selectWarehouse = ref<number>()
const received_at = ref()
const FormItemRest = Form.ItemRest
const emit = defineEmits(['success', 'register'])

const { createMessage } = useMessage()

const propsData = ref<PropsType>({ type: '', isUpdate: false })

const prevSelectWork = ref<DefaultOptionType[]>([])

const [registerTable, { setTableData, deleteTableDataRecord, getSelectRowKeys }] = useTable({
  showIndexColumn: false,
  columns: getDrawerTableColumns('add', 0, routeName).filter((item) => !['status'].includes(item.dataIndex as string)),
  dataSource: [],
  pagination: false,
  canResize: false,
  rowKey: 'id',
  rowSelection: {}
})

const [registerDrawer, { changeLoading, changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  changeLoading(true)
  getWarehouseFn()
  propsData.value = data
  prevSelectWork.value = []
  try {
    await resetFields()
    await updateSchema(
      getSchemasList(
        unref(propsData).isUpdate,
        unref(propsData).type,
        routeName as string,
        { validateFields, handlePurchaseOrderChange },
        data?.page
      )
    )
    setTableData([])
    if (data.page && data.page === 'purchase') {
      setFieldsValue({
        work: [
          {
            label: data.record.strid,
            value: data.record.id,
            key: data.record.id,
            originLabel: data.record.strid,
            option: {
              doc_id: data.record.id
            }
          }
        ]
      })
    }
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
})

const [registerBasicinfoForm, { resetFields, updateSchema, validateFields, setFieldsValue, validate, getFieldsValue }] = useForm({
  schemas: getSchemasList(true, propsData.value.type, routeName as string),
  baseColProps: { span: 24 },
  showActionButtonGroup: false,
  colon: true,
  labelAlign: 'left',
  labelCol: { style: { width: '120px' } }
})

// const [openFullLoading, closeFullLoading] = useLoading({
//   tip: '加载中...'
// })

//多选的情况
async function handlePurchaseOrderChange(workIds: DefaultOptionType[], loading) {
  try {
    // openFullLoading()
    loading.value = true
    let tableData: Recordable[] = []
    // 判断是增加还是减少
    if (workIds.length > prevSelectWork.value.length) {
      // 增加了关联,判断增加了哪个，只请求新增的
      // mapPrevSelectWork: 获取上一次选中的work的关联采购订单条数
      const mapPrevSelectWork = prevSelectWork.value.map((item) => item.value)

      // pushTableData： 新增了的work
      const pushTableData: DefaultOptionType[] = workIds.filter((item) => !mapPrevSelectWork.includes(item.value))
      for (const workId of pushTableData) {
        searchWorkList.value.push(cloneDeep(workId))
        let { items } = await getPurchaseDetail({ doc_id: workId.option.doc_id })
        //过滤掉qty_wait_received小于1的商品
        items = items.filter((item) => item.qty_wait_received > 0)
        if (items) tableData = [...(getFieldsValue().purchase_list || []), ...items]
      }
    } else if (workIds.length < prevSelectWork.value.length) {
      // 减少了关联,获取表单数据进行filter
      const mapWorks = workIds.map((item) => item.value)
      searchWorkList.value = searchWorkList.value.filter((item) => mapWorks.includes(item.value))
      tableData = (getFieldsValue().purchase_list || []).filter((item: Recordable) => mapWorks.includes(item.doc_id))
    }

    await setFieldsValue({
      purchase_list: tableData.map((item) => ({
        ...item,
        qty_received: item.qty_received ? item.qty_received : 0,
        warehouse_id: undefined,
        received_at: '',
        status: 0,
        qty_total: item.qty_total ? item.qty_total : item.qty_wait_received
      }))
    })
    //设置表格数据
    setTableData(tableData)
    prevSelectWork.value = workIds
  } finally {
    loading.value = false
    // closeFullLoading()
  }
}

function stopPropagation(e) {
  e.stopPropagation()
}

async function handleOk() {
  await changeOkLoading(true)
  try {
    const valid = await validate()

    const { work, dept_id, waybill_num, erp_num, supplier_id, pkg_num } = valid
    for (const workId of work) {
      const option = searchWorkList.value.find((item) => item.value === workId.value)
      const filterPurchaseList = valid.purchase_list.filter((item) => item.doc_id === option.value)
      const params = {
        doc: {
          dept_id,
          waybill_num,
          status: filterPurchaseList.some((item: Recordable) => item.status === 0)
            ? 0
            : filterPurchaseList.some((item: Recordable) => item.status === 1)
            ? 1
            : 2,
          erp_num,
          supplier_id,
          pkg_num,
          work_id: propsData.value.page && propsData.value.page === 'purchase' ? propsData.value.record.work_id : option.option.work_id
        },
        items: [
          ...filterPurchaseList.map((item: Recordable) => {
            const itemsParams = {}
            for (const key of itemsKeys) {
              if (key === 'purchase_id') itemsParams[key] = item.id
              else if (item[key] !== '' && !isUndefined(item[key]) && !isNull(item[key])) itemsParams[key] = item[key]
            }
            itemsParams['qty_received'] = item.qty_total
            return itemsParams
          })
        ]
      }

      for (let item of params.items) {
        if (propsData.value.type === 'add') {
          delete item.id
          delete item.qty_wait_received
          delete item.total_cost
          delete item.qty_received_total
          delete item.qty_purchased_total
          delete item.qty_purchased
        }
      }
      console.log(params, 'params')

      await editUpdateInWarehouse(params)
    }
    emit('success')
    createMessage.success('success')
    await closeDrawer()
    setTimeout(() => {
      changeOkLoading(false)
    }, 2000)
  } catch (err) {
    changeOkLoading(false)
    throw new Error(`${err}`)
  }
}

function handleRemoveDetail(record: Recordable) {
  deleteTableDataRecord(record.id)
  const purchase_list = getFieldsValue().purchase_list
  const idx = purchase_list.findIndex((item) => +item.id === +record.id)
  purchase_list.splice(idx, 1)
  const residual = purchase_list.filter((item) => item.doc_id === record.doc_id)
  // 如果删除的这条数据是workid的最后一个数据，则删除关联采购订单这个id
  if (residual.length > 0) return
  const workList = getFieldsValue().work
  const newWorkList = workList.filter((item) => item.value !== record.doc_id)
  setFieldsValue({ work: newWorkList })
}

function handleBatchEdit() {
  const keys = getSelectRowKeys()
  const purchase_list = cloneDeep(getFieldsValue().purchase_list)
  for (const item of purchase_list) {
    if (keys.includes(item.id)) {
      item.warehouse_id = selectWarehouse.value
      item.received_at = received_at.value
    }
  }
  setFieldsValue({
    purchase_list
  })
}
const warehouseFnData = ref([])
async function getWarehouseFn() {
  const { items }: any = await getWarehouse({ pageSize: 100 })
  console.log(items, 'items')
  warehouseFnData.value = items.map((item) => ({
    label: item.name,
    value: item.id,
    disabled: item.is_disabled == 1
  }))
  return warehouseFnData.value
}
</script>
<style scoped lang="less">
:deep(.ant-col.ant-form-item-label.ant-form-item-label-left) {
  font-weight: 600;
}
</style>
