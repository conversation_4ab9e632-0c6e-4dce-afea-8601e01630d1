<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    title="编辑入库单"
    width="90%"
    @ok="handleOk"
    :show-footer="propsData.isUpdate"
    @close="handleClose"
  >
    <BasicForm @register="registerBasicinfoForm">
      <template #Purchase="{ model }">
        <FormItemRest>
          <BasicTable @register="registerTable">
            <template #headerCell="{ column }">
              <template v-if="column.dataIndex === 'action'">
                <div>{{ column.customTitle }}</div>
                <div>
                  <Tooltip title="将勾选数据全部设置确定入库，请确定已勾选数据">
                    <Button type="primary" size="small" @click="handleSetAllStatus">快速确定清点</Button>
                  </Tooltip>
                </div>
              </template>
              <template v-else-if="column.dataIndex === 'qty_received'">
                <div>{{ column.customTitle }}</div>
                <div v-if="propsData.type === 'edit'">
                  <Tooltip title="将勾选数据的采购数量设置为入库商品数量，请确定已勾选数据">
                    <Button type="primary" size="small" @click="handleSetAllCount">快速设置入库商品</Button>
                  </Tooltip>
                </div>
              </template>
              <template v-else>
                <div>{{ column.customTitle }}</div>
              </template>
            </template>
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'action'">
                <TableAction :actions="createActions({ record, index })" />
              </template>
              <template v-if="column.key === 'imgs'">
                <TableImg :imgList="record.imgs" :simpleShow="true" @click="stopPropagation" />
              </template>
              <template v-else-if="column.dataIndex === 'warehouse_id' && model.purchase_list[index]">
                <Select v-model:value="model.purchase_list[index].warehouse_id" :options="warehouseFnData" :disabled="true" />
              </template>
              <template v-else-if="column.dataIndex === 'received_at' && model.purchase_list[index]">
                <DatePicker
                  v-model:value="model.purchase_list[index].received_at"
                  :disabled="!propsData.isUpdate || model.purchase_list[index].status === 2"
                  valueFormat="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                />
              </template>

              <template v-else-if="column.dataIndex === 'pkg_received' && model.purchase_list[index]">
                <InputNumber v-model:value="model.purchase_list[index].pkg_received" :disabled="!propsData.isUpdate" />
              </template>
              <template v-else-if="column.dataIndex === 'qty_total' && model.purchase_list[index]">
                <FormItemRest>
                  <InputNumber
                    v-model:value="model.purchase_list[index].qty_total"
                    :disabled="['detail'].includes(propsData.type) || !(propsData.type === 'edit' && compStatus === 0)"
                    :max="record.qty_wait_received"
                    :min="0"
                    :precision="2"
                  />
                </FormItemRest>
                <!-- {{ model.purchase_list[index].qty_total }} -->
              </template>
              <template v-else-if="column.dataIndex === 'pkg_num' && model.purchase_list[index]">
                <InputNumber v-model:value="model.purchase_list[index].pkg_num" :disabled="!propsData.isUpdate" />
              </template>
              <template v-else-if="column.dataIndex === 'qty_received' && model.purchase_list[index]">
                <InputNumber
                  v-model:value="model.purchase_list[index].qty_received"
                  :disabled="!propsData.isUpdate || model.purchase_list[index].status === 2"
                  :max="model.purchase_list[index].qty_total"
                  :min="0"
                  @change="(val) => (record.qty_received = val)"
                  :precision="2"
                />
              </template>
              <template v-else-if="column.dataIndex === 'qty_defective' && model.purchase_list[index]">
                <InputNumber v-model:value="model.purchase_list[index].qty_defective" :disabled="!propsData.isUpdate" />
              </template>
              <!-- <template v-else-if="column.dataIndex === 'qty_stocking' && model.purchase_list[index]">
                <FormItemRest>
                  <InputNumber v-model:value="model.purchase_list[index].qty_stocking" :disabled="!propsData.isUpdate" />
                </FormItemRest>
              </template> -->
              <template v-else-if="column.dataIndex === 'status' && model.purchase_list[index]">
                {{ statusOptions.mapDetailStatusItem[model.purchase_list[index].status] }}
                <!--                            <Select-->
                <!--                              :disabled="!propsData.isUpdate || propsData.cardItem?.status === 0"-->
                <!--                              :options="statusOptions.mapDetailStatus[model.status]"-->
                <!--                              v-model:value="model.purchase_list[index].status"-->
                <!--                              @change="handleStatusSelectChange"-->
                <!--                            />-->
              </template>
            </template>
          </BasicTable>
        </FormItemRest>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<script lang="ts" setup name="EditDrawer">
import { ref, unref } from 'vue'
import { Form, InputNumber, DatePicker, Button, Tooltip, message, Select } from 'ant-design-vue'

import { BasicTable, useTable, TableImg, TableAction, ActionItem } from '/@/components/Table'
// import { ScrollContainer } from '/@/components/Container'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
// import { getPurchaseDetail } from '/@/api/erp/purchaseOrder'
// import ApiSelect from '/@/components/Form/src/components/ApiSelect.vue'
import { useMessage } from '/@/hooks/web/useMessage'
import { getInWarehouseDetail, editUpdateInWarehouse, setInWarehouseStatus } from '/@/api/erp/inWarehouse'
import { getWarehouse } from '/@/api/baseData/warehouse'
import type { PropsType } from '../datas/types'
import { getDrawerTableColumns, getSchemasList, compStatus, itemsKeys, statusOptions } from '../datas/AddDrawer'
import { isNull, isUndefined } from 'lodash-es'
import { useRoute } from 'vue-router'

const route = useRoute()
const routeName = unref(route).name
const FormItemRest = Form.ItemRest
const emit = defineEmits(['success', 'register'])

const { createMessage } = useMessage()

const propsData = ref<PropsType>({ type: '', isUpdate: false })

const [registerTable, { setTableData, setColumns, setProps, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
  showIndexColumn: false,
  columns: getDrawerTableColumns('edit'),
  dataSource: [],
  pagination: false,
  canResize: false,
  rowKey: 'id',
  rowSelection: {
    fixed: 'left'
  },
  actionColumn: {
    width: 200,
    title: '确定清点',
    dataIndex: 'action',
    fixed: 'right'
  }
})

function createActions({ record, index }: { record: Recordable; index: number }): ActionItem[] {
  return [
    {
      // icon: 'ant-design:check-outlined',
      label: '全部确定',
      disabled: record.status === 2 || record.qty_received === 0,
      onClick: handleChangeRowStatus.bind(null, { status: 2, index })
    },
    {
      label: '全部取消',
      disabled: record.status === 1 || propsData.value.cardItem?.item[index]?.status === 2,
      onClick: handleChangeRowStatus.bind(null, { status: 1, index })
      // icon: 'ant-design:check-outlined'
    }
  ] as ActionItem[]
}

const [registerDrawer, { changeLoading, changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  changeLoading(true)
  changeOkLoading(false)

  getWarehouseFn()
  propsData.value = data
  try {
    await resetFields()
    await updateSchema(
      getSchemasList(unref(propsData).isUpdate, unref(propsData).type, routeName, { validateFields, handlePurchaseOrderChange })
    )
    setColumns(
      data.cardItem.status === 0
        ? getDrawerTableColumns('edit', data.cardItem.status, routeName).filter((item) => item.dataIndex !== 'status')
        : getDrawerTableColumns('edit', data.cardItem.status, routeName)
    )
    setTableData([])
    //初始化表单
    if (propsData.value.cardItem) {
      await setFieldsValue({ ...propsData.value.cardItem, purchase_list: [] })
      // setProps(propsData.value.cardItem.status !== 2 ? { actionColumn: void 0 } : {
      //   width: 200,
      //   title: '确定清点',
      //   dataIndex: 'action',
      //   fixed: 'right'
      // })
      setProps({
        actionColumn:
          propsData.value.cardItem.status !== 2 || propsData.value.type === 'detail'
            ? void 0
            : {
                width: 200,
                title: '确定清点',
                dataIndex: 'action',
                fixed: 'right'
              }
      })
    }
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
})

const [registerBasicinfoForm, { resetFields, updateSchema, validateFields, setFieldsValue, validate, getFieldsValue }] = useForm({
  schemas: getSchemasList(true, propsData.value.type, routeName),
  baseColProps: { span: 24 },
  showActionButtonGroup: false,
  colon: true,
  labelAlign: 'left',
  labelCol: { style: { width: '100px' } }
})
function handleChangeRowStatus({ index, status }: { status: number; index: number }) {
  let { purchase_list } = getFieldsValue()
  // 全部清点
  console.log(index)
  const filterArr = propsData.value.cardItem?.item?.filter((item) => item.status === 2).map((item) => item.id) || []
  purchase_list = purchase_list.map((item) => (item.status === 2 && filterArr.includes(item.id) ? item : { ...item, status }))
  // 单个清点(老板说不要单个清点，要的话注释上面打开下面那行)
  // purchase_list[index].status = status
  setFieldsValue({ purchase_list }).then(() => {
    handleStatusSelectChange()
  })
  setTableData(purchase_list)
}

function handleSetAllStatus() {
  const { purchase_list } = getFieldsValue()
  const keys = getSelectRowKeys()
  for (const item of purchase_list) {
    if (keys.includes(item.id)) {
      item.status = 2
    }
  }
  setFieldsValue({ purchase_list }).then(() => {
    handleStatusSelectChange()
  })
  setTableData(purchase_list)
}

function handleSetAllCount() {
  const { purchase_list } = getFieldsValue()
  const keys = getSelectRowKeys()
  console.log(keys)

  if (keys.length == 0) {
    message.error('请先选择采购商品')
  } else {
    for (const item of purchase_list) {
      if (keys.includes(item.id) && item.status !== 2) {
        item.qty_received = item.qty_total
      }
    }
    setFieldsValue({ purchase_list }).then(() => {
      handleStatusSelectChange()
    })
    setTableData(purchase_list)
  }
}
async function handlePurchaseOrderChange(id: number) {
  console.log(id)
  const { items } = await getInWarehouseDetail({ doc_in_id: propsData.value.cardItem!.id })
  console.log(items, 'items123')
  setFieldsValue({
    purchase_list: items.map((item) => ({
      ...item,
      pkg_received: item.pkg_received ? item.pkg_received : 0,
      qty_total: item.qty_total ? item.qty_total : 0,
      pkg_num: item.pkg_num ? item.pkg_num : 0,
      qty_received: item.qty_received ? item.qty_received : 0,
      qty_defective: item.qty_defective ? item.qty_defective : 0,
      // qty_stocking: item.qty_stocking ? item.qty_stocking : 0,
      warehouse_id: item.warehouse_id ? item.warehouse_id : undefined,
      received_at: item.received_at ? item.received_at : '',
      qty_request: item.qty_request ? item.qty_request : 0,
      qty_received_total: item.qty_received_total ? item.qty_received_total : 0,
      qty_purchased: item.qty_purchased ? item.qty_purchased : 0,
      qty_purchased_total: item.qty_purchased_total ? item.qty_purchased_total : 0
    }))
  }).then(() => {
    handleStatusSelectChange()
  })
  //设置表格数据
  setTableData(items)
}

function stopPropagation(e: Event) {
  e.stopPropagation()
}

async function handleOk() {
  changeOkLoading(true)
  try {
    const valid = await validate()
    console.log(valid, 'valid')
    const { dept_id, waybill_num, erp_num, pkg_num } = valid
    const [editWorkId] = [...new Set(valid.purchase_list.map((item) => item.purchase_work_id))]
    const params = {
      doc: {
        id: propsData.value.cardItem!.id,
        work_id: editWorkId, // 明细的采购单work_id，只取去重后的首个
        dept_id,
        waybill_num,
        // status, // 删除入库单的状态，入库单的状态由后端进行逻辑判断
        erp_num,
        // supplier_id,
        pkg_num
      },
      items: [
        ...valid.purchase_list.map((item: Recordable) => {
          const itemsParams = {}
          for (const key of itemsKeys) {
            if (key === 'purchase_id') itemsParams[key] = propsData.value.type === 'add' ? item.id : item.purchase_id
            else if (item[key] !== '' && !isUndefined(item[key]) && !isNull(item[key])) itemsParams[key] = item[key]
          }
          return itemsParams
        })
      ]
    }

    const statusParams = valid.purchase_list.map((item) => ({ id: item.id, status: item.status }))

    for (let item of params.items) {
      delete item.type
      // delete item.request_id
      // delete item.purchase_id
      delete item.doc_id
      delete item.doc_in_id
      delete item.origin_stocking_id
      delete item.src
      delete item.batch_code
      delete item.uniqid
      delete item.parent_uniqid
      delete item.unit_price
      delete item.qty_request
      delete item.qty_request_left
      delete item.total_amount
      delete item.updated_at
      delete item.created_at
      delete item.pkg_total
      // 产品状态和产品明细不在同一个接口
      delete item.status
      delete item.qty_purchased_res
      delete item.qty_purchased
      delete item.qty_purchased_total
    }

    console.log(params, 'params')
    const result = await editUpdateInWarehouse(params)
    propsData.value.cardItem?.status !== 0
      ? await setInWarehouseStatus({ doc_id: propsData.value.cardItem!.id as number, status: compStatus.value, items: statusParams })
      : ''
    emit('success')
    createMessage.success(result.msg)
    clearSelectedRowKeys()
    closeDrawer()
    // changeOkLoading(false)
  } catch (err) {
    changeOkLoading(false)
    throw new Error(`${err}`)
  }
}

function handleClose() {
  clearSelectedRowKeys()
}

function handleStatusSelectChange() {
  const { purchase_list } = getFieldsValue()
  compStatus.value = propsData.value.cardItem?.status === 0 ? 0 : purchase_list.every((item: Recordable) => item.status === 2) ? 3 : 2
}

const warehouseFnData = ref([])
async function getWarehouseFn() {
  const { items }: any = await getWarehouse({ pageSize: 100 })
  console.log(items, 'items')
  warehouseFnData.value = items.map((item) => ({
    label: item.name,
    value: item.id,
    disabled: item.is_disabled == 1
  }))
  console.log(warehouseFnData.value, 'warehouseFnData')

  return warehouseFnData.value
}
</script>
<style scoped lang="less">
:deep(.ant-col.ant-form-item-label.ant-form-item-label-left) {
  font-weight: 600;
}
</style>
