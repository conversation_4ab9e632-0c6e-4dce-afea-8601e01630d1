<template>
  <BasicModal @register="registerModal" v-bind="$attrs" title="已收包裹数" width="30%" :height="20" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script setup lang="ts">
import { ref, unref } from 'vue'
import { message } from 'ant-design-vue'
import { BasicModal, useModalInner } from '/@/components/Modal'

import { BasicForm, useForm } from '/@/components/Form'
import { schemas } from '../datas/EditNumModal'
import { editReceivedNum, setInWarehouseStatus, setPkgNum } from '/@/api/erp/inWarehouse'

const emits = defineEmits(['success', 'register'])
const propData = ref()

const [registerForm, { resetFields, validate, setFieldsValue, resetSchema }] = useForm({
  baseColProps: { span: 24 },
  showActionButtonGroup: false,
  labelCol: { span: 4 }
  // schemas
})

const [registerModal, { closeModal, changeOkLoading, changeLoading, setModalProps }] = useModalInner(async (data: Recordable) => {
  try {
    setModalProps({ title: data.type === 'received' ? '填写已收包裹' : '修改待收包裹' })
    changeLoading(true)
    await resetFields()
    propData.value = data
    const { item } = data

    resetSchema(schemas(data.status))
    setFieldsValue({ pkg_received: item.pkg_received, pkg_num: item.pkg_num })
  } catch (error) {
    message.error('未入库,请先入库了再点击')
    closeModal()
    throw new Error(`${error}`)
  } finally {
    changeLoading(false)
  }
})

async function handleSubmit() {
  try {
    await changeOkLoading(true)
    const { pkg_received, pkg_num } = await validate()
    const { id } = unref(propData).item
    const params = { pkg_received, doc_id: id, pkg_num }
    const { news } =
      propData.value.type === 'received'
        ? await editReceivedNum(params as { doc_id: number; pkg_received: number })
        : propData.value.status == 1
        ? await setPkgNum(params.doc_id, params.pkg_num)
        : await editReceivedNum(params as { doc_id: number; pkg_received: number })
    if (news === 'success' && propData.value.type === 'received')
      await setInWarehouseStatus({ doc_id: id, status: 2, items: propData.value.item.item.map((v) => ({ id: v.id, status: 1 })) })
    await closeModal()
    changeOkLoading(false)
    emits('success')
  } catch (err) {
    changeOkLoading(false)
    throw new Error(`${err}`)
  }
}
</script>
