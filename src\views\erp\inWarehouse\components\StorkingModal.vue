<template>
  <BasicModal v-bind="$attrs" title="查看实时库存" width="50%" :height="500" :footer="null" @register="register">
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>

<script lang="ts" setup name="StockingModel">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicTable, useTable } from '/@/components/Table'
import { columns } from '../datas/StorkingModal'
import { getBuybackList } from '/@/api/wms/salesOutbound'

const [register] = useModalInner(async (data) => {
  console.log(data)
  const { items } = await getBuybackList({ strid: data.item.strid })
  setTableData(items)
})

const [registerTable, { setTableData }] = useTable({
  showIndexColumn: false,
  columns,
  dataSource: [],
  pagination: false,
  bordered: true
})
</script>

<style scoped lang="less"></style>
