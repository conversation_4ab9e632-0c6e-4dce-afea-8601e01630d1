import type { FormSchema, BasicColumn } from '/@/components/Table'

export const schemas = (type: number): FormSchema[] => {
  return [
    {
      field: 'pkg_received',
      label: '数量',
      component: 'InputNumber',
      helpMessage: '包裹数量只能为0',
      componentProps: {
        precision: 0,
        min: 0
      },
      required: true,
      ifShow: type === 2 // 0 已收包裹 1 待收包裹
    },
    {
      field: 'pkg_num',
      label: '数量',
      component: 'InputNumber',
      helpMessage: '包裹数量只能为0',
      componentProps: {
        precision: 0,
        min: 0
      },
      required: true,
      ifShow: type === 1 // 0 已收包裹 1 待收包裹
    }
  ]
}

export const columns: BasicColumn[] = [
  {
    title: '订单号',
    dataIndex: 'no',
    width: 50
  },
  {
    title: '要收到的包裹数',
    dataIndex: 'no',
    width: 50
  },
  {
    title: '已接收包裹数',
    dataIndex: 'no',
    width: 50
  }
]
