<template>
  <div class="drawer">
    <BasicDrawer
      @register="register"
      @ok="handleSubmit"
      @cancel="closeadd"
      okText="提交"
      width="90%"
      :can-fullscreen="false"
      show-footer
      destroyOnClose
    >
      <div class="mt-2">
        <Card title="盘点信息" :bordered="false">
          <BasicForm @register="registerForm" />
        </Card>
        <Card title="盘点商品" class="mt-2" :bordered="false">
          <template #extra v-if="isUpdate">
            <Button type="primary" @click="handleAdd">添加商品</Button>
          </template>
          <BasicTable @register="registertable" :data-source="tabledata">
            <template #bodyCell="{ text, column, record }">
              <template v-if="column && column.dataIndex === 'action'">
                <TableAction
                  :actions="[
                    {
                      icon: 'ant-design:form-outlined',
                      // color: 'error',
                      label: '编辑',
                      onClick: handleEditTable.bind(null, record)
                    },
                    {
                      icon: 'ant-design:delete-outlined',
                      color: 'error',
                      label: '删除',
                      popConfirm: {
                        title: '是否确认删除',
                        placement: 'left',
                        confirm: handleDelete.bind(null, record)
                      }
                    }
                  ]"
                />
              </template>
              <template v-if="column.dataIndex === 'work_id'">
                {{ compAllSalesWorkList[text]?.source_uniqid }}
              </template>
              <template v-if="column && column.dataIndex === 'imgs'">
                <TableImg :size="60" :simpleShow="true" :imgList="text" />
              </template>
              <template v-if="column && column.dataIndex === 'warehouse_id'">
                <div v-for="(item, index) in statusdata" :key="index">
                  <div v-if="item.id == record.warehouse_id">
                    {{ item.name }}
                  </div>
                </div>
              </template>
            </template>
          </BasicTable>
        </Card>
      </div>
    </BasicDrawer>
    <AddModel @register="registermodal2" ref="addmodels" @add-product="hanleAddProduct" @edit-product="handleEditProduct" />
  </div>
</template>
<script lang="ts" setup>
import { ref, unref } from 'vue'
import { message, Card, Button } from 'ant-design-vue'
import { isNull, isUndefined } from 'lodash-es'
import { useModal } from '/@/components/Modal'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicTable, useTable, TableAction, TableImg } from '/@/components/Table'
import { editInventory } from '/@/api/erp/inventory'
import { getWarehouse } from '/@/api/baseData/warehouse'
import { schemas, columns, schemas2, docKeys, compAllSalesWorkList, allSalesWorkList } from '../datas/Modal'
import AddModel from './addmodel.vue'
import { IFormDataProduct } from '../datas/types'
import defaultUser from '/@/utils/erp/defaultUser'

//分类
const tabledata = ref([])
const type = ref()
const isUpdate = ref()
const id = ref()
const msg = ref()
//modal
const [register, { closeDrawer, setDrawerProps, changeOkLoading, changeLoading }] = useDrawerInner(async (data) => {
  try {
    await changeLoading(true)
    type.value = data.type
    isUpdate.value = data.isUpdate
    console.log(data)
    if (data.type === 'detail') {
      data.record.info.forEach((item: any) => {
        allSalesWorkList.value.push({ id: item.work_id, source_uniqid: item.source_uniqid })
      })
    }

    setDrawerProps({ showOkBtn: isUpdate.value })
    if (data.type === 'edit') {
      id.value = data.record.id
    }
    if (data.isUpdate == true) {
      resetSchema(schemas)
    } else {
      resetSchema(schemas2)
    }
    if (data.type === 'add') {
      setFieldsValue([])
      tabledata.value = []
      setFieldsValue({ applicant: defaultUser!.userId, inCharge: defaultUser!.userId, processor: defaultUser!.userId })
    } else {
      console.log(data.record, 'data.record')
      setFieldsValue(data.record)
      if (data.record.processor) {
        setFieldsValue({ processor: Number(data.record.processor) })
      }
      tabledata.value = data.record.info.map((item: Recordable) => ({ ...item }))
      console.log(tabledata.value, 'tabledata.value')
    }
    worklist()
  } catch (e) {
    console.error(e)
  } finally {
    await changeLoading(false)
  }
})

//商品添加model
const [registermodal2, { openModal }] = useModal()
//From
const [registerForm, { validate, resetFields, setFieldsValue, resetSchema }] = useForm({
  schemas,
  showActionButtonGroup: false
})

//Table
const [registertable, { getDataSource, insertTableDataRecord, updateTableDataRecord }] = useTable({
  columns,
  showTableSetting: true,
  tableSetting: { fullScreen: true },
  rowKey: 'id',
  actionColumn: {
    width: 160,
    title: '操作',
    dataIndex: 'action',
    ifShow: isUpdate as unknown as boolean
  }
})
async function handleAdd() {
  const data = await getDataSource()
  const params = {
    work_id: data[0]?.work_id ? data[0]?.work_id : undefined,
    good_work_id: data[0]?.good_work_id ? data[0]?.good_work_id : undefined
  }
  openModal(true, { isUpdate: false, params })
}
async function handleDelete(record: Recordable) {
  // deleteTableDataRecord(record.id)
  const deletindex = ref()
  tabledata.value.forEach((item: any, index) => {
    if (item.name == record.name) {
      deletindex.value = index
    }
  })
  tabledata.value.splice(deletindex.value, 1)
}

// 格式化提交的数据
// function formatSubmit() {
//   // getDataSource()有我们不需要的属性,所以需要清除
//   console.log(getDataSource(), 'getDataSource()')
//   const dataSource = getDataSource().map((item) => {
//     let temporary = {}
//     for (let colName of getColumns()) {
//       if (colName.key !== 'action') {
//         temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
//       }
//     }

//     return temporary
//   })
//   return dataSource
// }

//退出
const emit = defineEmits(['reload', 'register'])

function closeadd() {
  tabledata.value = []
  resetFields()
  closeDrawer()
}

const addmodels = ref<InstanceType<typeof AddModel>>()

async function hanleAddProduct(formData: IFormDataProduct) {
  console.log(formData)

  insertTableDataRecord(formData)
}

function handleEditProduct(formData) {
  updateTableDataRecord(formData.id, formData)
}

//单号
const statusdata = ref()
async function worklist() {
  const a: any = await getWarehouse()
  statusdata.value = unref(a.items)
}

async function handleEditTable(record: Recordable) {
  const data = await getDataSource()
  const params = {
    work_id: data[0]?.work_id ? data[0]?.work_id : undefined,
    good_work_id: data[0]?.good_work_id ? data[0]?.good_work_id : undefined
  }
  openModal(true, { isUpdate: true, record, params })
}

async function handleSubmit() {
  changeOkLoading(true)
  try {
    const adddata = await validate()

    // const data = formatSubmit()
    const data = getDataSource()
    data.forEach((item: any) => {
      delete item.undefined
    })

    for (const key of docKeys) {
      if (isUndefined(adddata[key]) || isNull(adddata[key])) delete adddata[key]
    }
    Reflect.set(adddata, 'info', data)
    if (type.value == 'edit') {
      Reflect.set(adddata, 'id', id.value)
    }
    if (type.value == 'detail') {
      resetFields()
      closeDrawer()
      tabledata.value = []
    } else {
      msg.value = await editInventory(adddata)
      console.log(msg.value)
      if (msg.value.msg == 'success') {
        message.success('添加成功')
        resetFields()
        await closeDrawer()
        tabledata.value = []
        emit('reload', 1)
      }
    }
    changeOkLoading(false)
  } catch (err) {
    changeOkLoading(false)
    throw new Error(`${err}`)
  }
}
</script>
