<template>
  <BasicModal @register="register" :title="getTitle" :showOkBtn="addsubmitshow" @ok="handleSubmit" width="700px">
    <BasicForm @register="registerForm" @field-value-change="imgFileChange">
      <template #Imgs>
        <Upload v-model:file-list="imgFileList" action="/api/oss/putImg2Stocking" list-type="picture-card" :custom-request="handleRequest">
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
import { nextTick, ref, watch, computed, unref } from 'vue'
import { Upload, message } from 'ant-design-vue'
import type { UploadFile } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'

import { commonImgUpload } from '/@/api/commonUtils/upload'
import { schemas1 } from '../datas/Modal'
import { getWorkList } from '/@/api/commonUtils'
import { salesWorkList } from '../datas/Modal'

//销售订单work_id
const init_work_id = ref()
//商品work_id
const init_good_work_id = ref()
//确认按钮
const addsubmitshow = ref(true)

interface PropsType {
  isUpdate: boolean
  record?: Recordable
}
const propsData = ref<PropsType>({ isUpdate: false })

const getTitle = computed(() => (!unref(propsData).isUpdate ? '新增' : '编辑'))

const imgFileList = ref<UploadFile[]>([])
// const curAction = ref<string>('')

const [registerForm, { setFieldsValue, validate, resetFields, validateFields, getFieldsValue }] = useForm({
  showActionButtonGroup: false,
  baseColProps: { span: 12 },
  schemas: schemas1
})

//modal
const [register, { closeModal, changeLoading, changeOkLoading }] = useModalInner(async (data) => {
  changeLoading(true)
  resetFields()
  init_work_id.value = data.params.work_id ? data.params.work_id : null
  init_good_work_id.value = data.params.good_work_id ? data.params.good_work_id : null
  try {
    propsData.value = data

    if (data.isUpdate) {
      const { record } = data
      await setFieldsValue(record)
      // console.log(record, 'record')
      imgFileList.value = record.imgs.length > 0 ? record.imgs.map((item) => ({ url: item, name: item, uid: item })) : []
    } else {
      imgFileList.value = []
    }
    if (data?.record?.work_id) {
      const { items } = await getWorkList({ id: data.record.work_id, type: 3, status: [1, 2, 3, 4, 5], pageSize: 9999 })
      salesWorkList.value = items
    }
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
})

watch(
  () => imgFileList.value,
  async (val) => {
    await setFieldsValue({ imgs: val })
  }
)

const emit = defineEmits(['addProduct', 'editProduct', 'register'])

//图片上传
async function handleRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonImgUpload(file, 'product')

  onSuccess!(result.path)
  imgFileList.value = imgFileList.value!.map((item, idx) => {
    const url = item.url || item.response
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: url ? url.match(/[^\/]+$/)[0] : idx
    }
  })
  await setFieldsValue({ imgs: imgFileList.value.map((item) => item.url) })
  await nextTick(async () => {
    try {
      await validateFields(['imgs'])
    } catch (e) {
      throw new Error(`${e}`)
    }
  })
}

async function handleSubmit() {
  try {
    await changeOkLoading(true)
    const fromdata = await validate()
    const params = {
      ...fromdata,
      id: unref(propsData).isUpdate ? propsData.value.record!.id : undefined,
      imgs:
        fromdata.imgs === null || fromdata.imgs.length === 0
          ? []
          : fromdata.imgs.map((item: UploadFile) => {
              return item!.url
            })
    }

    emit(!unref(propsData).isUpdate ? 'addProduct' : 'editProduct', params)

    await closeModal()
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  } finally {
    changeOkLoading(false)
  }
}

//图片写入imgfileList
async function imgFileChange(e, val) {
  console.log(e)

  if (e == 'work_id') {
    if (val !== init_work_id.value && init_work_id.value) {
      console.log('123')
      message.error('请选择同一销售订单')
      addsubmitshow.value = false
      return
    } else {
      addsubmitshow.value = true
    }
  }
  if (e == 'request_id') {
    const data = await getFieldsValue()
    console.log(data)
    imgFileList.value = data.imgs ? data.imgs.map((item) => ({ url: item, name: item, uid: item })) : []
  }
}
</script>
