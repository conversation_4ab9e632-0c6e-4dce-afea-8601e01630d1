import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { getStaffList } from '/@/api/baseData/staff'
// import { getItemStocking } from '/@/api/erp/inventory'
import { getWarehouse } from '/@/api/baseData/warehouse'
import { getItemRequest } from '/@/api/commonUtils'
// import { useMapStoreWithOut } from '/@/store/modules/commonMap'
import { ItemRequestResponse } from '/@/api/commonUtils/modle/types'
import type { Rule } from 'ant-design-vue/lib/form'
import { getWorkList } from '/@/api/commonUtils'
import { debounce } from 'lodash-es'
import { computed, ref } from 'vue'
// const mapStore = useMapStoreWithOut()
export const schemas: FormSchema[] = [
  {
    field: 'applicant',
    required: true,
    component: 'ApiSelect',
    label: '申请人',
    colProps: {
      span: 8
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: () => {
      return {
        api: getStaffList,
        resultField: 'items',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'id',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          disabled: true
        }
      }
    },
    labelWidth: 100
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'ApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: true
      }
    },
    colProps: {
      span: 8
    },
    labelWidth: 100,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'processor',
    label: '处理人',
    component: 'ApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: true
      }
    },
    colProps: {
      span: 8
    },
    labelWidth: 100,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  // {
  //   field: 'processor',
  //   component: 'ApiSelect',
  //   label: '员工',
  //   componentProps: {
  //     api: getStaffList,
  //     resultField: 'items',
  //     selectProps: {
  //       fieldNames: { key: 'key', value: 'id', label: 'name' },
  //       showSearch: true,
  //       placeholder: '请选择',
  //       optionFilterProp: 'name'
  //     }
  //   },
  //   colProps: {
  //     span: 8
  //   },
  //   labelWidth: 100,
  //   itemProps: {
  //     validateTrigger: 'blur'
  //   }
  // },
  // {
  //   field: 'number',
  //   component: 'ApiSelect',
  //   label: '关联销售订单号',
  //   colProps: {
  //     span: 8
  //   },
  //   componentProps: {
  //     resultField: 'items',
  //     api: getWorkList,
  //     selectProps: {
  //       allowClear: true,
  //       fieldNames: { key: 'id', value: 'id', label: 'strid' },
  //       showSearch: true,
  //       placeholder: '请选择',
  //       optionFilterProp: 'strid'
  //     },
  //     params: {
  //       type: 3,
  //       pageSize: 999999
  //     }
  //   },
  //   labelWidth: 100
  // },
  // {
  //   field: 'status',
  //   component: 'Select',
  //   label: '状态',
  //   colProps: {
  //     span: 8
  //   },
  //   componentProps: { options: mapStatus },
  //   labelWidth: 100
  // },
  {
    field: 'desc',
    component: 'InputTextArea',
    label: '描述',
    colProps: {
      span: 8
    },
    componentProps: {},
    // required: true,
    labelWidth: 100
  }
]
export const schemas2: FormSchema[] = [
  {
    field: 'applicant',
    required: true,
    component: 'ApiSelect',
    label: '申请人',
    colProps: {
      span: 8
    },
    componentProps: () => {
      return {
        api: getStaffList,
        resultField: 'items',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'id',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          disabled: true
        }
      }
    },
    labelWidth: 100,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'ApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: true
      }
    },
    colProps: {
      span: 8
    },
    labelWidth: 100,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'processor',
    component: 'ApiSelect',
    label: '员工',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: true
      }
    },
    colProps: {
      span: 8
    },
    labelWidth: 100,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  // {
  //   field: 'number',
  //   component: 'Input',
  //   label: '关联订单号',
  //   colProps: {
  //     span: 8
  //   },
  //   componentProps: {
  //     disabled: true
  //   },
  //   labelWidth: 100
  // },
  // {
  //   field: 'status',
  //   component: 'Select',
  //   label: '状态',
  //   colProps: {
  //     span: 8
  //   },
  //   componentProps: { options: mapStatus, disabled: true },
  //   labelWidth: 100
  // },
  {
    field: 'desc',
    component: 'InputTextArea',
    label: '描述',
    colProps: {
      span: 8
    },
    componentProps: { disabled: true },
    labelWidth: 100
  }
]
export const columns: BasicColumn[] = [
  // {
  //   title: '所属订单Id',
  //   dataIndex: 'work_id',
  //   width: 100
  // },
  {
    title: '商品ID',
    dataIndex: 'request_id',
    width: 150
  },
  {
    title: '销售任务单号',
    dataIndex: 'work_id',
    width: 100
  },
  {
    title: '名称',
    dataIndex: 'name',
    width: 100
  },
  // {
  //   title: 'puid',
  //   dataIndex: 'puid',
  //   width: 100
  // },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 100
  },
  {
    title: '单价',
    dataIndex: 'unit_price',
    width: 100
  },
  {
    title: '图片',
    dataIndex: 'imgs',
    width: 100
  },
  {
    title: '仓库',
    dataIndex: 'warehouse_id',
    width: 100
  },
  {
    title: '需求数量',
    dataIndex: 'qty_request_left',
    width: 100
  },
  {
    title: '盘点数量',
    dataIndex: 'qty_stocking',
    width: 100
  },
  // {
  //   title: '需收数量',
  //   dataIndex: 'qty_total',
  //   width: 100
  // },
  // {
  //   title: '实际入库数',
  //   dataIndex: 'qty_received',
  //   width: 100
  // },
  //
  // {
  //   title: '报废数量',
  //   dataIndex: 'qty_defective',
  //   width: 100
  // },
  // {
  //   title: '损毁数量',
  //   dataIndex: 'qty_damage',
  //   width: 100
  // },
  {
    title: '商品描述',
    dataIndex: 'desc',
    width: 100
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 100
  }
]

export const allSalesWorkList = ref<any[]>([])
export const salesWorkList = ref<any[]>([])
export const compAllSalesWorkList = computed(() => {
  const mapList = {}
  for (const sales of allSalesWorkList.value) {
    mapList[sales.id] = sales
  }
  return mapList
})
export const schemas1: FormSchema[] = [
  {
    field: 'warehouse_id',
    label: '仓库',
    component: 'ApiSelect',
    componentProps: {
      api: getWarehouse,
      params: { is_disabled: 0 },
      resultField: 'items',
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    // slot: 'Warehouse',
    colProps: {
      span: 24
    },
    labelWidth: 130,
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'work_id',
    label: '关联销售订单',
    component: 'Select',
    componentProps: ({ formModel }) => {
      return {
        // api: getWorkList,
        // params: { pageSize: 999999, type: 3, status: [1,2,3,4,5] },
        // optionFilterProp: 'source_uniqid',
        // resultField: 'items',
        // selectProps: {
        //   fieldNames: { key: 'id', value: 'id', label: 'source_uniqid' },
        //   showSearch: true,
        //   placeholder: '请选择',
        //   optionFilterProp: 'source_uniqid'
        // },
        options: salesWorkList.value,
        optionFilterProp: 'source_uniqid',
        filterOption: false,
        placeholder: '先输入销售订单进行搜索',
        fieldNames: { key: 'id', value: 'id', label: 'source_uniqid' },
        showSearch: true,
        onSearch: debounce(async (val) => {
          if (!val) return
          const { items } = await getWorkList({ item_left: 1, source_uniqid: val, status: [2, 3, 4, 5], pageSize: 100, order: 1 })
          salesWorkList.value = items
          allSalesWorkList.value.push(...salesWorkList.value, ...items)
        }, 200),
        onChange: () => {
          for (const key of Object.keys(formModel)) {
            if (key !== 'work_id' && key !== 'warehouse_id') formModel[key] = ''
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    required: true,
    colProps: {
      span: 24
    },
    labelWidth: 130
  },
  {
    field: 'request_id',
    label: '销售订单所属商品',
    component: 'PagingApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: (params) => {
          if (!formModel.work_id) return
          return getItemRequest({ work_id: formModel.work_id, ...params })
        },
        // params: { work_id: formModel.work_id },
        searchMode: true,
        alwaysLoad: true,
        searchParamField: 'name',
        resultField: 'items',
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        },
        onChange: (_, opt: ItemRequestResponse) => {
          if (opt) {
            Object.assign(formModel, {
              qty_stocking: opt.qty_request_left,
              unit_price: opt.unit_price,
              name: opt.name,
              unit: opt.unit,
              imgs: opt.imgs,
              qty_request_left: opt.qty_request_left,
              work_id: opt.work_id
            })
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    required: true,
    colProps: {
      span: 24
    },
    labelWidth: 130
  },
  {
    field: 'qty_request_left',
    label: '剩余需求数量',
    component: 'InputNumber',
    componentProps: {
      disabled: true
    },
    colProps: {
      span: 24
    },
    labelWidth: 130
  },
  {
    field: 'name',
    label: '商品名称',
    component: 'Input',
    // componentProps: {
    //   api: getItemStocking,
    //   resultField: 'items',
    //   params: {
    //     type: 2
    //   },
    //   searchParamField: 'name',
    //   searchMode: true,
    //   selectProps: {
    //     placeholder: '请选择',
    //     showSearch: true,
    //     fieldNames: { value: 'name', label: 'name' }
    //   },
    //   onChange: (val) => {
    //     handleFn(val)
    //   }
    // },
    required: true,
    colProps: {
      span: 24
    },
    labelWidth: 130
  },
  {
    field: 'unit',
    label: '单位',
    component: 'Input',
    colProps: {
      span: 24
    },
    labelWidth: 130
    // required: true
  },
  {
    field: 'unit_price',
    label: '单价',
    component: 'InputNumber',
    colProps: {
      span: 24
    },
    componentProps: {
      // addonAfter: 'CNY',
      min: 0,
      precision: 2
    },
    labelWidth: 130
    // required: true
  },

  // {
  //   field: 'qty_total',
  //   label: '需收数量',
  //   component: 'InputNumber',
  //   colProps: {
  //     span: 24
  //   },
  //   labelWidth: 130,
  //   required: true,
  //   componentProps: {
  //     min: 0
  //   }
  // },
  // {
  //   field: 'qty_received',
  //   component: 'InputNumber',
  //   label: '实际入库数',
  //   colProps: {
  //     span: 24
  //   },
  //   labelWidth: 130,
  //   required: true,
  //   componentProps: {
  //     min: 0
  //   }
  // },
  // {
  //   field: 'qty_defective',
  //   component: 'InputNumber',
  //   label: '报废数量',
  //   colProps: {
  //     span: 24
  //   },
  //   labelWidth: 130,
  //   required: true,
  //   componentProps: (record) => {
  //     return { min: 0, max: record.formModel.qty_stocking }
  //   }
  // },
  {
    field: 'qty_stocking',
    component: 'InputNumber',
    label: '盘点数量',
    colProps: {
      span: 24
    },
    labelWidth: 130,
    // required: true,
    componentProps: ({ formModel }) => {
      return {
        min: 0,
        max: formModel?.qty_request_left ?? Infinity,
        precision: 2
      }
    },
    rules: [
      {
        trigger: 'change',
        validator: (_rule: Rule, value: number) => (value > 0 ? Promise.resolve() : Promise.reject('现存数量必须大于0'))
      }
    ]
  },
  // {
  //   field: 'qty_damage',
  //   component: 'InputNumber',
  //   label: '损毁数量',
  //   colProps: {
  //     span: 24
  //   },
  //   labelWidth: 130,
  //   required: true,
  //   componentProps: (record) => {
  //     return { min: 0, max: record.formModel.qty_stocking }
  //   }
  // },
  {
    field: 'desc',
    component: 'InputTextArea',
    label: '商品描述',
    colProps: {
      span: 24
    },
    labelWidth: 130
    // required: true
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    colProps: {
      span: 24
    },
    labelWidth: 130
    // required: true
  },
  {
    field: 'imgs',
    component: 'Upload',
    label: '图片',
    colProps: {
      span: 24
    },
    labelWidth: 130,
    slot: 'Imgs'
  }
]

export const docKeys: string[] = ['inCharge', 'number', 'processor', 'desc']
