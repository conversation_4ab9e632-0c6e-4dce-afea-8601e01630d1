<template>
  <BasicDrawer @register="register" width="90%" show-footer destroyOnClose>
    <template #footer>
      <Button @click="handleInit" :loading="buttonloading">取消</Button>
      <Button :loading="buttonloading" type="primary" @click="handleOk" v-if="!unitpriceButton">确定</Button>
      <Popconfirm title="当前面盘点单种有商品的成本单价大于单价,是否提交！" placement="topRight" @confirm="handleOk" v-else>
        <Button :loading="buttonloading" type="primary">确定</Button>
      </Popconfirm>
    </template>
    <BasicForm :ref="(el) => (wrapFormRef = el)" v-model:model="formModel" @register="registerForm" />
    <div v-for="(item, idx) in packageList" :key="item.work_id">
      <Form
        ref="itemsRef"
        :model="{ ...item, idx }"
        @update:model="(val) => updatePackage(item.work_id, val)"
        :name="`package-${idx}`"
        :label-col="{ style: { width: `${packageFormProps.labelWidth}px` } }"
      >
        <Row>
          <Col
            v-for="pkgSchemas in packageFormProps.schemas"
            :key="pkgSchemas.field"
            :span="pkgSchemas.colProps?.span || packageFormProps.baseColProps.span"
          >
            <template v-if="pkgSchemas.field === 'info'">
              <Divider orientation="left">
                <span class="text-base">包裹明细</span>
              </Divider>
            </template>
            <template v-else-if="['width', 'height', 'length'].includes(pkgSchemas.field)">
              <FormItem
                :label="pkgSchemas.label"
                :name="pkgSchemas.field"
                :rules="[{ required: pkgSchemas.required, message: `请输入${pkgSchemas.label}` }]"
              >
                <component
                  :is="pkgSchemas.component"
                  v-model:value="packageList[idx][pkgSchemas.field]"
                  v-bind="pkgSchemas.componentProps"
                  @change="handleSetVolume(idx)"
                  :disabled="pkgSchemas.dynamicDisabled"
                  placeholder="请输入"
                />
              </FormItem>
            </template>
            <template v-else-if="pkgSchemas.field === 'items'">
              <FormItem
                :label="pkgSchemas.label"
                :name="pkgSchemas.field"
                :rules="[{ required: pkgSchemas.required, message: `请输入${pkgSchemas.label}` }]"
              >
                <component v-if="packageList[idx]" :is="pkgSchemas.render?.(Object.assign(packageList[idx], { idx }))" />
              </FormItem>
            </template>
            <template v-else-if="['warehouse_id', 'warehouse_item_id'].includes(pkgSchemas.field)">
              <FormItem
                :label="pkgSchemas.label"
                :name="pkgSchemas.field"
                :rules="[{ required: pkgSchemas.required, message: `请输入${pkgSchemas.label}` }]"
              >
                <component
                  :is="pkgSchemas.component"
                  v-model:value="packageList[idx][pkgSchemas.field]"
                  :api="
                    pkgSchemas.field === 'warehouse_id'
                      ? pkgSchemas.api
                      : (params) =>
                          item.warehouse_id && transformWarehouseOpt({ ...params, warehouse_id: item.warehouse_id, pageSize: 999 }, getWMI)
                  "
                  v-bind="pkgSchemas.componentProps"
                  :disabled="pkgSchemas.dynamicDisabled"
                  placeholder="请输入"
                />
              </FormItem>
            </template>
            <template v-else>
              <FormItem
                :label="pkgSchemas.label"
                :name="pkgSchemas.field"
                :rules="[{ required: pkgSchemas.required, message: `请输入${pkgSchemas.label}` }]"
              >
                <component
                  :is="pkgSchemas.component"
                  v-model:value="packageList[idx][pkgSchemas.field]"
                  v-bind="pkgSchemas.componentProps"
                  :disabled="pkgSchemas.dynamicDisabled"
                  placeholder="请输入"
                />
              </FormItem>
            </template>
          </Col>
        </Row>
      </Form>
    </div>
  </BasicDrawer>
</template>

<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import {
  formModel,
  mapIn,
  schemas,
  VxeTableRef,
  selectWorkId,
  formatItemRequest,
  wrapFormRef,
  packageList,
  packageSchemas
} from '../datas/packagesTransform.datas'
import { useMessage } from '/@/hooks/web/useMessage'
import { stocktspupdate } from '/@/api/erp/inventory'
import { ref, watch } from 'vue'
import { getSalesOrderAndDetail } from '/@/api/erp/sales'
import { cloneDeep } from 'lodash-es'
import { Divider, Form, Row, Col, Button, Popconfirm } from 'ant-design-vue'
import { div, mul } from '/@/utils/math'
import { transformWarehouseOpt } from '/@/views/erp/mainInWarehouse/datas/AddDrawer'
import { getWMI } from '/@/api/baseData/warehouse'

const itemsRef = ref()

const FormItem = Form.Item

const { createMessage } = useMessage()

const emits = defineEmits(['success'])

const propsData = ref({})

const buttonloading = ref(false)

const unitpriceButton = ref(false)

const [register, { closeDrawer, changeLoading }] = useDrawerInner(async (data) => {
  console.log(data)
  handleInit()
  propsData.value = data
  const mapOperateFn = {
    add: () => {},
    edit: handleEdit
  }
  await resetSchema(schemas)
  mapOperateFn[data.type]?.(data)
})

async function handleEdit({ record }) {
  try {
    buttonloading.value = true
    changeLoading(true)
    const { packageList: originPackageList, desc, department } = cloneDeep(record)

    // 获取当前的选中订单所有商品的最大转出数量
    const { items: salesItems } = await getSalesOrderAndDetail({
      work_ids: [originPackageList[0].work_id]
    })
    let salesArr = []
    for (const saleOrder of salesItems) {
      const itemRequest = formatItemRequest(saleOrder.items, saleOrder)
      salesArr = salesArr.concat(itemRequest)
    }

    // 最大转出数量写入映射对象
    for (const item of salesArr) {
      mapIn.value[item.rowKey] = item.maxInQuantity
    }
    console.log(salesItems)

    await setFieldsValue({ work_ids: originPackageList[0]?.work_id, desc, department })
    packageList.value = originPackageList
  } catch (err) {
    console.log(err)

    createMessage.error('详情初始化失败！')
  } finally {
    buttonloading.value = false
    changeLoading(false)
  }
}

const [registerForm, { validate, resetFields, setFieldsValue, resetSchema }] = useForm({
  schemas,
  baseColProps: {
    span: 8
  },
  labelWidth: 100,
  showActionButtonGroup: false
})

const packageFormProps = {
  baseColProps: {
    span: 6
  },
  labelWidth: 120,
  showActionButtonGroup: false,
  schemas: packageSchemas
}

function updatePackage(workId, packageItem) {
  const item = packageList.value.find((item) => item.work_id === workId)
  Object.assign(item, packageItem)
}

function getParams(data) {
  // const words = data.work_ids
  console.log(data)

  const { packageList } = data

  return {
    desc: data.desc,
    dept_id: packageList[0].dept_id,
    packageList: packageList.map((pack) => ({
      // ...pack,
      work_id: pack.work_id,
      quantity: pack.quantity,
      method: pack.method,
      length: pack.length,
      width: pack.width,
      height: pack.height,
      weight: pack.weight,
      volume: pack.volume,
      warehouse_id: pack.warehouse_id,
      warehouse_item_id: pack.warehouse_item_id,
      supplier_id: pack.supplier_id,
      pkg_quantity: pack.pkg_quantity,
      // product: items.filter((item) => item.work_id === workId),
      items: pack.items.map((item) => ({
        // ...item,
        code: `${item.code}`,
        quantity: item.quantity,
        warehouse_id: pack.warehouse_id,
        warehouse_item_id: pack.warehouse_item_id,
        work_id: item.work_id,
        request_id: item.request_id,
        request_sub_id: item.request_sub_id,
        material: item.material,
        type: 1,
        cost_price: item.cost_price

        // material: item.itemRequest.material
      }))
    }))
  }
}

watch(
  () => packageList.value,
  async (newValue) => {
    unitpriceButton.value = newValue[0]?.items.some((item) => Number(item.unit_price) < Number(item.cost_price))
  },
  {
    deep: true
  }
)

async function handleOk() {
  try {
    // 验证包裹信息
    for (const item of itemsRef.value) {
      const res = await item.validate()
      console.log(res)
    }
    let itemsValid = true
    // 验证明细信息
    for (const workId of Object.keys(VxeTableRef.value)) {
      if (!VxeTableRef.value[workId]) continue
      const errMap = await VxeTableRef.value[workId].validate(true)
      if (errMap) {
        itemsValid = false
        break
      }
    }
    if (!itemsValid) return createMessage.error('存在未填写的盘点明细信息！')

    // 验证描述和销售单信息
    const data = await validate()

    // 开始提交
    buttonloading.value = true
    changeLoading(true)
    // 处理数据

    const params = getParams({ ...data, packageList: packageList.value })
    if (propsData.value.type === 'edit') params.id = propsData.value.record.id
    console.log(params)
    const { msg } = await stocktspupdate(params)
    if (msg === 'success') {
      closeDrawer()
      createMessage.success('提交成功')
      emits('success')
    }
    setTimeout(() => {
      buttonloading.value = false
      changeLoading(false)
    }, 3000)
  } catch (err) {
    console.log(err)
    buttonloading.value = false
    changeLoading(false)
    createMessage.error('提交失败')
  }
}

function handleSetVolume(idx) {
  const { length, width, height } = packageList.value[idx]
  packageList.value[idx].volume = div(mul(mul(length ?? 0, width ?? 0), height ?? 0), 1000000, 6)
}

function handleInit() {
  mapIn.value = {}
  VxeTableRef.value = []
  resetFields()
  packageList.value = []
  selectWorkId.value = null
}
</script>
