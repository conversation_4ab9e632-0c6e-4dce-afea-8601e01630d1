<template>
  <div class="drawer">
    <BasicDrawer @register="register" width="90%" title="详情" :can-fullscreen="false" destroyOnClose>
      <div class="mt-2" v-if="types == 'detail'">
        <Card title="盘点信息" :bordered="false">
          <BasicForm @register="registerForm" />
        </Card>
        <Card title="盘点商品" class="mt-2" :bordered="false">
          <BasicTable @register="registertable" :data-source="tabledata">
            <template #bodyCell="{ text, column, record }">
              <template v-if="column && column.dataIndex === 'imgs'">
                <TableImg :size="60" :simpleShow="true" :imgList="text" />
              </template>
              <template v-if="column && column.dataIndex === 'warehouse_id'">
                <div v-for="(item, index) in statusdata" :key="index">
                  <div v-if="item.id == record.warehouse_id">
                    {{ item.name }}
                  </div>
                </div>
              </template>
            </template>
          </BasicTable>
        </Card>
      </div>
      <div v-else>
        <BasicTable @register="registerTable" />
      </div>
    </BasicDrawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, unref } from 'vue'
import { Card } from 'ant-design-vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicTable, useTable, TableImg } from '/@/components/Table'
import { getWarehouse } from '/@/api/baseData/warehouse'
import { schemas, columns } from '../datas/Modal'
import { useRender } from '/@/components/Table/src/hooks/useRender'

//分类
const tabledata = ref([])
const types = ref()
//modal
const [register, { changeLoading }] = useDrawerInner(async (data) => {
  try {
    types.value = data.type
    await changeLoading(true)
    if (data.type == 'detail') {
      setFieldsValue(data.record)
      if (data.record.processor) {
        setFieldsValue({ processor: Number(data.record.processor) })
      }
      tabledata.value = data.record.info.map((item: Recordable) => ({ ...item }))
      worklist()
    }
    const packarr = data.record.doc_stocking_convert_package.map((item: Recordable) => ({ ...item.packing_package }))
    setTableData(packarr)
  } catch (e) {
    console.error(e)
  } finally {
    await changeLoading(false)
  }
})

//From
const [registerForm, { setFieldsValue }] = useForm({
  schemas,
  showActionButtonGroup: false,
  disabled: true
})

//Table
const [registertable] = useTable({
  columns,
  showTableSetting: true,
  tableSetting: { fullScreen: true }
})

//单号
const statusdata = ref()
async function worklist() {
  const a: any = await getWarehouse()
  statusdata.value = unref(a.items)
}
const columnss = [
  {
    title: '装箱单号',
    dataIndex: 'packing_strid',
    resizable: true
  },
  {
    title: '包裹箱号',
    dataIndex: 'strid',
    resizable: true
  },
  {
    title: '是否已出库',
    dataIndex: 'is_out',
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        2: { label: '已出库', color: 'green' },
        1: { label: '出库中', color: 'green' },
        0: { label: '未出库', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },

  {
    title: '是否已入库',
    dataIndex: 'is_in',
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        2: { label: '已入库', color: 'green' },
        1: { label: '入库中', color: 'green' },
        0: { label: '未入库', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否作废',
    dataIndex: 'is_cancel',
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        1: { label: '已作废', color: 'red' },
        0: { label: '未作废', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否已备货',
    dataIndex: 'is_stock',
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        1: { label: '是', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否拼货',
    dataIndex: 'is_join',
    resizable: true,
    customRender: ({ text }) => {
      const mapStatus = {
        1: { label: '是', color: 'error' },
        0: { label: '否', color: 'success' }
      }
      return mapStatus[text] ? useRender.renderTag(mapStatus[text].label, mapStatus[text].color) : text
    }
  },
  {
    title: '是否报废',
    dataIndex: 'is_scrap',
    resizable: true,
    customRender: ({ text }) => {
      const mapStatus = {
        1: { label: '是', color: 'error' },
        0: { label: '否', color: 'success' }
      }
      return mapStatus[text] ? useRender.renderTag(mapStatus[text].label, mapStatus[text].color) : text
    }
  },
  {
    title: '仓库',
    dataIndex: 'warehouse_name',
    resizable: true
  },
  {
    title: '仓位',
    dataIndex: 'warehouse_item_name',
    resizable: true
  }
]
const [registerTable, { setTableData }] = useTable({
  columns: columnss,
  showIndexColumn: false,
  rowKey: 'id',
  immediate: false
})
</script>
