<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="30%" show-footer @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #Imgs>
        <Upload
          v-model:file-list="fileList"
          action="/api/oss/putImg2Stocking"
          list-type="picture-card"
          :custom-request="handleRequest"
          :disabled="!propsData.isUpdate"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<script setup lang="ts">
import { BasicForm, useForm } from '/@/components/Form'
// import { ScrollContainer } from '/@/components/Container'
import { BasicDrawer, useDrawerInner, DrawerInstance } from '/@/components/Drawer'
import { getSchemasList } from '../datas/drawer.data'
import { ref } from 'vue'
import { Upload, UploadFile } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { createInventory, editInventory, uploadInventoryImg } from '/@/api/erp/inventory'
import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { InventoryParams, PropsType } from '../datas/types'
import { useMessage } from '/@/hooks/web/useMessage'

const { createMessage } = useMessage()
const fileList = ref<UploadFile[]>([])
const propsData = ref<PropsType>({ type: '', isUpdate: false })
const emit = defineEmits<{ (e: 'success'): void; (e: 'register', $event: DrawerInstance): void }>()
const [registerDrawer, { changeOkLoading, closeDrawer, setDrawerProps }] = useDrawerInner(async (data: PropsType) => {
  propsData.value = data
  updateSchema(getSchemasList(data.isUpdate))
  setDrawerProps({ showFooter: data.isUpdate })
  fileList.value = []
  await resetFields()
  if (['edit', 'detail'].includes(data.type)) {
    fileList.value = (
      typeof data.record?.imgs === 'string'
        ? [{ url: data.record?.imgs, uid: +new Date().toString(), name: data.record?.imgs }]
        : (data.record?.imgs as string[]).map((item: string, idx: number) => ({ url: item, uid: idx.toString(), name: item }))
    ) as UploadFile[]
    await setFieldsValue({
      ...data.record,
      desc2: data.record?.desc,
      processor: data.record?.processor ? +data.record.processor : ''
    })
  }
})

const [registerForm, { resetFields, validate, setFieldsValue, updateSchema }] = useForm({
  schemas: getSchemasList(true),
  actionColOptions: { span: 24 },
  labelAlign: 'left',
  showActionButtonGroup: false,
  baseColProps: {
    span: 24
  },
  labelCol: {
    style: 'padding-left: 10px; width: 120px; white-space: break-spaces; overflow: visible;'
  }
})

async function handleSubmit(): Promise<void> {
  try {
    await changeOkLoading(true)
    const values: InventoryParams = await validate()
    console.log(propsData.value.record)
    await handleSubmitForm(values)
    changeOkLoading(false)
  } catch (err) {
    changeOkLoading(false)
    throw new Error(`${err}`)
  } finally {
    changeOkLoading(false)
  }
}

async function handleRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await uploadInventoryImg({ file })
  onSuccess!(result.path)
  fileList.value = fileList.value!.map((item) => ({
    url: (item.url as string) || (item.response as string),
    uid: item.uid,
    name: ''
  }))
}

async function handleSubmitForm(values: InventoryParams) {
  await changeOkLoading(true)
  try {
    const data: InventoryParams = {
      ...values,
      imgs: fileList.value!.map((item) => item.url) as string[],
      unit_price: values.unit_price ? +values.unit_price : 0
    }
    const result = propsData.value.type === 'add' ? await createInventory(data) : await editInventory(propsData.value.record!.doc_id, data)
    emit('success')
    createMessage.success(result.msg)
    await closeDrawer()
    changeOkLoading(false)
  } catch (err) {
    changeOkLoading(false)
    throw new Error(`${err}`)
  }
}
</script>
