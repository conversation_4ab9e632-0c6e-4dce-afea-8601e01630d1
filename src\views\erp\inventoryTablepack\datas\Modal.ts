import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { getStaffList } from '/@/api/baseData/staff'
export const schemas: FormSchema[] = [
  {
    field: 'applicant',
    required: true,
    component: 'ApiSelect',
    label: '申请人',
    colProps: {
      span: 8
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: () => {
      return {
        api: getStaffList,
        resultField: 'items',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'id',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          disabled: true
        }
      }
    },
    labelWidth: 100
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'ApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: true
      }
    },
    colProps: {
      span: 8
    },
    labelWidth: 100,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'processor',
    label: '处理人',
    component: 'ApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: true
      }
    },
    colProps: {
      span: 8
    },
    labelWidth: 100,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'desc',
    component: 'InputTextArea',
    label: '描述',
    colProps: {
      span: 8
    },
    componentProps: {},
    // required: true,
    labelWidth: 100
  }
]

export const columns: BasicColumn[] = [
  // {
  //   title: '所属订单Id',
  //   dataIndex: 'work_id',
  //   width: 100
  // },
  {
    title: '商品ID',
    dataIndex: 'request_id',
    width: 150,
    resizable: true
  },
  {
    title: '销售任务单号',
    dataIndex: 'source_uniqid',
    width: 200,
    resizable: true
  },
  {
    title: '名称',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  // {
  //   title: 'puid',
  //   dataIndex: 'puid',
  //   width: 100
  // },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 100,
    resizable: true
  },
  {
    title: '单价',
    dataIndex: 'unit_price',
    width: 100,
    resizable: true
  },
  {
    title: '成本单价',
    dataIndex: 'cost_price',
    width: 100,
    resizable: true
  },
  {
    title: '图片',
    dataIndex: 'imgs',
    width: 100,
    resizable: true
  },
  {
    title: '仓库',
    dataIndex: 'warehouse_id',
    width: 100,
    resizable: true
  },
  {
    title: '需求数量',
    dataIndex: 'qty_request_left',
    width: 100,
    resizable: true
  },
  {
    title: '盘点数量',
    dataIndex: 'qty_stocking',
    width: 100,
    resizable: true
  },
  // {
  //   title: '需收数量',
  //   dataIndex: 'qty_total',
  //   width: 100
  // },
  // {
  //   title: '实际入库数',
  //   dataIndex: 'qty_received',
  //   width: 100
  // },
  //
  // {
  //   title: '报废数量',
  //   dataIndex: 'qty_defective',
  //   width: 100
  // },
  // {
  //   title: '损毁数量',
  //   dataIndex: 'qty_damage',
  //   width: 100
  // },
  {
    title: '商品描述',
    dataIndex: 'desc',
    width: 100,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 100
  }
]
