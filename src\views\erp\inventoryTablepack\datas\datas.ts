import type { BasicColumn, FormSchema } from '/@/components/Table'
import { h } from 'vue'
import { Tag } from 'ant-design-vue'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { getDeptTree } from '/@/api/admin/dept'

export const columns: BasicColumn[] = [
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 150,
    resizable: true,
    fixed: true,
    ellipsis: false
  },
  // {
  //   title: '关联单号',
  //   dataIndex: 'number',
  //   width: 150,
  //   resizable: true,
  //   fixed: true,
  //   ellipsis: false
  // },
  {
    title: '单号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '分类',
    dataIndex: 'type',
    width: 100,
    resizable: true,
    customRender: (val: any) => {
      const name = {
        1: '库存转换单 ',
        2: '盘点单 ',
        3: '入库单'
      }
      return h('div', {}, name[val.record.type])
    }
  },

  {
    title: '审核状态',
    dataIndex: 'examine',
    width: 100,
    resizable: true,
    customRender: (val: any) => {
      if (val.record.status == 0) {
        return h(Tag, { color: '' }, () => '待审核')
      } else if (val.record.status == 1) {
        return h(Tag, { color: 'green' }, () => '已审核')
      } else if (val.record.status == 2) {
        return h(Tag, { color: 'red' }, () => '审核失败')
      }
    }
  },
  {
    title: '审核日期',
    dataIndex: 'status_at',
    width: 100,
    resizable: true,
    customRender({ value }) {
      return value ? value : '-'
    }
  },
  {
    title: '处理人',
    dataIndex: 'processor',
    width: 100,
    resizable: true
  },
  {
    title: '负责人',
    dataIndex: 'inCharge',
    width: 100,
    resizable: true
  },
  {
    title: '申请人',
    dataIndex: 'applicant',
    width: 100,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 100,
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'updated_at',
    width: 150,
    resizable: true
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 150,
    resizable: true
  }
]
export const columns2: BasicColumn[] = [
  {
    title: '商品名称',
    dataIndex: 'name',
    width: 150,
    resizable: true,
    fixed: true,
    ellipsis: false
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 150,
    resizable: true,
    fixed: true,
    ellipsis: false
  },
  {
    title: '单价',
    dataIndex: 'unit_price',
    width: 150,
    resizable: true,
    customRender: (val: any) => {
      return h('div', {}, `${val.record.unit_price} ￥`)
    }
  },
  {
    title: '成本单价',
    dataIndex: 'cost_price',
    width: 150,
    resizable: true,
    customRender: (val: any) => {
      return h('div', {}, `${val.record.cost_price} ￥`)
    }
  },
  {
    title: '仓库',
    dataIndex: 'warehouse_id',
    width: 100,
    resizable: true
  },
  {
    title: '图片',
    dataIndex: 'imgs',
    width: 100,
    resizable: true
  },
  {
    title: '现存数量',
    dataIndex: 'qty_stocking',
    width: 150,
    resizable: true,
    customRender: (val: any) => {
      return h('div', {}, `${val.record.qty_stocking} ${val.record.unit}`)
    }
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 150,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 150,
    resizable: true
  }
]
export const schemas: FormSchema[] = [
  {
    field: 'name',
    label: '商品名称',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'source_uniqid',
    label: '销售订单号',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'strid',
    label: '单号',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'updated_at',
    label: '创建时间',
    component: 'SingleRangeDate',
    colProps: { span: 8 },
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'status_at',
    label: '审核日期',
    component: 'SingleRangeDate',
    colProps: { span: 8 },
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'applicant',
    label: '申请人',
    component: 'PagingApiSelect',
    componentProps: {
      searchMode: true,
      pagingMode: true,
      api: getCreatorList,
      resultField: 'items',
      returnParamsField: 'id',
      pagingSize: 20,
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    colProps: { span: 8 },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'PagingApiSelect',
    componentProps: {
      searchMode: true,
      pagingMode: true,
      api: getCreatorList,
      returnParamsField: 'id',
      resultField: 'items',
      pagingSize: 20,
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    colProps: { span: 8 },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'processor',
    label: '处理人',
    component: 'PagingApiSelect',
    componentProps: {
      searchMode: true,
      pagingMode: true,
      api: getCreatorList,
      returnParamsField: 'id',
      resultField: 'items',
      pagingSize: 20,
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    colProps: { span: 8 },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    },
    colProps: { span: 8 }
  }
]
