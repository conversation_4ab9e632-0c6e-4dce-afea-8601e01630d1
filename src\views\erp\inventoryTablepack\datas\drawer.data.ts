import { FormSchema } from '/@/components/Form'
import { getWlist } from '/@/api/baseData/warehouse'
import { getStaffList } from '/@/api/baseData/staff'

export const getSchemasList = (isUpdate: boolean): FormSchema[] => [
  {
    field: 'name',
    label: '名称',
    component: 'Input',
    required: true,
    componentProps: {
      disabled: !isUpdate
    }
  },
  {
    field: 'warehouse_id',
    label: '所属仓库',
    component: 'ApiSelect',
    componentProps: {
      api: getWlist,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: !isUpdate
      }
    },
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'applicant',
    label: '申请人',
    component: 'ApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: !isUpdate
      }
    },
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'ApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: !isUpdate
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'processor',
    label: '处理人',
    component: 'ApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'key', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: !isUpdate
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'unit',
    label: '单位',
    component: 'Input',
    componentProps: {
      disabled: !isUpdate
    }
  },
  {
    field: 'unit_price',
    label: '单价',
    component: 'InputNumber',
    componentProps: {
      disabled: !isUpdate,
      precision: 2
    }
  },
  // {
  //   field: 'pkg_num',
  //   label: '接收的包裹数',
  //   component: 'InputNumber',
  //   componentProps: {
  //     disabled: !isUpdate
  //   }
  // },
  // {
  //   field: 'pkg_received',
  //   label: '收到的包裹数',
  //   component: 'InputNumber',
  //   componentProps: {
  //     disabled: !isUpdate
  //   }
  // },
  // {
  //   field: 'qty_total',
  //   label: '收到商品数',
  //   component: 'InputNumber',
  //   componentProps: {
  //     disabled: !isUpdate
  //   }
  // },
  // {
  //   field: 'qty_received',
  //   label: '实际入库的商品数',
  //   component: 'InputNumber',
  //   componentProps: {
  //     disabled: !isUpdate
  //   }
  // },
  // {
  //   field: 'qty_defective',
  //   label: '报废商品数',
  //   component: 'InputNumber',
  //   componentProps: {
  //     disabled: !isUpdate
  //   }
  // },
  {
    field: 'qty_stocking',
    label: '现有商品数',
    component: 'InputNumber',
    componentProps: {
      disabled: !isUpdate
    },
    required: true
  },
  {
    field: 'desc1',
    label: '盘点描述',
    component: 'InputTextArea',
    required: true,
    componentProps: {
      disabled: !isUpdate
    }
  },
  {
    field: 'desc2',
    label: '库存描述',
    component: 'InputTextArea',
    required: true,
    componentProps: {
      disabled: !isUpdate
    }
  },
  {
    field: 'remark',
    label: '库存备注',
    component: 'InputTextArea',
    componentProps: {
      disabled: !isUpdate
    }
  },
  {
    field: 'imgs',
    label: '盘点图片',
    component: 'Upload',
    slot: 'Imgs'
  }
]
