import { FormSchema, PagingApiSelect } from '/@/components/Form'
import { VxeBasicTable } from '/@/components/VxeTable'
import { Button, Form, Input, InputNumber, Popover, Select } from 'ant-design-vue'
import { computed, h, reactive, ref, withDirectives } from 'vue'
import { getWarehouse } from '/@/api/baseData/warehouse'
import { transformWarehouseOpt } from '/@/views/erp/mainInWarehouse/datas/AddDrawer'
import { useMessage } from '/@/hooks/web/useMessage'
import { useI18n } from '/@/hooks/web/useI18n'
import { getWorkList } from '/@/api/commonUtils'
import { getSalesOrderListReq } from '/@/api/erp/sales'
import { add, sub } from '/@/utils/math'
import { cloneDeep, random } from 'lodash-es'
import { VxeTablePropTypes } from 'vxe-table'
import loadingDirective from '/@/directives/loading'
import { TableImg } from '/@/components/Table'

const FormItemRest = Form.ItemRest

const { createMessage } = useMessage()

export const VxeTableRef = ref({})

export const wrapFormRef = ref(null)

export const formModel = ref({})

export const formRef = ref(null)

export const packageList = ref<any[]>([])

export const loading = ref<boolean>(false)

const { tm } = useI18n()

// 每个itemRequest的最大值
export const mapIn = ref({})

const mapPackageFormDetails = computed(() => {
  return packageList.value.map((item) => item.items).flat()
})

// 计算每个request总共的转入数量
const mapFormInData = computed(() => {
  const mapFormData = {}
  for (const item of mapPackageFormDetails.value ?? []) {
    mapFormData[item.rowKey] = mapFormData?.[item.rowKey] ? add(+mapFormData?.[item?.rowKey] ?? 0, +item.quantity ?? 0) : +item.quantity
  }
  return mapFormData
})

// 计算每个转入产品的最大可使用数量
const compInMaxCount = computed(() => {
  const mapInCount = {}
  for (const key of Object.keys(mapIn.value) ?? []) {
    // const key = item.rowKey
    mapInCount[key] = mapFormInData.value?.[key] ? sub(+mapIn.value[key] ?? 0, +mapFormInData.value?.[key] ?? 0) : +mapIn.value[key]
  }
  return mapInCount
})

const originColumns = [
  {
    title: '产品名称',
    field: 'name',
    width: 200,
    resizable: true
  },
  {
    title: '销售任务单号',
    field: 'work_id',
    width: 200
  },
  {
    title: '单位',
    field: 'unit',
    width: 100
  },
  {
    title: '单价',
    field: 'unit_price',
    width: 100
  },
  {
    title: '成本单价',
    field: 'cost_price',
    editRender: { name: 'AInputNumber' },
    slots: {
      edit: ({ row }) => <InputNumber v-model:value={row.cost_price} min={0.01} precision={2}></InputNumber>,
      default: ({ row }) => row.cost_price ?? 0
    },
    width: 150,
    resizable: true
  },
  {
    title: '图片',
    field: 'imgs',
    width: 150,
    slots: {
      default: ({ row }) => <TableImg size={60} simpleShow={true} imgList={row.imgs} />
    }
  },
  {
    title: '需求数量',
    field: 'qty_request_left',
    width: 100
  },
  {
    title: '产品数量',
    field: 'quantity',
    editRender: { name: 'AInputNumber', placeholder: '点击输入转入产品数量' },
    slots: {
      edit: ({ row }) => (
        <InputNumber
          v-model:value={row.quantity}
          max={add(compInMaxCount.value[row.rowKey] ?? 0, row.quantity)}
          min={0.01}
          precision={2}
        ></InputNumber>
      ),
      default: ({ row }) => row.quantity
    },
    width: 200,
    resizable: true
  },
  {
    title: '材质',
    field: 'material',
    editRender: { name: 'AInput' },
    slots: {
      edit: ({ row }) => <Input v-model:value={row.material} />,
      default: ({ row }) => row.material
    },
    width: 200,
    resizable: true
  },
  {
    title: '海关码',
    field: 'code',
    editRender: { name: 'AInput', placeholder: '点击输入转入产品数量' },
    slots: {
      edit: ({ row }) => <Input v-model:value={row.code} />,
      default: ({ row }) => row.code
    },
    width: 180,
    resizable: true
  },
  {
    title: '操作',
    field: 'actions',
    width: 150,
    resizable: true,
    fixed: 'right',
    slots: {
      header: ({ column, $table }) => (
        <>
          <div>{column.title}</div>
          <Button size="small" type="primary" onClick={() => handleDelRow(null, null, $table)}>
            删除勾选数据
          </Button>
        </>
      ),
      default: ({ row, rowIndex, $table }) => (
        <Button type="primary" size="small" onClick={() => handleDelRow(row, rowIndex, $table)}>
          删除
        </Button>
      )
    }
  }
]

export const gridOptions = reactive<any>({
  // id: 'VxeTable',
  // loading: loading,
  keepSource: true,
  columns: originColumns,
  height: 500,
  proxyConfig: null,
  toolbarConfig: null,
  align: 'center',
  headerAlign: 'center'
  // checkboxConfig: {
  //   trigger: 'row'
  // }
})

export const selectWorkId = ref()

export const schemas: FormSchema[] = [
  {
    field: 'desc',
    colProps: {
      span: 24
    },
    // required: true,
    component: 'InputTextArea',
    label: '仓库描述'
  },
  {
    field: 'work_ids',
    colProps: {
      span: 24
    },
    required: true,
    component: 'PagingApiSelect',
    componentProps: ({ formModel }) => {
      return {
        resultField: 'items',
        api: (params) => getWorkList({ ...params, status: [2, 3, 4, 5], types: [2, 3] }),
        searchMode: true,
        pagingMode: true,
        returnParamsField: 'id',
        searchParamField: 'source_uniqid',
        selectProps: {
          class: 'w-[300px]',
          size: 'small',
          allowClear: true,
          fieldNames: { key: 'id', value: 'id', label: 'source_uniqid' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'source_uniqid',
          dropdownRender: ({ menuNode }) => {
            const vNode = h('div', {}, menuNode)
            return withDirectives(vNode, [[loadingDirective, loading.value]])
          },
          onChange(_, shall) {
            formModel.department = shall?.department_name
          }
        },
        onSelect: handleAddPackage,
        onDeselect: handleDelPackage
      }
    },
    label: '盘点销售单',
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'department',
    label: '部门',
    component: 'Input',
    dynamicDisabled: true,
    colProps: {
      span: 24
    }
  }
]

export const packageSchemas: FormSchema[] = [
  {
    field: 'info',
    label: '',
    component: 'Divider',
    colProps: {
      span: 24
    }
  },
  {
    field: 'quantity',
    label: '打包产品数量',
    component: InputNumber,
    componentProps: { min: 0.01, precision: 2 },
    required: true
  },
  {
    field: 'method',
    label: '打包方式',
    required: true,
    component: Select,
    componentProps: {
      options: tm('packages.packagesMethods')
    }
  },
  {
    field: 'length',
    label: '长（CM）',
    required: true,
    component: InputNumber
    // componentProps: ({ formModel, formActionType }) => {
    //   return {
    //     min: 0.01,
    //     precision: 2,
    //     onChange: (val) => {
    //       nextTick(() => {
    //         formActionType?.setFieldsValue({
    //           volume: div(mul(mul(val ?? 0, formModel.width ?? 0), formModel.height ?? 0), 1000000, 6)
    //         })
    //       })
    //     }
    //   }
    // }
  },
  {
    field: 'width',
    label: '宽（CM）',
    required: true,
    component: InputNumber
    // componentProps: ({ formModel, formActionType }) => {
    //   return {
    //     min: 0.01,
    //     precision: 2,
    //     onChange: (val) => {
    //       nextTick(() => {
    //         formActionType?.setFieldsValue({
    //           volume: div(mul(mul(val ?? 0, formModel.length ?? 0), formModel.height ?? 0), 1000000, 6)
    //         })
    //       })
    //     }
    //   }
    // }
  },
  {
    field: 'height',
    label: '高（CM）',
    required: true,
    component: InputNumber
    // componentProps: ({ formModel, formActionType }) => {
    //   return {
    //     min: 0.01,
    //     precision: 2,
    //     onChange: (val) => {
    //       nextTick(() => {
    //         formActionType?.setFieldsValue({
    //           volume: div(mul(mul(val ?? 0, formModel.width ?? 0), formModel.length ?? 0), 1000000, 6)
    //         })
    //       })
    //     }
    //   }
    // }
  },
  {
    field: 'volume',
    label: '体积（CBM）',
    required: true,
    component: InputNumber,
    dynamicDisabled: true
    // componentProps: { min: 0.01, precision: 2 }
  },
  {
    field: 'weight',
    label: '重量（KG）',
    required: true,
    component: InputNumber,
    componentProps: { min: 0.01, precision: 2 }
  },
  {
    field: 'pkg_quantity',
    label: '打包件数',
    required: true,
    component: InputNumber,
    componentProps: { min: 0.01, precision: 2 }
  },
  {
    field: 'supplier_strid',
    label: '供应商箱号',
    // required: true,
    component: Input
  },
  {
    field: 'warehouse_id',
    label: '仓库',
    required: true,
    component: PagingApiSelect,
    componentProps: {
      placeholder: '请选择仓库',
      pagingMode: true,
      // api: getWarehouse,
      api: (params) => transformWarehouseOpt({ ...params }, getWarehouse),
      searchMode: true,
      returnParamsField: 'id',
      selectProps: { fieldNames: { value: 'id', label: 'name' }, placeholder: '请选择' },
      resultField: 'items'
      // onChange(idx, val, opt) {
      //   console.log(val, opt, idx)
      // }
    }
  },
  {
    field: 'warehouse_item_id',
    label: '仓位',
    required: true,
    component: PagingApiSelect,
    componentProps: {
      placeholder: '请选择仓位',
      pagingMode: true,
      pagingSize: 500,
      // api: (params) => formModel.warehouse_id && transformWarehouseOpt({ ...params, warehouse_id: formModel.warehouse_id }, getWMI),
      searchMode: true,
      returnParamsField: 'id',
      selectProps: { fieldNames: { value: 'id', label: 'name' }, placeholder: '请选择' },
      resultField: 'items'
    }
  },
  {
    field: 'remark',
    label: '盘点备注',
    component: Input
  },
  {
    colProps: {
      span: 24
    },
    required: true,
    field: 'items',
    label: '盘点的商品信息',
    component: 'Select',
    render: (model) => {
      return (
        <FormItemRest>
          <Popover title="选择需要转入本订单的商品" trigger="click" placement="right">
            {{
              content: () => (
                <div>
                  <Select
                    class="w-[300px]"
                    mode="multiple"
                    options={model.detailOpt.map((item) => ({ ...item, disabled: +compInMaxCount.value[item.rowKey] <= 0 }))}
                    size="small"
                    v-model:value={model.selectDetail}
                    fieldNames={{ label: 'name', value: 'rowKey', key: 'rowKey' }}
                    // onSelect={(val) => handleSelectDetails(val, model)}
                    // onDeselect={(val) => handleDeselectDetails(val, model)}
                  ></Select>
                  <Button class="ml-2" size="small" type="primary" loading={loading.value} onClick={() => handleSelectDetails(model)}>
                    确定
                  </Button>
                </div>
              ),
              default: () => <Button type="primary">新增</Button>
            }}
          </Popover>
          <VxeBasicTable
            {...gridOptions}
            id={`${model.idx}`}
            ref={(el) => (VxeTableRef.value[model.work_id] = el)}
            class="!p-0 mt-1"
            v-model:data={model.items}
            editConfig={{ trigger: 'click', mode: 'cell', showStatus: true, enabled: true, showIcon: true }}
            edit-rules={validRules.value}
          />
        </FormItemRest>
      )
    }
  }
]

async function handleAddPackage(val, opt) {
  loading.value = true
  try {
    const { items, works } = await getSalesOrderListReq({ work_id: val, pageSize: 1000 })

    const detailArr = formatItemRequest(items, works)
    for (const item of detailArr) {
      mapIn.value[item.rowKey] = item.maxInQuantity
    }

    // 清空数组，只保留最新添加的数据
    packageList.value = [
      {
        source_uniqid: opt.source_uniqid,
        work_id: opt.id,
        dept_id: opt.dept_id,
        work: opt,
        items: detailArr,
        detailOpt: cloneDeep(detailArr)
      }
    ]
  } catch (err) {
    createMessage.error('获取订单详情失败！')
  } finally {
    loading.value = false
  }
}

async function handleDelPackage(val) {
  packageList.value = packageList.value.filter((item) => item.work_id !== val)
}

function handleSelectDetails(formData) {
  const formModel = packageList.value[formData.idx]
  const pushData = formData.detailOpt.filter((goods) => formModel.selectDetail.includes(goods.rowKey))
  // const items = formData.items
  formModel.items = formModel.items.concat(
    cloneDeep(pushData.map((item) => ({ ...item, itemKey: random(1, 100000000), quantity: +compInMaxCount.value[item.rowKey] })))
  )
  formModel.selectDetail = []
}

export function formatItemRequest(data, works, { needFilter = true, setNullId = true } = {}) {
  let newData = data
    // 将多维数组变成一维数组
    .map((itemRequest) =>
      itemRequest?.items_sub?.length > 0 ? itemRequest.items_sub.map((itemSub) => ({ ...itemSub, type: 2 })) : { ...itemRequest, type: 1 }
    )
    .flat(1)

    // 映射成统一的字段
    .map((itemRequest) => {
      if (itemRequest.type == 2) {
        const subRequest = {
          ...itemRequest,
          work_id: itemRequest.work_id,
          request_id: itemRequest.request_id,
          request_sub_id: itemRequest.id,
          maxInQuantity: itemRequest.quantity_left, // 诚哥指定20240927
          source_uniqid: works.source_uniqid,
          quantity: compInMaxCount.value?.[`child-${itemRequest.id}`] ?? itemRequest.quantity_left, // 诚哥指定20240927
          rowKey: `child-${itemRequest.id}`,
          itemKey: random(1, 100000000),
          id: setNullId ? null : itemRequest.id
        }

        return subRequest
      }
      const parentRequest = {
        ...itemRequest,
        work_id: itemRequest.work_id,
        request_id: itemRequest.id,
        maxInQuantity: itemRequest.qty_request_left, // 诚哥指定20240927
        source_uniqid: works.source_uniqid,
        quantity: compInMaxCount.value?.[`parent-${itemRequest.id}`] ?? itemRequest.qty_request_left, // 诚哥指定20240927
        rowKey: `parent-${itemRequest.id}`,
        itemKey: random(1, 100000000),
        id: setNullId ? null : itemRequest.id
      }

      return parentRequest
    })

  if (needFilter) {
    // 过滤转入数量为0的数据
    newData = newData.filter((item) => item.quantity != 0)
  }

  return newData
}

function handleDelRow(row?, idx?, $table) {
  console.log($table)
  const key = $table.props.id
  if (!row) {
    const selectRow = $table?.getCheckboxRecords()
    const itemSelectKeys = selectRow.map((item) => item.itemKey)
    packageList.value[key].items = packageList.value[key].items.filter((item) => !itemSelectKeys.includes(item.itemKey))
    return
  }
  packageList.value[key].items = packageList.value[key].items.filter((item) => row.itemKey !== item.itemKey)
}

interface RowFields {
  packing_package_id: number
  packing_package_items_id: number
  quantity_origin: number
  quantity: number
  warehouse_id: number
  warehouse_item_id: number
  code: string
  material: string
}

export const validRules = ref<VxeTablePropTypes.EditRules<RowFields>>({
  code: [{ required: true, message: '必须填写海关码' }],
  material: [{ required: true, message: '必须填写材质' }],
  cost_price: [{ required: true, message: '必须成本单价' }]
})
