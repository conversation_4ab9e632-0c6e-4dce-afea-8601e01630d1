export interface InventoryItem {
  applicant: number
  created_at: string
  creator: string
  desc: string
  desc1: string
  doc_id: number
  id: number
  imgs: string[] | string
  inCharge: number
  name: string
  origin_stocking_id: null
  pkg_num: number
  pkg_received: number
  processor: string | number
  puid: string
  purchase_id: null
  qty_defective: number
  qty_received: number
  qty_stocking: number
  qty_total: number
  remark: string
  request_id: null
  src: number
  strid: string
  type: string
  unit: string
  unit_price: number
  updated_at: string
  warehouse_id: number
  work_id: null

  [property: string]: any
}

export interface InventoryParams {
  /**
   * 申请人id
   */
  applicant: number
  /**
   * 盆点描述
   */
  desc1: string
  /**
   * 库存描述
   */
  desc2?: string
  /**
   * 图片json
   */
  imgs?: string[]
  /**
   * 负责人id
   */
  inCharge?: number
  /**
   * 名称或商品名称
   */
  name?: string
  /**
   * 要接受的包裹数
   */
  pkg_num?: number
  /**
   * 已经收到的包裹数
   */
  pkg_received?: number
  /**
   * 员工id
   */
  processor?: number
  /**
   * 报废的商品数
   */
  qty_defective?: number
  /**
   * 实际入库商品数
   */
  qty_received?: number
  /**
   * 现有的商品数
   */
  qty_stocking?: number
  /**
   * 要收到的商品数
   */
  qty_total?: number
  /**
   * 库存备注
   */
  remark?: string
  /**
   * 单位
   */
  unit?: string
  /**
   * 单价
   */
  unit_price?: number
  /**
   * 仓库id
   */
  warehouse_id: number

  [property: string]: any
}

export interface PropsType {
  isUpdate: boolean
  record?: InventoryItem
  type: string
}

export interface IRecord {
  id: number
  strid: string
  applicant: number
  inCharge: null | string | number
  creator: string
  processor: null | string | number
  info: [
    {
      imgs: string[]
      name: string
      unit: string
      work_id: number
      unit_price: string
      request_id: number
      qty_stocking: number
      warehouse_id: number
      qty_request_left: number
    }
  ]
  desc: null | string
  type: string
  number: null | number
  status: number
  updated_at: string
  created_at: string
}

export interface IRecordProduct {
  work_id: number
  qty_stocking: number
  unit_price: string
  name: string
  unit: string
  imgs: string[]
  qty_request_left: number
  request_id: number
  warehouse_id: number
  desc: string
  remark: string
  id: number
}

export interface IFormDataProduct {
  id: number
  work_id: number
  request_id: number
  qty_request_left: number
  name: string
  unit: string
  unit_price: string
  warehouse_id: number
  qty_stocking: number
  desc: string
  remark: string
  imgs: string[]
}
