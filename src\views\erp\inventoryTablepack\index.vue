<template>
  <div>
    <BasicTable class="p-4" :data-cachekey="routePath" @register="registerTable">
      <template #toolbar>
        <a-button v-if="hasPermission([674])" type="primary" @click="handleCreate"><PlusOutlined /> 新建</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
        <template v-if="column.dataIndex === 'processor'">
          <div v-for="(item, index) in statusdata" :key="index">
            <div v-if="item.id == record.processor">
              {{ item.name }}
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'inCharge'">
          <div v-for="(item, index) in statusdata" :key="index">
            <div v-if="item.id == record.inCharge">
              {{ item.name }}
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'applicant'">
          <div v-for="(item, index) in statusdata" :key="index">
            <div v-if="item.id == record.applicant">
              {{ item.name }}
            </div>
          </div>
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <BasicTable @register="registerTable2" :data-source="record.info" :columns="columns2" v-if="record.info.length > 0">
          <template #bodyCell="{ text, column: childColumn, record: childrecord }">
            <template v-if="childColumn.dataIndex === 'warehouse_id'">
              <div v-for="(item, index) in warehouse" :key="index">
                <div v-if="item.id == childrecord.warehouse_id">
                  {{ item.name }}
                </div>
              </div>
            </template>
            <template v-if="childColumn.dataIndex === 'imgs'">
              <TableImg :size="60" :simpleShow="true" :imgList="text" />
            </template>
          </template>
        </BasicTable>
      </template>
    </BasicTable>
    <AddinventtorytableDrawer @register="registerDrawer" @success="reload" />
    <detailDrawer @register="registerDetailDrawer" />
  </div>
</template>

<script setup lang="ts" name="/erp/inventoryTable">
import { PlusOutlined } from '@ant-design/icons-vue'
import { BasicTable, useTable, TableAction, TableImg } from '/@/components/Table'
import type { ActionItem } from '/@/components/Table'
import { columns, columns2, schemas } from './datas/datas'
import { getInventoryList, delInventory, stocktspsetStatus } from '/@/api/erp/inventory'
import { useMessage } from '/@/hooks/web/useMessage'
import AddinventtorytableDrawer from './components/AddinventtorytableDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { getStaffList } from '/@/api/baseData/staff'
import { unref, ref } from 'vue'
import { getWarehouse } from '/@/api/baseData/warehouse'
import { IRecord } from './datas/types'
import { usePermission } from '/@/hooks/web/usePermission'
import { useRoute } from 'vue-router'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'
import detailDrawer from './components/detailDrawer.vue'

const route = useRoute()
const { path: routePath } = route
const { hasPermission } = usePermission()
const { createMessage } = useMessage()
const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer()

const [registerTable, { reload }] = useTable({
  title: '盘点单',
  showIndexColumn: false,
  columns,
  showTableSetting: true,
  isTreeTable: true,
  rowKey: 'id',
  api: getInventoryList,
  actionColumn: hasPermission([675, 676, 678, 679, 677])
    ? {
        width: 200,
        title: '操作',
        dataIndex: 'action'
      }
    : void 0,
  afterFetch: () => {
    worklist()
    warehouseFn()
    // data.forEach((item) => {
    //   if (item.info.length > 0) {
    //     Reflect.set(item, 'children', [])
    //   }
    // })
  },
  useSearchForm: true,
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    alwaysShowLines: 1,
    autoAdvancedLine: 1,
    showAdvancedButton: true,
    schemas,
    fieldMapToTime: [
      ['created_at', ['start_time', 'end_time'], 'X'],
      ['updated_at', ['updated_at_start', 'updated_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['status_at', ['status_at_start', 'status_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  }
})

function createActions(record: IRecord): ActionItem[] {
  return [
    {
      icon: 'ant-design:file-search-outlined',
      label: '审核',
      popConfirm: {
        title: '修改状态后无法再次修改状态，且无法编辑，确定要通过审核吗？',
        placement: 'left',
        confirm: handleApprove.bind(null, record)
      },
      disabled: record.status === 1,
      ifShow: hasPermission([675])
    },
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      disabled: record.status === 1,
      ifShow: hasPermission([676])
    }
  ] as ActionItem[]
}

function createDropDownActions(record: IRecord): ActionItem[] {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record, 'detail'),
      ifShow: hasPermission([678])
    },
    {
      icon: 'ant-design:eye-outlined',
      label: '查看包裹',
      onClick: handleDetail.bind(null, record, 'package'),
      ifShow: hasPermission([679])
    },
    {
      icon: 'ant-design:delete-outlined',
      label: '删除',
      color: 'error',
      popConfirm: {
        title: '是否确认删除',
        placement: 'left',
        confirm: handleDelete.bind(null, record),
        disabled: record.status === 1
      },
      ifShow: hasPermission([677])
    }
  ]
}

async function handleDelete(record: IRecord) {
  const result = await delInventory(record.id)
  createMessage.success(result.msg)
  await reload()
}

function handleCreate() {
  openDrawer(true, { isUpdate: true, type: 'add' })
  setDrawerProps({ title: '新建盘点单' })
}

function handleDetail(record: IRecord, type: string) {
  openDetailDrawer(true, { record, type })
}

function handleEdit(record: IRecord) {
  openDrawer(true, {
    record,
    isUpdate: true,
    type: 'edit'
  })
  setDrawerProps({ title: '编辑盘点单' })
}

//树形内嵌
const [registerTable2] = useTable({
  showIndexColumn: false,
  pagination: false,
  maxHeight: 150
})

// async function examine(record: any) {
//   const ids: any = []
//   ids.push(record.id)
//   if (record.status === 1) {
//     return message.error('单据已审核,不可在更改')
//   } else {
//     const a = await postconfirm({ ids: ids })
//     if (a.msg === 'success') {
//       reload()
//     }
//   }
// }

//员工
const statusdata = ref()
async function worklist() {
  const a: any = await getStaffList()
  statusdata.value = unref(a.items)
}

//仓库
const warehouse = ref()
async function warehouseFn() {
  const a: any = await getWarehouse()
  warehouse.value = unref(a.items)
}

// 审核
async function handleApprove(record: IRecord) {
  try {
    const { msg } = await stocktspsetStatus({ id: record.id, status: 1 })
    if (msg === 'success') {
      createMessage.success('审核成功')
      reload()
      return
    }
    createMessage.error('审核失败')
  } catch (e) {
    throw new Error(`${e}`)
  }
}
//useDrawer
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
</script>

<style lang="less" scoped>
.img-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 10px;
}
</style>
