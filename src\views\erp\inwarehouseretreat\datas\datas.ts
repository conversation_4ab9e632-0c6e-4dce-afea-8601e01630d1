import { getStaffList } from '/@/api/baseData/staff'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { DIVIDER_SCHEMA, GET_STATUS_SCHEMA } from '/@/const/status'

export const columns: BasicColumn[] = [
  {
    title: '单号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 200,
    resizable: true,
    customRender({ text }) {
      const map = {
        1: { label: '销售退货', color: 'pink' },
        2: { label: '采购退货', color: 'orange' },
        3: { label: '入库退货', color: 'blue' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 200,
    resizable: true,
    customRender({ text }) {
      const map = {
        0: { label: ' 待执行', color: 'pink' },
        1: { label: '执行中 ', color: 'orange' },
        15: { label: '完成', color: 'blue' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否作废',
    dataIndex: 'is_cancel',
    width: 200,
    resizable: true,
    customRender({ text }) {
      const map = {
        0: { label: '否', color: 'green' },
        1: { label: '是 ', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 200,
    resizable: true
  },
  {
    title: '审核人',
    dataIndex: 'auditor_name',
    width: 200,
    resizable: true
  },
  {
    title: '审核日期',
    dataIndex: 'status_at',
    width: 200,
    resizable: true
  },
  {
    title: '创建日期',
    dataIndex: 'created_at',
    width: 200,
    resizable: true
  },
  {
    title: '作废日期',
    dataIndex: 'cancel_at',
    width: 200,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    resizable: true
  },
  {
    title: '作废备注',
    dataIndex: 'cancel_remark',
    width: 200,
    resizable: true
  }
]
const status_schema = GET_STATUS_SCHEMA([
  {
    label: '待执行',
    value: 0
  },
  {
    label: '执行中',
    value: 1
  },
  {
    label: '完成',
    value: 15
  }
])

export const searchFormSchema: FormSchema[] = [
  status_schema,
  DIVIDER_SCHEMA,
  {
    field: 'strid',
    label: '单号',
    component: 'Input'
  },
  // {
  //   field: 'status',
  //   label: '状态',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       {
  //         label: '待执行',
  //         value: 0
  //       },
  //       {
  //         label: '执行中',
  //         value: 1
  //       },
  //       {
  //         label: '完成',
  //         value: 15
  //       }
  //     ]
  //   }
  // },
  {
    field: 'creator',
    label: '创建人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'created_at',
    label: '创建日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  }
]

export const packercolumns: BasicColumn[] = [
  {
    title: '包裹号',
    dataIndex: 'strid',
    width: 250,
    resizable: true
  },
  {
    title: '是否已出库',
    dataIndex: 'is_out',
    customRender: ({ text }) => {
      const map = {
        2: { label: '已出库', color: 'green' },
        1: { label: '出库中', color: 'green' },
        0: { label: '未出库', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },

  {
    title: '是否已入库',
    dataIndex: 'is_in',
    customRender: ({ text }) => {
      const map = {
        2: { label: '已入库', color: 'green' },
        1: { label: '入库中', color: 'green' },
        0: { label: '未入库', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  // {
  //   title: '是否作废',
  //   dataIndex: 'is_cancel',
  //   customRender: ({ text }) => {
  //     const map = {
  //       1: { label: '已作废', color: 'red' },
  //       0: { label: '未作废', color: 'green' }
  //     }
  //     const curTag = map[text]
  //     if (!curTag) return text
  //     return useRender.renderTag(curTag.label, curTag.color)
  //   }
  // },
  {
    title: '是否已备货',
    dataIndex: 'is_stock',
    customRender: ({ text }) => {
      const map = {
        1: { label: '是', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否旧包裹',
    dataIndex: 'is_old',
    customRender: ({ text }) => {
      const map = {
        1: { label: '是', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否拼货',
    dataIndex: 'is_join',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        1: { label: '是', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  }
]
