import { getRelatePurchaseList } from '/@/api/erp/inWarehouse'
import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { Rule } from 'ant-design-vue/lib/form'
import { ref } from 'vue'
import { useI18n } from '/@/hooks/web/useI18n'
import { Popover } from 'ant-design-vue'
import Icon from '/@/components/Icon'
import { isNullOrUnDef } from '/@/utils/is'

//已选择的商品
export const selectRowKeys = ref<any[]>([])

const { t } = useI18n()

export const schemas = (hand: Function, type: string): FormSchema[] => [
  {
    field: 'strid',
    label: '退货单号',
    component: 'Input',
    dynamicDisabled: true,
    ifShow: ['detail', 'edit'].includes(type)
  },
  {
    field: 'purchase_order_id',
    label: '关联采购订单',
    component: 'ApiSelect',
    required: ['add'].includes(type),
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: ({}) => {
      return {
        api: getRelatePurchaseList,
        searchMode: true,
        searchParamField: 'strid',
        immediate: false,
        selectProps: {
          fieldNames: { value: 'work_id', label: 'strid' },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          mode: 'multiple',
          disabled: ['detail'].includes(type),
          optionFilterProp: 'strid'
        },
        resultField: 'items',
        onChange: async (val: number) => {
          console.log('采购单change')
          if (!val) return
          try {
            hand && hand(val)
          } catch (e) {
            throw new Error(`${e}`)
          }
        }
      }
    }
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    dynamicDisabled: ['detail'].includes(type)
  },
  {
    field: 'product_info',
    label: '商品信息',
    component: 'Input',
    required: true,
    slot: 'ProductSlot',
    rules: [{ required: true, validator: validateProductInfo }]
  }
]

function validateProductInfo(_rule: Rule, value: any[]) {
  if (!value || value.length === 0) return Promise.reject('请先选择关联的入库单')
  return Promise.resolve()
}

export const inwharecolumns: BasicColumn[] = [
  {
    title: '包裹号',
    dataIndex: 'strid',
    width: 250,
    resizable: true
  },
  {
    title: '是否已出库',
    dataIndex: 'is_out',
    customRender: ({ text }) => {
      const map = {
        2: { label: '已出库', color: 'green' },
        1: { label: '出库中', color: 'green' },
        0: { label: '未出库', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },

  {
    title: '是否已入库',
    dataIndex: 'is_in',
    customRender: ({ text }) => {
      const map = {
        2: { label: '已入库', color: 'green' },
        1: { label: '入库中', color: 'green' },
        0: { label: '未入库', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否作废',
    dataIndex: 'is_cancel',
    customRender: ({ text }) => {
      const map = {
        1: { label: '已作废', color: 'red' },
        0: { label: '未作废', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否已备货',
    dataIndex: 'is_stock',
    customRender: ({ text }) => {
      const map = {
        1: { label: '是', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否旧包裹',
    dataIndex: 'is_old',
    customRender: ({ text }) => {
      const map = {
        1: { label: '是', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否拼货',
    dataIndex: 'is_join',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        1: { label: '是', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否退货',
    dataIndex: 'is_retreat',
    customRender: ({ text }) => {
      const map = {
        3: { label: '已退货 ', color: 'red' },
        2: { label: '已退货备货 ', color: 'red' },
        1: { label: '退货中 ', color: 'red' },
        0: { label: '未退货', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  }
]

export const childrencolumns: BasicColumn[] = [
  {
    title: '所属销售单',
    dataIndex: 'source_uniqid',
    resizable: true
  },
  {
    title: '所属采购单',
    dataIndex: 'purchase_strid',
    resizable: true
  },
  {
    title: '商品名称',
    dataIndex: 'name',
    resizable: true
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    resizable: true
  },
  {
    title: '质检状态',
    dataIndex: 'purchase_qc_status',
    resizable: true,
    width: 150,
    customRender: ({ text }) => {
      const slots = {
        content: () => (
          <span>
            请先将相关的质检单执行 <span style="color: red">作废</span> 操作，再进行退货
          </span>
        )
      }
      return (
        <div>
          {useRender.renderTag(t(`tag.qcStatusTag.${text}.label`), t(`tag.qcStatusTag.${text}.color`))}
          {text === 0 ? null : (
            <Popover title="已质检无法退货" v-slots={slots}>
              <Icon icon="clarity:warning-standard-solid" color="red" class="ml-2" />
            </Popover>
          )}
        </div>
      )
    }
  },
  {
    title: '未质检剩余数量',
    dataIndex: 'purchase_qr_num_left',
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? 0 : text
    }
  },
  {
    title: '未采购剩余数量',
    dataIndex: 'purchase_qty_purchased_actual',
    resizable: true
  },
  {
    title: '材质',
    dataIndex: 'material',
    resizable: true
  },
  {
    title: '长',
    dataIndex: 'length',
    resizable: true
  },
  {
    title: '宽',
    dataIndex: 'width',
    resizable: true
  },
  {
    title: '高',
    dataIndex: 'height',
    resizable: true
  },
  {
    title: '海关码',
    dataIndex: 'code',
    resizable: true
  }
]
