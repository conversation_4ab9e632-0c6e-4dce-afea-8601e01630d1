<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleCancel" :loading="buttonlodaing" v-if="hasPermission(525)">批量审核</Button>
        <Button type="primary" @click="handleadd" v-if="hasPermission(529)">新增入库退货单</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
      </template>
      <template #expandedRowRender="{ record: packagerecord }">
        <BasicTable @register="registerChildrenTable" :data-source="packagerecord.package" />
      </template>
    </BasicTable>
    <editDrawer @register="registerDrawer" @success="reload" />
  </div>
</template>
<script setup lang="tsx">
import { getListByInRetreat, setIsCalcelByInRetreat, setStatusByInRetreat } from '/@/api/erp/inwareretreat'
import { columns, packercolumns, searchFormSchema } from './datas/datas'
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import type { ActionItem } from '/@/components/Table'
import { Textarea, Form, message, Button } from 'ant-design-vue'
import { ref } from 'vue'
import { usePermission } from '/@/hooks/web/usePermission'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'

const buttonlodaing = ref(false)
const FormItem = Form.Item
const cancel_remark = ref('')
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const { hasPermission } = usePermission()
const [registerTable, { reload, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
  api: getListByInRetreat,
  columns,
  showIndexColumn: false,
  useSearchForm: true,
  rowKey: 'id',
  afterFetch: async (data) => {
    await clearSelectedRowKeys()
    return data
  },
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas: searchFormSchema,
    fieldMapToTime: [['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  },
  actionColumn: {
    width: 170,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  showTableSetting: true,
  rowSelection: {
    getCheckboxProps: (record) => {
      if (record.status !== 0 || record.is_cancel !== 0) {
        return { disabled: true }
      } else {
        return { disabled: false }
      }
    }
  }
})

const [registerChildrenTable] = useTable({
  showIndexColumn: false,
  useSearchForm: false,
  showTableSetting: false,
  canResize: false,
  maxHeight: 400,
  columns: packercolumns
})
function createActions(record: Recordable): ActionItem[] {
  let buttonList: ActionItem[] = [
    {
      // icon: 'ant-design:eye-outlined',
      label: '审核',
      color: 'success',
      popConfirm: {
        okText: '确定',
        title: '确定将退货单状态设置成完成状态吗',
        cancelText: '取消',
        placement: 'left',
        confirm: handleSetStatus.bind(null, record)
      },
      ifShow: hasPermission([526]),
      disabled: record.status === 15 || record.is_cancel !== 0
    }
  ]

  return buttonList
}

function createDropDownActions(record: Recordable): ActionItem[] {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record)
      //   ifShow: hasPermission([143])
    },
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      disabled: record.status === 15 || record.is_cancel !== 0,
      ifShow: hasPermission([527])
    },
    {
      icon: 'ant-design:delete-outlined',
      color: 'error',
      label: '作废',
      // disabled: record.status !== 0,
      popConfirm: {
        title: (
          <div>
            <Form>
              <FormItem label={'作废备注'} required>
                <Textarea v-model:value={cancel_remark.value} allow-clear />
              </FormItem>
            </Form>
          </div>
        ),
        placement: 'left',
        confirm: handleclearStatus.bind(null, record),
        disabled: record.status === 15 || record.is_cancel !== 0
      },
      ifShow: hasPermission([528])
    }
  ]
}

//审核
async function handleSetStatus(record: Recordable) {
  await setStatusByInRetreat({ ids: [record.id] })
  await reload()
}
//详情
function handleDetail(record: Recordable) {
  console.log(record)
  openDrawer(true, { record, type: 'detail' })
  setDrawerProps({ title: '入库退货详情', showFooter: false })
}
//编辑
function handleEdit(record: Recordable) {
  openDrawer(true, { record, type: 'edit' })
  setDrawerProps({ title: '编辑入库退货', showFooter: true })
}
//add
function handleadd() {
  openDrawer(true, { type: 'add' })
  setDrawerProps({ title: '新增入库退货', showFooter: true })
}

async function handleclearStatus(record) {
  if (!cancel_remark.value) return message.error('请填写作废备注')
  if (record.status !== 0) return message.error('已执行，入库退货单不允许作废')
  await setIsCalcelByInRetreat({ id: record.id, cancel_remark: cancel_remark.value, is_cancel: 1 })
  await reload()
  cancel_remark.value = ''
}

async function handleCancel() {
  try {
    buttonlodaing.value = true
    const selectedRowKeys = getSelectRowKeys()
    if (selectedRowKeys.length == 0) {
      buttonlodaing.value = false
      return message.error('请选择入库退货单')
    }
    const { msg } = await setStatusByInRetreat({ ids: [...selectedRowKeys] })
    if (msg === 'success') {
      setTimeout(() => {
        buttonlodaing.value = false
      }, 1000)
      reload()
    } else {
      buttonlodaing.value = false
    }
  } catch (e) {
    console.log(e)
    buttonlodaing.value = false
  }
}
</script>
