<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="新增入库单" width="90%" @ok="handleOk" show-footer destroy-on-close>
    <ScrollContainer>
      <BasicForm :ref="(el) => (BasicFormRef = el)" @register="registerBasicinfoForm">
        <template #PackagesInfo="{ model }">
          <FormItemRest>
            <div class="flex justify-between items-center">
              <div>
                <Popover v-if="['add', 'edit'].includes(propsData.type)" class="mr-1" trigger="click" title="批量关联已勾选数据的部门">
                  <template #content>
                    <div class="mt-1 justify-between flex">
                      <PagingApiSelect
                        size="small"
                        placeholder="请选择仓库"
                        :pagingMode="true"
                        :api="(params) => transformWarehouseOpt({ ...params }, getWarehouse)"
                        :search-mode="true"
                        return-params-field="id"
                        v-model:value="commonWarehouseInfo.warehouse_id"
                        :select-props="{ fieldNames: { value: 'id', label: 'name' }, placeholder: '请选择', class: 'w-[150px]' }"
                        resultField="items"
                        @change="(val, opt) => (commonWarehouseInfo.warehouse = opt)"
                      />
                      <PagingApiSelect
                        size="small"
                        placeholder="请选择仓位"
                        :pagingMode="true"
                        :api="(params) => transformWarehouseOpt({ ...params, warehouse_id: commonWarehouseInfo.warehouse_id }, getWMI)"
                        :search-mode="true"
                        return-params-field="id"
                        v-model:value="commonWarehouseInfo.warehouse_item_id"
                        :select-props="{ fieldNames: { value: 'id', label: 'name' }, placeholder: '请选择', class: 'w-[150px] !ml-1' }"
                        resultField="items"
                        @change="(val, opt) => (commonWarehouseInfo.warehouse_item = opt)"
                      />
                      <a-button class="ml-1" type="primary" size="small" @click="handleBatchTable"> 确定 </a-button>
                    </div>
                  </template>
                  <a-button type="primary">一键设置仓库仓位</a-button>
                </Popover>

                <Popover v-if="['edit'].includes(propsData.type)" trigger="click" title="选中列表中对应的采购单">
                  <template #content>
                    <div class="mt-1 justify-between flex">
                      <PagingApiSelect
                        v-model:value="selectPackagesList"
                        size="small"
                        placeholder="请选择包裹"
                        :pagingMode="true"
                        :api="getRelatePurchaseList"
                        mode="multiple"
                        resultField="items"
                        :search-mode="true"
                        :params="{
                          status: 1,
                          is_wait: 1
                        }"
                        :allowClear="true"
                        searchParamField="strid"
                        :select-props="{
                          fieldNames: { key: 'work_id', value: 'work_id', label: 'strid' },
                          placeholder: '请选择',
                          class: 'w-[250px]'
                        }"
                        :dropdownRender="
                          ({ menuNode }) => {
                            const vNode = h('div', {}, menuNode)

                            return withDirectives(vNode, [[loadingDirective, loading.value]])
                          }
                        "
                      />
                      <a-button class="ml-1" type="primary" size="small" @click="handlePushPackages"> 确定 </a-button>
                    </div>
                  </template>
                  <a-button type="primary" class="mr-1">添加包裹</a-button>
                </Popover>

                <Popover trigger="click" title="选中列表中对应的采购单">
                  <template #content>
                    <div class="mt-1 justify-between flex">
                      <PagingApiSelect
                        v-model:value="selectPutPackagesList"
                        size="small"
                        placeholder="请选择包裹，输入项目ID可以进行搜索"
                        :pagingMode="true"
                        :api="getPackageList"
                        mode="multiple"
                        resultField="items"
                        :search-mode="true"
                        :params="{
                          is_join: 1
                        }"
                        :allowClear="true"
                        searchParamField="project_number"
                        :select-props="{
                          fieldNames: { key: 'id', value: 'id', label: 'strid' },
                          placeholder: '请选择',
                          class: 'w-[250px]'
                        }"
                        :dropdownRender="
                          ({ menuNode }) => {
                            const vNode = h('div', {}, menuNode)

                            return withDirectives(vNode, [[loadingDirective, loading.value]])
                          }
                        "
                      />
                      <a-button class="ml-1" type="primary" size="small" @click="handlePutPackages"> 确定 </a-button>
                    </div>
                  </template>
                  <a-button type="primary">添加拼货包裹</a-button>
                </Popover>
              </div>

              <span class="text-base font-600">包裹总数：{{ model?.packagesInfo?.length ?? 0 }}</span>
            </div>

            <VxeBasicTable
              :ref="(el) => (VxeTableRef = el)"
              class="!p-0 mt-1"
              :data="model?.packagesInfo ?? []"
              v-bind="gridOptions"
              :editConfig="{ trigger: 'click', mode: 'cell', showStatus: true, enabled: true, showIcon: true }"
              :edit-rules="validRules"
            />
          </FormItemRest>
        </template>
        <template #Purchase>
          <FormItemRest>
            <BasicTable :ref="(el) => (BasicTableRef = el)" @register="registerTable" />
          </FormItemRest>
        </template>
        <template #Put>
          <FormItemRest>
            <BasicTable :ref="(el) => (BasicPutTableRef = el)" @register="registerPutTable" />
          </FormItemRest>
        </template>
        <template #Imgs>
          <Upload
            v-model:file-list="filesList"
            action="/api/oss/putImg"
            list-type="picture-card"
            :custom-request="handleFileRequest"
            :multiple="true"
          >
            <div>
              <plus-outlined />
              <div style="margin-top: 8px">Upload</div>
            </div>
          </Upload>
        </template>
      </BasicForm>
    </ScrollContainer>
  </BasicDrawer>
</template>

<script lang="ts" setup name="AddDrawer">
import { ref, unref, withDirectives, h, watch } from 'vue'
import { Form, Popover, UploadFile, Upload } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { DefaultOptionType } from 'ant-design-vue/es/vc-tree-select/TreeSelect'
import { cloneDeep } from 'lodash-es'
import { BasicTable, useTable } from '/@/components/Table'
import { ScrollContainer } from '/@/components/Container'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
// import { getPurchaseDetail } from '/@/api/erp/purchaseOrder'
import { useMessage } from '/@/hooks/web/useMessage'
import { PagingApiSelect } from '/@/components/Form'
import { editUpdateInWarehouseNew } from '/@/api/erp/mainInWarehouse'
// import { getPurchaseDetail } from '/@/api/erp/purchaseOrder'
import { getWarehouse, getWMI } from '/@/api/baseData/warehouse'
import loadingDirective from '/@/directives/loading'
// import { getStoreList } from '/@/api/commonUtils'
// import { useMapStoreWithOut } from '/@/store/modules/commonMap'
// import type { PropsType } from '../datas/types'
import {
  compSelectGoods,
  getDrawerTableColumns,
  getSchemasList,
  gridOptions,
  validRules,
  BasicFormRef,
  BasicTableRef,
  VxeTableRef,
  selectPurchase,
  transformWarehouseOpt,
  handleBatchTable,
  commonWarehouseInfo,
  loading,
  BasicPutTableRef,
  putTableColumns
} from '../datas/AddDrawer'

import { useRoute } from 'vue-router'
import { VxeBasicTable } from '/@/components/VxeTable'
import { getPackageDetail, getPackageList } from '/@/api/erpFlow/packages'
import { getRelatePurchaseList } from '/@/api/erp/inWarehouse'
import { commonFileUpload } from '/@/api/commonUtils/upload'
// import { useLoading } from '/@/components/Loading'

const route = useRoute()
const routeName = unref(route).name
// const commonMap = useMapStoreWithOut()
// const selectWarehouse = ref<number>()
// const received_at = ref()
const FormItemRest = Form.ItemRest
const emit = defineEmits(['success', 'register'])

//附件
const filesList = ref<UploadFile[]>([])

const { createMessage } = useMessage()

const propsData = ref({ type: '', isUpdate: false })

const selectPackagesList = ref([])

const selectPutPackagesList = ref([])

const prevSelectWork = ref<DefaultOptionType[]>([])

const [registerTable, { setTableData, clearSelectedRowKeys, setColumns }] = useTable({
  showIndexColumn: false,
  columns: getDrawerTableColumns('add', 0).filter((item) => !['status'].includes(item.dataIndex as string)),
  dataSource: [],
  pagination: true,
  canResize: false,
  rowKey: 'id'
  // rowSelection: {}
})

const [registerPutTable, { setTableData: setPutTableData }] = useTable({
  showIndexColumn: false,
  columns: putTableColumns,
  dataSource: [],
  pagination: true,
  canResize: false,
  rowKey: 'id'
  // rowSelection: {}
})

const [registerDrawer, { changeLoading, changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  // changeLoading(true)
  getWarehouseFn()
  clearSelectedRowKeys()
  propsData.value = data
  filesList.value = []
  prevSelectWork.value = []
  selectPurchase.value = {}
  const mapHandler = {
    add: handleAdd,
    edit: handleEdit,
    detail: handleDetail
  }
  mapHandler[data.type](data)
  // changeLoading(false)
})

const [registerBasicinfoForm, { resetFields, updateSchema, validateFields, setFieldsValue, validate, getFieldsValue, resetSchema }] =
  useForm({
    schemas: getSchemasList(true, propsData.value.type, routeName as string),
    baseColProps: { span: 24 },
    showActionButtonGroup: false,
    colon: true,
    labelAlign: 'left',
    labelCol: { style: { width: '120px' } }
  })

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  console.log(file)
  try {
    changeOkLoading(true)
    const curFile = filesList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'mainInWarehouse', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      createMessage.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({
      imgs: filesList.value.filter((item) => item.url).map((item) => item.url)
    })
    // 判断是否所有的文件都已经上传成功，如果有uploading状态的就不修改按钮状态
    const isAllDone = filesList.value.every((item) => item.url)
    if (isAllDone) {
      changeOkLoading(false)
    }
    // changeOkLoading(false)
  } catch (err) {
    // console.log(filesList.value)
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}

watch(
  () => filesList.value,
  (val) => {
    setFieldsValue({ imgs: val.filter((item) => item.url).map((item) => item.url) })
  }
)

async function handlePushPackages() {
  try {
    loading.value = true
    const { packagesInfo } = getFieldsValue()
    const packageIds = packagesInfo?.map((item) => item.packing_package_id) ?? []
    console.log(getFieldsValue())
    const { items } = await getPackageDetail({ pageSize: 3000, purchase_work_ids: selectPackagesList.value })
    for (const work_id of selectPackagesList.value) {
      selectPurchase.value[work_id] = items.filter((item) => {
        // 过滤已经在词入库单，且已取消，已作废，已出库，已备货，已入库，旧包裹的包裹
        const { is_cancel, is_old, is_in, is_out, is_stock, is_scrap } = item
        return (
          !packageIds.includes(item.id) &&
          is_cancel === 0 &&
          is_old === 0 &&
          is_in === 0 &&
          is_out === 0 &&
          is_stock === 0 &&
          is_scrap === 0
        )
      })
      await handlePurchaseOrderChange(work_id, loading, 'select')
    }
    // handlePurchaseOrderChange(val, loading, 'select')
    loading.value = false
    selectPackagesList.value = []
  } catch (err) {
    console.log(err)
  }
}

async function handlePutPackages() {
  try {
    loading.value = true
    const packagesInfo = getFieldsValue().packagesInfo || []

    let hasRepeat = false

    for (const packages of packagesInfo) {
      if (selectPutPackagesList.value.includes(packages.packing_package_id)) {
        createMessage.error(`${packages.pkg_name}已经存在包裹中`)
        hasRepeat = true
        break
      }
    }

    if (hasRepeat) return (loading.value = false)

    const { items } = await getPackageDetail({ pageSize: 1000, ids: selectPutPackagesList.value })
    await setFieldsValue({
      packagesInfo: [
        ...(packagesInfo ?? []),
        ...items.map((packages) => ({
          packing_package_id: packages.id,
          pkg_name: packages.strid,
          purchase_work_id: null,
          source: 1,
          source_uniqids: packages.source_uniqids
          // is_join: packages.is_join
        }))
      ]
    })

    const tableData = items.map((item) => item.items).flat()
    setPutTableData(tableData)

    loading.value = false
    selectPutPackagesList.value = []
  } catch (err) {
    console.log(err)
  }
}

async function handleAdd(data) {
  await resetFields()
  await updateSchema(
    getSchemasList(
      unref(propsData).isUpdate,
      unref(propsData).type,
      routeName as string,
      { validateFields, handlePurchaseOrderChange, getFieldsValue },
      data?.page
    )
  )
  setTableData([])
  if (data.page && data.page === 'purchase') {
    const { work_id } = data.record
    await updateSchema([
      {
        field: 'work',
        componentProps: { defaultOptions: [{ strid: data.record.strid, work_id }] }
      }
    ])
    const { items } = await getPackageDetail({ pageSize: 1000, purchase_work_ids: [work_id] })
    selectPurchase.value[work_id] = items
    handlePurchaseOrderChange(work_id, data.record, 'select')
    await setFieldsValue({
      work: [data.record.work_id]
    })
  }
}

async function handleEdit({ record }) {
  try {
    changeLoading(true)
    const { items } = await getPackageDetail({
      pageSize: 1000,
      ids: record.packageList.map((item) => item.packing_package_id) as number[]
    })

    const joinPurchaseList = items
      .filter((item) => item.is_join)
      .map((item) => item.items)
      .flat(1)
      .map((item) => ({
        ...item,
        warehouse_id: (record.packageList.find((pkg) => pkg.packing_package_id === item.packing_package_id) ?? {}).warehouse_id
      }))

    BasicPutTableRef.value?.tableAction?.setTableData(joinPurchaseList)

    const purchaseList = items
      .filter((item) => !item.is_join)
      .map((item) => item.items)
      .flat(1)
      .map((item) => ({
        ...item,
        warehouse_id: (record.packageList.find((pkg) => pkg.packing_package_id === item.packing_package_id) ?? {}).warehouse_id
      }))

    const workIdList = [...new Set(purchaseList.map((item) => item.purchase_work_id))]
    for (const item of workIdList) {
      selectPurchase.value[item.purchase_work_id] = items
    }
    await resetSchema(getSchemasList(true, propsData.value.type, routeName as string, { getFieldsValue, handlePurchaseOrderChange }))
    await setFieldsValue({
      inCharge: record.inCharge,
      packagesInfo: record.packageList.map((item) => ({
        ...item,
        pkg_name: item.strid,
        warehouse: { id: item.warehouse_id, name: item.warehouse_name },
        warehouse_item: { id: item.warehouse_item_id, name: item.warehouse_item_name },
        source_uniqids: items.find((pkg) => pkg.id === item.packing_package_id)?.source_uniqids
      })),
      packageList: record.packageList.map((item) => item.packing_package_id),
      purchase_list: purchaseList,
      imgs: record.imgs
    })

    filesList.value = record.imgs?.map((img) => ({ url: img, name: img, uid: Math.floor(Math.random() * 1000000) })) ?? []
    updateSchema({
      field: 'packageList',
      componentProps: { defaultOptions: record.packageList.map((item) => ({ strid: item.strid, id: item.packing_package_id })) }
    })
    BasicTableRef.value?.tableAction?.setTableData(purchaseList)
  } catch (err) {
    console.log(err)
  } finally {
    changeLoading(false)
  }
}

function handleDetail(data) {
  setColumns(getDrawerTableColumns('detail', 0).filter((item) => !['status'].includes(item.dataIndex as string)))
  handleEdit(data)
}

// const [openFullLoading, closeFullLoading] = useLoading({
//   tip: '加载中...'
// })

//多选的情况
async function handlePurchaseOrderChange(val, option, type) {
  try {
    const formData = cloneDeep(getFieldsValue())
    if (type === 'select') {
      const goodsIdsArr = compSelectGoods.value.map((item) => item.id)
      const purchase_list = compSelectGoods.value
        .filter((item, idx) => goodsIdsArr.indexOf(item.id) === idx)
        .map((item) => ({
          ...item,
          qty_received: item.qty_received ? item.qty_received : 0,
          warehouse_id: undefined,
          received_at: '',
          status: 0,
          qty_total: item.type === 2 ? item.product.qty_total : item.quantity,
          packing_package_items_id: item.id
        }))
      const curPackages = (formData.packagesInfo || []).map((item) => item.packing_package_id)
      await setFieldsValue({
        purchase_list,
        packagesInfo: [
          ...(formData.packagesInfo ?? []),
          ...selectPurchase.value[val]
            .filter((item) => !curPackages.includes(item.id))
            .map((packages) => ({
              packing_package_id: packages.id,
              pkg_name: packages.strid,
              purchase_work_id: val,
              source: 1,
              source_uniqids: packages.source_uniqids
            }))
        ]
        // packagesInfo: [...(formData.packagesInfo ?? []), { packing_package_id: val, pkg_name: option.strid }]
      })
      setTableData(purchase_list)
    } else {
      const goodsIdsArr = compSelectGoods.value.map((item) => item.id)
      const purchase_list = compSelectGoods.value
        .filter((item, idx) => goodsIdsArr.indexOf(item.id) === idx)
        .map((item) => ({
          ...item,
          qty_received: item.qty_received ? item.qty_received : 0,
          warehouse_id: undefined,
          received_at: '',
          status: 0,
          qty_total: item.type === 2 ? item.product.qty_total : item.quantity,
          packing_package_items_id: item.id
        }))
      // let purchase_list = formData.purchase_list || []
      // purchase_list = purchase_list.filter((item) => item.purchase_work_id !== val)
      await setFieldsValue({
        purchase_list,
        packagesInfo: formData.packagesInfo?.filter((item) => item.purchase_work_id !== val) || []
      })
      setTableData(purchase_list)
    }
  } catch (err) {
    console.log(err)
  } finally {
    // loading.value = false
    // closeFullLoading()
  }
}

// function stopPropagation(e) {
//   e.stopPropagation()
// }

async function handleOk() {
  changeOkLoading(true)
  try {
    let valid
    try {
      valid = await validate()
    } catch (error) {
      const errMsg = error.errorFields.map((item) => item.errors).flat()
      for (const errMessage of errMsg) {
        createMessage.error(errMessage)
      }
    }
    const errMap = await VxeTableRef.value?.validate(true)

    if (errMap) {
      changeOkLoading(false)
      return createMessage.error('请完善包裹信息')
    }

    const { inCharge, packagesInfo, imgs } = valid
    // const { waybill_num, purchase_list, inCharge, packagesInfo } = valid
    // 从商品中反推出work_id,再利用work_id生成走以前的生成逻辑
    // 不能直接使用valid。work，这个没有跟包裹和商品对应
    // const workList = [...new Set(purchase_list.map((item) => item.purchase_work_id))]
    //
    // const inWarehouseList = []
    //
    // for (const work of workList) {
    //   const goodsList = purchase_list.filter((item) => work === item.purchase_work_id)
    //   const workItem = {
    //     doc: {
    //       waybill_num,
    //       work_id: work
    //     },
    //     items: goodsList
    //       .filter((item) => item.purchase_work_id === work)
    //       .map((item) =>
    //         pick(
    //           { ...item, purchase_id: item.item_purchase_id, ...(item?.product ?? {}) },
    //           [
    //             'imgs',
    //             'name',
    //             'puid',
    //             'purchase_id',
    //             'qty_received',
    //             'qty_total',
    //             'received_at',
    //             'request_id',
    //             'status',
    //             'unit',
    //             'unit_price',
    //             'warehouse_id',
    //             'packing_package_id',
    //             'packing_package_items_id'
    //           ].filter((item) => item)
    //         )
    //       )
    //     // packageList: packagesInfo
    //   }
    //   // inWarehouseList.push(workItem)
    // }

    const data = {
      id: propsData.value.type === 'edit' ? propsData.value?.record?.id : void 0,
      inCharge,
      packageList: packagesInfo,
      imgs
      // inWarehouseList
    }
    console.log(data)

    const { msg } = await editUpdateInWarehouseNew(data)

    if (msg === 'success') {
      emit('success')
      createMessage.success('success')
      closeDrawer()
      setTimeout(() => {
        changeOkLoading(false)
      }, 2000)
    }
  } catch (err) {
    changeOkLoading(false)
  }
}

// function handleBatchEdit() {
//   const keys = getSelectRowKeys()
//   const purchase_list = cloneDeep(getFieldsValue().purchase_list)
//   for (const item of purchase_list) {
//     if (keys.includes(item.id)) {
//       // item.warehouse_id = selectWarehouse.value
//       item.received_at = received_at.value
//     }
//   }
//   setFieldsValue({
//     purchase_list
//   })
// }
const warehouseFnData = ref([])
async function getWarehouseFn() {
  const { items }: any = await getWarehouse({ pageSize: 100 })
  console.log(items, 'items')
  warehouseFnData.value = items.map((item) => ({
    label: item.name,
    value: item.id,
    disabled: item.is_disabled == 1
  }))
  return warehouseFnData.value
}
</script>
<style scoped lang="less">
:deep(.ant-col.ant-form-item-label.ant-form-item-label-left) {
  font-weight: 600;
}
</style>
