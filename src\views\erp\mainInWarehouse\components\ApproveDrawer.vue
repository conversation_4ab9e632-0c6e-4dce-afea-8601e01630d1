<template>
  <div>
    <BasicModal @register="registerModal" title="主入库单审核" width="40%" @ok="handleOk" :closeFunc="handleClose">
      <BasicForm @register="registerForm" />
    </BasicModal>
  </div>
</template>

<script setup lang="ts">
import { useModalInner, BasicModal } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { approveInWarehouse } from '/@/api/erp/mainInWarehouse'
import { useMessage } from '/@/hooks/web/useMessage'
import { ref } from 'vue'
import { getPackageDetail } from '/@/api/erpFlow/packages'
import { add } from '/@/utils/math'

const { createMessage } = useMessage()

const emits = defineEmits(['finally'])

const propsData = ref({})

const [registerForm, { validate, setFieldsValue, resetFields }] = useForm({
  labelWidth: 150,
  baseColProps: { span: 12 },
  actionColOptions: { span: 24 },
  showActionButtonGroup: false,
  // autoSubmitOnEnter: true,
  schemas: [
    {
      label: '预计总体积',
      field: 'originVolume',
      component: 'InputNumber',
      dynamicDisabled: true
    },
    {
      label: '包裹总重量',
      field: 'originWeight',
      component: 'InputNumber',
      dynamicDisabled: true
    },
    {
      label: '入库总体积',
      field: 'volume',
      helpMessage: '输入的数值会按比例分摊更新到各个包裹的体积',
      itemHelpMessage: '不填入默认等于预计总体积',
      component: 'InputNumber',
      componentProps: {
        min: 0.01
      }
      // required: true
    },
    {
      label: '入库总重量',
      field: 'weight',
      helpMessage: '输入的数值会按比例分摊更新到各个包裹的重量',
      itemHelpMessage: '不填入默认等于预计总重量',
      component: 'InputNumber',
      componentProps: {
        min: 0.01
      }
      // required: true
    }
  ]
})

const [registerModal, { changeOkLoading, changeLoading, closeModal }] = useModalInner((data) => {
  console.log(data)
  propsData.value = data
  resetFields()
  changeOkLoading(true)
  changeLoading(true)
  handleInit(data)
})

function handleClose() {
  emits('finally')
  return true
}

async function handleInit(data) {
  const { record } = data
  const ids = record.packageList.map((item) => item.packing_package_id)
  const { items } = await getPackageDetail({
    pageSize: 1000,
    ids: ids as number[]
  })

  const { volume, weight } = items.reduce(
    (total, item) => {
      total.volume = add(item.volume, total.volume)
      total.weight = add(item.weight, total.weight)
      return total
    },
    { volume: 0, weight: 0 }
  )

  await setFieldsValue({ originVolume: volume, originWeight: weight })
  changeOkLoading(false)
  changeLoading(false)
}

async function handleOk() {
  try {
    // record.status = 1
    changeOkLoading(true)
    changeLoading(true)

    const { volume, weight } = await validate()
    const { msg } = await approveInWarehouse({ doc_id: propsData.value.record.id, volume, weight, status: 15 })
    if (msg === 'success') {
      createMessage.success('审核成功')
      closeModal()
      return
    }
    createMessage.error('审核失败')
  } catch (err) {
    createMessage.error('审核失败')
  } finally {
    emits('finally')
    setTimeout(() => {
      changeOkLoading(false)
      changeLoading(false)
    }, 2000)
  }
}
</script>
