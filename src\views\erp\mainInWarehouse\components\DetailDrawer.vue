<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="90%" :show-footer="false">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="onExport"><DownloadOutlined />导出excel</a-button>
      </template>
    </BasicTable>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { DownloadOutlined } from '@ant-design/icons-vue'
import { BasicTable, useTable } from '/@/components/Table'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { columns, mapDetailStatusItem } from '../datas/detailModal'
import { aoaToSheetXlsx } from '/@/components/Excel'

const [registerTable, { setTableData }] = useTable({
  showIndexColumn: false,
  columns,
  dataSource: [],
  canResize: false,
  rowKey: 'id',
  pagination: false
})
const arrData = ref([])

const [registerDrawer, { changeLoading, changeOkLoading }] = useDrawerInner(async ({ data }) => {
  try {
    changeLoading(true)
    changeOkLoading(false)
    arrData.value = data ?? []
    setTableData(data)
  } finally {
    changeLoading(false)
  }
})

const onExport = () => {
  const header = columns.map((item) => item.title)
  const mapHeader = columns.map((item) => item.dataIndex)
  const data = arrData.value
    .map((item: any) => ({
      ...item,
      status: Object.keys(mapDetailStatusItem).includes(item.status.toString()) ? mapDetailStatusItem[item.status] : item.status
    }))
    .map((item) => mapHeader.map((key) => item[key]))

  aoaToSheetXlsx({
    data,
    header,
    filename: '二维数组方式导出excel.xlsx'
  })
}
</script>
