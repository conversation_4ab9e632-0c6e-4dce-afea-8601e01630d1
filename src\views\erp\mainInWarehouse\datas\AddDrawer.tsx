import { h, ref, withDirectives, computed, reactive, unref, nextTick } from 'vue'
import { ArrowRightOutlined } from '@ant-design/icons-vue'
import { BasicColumn, BasicTableProps, FormSchema } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { TPage } from '/@/views/erp/purchaseOrder/datas/types'

import { getStaffList } from '/@/api/baseData/staff'
import { getRelatePurchaseList } from '/@/api/erp/inWarehouse'
import loadingDirective from '/@/directives/loading'
import { getPackageDetail } from '/@/api/erpFlow/packages'
import { statusOptions } from '/@/views/erp/inWarehouse/datas/AddDrawer'
import { cloneDeep, isUndefined } from 'lodash-es'
import { PagingApiSelect, Rule } from '/@/components/Form'
import { getWarehouse, getWMI } from '/@/api/baseData/warehouse'
import { VxeTablePropTypes } from 'vxe-table'
import { useMessage } from '/@/hooks/web/useMessage'

export const loading = ref<boolean>(false)
export const compStatus = ref<number>(0)
export const selectPurchase = ref({})
export const BasicFormRef = ref(null)
export const BasicTableRef = ref(null)
export const BasicPutTableRef = ref(null)
export const VxeTableRef = ref(null)
export const commonWarehouseInfo = ref({})
const { createMessage } = useMessage()

export const compSelectGoods = computed(() => {
  let goodsList: any[] = []
  for (const pkg of Object.values(selectPurchase.value)) {
    for (const goods of pkg) {
      goodsList = goodsList.concat(goods.items)
    }
  }
  return goodsList
})

export const getDrawerTableColumns = (type?: string, status?: number): BasicColumn[] => [
  // {
  //   title: '',
  //   dataIndex: 'approve',
  //   width: 50,
  //   ifShow: type === 'add'
  // },
  {
    title: '名称',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  {
    title: '是否子产品',
    dataIndex: 'type',
    width: 100,
    resizable: true,
    customRender: ({ record }) => useRender.renderTag(record.type === 1 ? '否' : '是', record.type === 1 ? 'success' : 'processing')
  },
  {
    title: '所属主产品',
    dataIndex: 'main_name',
    width: 150,
    resizable: true,
    customRender: ({ record }) => (record.type === 2 ? record.product.name : '-')
  },
  {
    title: '图片',
    dataIndex: 'imgs',
    width: 150,
    resizable: true
  },
  {
    title: '产品编号',
    dataIndex: 'puid',
    width: 200,
    resizable: true
  },
  {
    title: '入库状态',
    dataIndex: 'status',
    width: 150,
    resizable: true,
    ifShow: [2, 3].includes(status)
  },
  // {
  //   title: '单价',
  //   dataIndex: 'unit_price',
  //   width: 100,
  //   resizable: true
  //   // ifShow: (routeName === '/erp/mainInWarehouse' && hasPermission(321)) || (routeName === '/erp/inWarehouseNotice' && hasPermission(322))
  //   // ifShow: type === 'add'
  // },

  //下面的是可以编辑的
  // {
  //   title: '仓库',
  //   dataIndex: 'warehouse_id',
  //   width: 150,
  //   resizable: true
  // },
  // {
  //   title: '收到的日期',
  //   dataIndex: 'received_at',
  //   width: 200,
  //   resizable: true,
  //   ifShow: type !== 'detail'
  // },
  {
    title: '入库数量',
    dataIndex: 'qty_total',
    width: 150,
    resizable: true,
    customRender: ({ record }) => (record.type === 2 ? record.product.qty_total : record.quantity)
  },
  {
    title: '已入库商品数',
    dataIndex: 'qty_received',
    width: 150,
    resizable: true,
    ifShow: [2, 3].includes(status as number)
  },
  // {
  //   title: '订单需求数量',
  //   dataIndex: 'qty_request',
  //   width: 100,
  //   resizable: true
  // },
  {
    title: '订单实际需求数量',
    dataIndex: 'qty_request_actual',
    width: 100,
    resizable: true,
    customRender: ({ record }) => (record.type === 2 ? record.product.qty_request_actual : record.qty_request_actual)
  },
  {
    title: '已入库数量',
    dataIndex: 'qty_received_total',
    width: 100,
    resizable: true,
    customRender: ({ record }) => (record.type === 2 ? record.product.qty_received_total : record.qty_received_total)
  },
  {
    title: '订单采购数量',
    dataIndex: 'qty_purchased',
    width: 100,
    resizable: true,
    customRender: ({ record }) => (record.type === 2 ? record.product.qty_purchased : record.qty_purchased)
  },
  {
    title: '总采购商品数',
    // dataIndex: 'qty_purchased',
    dataIndex: 'qty_purchased_total',
    width: 150,
    resizable: true,
    customRender: ({ record }) => (record.type === 2 ? record.product.qty_purchased_total : record.qty_purchased_total)
  }
  // {
  //   title: '要接收的包裹数',
  //   dataIndex: 'pkg_num',
  //   width: 150,
  //   resizable: true
  // },
  // {
  //   title: '本次入库商品数',
  //   dataIndex: 'qty_received',
  //   width: 150,
  //   resizable: true
  // },
]

export const putTableColumns: BasicColumn[] = [
  {
    title: '名称',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  {
    title: '入库数量',
    dataIndex: 'quantity',
    width: 150,
    resizable: true
  }
]

export const getSchemasList = (
  isUpdate: boolean,
  type: string,
  routeName: string,
  handleFn?: { validateFields?: Function; handlePurchaseOrderChange: Function; getFieldsValue: Function },
  page?: TPage
): FormSchema[] => {
  return [
    {
      field: 'status',
      label: '状态',
      component: 'RadioButtonGroup',
      defaultValue: 0,
      render: ({ model }) =>
        h('div', {}, [
          h('span', {}, statusOptions.mapOrderStatus[model.status]?.label ?? '未出库'),
          h(ArrowRightOutlined, { style: 'margin: 0 10px' }),
          h('span', {}, statusOptions.mapOrderStatus[compStatus.value]?.label ?? '未出库')
        ])
    },
    {
      field: 'imgs',
      label: '图片',
      component: 'Upload',
      required: true,
      slot: 'Imgs',
      rules: [
        {
          required: true,
          validator: async (_rule: Rule, value: string) => {
            if (!value || value.length === 0) return Promise.reject('请上传附件')
            return Promise.resolve()
          }
          // trigger: 'change'
        }
      ]
    },
    {
      field: 'inCharge',
      label: '负责人',
      component: 'PagingApiSelect',
      itemProps: {
        validateTrigger: 'blur'
      },
      required: true,
      componentProps() {
        return {
          api: getStaffList,
          resultField: 'items',
          selectProps: {
            allowClear: true,
            fieldNames: { key: 'id', value: 'id', label: 'name' },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name'
            // disabled: type === 'detail'
            // disabled: true
          }
        }
      }
    },
    {
      field: 'waybill_num',
      label: '车牌/快递单',
      component: 'Input',
      componentProps: {
        disabled: !isUpdate || routeName === '/erp/inWarehouseNotice'
      }
    },
    {
      field: 'work',
      label: '关联采购订单',
      component: 'PagingApiSelect',
      helpMessage: '下方的采购单包裹会根据这里选中的采购订单，列出选中采购订单的所有包裹',
      // required: true,
      itemProps: {
        validateTrigger: 'blur'
      },
      ifShow: ['add'].includes(type),
      dynamicDisabled: page === 'purchase',
      componentProps: {
        disabled: !isUpdate,
        api: getRelatePurchaseList,
        params: {
          status: 1,
          // pageSize: 999999,
          is_wait: 1
        },
        searchParamField: 'strid',
        // immediate: false,
        selectProps: {
          fieldNames: { key: 'work_id', value: 'work_id', label: 'strid' },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          disabled: !isUpdate || page === 'purchase',
          mode: 'multiple',
          // labelInValue: true,
          dropdownRender: ({ menuNode }) => {
            const vNode = h('div', {}, menuNode)

            return withDirectives(vNode, [[loadingDirective, loading.value]])
          }
        },
        pagingMode: true,
        searchMode: true,
        resultField: 'items',
        onSelect: async (val) => {
          await nextTick(async () => {
            loading.value = true
            // searchWorkList.value = searchWorkList.value.concat(option)
            const { items } = await getPackageDetail({ pageSize: 1000, purchase_work_ids: [val] })

            selectPurchase.value[val] = items.filter((item) => {
              const { is_cancel, is_old, is_in, is_out, is_stock, is_scrap } = item
              return is_cancel === 0 && is_old === 0 && is_in === 0 && is_out === 0 && is_stock === 0 && is_scrap === 0
            })
            handleFn!.handlePurchaseOrderChange(val, loading, 'select')
            loading.value = false
          })
        },
        onDeselect: (val) => {
          delete selectPurchase.value[val]
          // searchWorkList.value = searchWorkList.value.filter((item) => item.work_id !== val)
          handleFn!.handlePurchaseOrderChange(val, loading, 'deselect')
        },
        onChange: async (val: number) => {
          console.log(val, 'val')

          try {
            // if (['add'].includes(type)) await handleFn!.validateFields!(['work_id'])
            // if (!['add'].includes(type)) handleFn!.handlePurchaseOrderChange(val, loading)
          } catch (e) {
            throw new Error(`${e}`)
          }
        }
      }
    },
    // {
    //   field: 'packageList',
    //   label: '采购单包裹',
    //   required: true,
    //   component: 'PagingApiSelect',
    //   // ifShow: type === 'add',
    //   componentProps: {
    //     api: async (params) => {
    //       const { items } = await getPackageDetail({ ...params, purchase_work_ids: handleFn?.getFieldsValue().work })
    //       return { items: items.filter((item) => !item.is_old && !item.is_cancel && !item.is_out) }
    //     },
    //     searchParamField: 'strid',
    //     // immediate: false,
    //     selectProps: {
    //       fieldNames: { key: 'id', value: 'id', label: 'strid' },
    //       showSearch: true,
    //       placeholder: '先选择上方采购订单',
    //       // allowClear: false,
    //       mode: 'multiple',
    //       dropdownRender: ({ menuNode }) => {
    //         const vNode = h('div', {}, menuNode)
    //
    //         return withDirectives(vNode, [[loadingDirective, loading.value]])
    //       }
    //     },
    //     allowClear: false,
    //     pagingMode: true,
    //     searchMode: true,
    //     resultField: 'items',
    //     returnParamsField: 'purchase_work_ids',
    //     onSelect: (val: number, option) => {
    //       // console.log(option)
    //       selectPkg.value[option.strid] = option
    //       handleFn!.handlePurchaseOrderChange(val, option, 'select')
    //     },
    //     onDeselect: (val: number, option) => {
    //       delete selectPkg.value[option.strid]
    //       handleFn!.handlePurchaseOrderChange(val, option, 'deselect')
    //     }
    //   }
    // },
    {
      field: 'packagesInfo',
      // ifShow: type === 'add',
      label: '包裹信息',
      component: 'Select',
      slot: 'PackagesInfo'
      // required: true,
      // rules: [{ required: true, validator: validatePackages }]
    },
    {
      field: 'purchase_list',
      label: '入库明细',
      component: 'Input',
      slot: 'Purchase'
    },
    {
      field: 'put_list',
      label: '拼货明细',
      component: 'Input',
      slot: 'Put'
    }
  ]
}

// function validatePackages(_rule: Rule, value: Recordable[]) {
//   if (!value || value.length === 0) return Promise.reject('包裹信息必须至少有一个包裹！')
//   return Promise.resolve()
// }

//校验
// function validateGoods(_rule: Rule, value: Recordable[]) {
//   // console.log(value, 'value')
//   if (!value || value.length === 0) return Promise.reject('请先选择关联的采购订单')
//   const validResult = value.every(
//     (item) =>
//       // item.qty_total !== undefined &&
//       !isUndefined(item.received_at) && !isUndefined(item.warehouse_id)
//     // item.pkg_num !== undefined
//   )
//   if (!validResult) return Promise.reject('请完善产品列表内的信息')
//   return Promise.resolve()
// }

export async function transformWarehouseOpt(params, api) {
  try {
    const data = await api(params)
    // const data = await getWarehouse(params)
    for (const ware of data.items) {
      ware.disabled = ware.is_disabled
    }
    return data
  } catch (err) {
    // console.log(err)
  }
}

function handleSelectWarehouse(val, opt, row) {
  row.warehouse_item_id = null
  row.warehouse_item = {}
  row.warehouse = opt
}

function handleSelectWarehouseItem(val, opt, row) {
  row.warehouse_item = opt
}

const packageListColumns = [
  {
    title: '包裹',
    field: 'pkg_name'
  },
  {
    title: '销售单号',
    field: 'source_uniqids',
    slots: {
      default: ({ row }) => {
        return row.source_uniqids ? useRender.renderTags(row.source_uniqids) : '-'
      }
    }
  },
  {
    title: '来源',
    field: 'source',
    width: 100,
    resizable: true,
    ifShow: false
  },
  {
    title: '仓库',
    field: 'warehouse_id',
    editRender: { name: 'ASelect', placeholder: '请点击输入' },
    slots: {
      edit: ({ row }) => (
        <PagingApiSelect
          placeholder="请选择仓库"
          pagingMode={true}
          api={(params) => transformWarehouseOpt({ ...params }, getWarehouse)}
          search-mode={true}
          // pagingSize={500}
          // always-load={true}
          return-params-field="id"
          v-model:value={row.warehouse_id}
          select-props={{ fieldNames: { value: 'id', label: 'name' }, placeholder: '请选择' }}
          resultField="items"
          onChange={(val, opt) => handleSelectWarehouse(val, opt, row)}
        />
      ),
      default: ({ row }) => row?.warehouse?.name
    }
  },
  {
    title: '仓位',
    field: 'warehouse_item_id',
    editRender: { name: 'ASelect', placeholder: '请点击输入' },
    slots: {
      edit: ({ row }) => (
        <PagingApiSelect
          placeholder="请选择仓位"
          pagingMode={true}
          pagingSize={500}
          api={(params) => transformWarehouseOpt({ ...params, warehouse_id: row.warehouse_id }, getWMI)}
          search-mode={true}
          // always-load={true}
          return-params-field="id"
          v-model:value={row.warehouse_item_id}
          select-props={{ fieldNames: { value: 'id', label: 'name' }, placeholder: '请选择' }}
          resultField="items"
          onChange={(val, opt) => handleSelectWarehouseItem(val, opt, row)}
        />
      ),
      default: ({ row }) => row?.warehouse_item?.name
    }
  },
  {
    title: '操作',
    field: 'action',
    slots: {
      default: ({ row, rowIndex }) => (
        <>
          <a-button type="primary" danger size="small" onClick={() => handleRemovePackage(row, rowIndex)}>
            删除
          </a-button>
        </>
      )
    }
  }
]

export function handleBatchTable() {
  // const rows = unref(VxeTableRef)?.getCheckboxRecords()
  const formData = unref(BasicFormRef)?.formActionType?.getFieldsValue()
  const packagesInfo = cloneDeep(formData.packagesInfo)
  if (!packagesInfo?.length) return createMessage.error('当前没有包裹')
  for (const packages of packagesInfo) {
    packages.warehouse_id = commonWarehouseInfo.value.warehouse_id
    packages.warehouse = commonWarehouseInfo.value.warehouse
    packages.warehouse_item_id = commonWarehouseInfo.value.warehouse_item_id
    packages.warehouse_item = commonWarehouseInfo.value.warehouse_item
  }
  unref(BasicFormRef)?.formActionType.setFieldsValue({
    packagesInfo
  })
}

function handleRemovePackage(row, rowIndex) {
  const formData = unref(BasicFormRef)?.formActionType?.getFieldsValue()
  const packagesInfo = cloneDeep(formData.packagesInfo)
  // 判断如果是拼货包裹
  if (row.is_join) {
    packagesInfo.splice(rowIndex, 1)
    unref(BasicFormRef)?.formActionType.setFieldsValue({
      packagesInfo
    })
    const tableAction = unref(BasicPutTableRef)?.tableAction
    const tableData = tableAction.getDataSource()
    const newData = tableData.filter((item) => item.packing_package_id !== row.packing_package_id)
    console.log(unref(BasicPutTableRef))

    tableAction?.setTableData(newData)
    return
  }

  // 下面是采购订单的包裹处理
  let purchase_list = cloneDeep(formData.purchase_list)
  packagesInfo.splice(rowIndex, 1)
  purchase_list = purchase_list.filter((item) => item.packing_package_id !== row.packing_package_id)
  unref(BasicFormRef)?.formActionType.setFieldsValue({
    packagesInfo,
    purchase_list
  })
  unref(BasicTableRef)?.tableAction?.setTableData(purchase_list)

  // 反向判断删除的包裹是不是选中的work最后一个包裹，如果是，则删除work的id
  const packageWorksArr = packagesInfo.filter((packages) => packages.purchase_work_id === row.purchase_work_id)
  if (packageWorksArr.length === 0)
    unref(BasicFormRef)?.formActionType.setFieldsValue({ work: formData.work?.filter((id) => id !== row.purchase_work_id) ?? [] })
  console.log(row, rowIndex)
}

export const gridOptions = reactive<BasicTableProps>({
  id: 'VxeTable',
  loading: loading,
  keepSource: true,
  // editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
  columns: packageListColumns.filter((item) => isUndefined(item.ifShow) || item.ifShow),
  height: 300,
  proxyConfig: null,
  toolbarConfig: null
  // checkboxConfig: {
  //   trigger: 'row'
  // }
})

interface RowFields {
  warehouse_id: number
  warehouse_item_id: number
}

export const validRules = ref<VxeTablePropTypes.EditRules<RowFields>>({
  warehouse_id: [{ required: true, message: '必须选择仓库' }],
  warehouse_item_id: [{ required: true, message: '必须选择仓位' }]
})
