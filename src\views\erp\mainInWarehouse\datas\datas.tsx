import { getStaffList } from '/@/api/baseData/staff'
import { getErpSupplier } from '/@/api/commonUtils'
import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { GET_STATUS_SCHEMA } from '/@/const/status'
import { useI18n } from '/@/hooks/web/useI18n'
import { isNullOrUnDef } from '/@/utils/is'

const { tm } = useI18n()
const state_schema = GET_STATUS_SCHEMA([
  { label: '待执行/未入库', value: 0 },
  { label: '执行中/入库中', value: 1 },
  { label: '已完成/已入库', value: 2 }
])

const source = {
  1: { label: 'pc', color: 'blue' },
  2: { label: '小程序', color: 'green' }
}

export const searchSchemas: FormSchema[] = [
  state_schema,
  {
    field: 'strid',
    label: '入库单号',
    component: 'Input'
  },
  {
    field: 'source_uniqid',
    label: '销售订单号',
    component: 'Input'
  },
  {
    field: 'purchase_strid',
    label: '采购单号',
    component: 'Input'
  },
  {
    field: 'created_at',
    label: '创建时间',
    component: 'SingleRangeDate'
  },
  {
    field: 'status_at',
    label: '审核时间',
    component: 'SingleRangeDate'
  },
  {
    field: 'supplier_id',
    label: '供应商',
    component: 'PagingApiSelect',
    componentProps: () => {
      return {
        api: getErpSupplier,
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        },
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        resultField: 'items'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'is_pay',
    label: '是否已付款',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'creator',
    label: '创建人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'status_inCharge',
    label: '审核人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  }
]

export const columns: BasicColumn[] = [
  // {
  //   title: '回货记录',
  //   dataIndex: 'return_order_no'
  // }
  {
    title: '主入库单号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 150,
    resizable: true,
    customRender: ({ text }) => (mapStatus[text] ? useRender.renderTag(mapStatus[text].label, mapStatus[text].color) : text)
  },
  {
    title: '是否已付款',
    dataIndex: 'is_pay',
    width: 100,
    resizable: true,
    customRender: ({ text }) =>
      tm(`tsg.tagColor`)?.[text] ? useRender.renderTag(tm(`tsg.tagColor`)?.[text].label, tm(`tsg.tagColor`)?.[text].color) : text
  },
  {
    title: '图片',
    dataIndex: 'imgs',
    width: 150,
    resizable: true
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 150,
    resizable: true
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '审核人',
    dataIndex: 'status_inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '供应商',
    dataIndex: 'purchaseList',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      const nameArr = text.map((item) => item.supplier_name)
      return text ? useRender.renderTags(nameArr) : '-'
    }
  },
  {
    title: '审核时间',
    dataIndex: 'status_at',
    width: 150,
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 150,
    resizable: true
  }
]

export const mainTableColumns = [
  {
    title: '包裹箱号',
    dataIndex: 'strid'
  },
  {
    title: '订单来源',
    dataIndex: 'source',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : useRender.renderTag(source[text].label, source[text].color)
    }
  },
  {
    title: '是否已出库',
    dataIndex: 'is_out',
    customRender: ({ text }) => {
      const map = {
        2: { label: '已出库', color: 'green' },
        1: { label: '出库中', color: 'green' },
        0: { label: '未出库', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否已入库',
    dataIndex: 'is_in',
    customRender: ({ text }) => {
      const map = {
        2: { label: '已入库', color: 'green' },
        1: { label: '入库中', color: 'green' },
        0: { label: '未入库', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否作废',
    dataIndex: 'is_cancel',
    customRender: ({ text }) => {
      const map = {
        1: { label: '已作废', color: 'red' },
        0: { label: '未作废', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否已备货',
    dataIndex: 'is_stock',
    customRender: ({ text }) => {
      const map = {
        1: { label: '是', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否拼货',
    dataIndex: 'is_join',
    customRender: ({ text }) => {
      const mapStatus = {
        1: { label: '是', color: 'error' },
        0: { label: '否', color: 'success' }
      }
      return mapStatus[text] ? useRender.renderTag(mapStatus[text].label, mapStatus[text].color) : text
    }
  },
  {
    title: '仓库',
    dataIndex: 'warehouse_name'
  },
  {
    title: '仓位',
    dataIndex: 'warehouse_item_name'
  }
]

export const mapStatus = {
  0: { label: '待执行/未入库', color: 'error' },
  1: { label: '执行中/入库中', color: 'processing' },
  15: { label: '已完成/已入库', color: 'success' }
}
