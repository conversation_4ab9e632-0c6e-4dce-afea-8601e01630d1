import { BasicColumn } from '/@/components/Table'
export const mapDetailStatusItem = {
  0: '未入库',
  1: '已入库',
  2: '已清点'
}
//我导出excel是根据下面的title和dataIndex的
export const columns: BasicColumn[] = [
  {
    title: '名称',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  {
    title: 'unique_id',
    dataIndex: 'uniqid',
    width: 150,
    resizable: true
  },
  {
    title: '产品编号',
    dataIndex: 'puid',
    width: 150,
    resizable: true
  },
  {
    title: '入库状态',
    dataIndex: 'status',
    width: 150,
    resizable: true,
    customRender: ({ value }) => (Object.keys(mapDetailStatusItem).includes(value.toString()) ? mapDetailStatusItem[value] : value)
  },
  {
    title: '单价',
    dataIndex: 'unit_price',
    width: 100,
    resizable: true
  },
  {
    title: '仓库',
    dataIndex: 'warehouse_name',
    width: 150,
    resizable: true
  },
  {
    title: '仓位',
    dataIndex: 'warehouse_item_name',
    width: 150,
    resizable: true
  },
  {
    title: '收到的日期',
    dataIndex: 'received_at',
    width: 200,
    resizable: true
  },
  {
    title: '入库数量',
    dataIndex: 'qty_total',
    width: 150,
    resizable: true
  },
  {
    title: '已入库商品数',
    dataIndex: 'qty_received',
    width: 150,
    resizable: true
  },
  {
    title: '现有库存数量',
    dataIndex: 'qty_stocking',
    width: 150,
    resizable: true
  }
]
