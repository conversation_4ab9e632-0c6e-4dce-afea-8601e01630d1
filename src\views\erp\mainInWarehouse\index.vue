<template>
  <div>
    <BasicTable :data-cachekey="routePath" @register="registerTable">
      <template #toolbar>
        <Button v-if="hasPermission(496)" type="primary" @click="handleaddexpen([], 'more')">
          <PlusOutlined />
          批量生成AGR支出单
        </Button>
        <Button v-if="hasPermission(497)" type="primary" @click="handleBeforeGenerate([], 'more')">
          <PlusOutlined />
          批量生成AGR付款单
        </Button>
        <Button v-if="hasPermission(486)" type="primary" @click="handleAdd">
          <PlusOutlined />
          新增
        </Button>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :stopa-buttonPropagation="true"
            :actions="createActions(record)"
            :drop-down-actions="createDropDownActions(record)"
          />
        </template>
        <template v-if="column.dataIndex === 'imgs'">
          <TableImg :size="60" :simpleShow="true" :imgList="record.imgs" />
        </template>
      </template>
      <template #expandedRowRender="{ record: childrenrecord }">
        <BasicTable class="expand-row-table" :data-source="childrenrecord.packageList" :columns="mainTableColumns" :maxHeight="400" />
      </template>
    </BasicTable>
    <AddDrawer @register="registerAddDrawer" @success="handleAddSuccess" />
    <WarehousingDrawer @register="registerWarehousingDrawer" @success="handleAddSuccess" />
    <PurchaseModal @register="registerModal" @success="handleAddSuccess" />
    <DetailDrawer @register="registerWithoutPackagetDrawer" @success="reload" />
    <ApproveDrawer @register="registerApproveModal" @finally="reload" />
  </div>
</template>

<script setup lang="ts">
import { ActionItem, BasicTable, TableAction, TableImg, useTable } from '/@/components/Table'
import { getMainInWarehouse } from '/@/api/erp/mainInWarehouse'
import { columns, mainTableColumns, searchSchemas } from '/@/views/erp/mainInWarehouse/datas/datas'
import { Button, message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import AddDrawer from './components/AddDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
// import { useMessage } from '/@/hooks/web/useMessage'
import { usePermission } from '/@/hooks/web/usePermission'
import WarehousingDrawer from '../../financialDocuments/otherExpend/components/WarehousingDrawer.vue'
import PurchaseModal from '../purchaseOrder/components/PurchaseModal.vue'
import { useModal } from '/@/components/Modal'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import DetailDrawer from './components/DetailDrawer.vue'
import { getstockList } from '/@/api/erp/inventory'
import ApproveDrawer from '/@/views/erp/mainInWarehouse/components/ApproveDrawer.vue'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'

const route = useRoute()
const { path: routePath } = route

// const { createMessage } = useMessage()

const { hasPermission } = usePermission()

const [registerTable, { getForm, reload, getSelectRows, clearSelectedRowKeys }] = useTable({
  title: '主入库单',
  api: getMainInWarehouse,
  showIndexColumn: false,
  columns,
  useSearchForm: true,
  showTableSetting: true,
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas: searchSchemas,
    fieldMapToTime: [
      ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['inwarehouse_at', ['inwarehouse_at_start', 'inwarehouse_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['status_at', ['status_at_start', 'status_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  },
  actionColumn: {
    width: 340,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  rowKey: 'id',
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: (record) => {
      if (record.status == 0) {
        return { disabled: true }
      } else {
        return { disabled: false }
      }
    }
  },
  afterFetch: (data) => {
    clearSelectedRowKeys()
    return data
  }
})

const [registerApproveModal, { openModal: openApproveModal }] = useModal()
const [registerAddDrawer, { openDrawer: openAddDrawer, setDrawerProps: setAddDrawerProps }] = useDrawer()
const [registerWithoutPackagetDrawer, { openDrawer: openWithoutPackageDrawer, setDrawerProps: setWithoutPackageDrawerProps }] = useDrawer()

function handleAdd() {
  setAddDrawerProps({ title: '新增主入库单', showFooter: true })
  openAddDrawer(true, { isUpdate: true, type: 'add' })
}

function handleEditClick(record) {
  setAddDrawerProps({ title: '编辑主入库单', showFooter: true })
  openAddDrawer(true, { isUpdate: true, type: 'edit', record: record })
}

async function handleDetailWithoutPackage(record) {
  try {
    const { items } = await getstockList({ doc_in_warehouse_header_id: record.id, pageSize: 10000 })

    setWithoutPackageDrawerProps({ title: '入库单详情(无包裹)' })
    openWithoutPackageDrawer(true, { type: 'detailWithoutPackage', data: items })
  } catch (err) {
    message.error('获取入库单详情失败')
    console.error(err)
  } finally {
  }
}

function handleDetailClick(record) {
  setAddDrawerProps({ title: '主入库单详情', showFooter: false })
  openAddDrawer(true, { isUpdate: false, type: 'detail', record: record })
}

async function handleAddSuccess() {
  await getForm().resetFields()
  reload()
}

function handleBeforeApprove(record) {
  record.status = 1
  openApproveModal(true, { record })
}

// async function handleApprove(record) {
//   try {
//     // record.status = 1
//     const { msg } = await approveInWarehouse({ doc_id: record.id, status: 15 })
//     if (msg === 'success') {
//       createMessage.success('审核成功')
//     }
//   } catch (err) {
//     createMessage.error('审核失败')
//   } finally {
//     reload()
//   }
// }

function createActions(record): ActionItem[] {
  return [
    {
      label: '详情(无包裹)',
      onClick: handleDetailWithoutPackage.bind(null, record),
      ifShow: hasPermission(542)
    },
    {
      label: '详情',
      onClick: handleDetailClick.bind(null, record),
      ifShow: hasPermission(487)
    },
    {
      label: '编辑',
      onClick: handleEditClick.bind(null, record),
      disabled: record.status !== 0,
      ifShow: hasPermission(488)
    },
    {
      label: '审核',
      onClick: handleBeforeApprove.bind(null, record),
      // popConfirm:
      //   record.status === 0
      //     ? {
      //         title: '是否通过审核？',
      //         placement: 'left',
      //         confirm: handleApprove.bind(null, record)
      //       }
      //     : void 0,
      disabled: record.status !== 0,
      ifShow: hasPermission(489)
    }
  ]
}

function createDropDownActions(record): ActionItem[] {
  return [
    {
      label: 'AGR生成支出单',
      onClick: handleaddexpen.bind(null, record, 'little'),
      disabled: record.status == 0,
      ifShow: hasPermission([496])
    },
    {
      label: 'AGR生成付款单',
      onClick: handleBeforeGenerate.bind(null, record, 'little'),
      disabled: record.status == 0 || record.is_pay == 1,
      ifShow: hasPermission([497])
    }
  ]
}

//创建支出单
const [registerWarehousingDrawer, { openDrawer: openDrawerWarehousing, setDrawerProps: setDrawerPropswarehousing }] = useDrawer()

async function handleaddexpen(record, type) {
  const purchaseList = ref<any>([])
  if (type === 'more') {
    const tabdata = await getSelectRows()
    if (tabdata.length < 1) return message.error('请选取主入库单')
    tabdata.forEach((item) => {
      purchaseList.value.push(...item.purchaseList)
    })
  }
  openDrawerWarehousing(true, { type: 'inwarehouse', record: type === 'more' ? { purchaseList: purchaseList.value } : record })
  setDrawerPropswarehousing({ title: '新增支出单' })
}

//创建付款单
const [registerModal, { openModal }] = useModal()

function handleBeforeGenerate(record, type) {
  const purchaseList = ref<any>([])
  if (type === 'more') {
    const tabdata = getSelectRows()
    if (tabdata.length < 1) return message.error('请选取主入库单')
    tabdata.forEach((item) => {
      purchaseList.value.push(...item.purchaseList)
    })
    const supplierid = purchaseList.value[0].supplier_id
    if (purchaseList.value.some((item) => Number(item.supplier_id) !== Number(supplierid))) {
      return message.error('请选择主入库单下采购相同的供应商')
    }

    if (tabdata.some((item) => item.is_pay == 1)) {
      return message.error('已经付款的入库单无法再付款')
    }
  }
  const newrecord =
    type === 'more'
      ? purchaseList.value.map((item) => {
          return {
            strid: item.purchase_strid,
            no_amount: item.total_price,
            work_id: item.purchase_work_id,
            supplier_id: item.supplier_id,
            is_ticket: item.is_ticket,
            ticket: item.ticket,
            doc_in_warehouse_header_id: item.doc_in_warehouse_header_id
          }
        })
      : record.purchaseList.map((item) => {
          return {
            strid: item.purchase_strid,
            no_amount: item.total_price,
            work_id: item.purchase_work_id,
            supplier_id: item.supplier_id,
            is_ticket: item.is_ticket,
            ticket: item.ticket,
            doc_in_warehouse_header_id: item.doc_in_warehouse_header_id
          }
        })
  console.log(newrecord)

  openModal(true, {
    type: 'inwarehouse',
    record: newrecord,
    supplierId: newrecord[0].supplier_id,
    dept_id: newrecord[0].dept_id
  })
}
</script>
<style scoped lang="less">
.expand-row-table {
  :deep(.ant-table) {
    margin: 0 !important;
  }
}
</style>
