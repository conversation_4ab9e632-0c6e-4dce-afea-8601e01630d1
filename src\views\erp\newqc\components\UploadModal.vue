<template>
  <BasicModal @register="register" title="附件上传" @ok="handleOk" width="800px">
    <BasicForm @register="registerform">
      <template #Images>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template></BasicForm
    ></BasicModal
  >
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { UploadSchemas } from '../datas/upload.data'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { UploadFile, Upload, message } from 'ant-design-vue'
import { ref, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { QrsetImagesAndVideos } from '/@/api/erp/qc'

//id
const init_id = ref()
const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
  console.log(data)
  filesList.value = data?.images?.map((file) => ({ name: file, url: file, uid: Math.random() * 100000 })) ?? []
  init_id.value = data.id
  setFieldsValue({ videos: data?.videos, images: data?.images })
})
const [registerform, { setFieldsValue, getFieldsValue }] = useForm({
  schemas: UploadSchemas,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelCol: { span: 5 }
})

//附件
const filesList = ref<UploadFile[]>([])

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ images: val.map((item) => item.url) })
  }
)

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    console.log(file)
    changeOkLoading(true)
    const curFile = filesList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({
      images: filesList.value.map((item) => item.url)
    })
    // 判断是否所有的文件都已经上传成功，如果有uploading状态的就不修改按钮状态
    const isAllDone = filesList.value.every((item) => item.url)
    if (isAllDone) {
      changeOkLoading(false)
    }
  } catch (err) {
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}

const emit = defineEmits(['relaod', 'register'])
// 提交
async function handleOk() {
  try {
    await changeOkLoading(true)
    const formdata = await getFieldsValue()
    const params = {
      id: init_id.value,
      images: formdata.images,
      videos: formdata.videos ?? []
    }
    const res: any = await QrsetImagesAndVideos(params)

    if (res.msg == 'success') {
      emit('relaod')
      await closeModal()
      filesList.value = []
    }
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  } finally {
    changeOkLoading(false)
  }
}
</script>
