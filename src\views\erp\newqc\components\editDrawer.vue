<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="90%" show-footer @ok="handleSubmit" destroyOnClose @close="handleClose">
    <BasicForm @register="registerForm">
      <template #deptSlot="{ model }">
        <Select v-model:value="model.dept_id" :options="deptOptions" :disabled="true" />
      </template>
      <template #Images>
        <FormItemRest>
          <Upload
            v-model:file-list="imgFileList"
            accept="image/*"
            action="/api/oss/putImg2Stocking"
            list-type="picture-card"
            :custom-request="handleRequest"
            multiple
            :capture="null"
            @change="handleRemove"
          >
            <div>
              <plus-outlined />
              <div style="margin-top: 8px">Upload</div>
            </div>
          </Upload>
        </FormItemRest>
      </template>
      <template #Items="{ model }">
        <FormItemRest>
          <Alert type="info" show-icon>
            <template #description>
              请勾选 <span class="text-[#ff0000] font-bold" style="font-size: 15px">待质检</span> 的数据作为质检产品
            </template>
          </Alert>
          <VxeBasicTable ref="tableRef" class="!p-0" :data="model.items" v-bind="gridOptions" :checkboxConfig="checkboxConfig">
            <template #Imgs="{ row }">
              <Row :gutter="15">
                <Col v-for="img in row.imgs" :key="img" :span="4">
                  <img :src="img" :alt="row.uniqid" />
                </Col>
              </Row>
            </template>
            <template #QcNum="{ rowIndex }">
              <InputNumber
                v-if="model.items && model.items[rowIndex]"
                :max="model.items[rowIndex].qr_num_left"
                :min="0.01"
                v-model:value="model.items[rowIndex].qr_num"
                :precision="2"
              />
            </template>
            <template #GoodsStatus="{ row }">
              {{ t(`qc.${propsData.qcType === 1 ? 'mapPurchaseStatusQc' : 'mapStockStatusQc'}.${row.status}`) }}
            </template>
            <template #QcStatus="{ row }">
              {{ [0, 1, 2].includes(row.qc_status) ? t(`qc.mapQcStatus.${row.qc_status}`) : row.qc_status }}
            </template>
          </VxeBasicTable>
        </FormItemRest>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<script setup lang="ts" name="DetectionDrawer">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { QualityDetectionItem } from '/@/api/erp/modle/types'
import { getSchemas, gridOptions } from '../datas/edit.data'
import { nextTick, ref, reactive } from 'vue'
import { useForm, BasicForm } from '/@/components/Form'
import { PlusOutlined } from '@ant-design/icons-vue'
import { Upload, UploadFile, Form, UploadChangeParam, Select, Row, Col, Alert, message, InputNumber } from 'ant-design-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonImgUpload } from '/@/api/commonUtils/upload'
import { useMessage } from '/@/hooks/web/useMessage'
import { qrnupdate, qrndetails } from '/@/api/erp/qc'
import { VxeBasicTable, VxeTablePropTypes, VxeGridInstance } from '/@/components/VxeTable'
import { isNull, isUndefined, pick } from 'lodash-es'
import { useI18n } from '/@/hooks/web/useI18n'
import { sub } from '/@/utils/math'

const { t } = useI18n()
// const emits = defineEmits<{
//   success: () => void
//   register: ($event: DrawerInstance) => void
// }>()
//部门选项
const deptOptions = ref<any[]>([])

const tableRef = ref<VxeGridInstance>(null)

const emits = defineEmits(['success', 'register'])

const FormItemRest = Form.ItemRest
const imgFileList = ref<UploadFile[]>([])
const propsData = ref<{
  record?: QualityDetectionItem
  type: 'add' | 'edit'
  qcType: 1 | 2
  purchaseRecord?: QualityDetectionItem
  stockingRecord?: any
}>({})
const { createMessage } = useMessage()
const checkboxConfig = reactive<VxeTablePropTypes.CheckboxConfig<any>>({
  trigger: 'row',
  checkMethod: (data) => {
    return (
      propsData.value.type === 'add' && propsData.value.qcType === 1 && [0, 2].includes(data?.row?.qc_status) && data?.row?.status === 2
    )
  }
})
const [registerDrawer, { changeOkLoading, closeDrawer, changeLoading }] = useDrawerInner(
  async (data: {
    record?: QualityDetectionItem
    type: 'add' | 'edit'
    qcType: 1 | 2
    purchaseRecord?: QualityDetectionItem
    stockingRecord?: any
  }) => {
    try {
      propsData.value = data

      // 参数初始化的核心函数，根据各自不同的逻辑调用不同的函数，不再像以前逻辑耦合在一起
      HandleInit(data)
    } catch (e) {
      throw new Error(e)
    }
  }
)

const [registerForm, { setFieldsValue, validateFields, validate, resetSchema }] = useForm({
  showActionButtonGroup: false,
  labelCol: { span: 2 },
  baseColProps: { span: 24 },
  colon: true
})

async function handleRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    const curFile = imgFileList.value.find((item) => item.uid === file.uid)
    const result = await commonImgUpload(file, 'qualityDetection', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      imgFileList.value = imgFileList.value!.filter((item) => item.url)
      return
    }
    imgFileList.value = imgFileList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({ images: imgFileList.value.map((item) => item.url) })
    await nextTick(async () => {
      try {
        await validateFields(['images'])
      } catch (e) {
        throw new Error(`${e}`)
      }
    })
  } catch (err) {
    if (err.code === 'ERR_NETWORK') imgFileList.value = imgFileList.value!.filter((item) => item.status === 'done' || item.url)
    else imgFileList.value = imgFileList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}

async function handleRemove({ fileList }: UploadChangeParam) {
  await setFieldsValue({ images: fileList.map((item) => item.url) })
}

async function handleSubmit() {
  changeOkLoading(true)
  try {
    const values = await validate()
    const isEmptyImg = values.images.some((item) => isUndefined(item) || isNull(item))
    if (isEmptyImg) {
      changeOkLoading(false)
      return createMessage.error('请等待图片上传完成，再提交！')
    }
    if (tableRef.value.getCheckboxRecords(true).length === 0) {
      changeOkLoading(false)
      return createMessage.error('请勾选待质检的数据作为质检产品')
    }
    let params = { ...values, items: tableRef.value.getCheckboxRecords(true) }
    console.log(params)
    console.log(tableRef.value.getCheckboxRecords(true))

    // 根据不同的type获取保留的字段
    const fields = getParams(propsData.value.type, propsData.value.qcType)
    params.items = params.items.map((item) => pick(item, fields))
    console.log(params)

    const { msg } = await qrnupdate(params)

    if (msg === 'success') {
      handleClose()
      closeDrawer()
      changeOkLoading(false)
      createMessage.success('生成质检报告成功')
      emits('success', { ...propsData.value.record, order_id: propsData.value.order_id })
    }
  } catch (e) {
    changeOkLoading(false)
    throw new Error(e)
  }
}

function handleClose() {
  imgFileList.value = []
}

/**
 * 根据不同的type，获取保留的字段
 * @param {add | edit} type 新增or编辑
 * @param {number} qcType 1：采购，2：库存
 * @returns string[] 各自类型的保留参数
 */
function getParams(type: 'add' | 'edit', qcType: 1 | 2) {
  const mapQcType = {
    1: {
      add: ['request_id', 'purchase_id', 'qr_num'],
      edit: ['request_id', 'purchase_id', 'qr_num', 'id']
    },
    2: {
      add: ['request_id', 'purchase_id', 'item_stocking_id', 'qr_num', 'sale_work_id'],
      edit: ['request_id', 'purchase_id', 'item_stocking_id', 'qr_num', 'sale_work_id', 'id']
    }
  }
  return mapQcType[qcType][type]
}

async function setPurchaseQcOrder({ qcType, purchaseRecord }) {
  await setFieldsValue({
    type: qcType,
    doc_purchase_id: purchaseRecord?.id ?? null,
    purchase_work_id: purchaseRecord.work_id,
    dept_id: purchaseRecord.dept_id
  })
}

async function setStockQcOrder({ qcType, stockingRecord }) {
  const { dept_id, work_id } = stockingRecord[0]
  await setFieldsValue({
    type: qcType,
    items: stockingRecord.map((item) => ({
      ...item,
      item_stocking_id: item.id,
      qr_num: sub(+item.qty_received, +item.qty_return, 2),
      sale_work_id: item.work_id
    })),
    sale_work_id: work_id,
    dept_id: dept_id
  })
  await tableRef.value.setAllCheckboxRow(true)
}

// 新增初始化，分发不同新增操作的处理
async function addInit({ qcType, purchaseRecord, stockingRecord }) {
  await resetSchema(getSchemas('add', { setFieldsValue, tableRef }))
  const mapFn = {
    1: purchaseRecord ? setPurchaseQcOrder.bind(null, { qcType, purchaseRecord }) : null,
    2: stockingRecord ? setStockQcOrder.bind(null, { qcType, stockingRecord }) : null
  }
  mapFn[qcType]?.()
}

// 编辑初始化
async function editInit({ record }) {
  try {
    changeLoading(true)
    const { items: detail } = await qrndetails({ id: record.id })
    propsData.value.qcType = detail.type
    imgFileList.value = detail.images?.map((item) => ({ url: item, name: item, uid: item })) ?? []
    // await setFieldsValue({ images: imgFileList.value.map((item) => item.url) })
    resetSchema(getSchemas('edit', { setFieldsValue, tableRef }))
    await setFieldsValue({ ...detail, items: detail.item })
    await nextTick(() => {
      tableRef.value.setAllCheckboxRow(true)
    })
    changeLoading(false)
    console.log(detail)
  } catch (e) {
    throw new Error(e)
  }
}

// 详情初始化
async function detailInit({ record }) {
  try {
    changeLoading(true)
    const { items: detail } = await qrndetails({ id: record.id })
    imgFileList.value = detail.images?.map((item) => ({ url: item, name: item, uid: item })) ?? []
    // await setFieldsValue({ images: imgFileList.value.map((item) => item.url) })
    resetSchema(getSchemas('detail', { setFieldsValue, tableRef }))
    setFieldsValue({ ...detail, items: detail.item })
    changeLoading(false)
  } catch (e) {
    throw new Error(e)
  }
}

// 分发不同操作的不同设置参数
function HandleInit({
  type,
  qcType,
  purchaseRecord,
  stockingRecord,
  record
}: {
  type: 'add' | 'edit'
  qcType: 1 | 2
  purchaseRecord?: Object[]
  stockingRecord?: Object[]
  record?: Object
}) {
  const mapFn = {
    add: addInit.bind(null, { qcType, purchaseRecord, stockingRecord }),
    edit: editInit.bind(null, { record }),
    detail: detailInit.bind(null, { record })
  }
  mapFn[type]()
}
</script>
