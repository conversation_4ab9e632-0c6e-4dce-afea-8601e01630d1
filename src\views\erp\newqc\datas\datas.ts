import { BasicColumn, FormSchema } from '/@/components/Table'
import { useI18n } from '/@/hooks/web/useI18n'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { isNull, isUndefined, isArray } from 'lodash-es'
import { getStaffList } from '/@/api/baseData/staff'
import { Tag } from 'ant-design-vue'
import { h } from 'vue'
import { isNullOrUnDef } from '/@/utils/is'
import { getDeptSelectTree } from '/@/api/admin/dept'

const { t } = useI18n()

const programstatus = {
  0: { text: '待审核', color: 'blue' },
  1: { text: '审核通过', color: 'green' },
  2: { text: '驳回', color: 'red' }
}
export const columns: BasicColumn[] = [
  {
    dataIndex: 'id',
    title: 'ID',
    resizable: true,
    width: 100
  },
  {
    dataIndex: 'strid',
    title: '质检单号',
    resizable: true,
    width: 200
  },
  {
    dataIndex: 'type',
    title: '质检类型',
    resizable: true,
    width: 100,
    customRender: ({ text }) =>
      !isNull(text) && !isUndefined(text)
        ? useRender.renderTag(t(`qc.mapQcOrderType.${text}.text`), t(`qc.mapQcOrderType.${text}.color`))
        : '-'
  },
  //   {
  //     dataIndex: 'is_engineering',
  //     title: '是否工程订单',
  //     resizable: true,
  //     width: 150,
  //     customRender: ({ text }) => {
  //       return !isNull(text) && !isUndefined(text)
  //         ? useRender.renderTag(t(`tag.tagColor.${text}.label`), t(`tag.tagColor.${text}.color`))
  //         : '-'
  //     }
  //   },
  {
    dataIndex: 'sale_is_audit',
    title: '销售单结算核对状态',
    resizable: true,
    width: 150,
    customRender: ({ text }) => {
      const map = {
        0: { text: '未完成结算', color: 'red' },
        1: { text: '已完成结算', color: 'green' },
        2: { text: '待结算', color: 'blue' }
      }
      return h(Tag, { color: map[text].color }, map[text].text)
    }
  },
  {
    dataIndex: 'sale_audit_at',
    title: '销售单结算核对时间',
    resizable: true,
    width: 200
  },
  {
    dataIndex: 'source_uniqid',
    title: '所属销售单',
    resizable: true,
    width: 200
  },
  {
    dataIndex: 'sale_total_price',
    title: '销售总价',
    resizable: true,
    width: 100
  },
  {
    dataIndex: 'total_amount',
    title: '质检总金额',
    resizable: true,
    width: 100
  },
  {
    dataIndex: 'purchase_strid',
    title: '采购单号',
    resizable: true,
    width: 250
  },
  {
    dataIndex: 'status',
    title: '审核状态',
    resizable: true,
    width: 100,
    customRender: ({ record, text }) =>
      !isNull(text) && !isUndefined(text)
        ? record.is_cancel == 0
          ? useRender.renderTag(t(`tag.approveStatus.${text}.label`), t(`tag.approveStatus.${text}.color`))
          : h(Tag, { color: 'red' }, () => '已作废 失效')
        : '-'
  },
  {
    dataIndex: 'program_status',
    title: '方案经理审核状态',
    resizable: true,
    width: 105,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: programstatus[text].color }, () => programstatus[text].text)
    }
  },
  {
    dataIndex: 'is_cancel',
    title: '是否作废',
    resizable: true,
    width: 100,
    customRender: ({ text }) =>
      !isNull(text) && !isUndefined(text) ? useRender.renderTag(t(`tag.tagColor.${text}.label`), t(`tag.tagColor.${text}.color`)) : '-'
  },
  {
    dataIndex: 'cancel_at',
    title: '作废日期',
    resizable: true,
    width: 120
  },
  {
    dataIndex: 'program_remark',
    title: '方案经理审核备注',
    resizable: true,
    width: 320
  },
  {
    dataIndex: 'cancel_remark',
    title: '作废备注',
    resizable: true,
    width: 320
  },
  {
    dataIndex: 'program_status_at',
    title: '方案经理审核日期',
    resizable: true,
    width: 150
  },
  {
    dataIndex: 'status_at',
    title: '审核日期',
    resizable: true,
    width: 120
  },
  {
    dataIndex: 'double_check',
    title: '是否多次质检',
    resizable: true,
    width: 100,
    customRender: ({ text }) => (!isNull(text) ? t(`qc.mapQcDoubleCheck.${text}`) : '-')
  },
  {
    dataIndex: 'result',
    title: '质检结果',
    resizable: true,
    width: 100,
    customRender: ({ text }) => (!isNull(text) ? t(`qc.mapQcResult.${text}`) : '-')
  },
  // {
  //   dataIndex: 'product',
  //   title: '质检商品',
  //   resizable: true,
  //   width: 150,
  //   ellipsis: false
  // },
  {
    dataIndex: 'qr_type',
    title: '质检方式',
    resizable: true,
    width: 150,
    customRender: ({ text }) => (text ? t(`qc.mapQcType.${text}`) : '-')
  },
  {
    dataIndex: 'content',
    title: '质检内容',
    resizable: true,
    width: 450,
    customRender: ({ record }) =>
      !isNull(record.content) && !isUndefined(record.content) && isArray(record.content) ? useRender.renderTags(record.content) : '-'
  },
  {
    dataIndex: 'qr_stage',
    title: '质检时期',
    resizable: true,
    width: 150,
    customRender: ({ text }) => (text ? t(`qc.mapQcStage.${text}`) : '-')
  },
  {
    dataIndex: 'images',
    title: '图片',
    resizable: true,
    width: 100
  },
  {
    dataIndex: 'dept_name',
    title: '部门',
    resizable: true,
    width: 150
  },
  {
    dataIndex: 'inCharge_name',
    title: '质检人',
    resizable: true,
    width: 200
    // customRender: ({ record }) => commonMap.getMapPerson[record.user_id]
  },
  {
    dataIndex: 'created_at',
    title: '创建时间',
    resizable: true,
    width: 200
  }
]
export const searchSchemas: FormSchema[] = [
  {
    field: 'type',
    label: '质检类型',
    component: 'Select',
    componentProps: {
      options: [
        { label: '采购质检', value: 1 },
        { label: '库存质检', value: 2 }
      ]
    }
  },
  {
    field: 'source_uniqid',
    label: '销售单号',
    component: 'Input'
  },
  {
    field: 'purchase_strid',
    label: '采购单号',
    component: 'Input'
  },
  {
    field: 'strid',
    label: '质检单号',
    component: 'Input'
  },
  {
    field: 'status',
    label: '审批状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '待审批', value: 0 },
        { label: '审批通过', value: 1 }
      ]
    }
  },
  {
    field: 'creator',
    label: '创建人',
    component: 'PagingApiSelect',
    componentProps() {
      return {
        api: getStaffList,
        resultField: 'items',
        selectProps: {
          allowClear: true,
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        }
      }
    }
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptSelectTree,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        treeDefaultExpandAll: true,
        showSearch: true,
        treeLine: {
          showLeafIcon: false
        },
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  {
    field: 'created_at',
    label: '创建时间',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'is_engineering',
    label: '是否工程订单',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'sale_is_audit',
    label: '销售单结算核对状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未完成结算', value: 0 },
        { label: '已完成结算', value: 1 },
        { label: '待结算', value: 2 }
      ]
    }
  },
  {
    field: 'sale_audit_at',
    label: '销售单结算核对时间',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'is_cancel',
    label: '是否作废',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  }
]
export const gbuilderColumns: FormSchema[] = [
  {
    field: 'status',
    label: '状态',
    component: 'RadioButtonGroup',
    required: true,
    defaultValue: 1,
    componentProps: {
      options: [
        {
          label: '通过',
          value: 1
        },
        {
          label: '驳回',
          value: 2
        }
      ]
    }
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    required: true,
    componentProps: {
      placeholder: '请输入原因'
    }
  }
]
