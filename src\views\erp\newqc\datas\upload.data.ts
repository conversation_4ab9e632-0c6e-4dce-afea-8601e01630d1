import { uploadApi } from '/@/api/sys/upload'
import { FormSchema } from '/@/components/Form'

export const UploadSchemas: FormSchema[] = [
  {
    field: 'images',
    label: '图片上传',
    component: 'Upload',
    slot: 'Images',
    rules: [
      {
        required: true,
        message: '请上传图片',
        trigger: 'blur'
      }
    ],
    colProps: {
      span: 24
    }
  },
  {
    field: 'videos',
    label: '视频',
    component: 'Upload',
    componentProps: {
      api: uploadApi,
      maxSize: 200000,
      accept: ['video/*'] // 接受视频文件
    }
  }
]
