<template>
  <div>
    <BasicTable @register="registerTable">
      <template #form-advanceBefore>
        <Button type="primary" class="mr-8px" @click="handleBeforeExport" :loading="isBtnLoding" v-if="hasPermission(596)"
          >条件导出EXCEL</Button
        >
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
        <template v-if="column.dataIndex === 'images'">
          <TableImg :imgList="record.images" :simpleShow="true" />
        </template>
      </template>
    </BasicTable>
    <editDrawer @register="registerDrawer" @success="reload" />
    <UploadModal @register="registerModal" @relaod="reload" />
    <UpdateAtModal @register="registerUpdateAtModal" @success="reload" />
  </div>
</template>
<script setup lang="tsx">
import { columns, gbuilderColumns, searchSchemas } from './datas/datas'
import { BasicTable, useTable, TableImg, TableAction, ActionItem } from '/@/components/Table'
import { usePermission } from '/@/hooks/web/usePermission'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { qrngetList, qrnsetCancel, qrnsetProgramStatus, qrnsetStatus } from '/@/api/erp/qc'
import { message, Textarea, Button } from 'ant-design-vue'
import { QualityDetectionItem } from '/@/api/erp/modle/types'
import UploadModal from './components/UploadModal.vue'
import { useModal } from '/@/components/Modal'
import UpdateAtModal from '/@/views/revisitManage/revisitLog/components/UpdateAtModal.vue'
import * as propertyConst from '/@/views/revisitManage/revisitLog/datas/const'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'
import { ref } from 'vue'
import { BasicForm, useForm } from '/@/components/Form'

const { hasPermission } = usePermission()
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerModal, { openModal: openUploadModal }] = useModal()
const [registerUpdateAtModal, { openModal, setModalProps }] = useModal()
const isBtnLoding = ref(false)
const formRef = ref<any>()
const [registerTable, { reload, setLoading, getForm }] = useTable({
  title: '质检单',
  showIndexColumn: false,
  showTableSetting: true,
  isTreeTable: true,
  rowKey: 'id',
  columns: columns,
  api: qrngetList,
  actionColumn: { width: 350, title: '操作', dataIndex: 'action' },
  useSearchForm: true,
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    alwaysShowLines: 1,
    schemas: searchSchemas,
    fieldMapToTime: [
      ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['sale_audit_at', ['sale_audit_at_start', 'sale_audit_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  }
})

function createActions(record: QualityDetectionItem): ActionItem[] {
  return [
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      disabled: record.status !== 0 || record.is_cancel !== 0 || record.program_status !== 0,
      ifShow: hasPermission(588)
    },
    {
      label: '方案经理审核',
      icon: 'ant-design:file-search-outlined',
      popConfirm: {
        title: (
          <div>
            <div>方案经理审核</div>
            <div>
              <BasicForm
                ref={(el: any) => (formRef.value = el?.formActionType)}
                register={useForm}
                showActionButtonGroup={false}
                schemas={gbuilderColumns}
                baseColProps={{ span: 24 }}
                labelWidth={100}
              />
            </div>
          </div>
        ),
        placement: 'left',
        confirm: handlegrams.bind(null, record),
        disabled: record.status !== 0 || record.is_cancel === 1 || record.result !== 0
      },
      ifShow: hasPermission(712)
    },
    {
      label: '审核',
      icon: 'ant-design:file-search-outlined',
      popConfirm: {
        title: '确定通过审核？',
        placement: 'left',
        confirm: handleApprove.bind(null, record),
        disabled: record.status !== 0 || record.is_cancel === 1 || (record.program_status == 0 && record.result == 0)
      },
      ifShow: hasPermission(589)
    }
  ]
}

function createDropDownActions(record) {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '查看详情',
      onClick: handleView.bind(null, record),
      ifShow: hasPermission(265)
    },
    // {
    //   icon: 'ant-design:delete-outlined',
    //   label: '删除',
    //   popConfirm: {
    //     title: '删除后将无法恢复数据，确定删除吗',
    //     placement: 'left',
    //     confirm: handleDel.bind(null, record),
    //     disabled: record.status !== 0
    //   },
    //   // disabled: record.status !== 0,
    //   ifShow: hasPermission(590)
    // },
    // {
    //   label: '申请延期',
    //   onClick: () => {
    //     setModalProps({ title: '申请延期' })
    //     openModal(true, { work_id: record.work_id, type: propertyConst.QCAPPLYDELAYLABLE, id: record.sale_work_id })
    //   },
    //   tooltip: '需求生产完成日期默认会在填写的交付日期上减五天',
    //   ifShow: hasPermission([288]),
    //   disabled: record.type !== 1 || record.is_cancel !== 0
    //   // disabled: record.follow_up_at ? new Date(record.follow_up_at).getTime() > new Date().getTime() : true
    //   // color: record.follow_up_at ? (new Date(record.follow_up_at).getTime() > new Date().getTime() ? 'success' : 'error') : 'warning'
    // },
    {
      label: '作废',
      icon: 'material-symbols:closed-caption-disabled-outline',
      popConfirm: {
        title: (
          <div class="w-100">
            <div>作废原因</div>
            <Textarea autoSize={false} v-model:value={record.nullifyRemark} placeholder="请输入作废备注" allow-clear />
          </div>
        ),
        placement: 'left',
        confirm: handleNullify.bind(null, record),
        disabled: record.is_cancel === 1
      },
      ifShow: hasPermission(591)
    },
    {
      label: '申请延期',
      onClick: () => {
        setModalProps({ title: '申请延期' })
        openModal(true, { work_id: record.work_id, type: propertyConst.QCAPPLYDELAYLABLE, id: record.sale_work_id })
      },
      tooltip: '需求生产完成日期默认会在填写的交付日期上减五天',
      ifShow: hasPermission([592]),
      disabled: record.type !== 1 || record.is_cancel !== 0
      // disabled: record.follow_up_at ? new Date(record.follow_up_at).getTime() > new Date().getTime() : true
      // color: record.follow_up_at ? (new Date(record.follow_up_at).getTime() > new Date().getTime() ? 'success' : 'error') : 'warning'
    },
    // {
    //   label: '异常填写',
    //   icon: 'material-symbols:closed-caption-disabled-outline',
    //   popConfirm: {
    //     title: (
    //       <div class="w-100">
    //         <div>异常填写</div>
    //         <Textarea autoSize={false} v-model:value={record.qc_abnormal_remark} placeholder="请输入异常原因" allow-clear />
    //       </div>
    //     ),
    //     placement: 'left',
    //     confirm: handleabnormal.bind(null, record),
    //     disabled: record.is_cancel === 1
    //   },
    //   ifShow: hasPermission(592)
    // },
    {
      icon: 'ant-design:upload-outlined',
      color: 'error',
      label: '附件更改',
      onClick: handleUppload.bind(null, record),
      ifShow: hasPermission([593])
    }
  ]
}

function handleEdit(record) {
  setDrawerProps({ title: '编辑质检单', showFooter: true })
  openDrawer(true, { record, type: 'edit' })
}

function handleView(record) {
  setDrawerProps({ title: '查看质检单', showFooter: false })
  openDrawer(true, { record, type: 'detail' })
}

// async function handleDel(record) {
//   try {
//     const { msg } = await qrndeleteReport({ id: record.id })
//     if (msg === 'success') {
//       message.success('删除成功')
//       reload()
//     }
//   } catch (err) {
//     throw new Error(err)
//   }
// }

async function handleApprove(record) {
  try {
    const { msg } = await qrnsetStatus({ id: record.id, status: 1 })
    if (msg === 'success') {
      message.success('审核成功')
      await reload()
    }
  } catch (err) {
    throw new Error(err)
  } finally {
    await reload()
  }
}

async function handleNullify(record) {
  try {
    if (!record.nullifyRemark) return message.error('请先填写作废备注')
    const { msg } = await qrnsetCancel({ id: record.id, cancel_remark: record.nullifyRemark })
    if (msg === 'success') {
      message.success('作废成功')
      reload()
    }
  } catch (err) {
    throw new Error(err)
  }
}

// async function handleabnormal(record) {
//   try {
//     if (!record.qc_abnormal_remark) return message.error('请先填写异常原因')
//     const { msg } = await qrnsetQcAbnormalRemark({ work_id: record.work_id, qc_abnormal_remark: record.qc_abnormal_remark })
//     if (msg === 'success') {
//       message.success('作废成功')
//       reload()
//     }
//   } catch (err) {
//     throw new Error(err)
//   }
// }

function handleUppload(record) {
  openUploadModal(true, record)
}

const handleBeforeExport = async () => {
  try {
    isBtnLoding.value = true
    setLoading(true)
    const params = getForm()?.getFieldsValue()
    const response = await qrngetList({ is_excel: 1, ...params, pageSize: 10000 }, 'blob', { isTransformResponse: false })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    downloadLink.download = `质检-${+new Date()}.xlsx`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    message.success('导出成功')
  } catch (err) {
    message.error('导出失败')
    setLoading(false)
    throw new Error(err)
  } finally {
    setLoading(false)
    isBtnLoding.value = false
  }
}

async function handlegrams(record) {
  try {
    setLoading(true)
    const formdata = await formRef.value?.validate()
    await qrnsetProgramStatus({
      id: record.id,
      program_status: formdata.status,
      program_remark: formdata.remark
    })
    setLoading(false)
    reload()
    formRef.value?.resetFields()
  } catch (e) {
    console.log(e)
    setLoading(false)
  }
}
</script>
