<template>
  <BasicModal
    v-bind="$attrs"
    title="输入订单出货的数量"
    :closeFunc="handleClose"
    :maskClosable="false"
    @register="registerModal"
    @ok="handleSubmit"
  >
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'quantity'">
          <InputNumber :min="0" v-model:value="record['quantity']" :precision="2" />
        </template>
        <template v-if="column.dataIndex === 'pkg_quantity' && !verify">
          <InputNumber :min="0.01" v-model:value="record['pkg_quantity']" :precision="2" />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script setup lang="ts" name="OrderCountModal">
import { ref } from 'vue'
import { InputNumber } from 'ant-design-vue'
import { BasicModal, useModalInner, ModalMethods } from '/@/components/Modal'
import { BasicTable, useTable } from '/@/components/Table'
import { getOrderOutCount, setOrderOutCount, setVerifyCount, verifyOutWarehouse } from '/@/api/erp/outWarehouse'
import { useMessage } from '/@/hooks/web/useMessage'
import { IRecord } from '../datas/types'

const emits = defineEmits<{ (e: 'success'): void; (e: 'register', $event: ModalMethods): void }>()
const columns = [
  {
    dataIndex: 'source_uniqid',
    title: '销售订单号'
  },
  {
    dataIndex: 'pkg_quantity',
    title: '预计出库包裹数'
  },
  {
    dataIndex: 'quantity',
    title: '确认出库包裹数量'
  }
  // {
  //   dataIndex: 'quantity_max',
  //   title: '出货最大数量'
  // },
]
const { createMessage } = useMessage()
const propsData = ref<Partial<IRecord>>({})
const verify = ref<0 | 1>(0)
// const propsItems = ref<Recordable[]>([])
// const compItems = computed<{ [key: number]: Recordable[] }>(() => {
//   const mapItem = {}
//   for (const item of propsItems.value) {
//     //下面这行有问题
//     mapItem[item.work_id] = item
//   }

//   return mapItem
// })

const [registerModal, { setModalProps, closeModal, changeOkLoading }] = useModalInner(async ({ record, isVerify }) => {
  setLoading(true)
  propsData.value = record
  verify.value = isVerify
  setModalProps({ title: isVerify ? '实际出库包裹数' : '预计出库包裹数', okText: isVerify ? '确定' : '通过审核' })

  setColumns(columns.filter((item) => (!isVerify ? ['source_uniqid', 'pkg_quantity'].includes(item.dataIndex) : item)))

  // propsItems.value = record.item
  // console.log(record.item, 'record.item')
  const workIds = [...record.item.map((item: Recordable) => item.work_id)]
  const workIdsArr = [...new Set(workIds)]
  try {
    setLoading(true)
    const { items } = await getOrderOutCount({ work_ids: workIdsArr, doc_id: record.id })

    await setTableData(
      items.map((item) => ({
        ...item,
        pkg_quantity: item.pkg_quantity || 1,
        quantity: +item.quantity > 0 ? +item.quantity : item.pkg_quantity
      }))
    )
    setLoading(false)
  } catch (e) {
    setLoading(false)
    throw new Error(`${e}`)
  }
})

const [registerTable, { setTableData, setLoading, getDataSource, setColumns }] = useTable({
  columns,
  dataSource: [],
  pagination: false,
  showIndexColumn: false,
  scroll: { y: 300 }
})

async function handleClose() {
  setTableData([])
  return true
}

interface ITableData {
  quantity: number
  work_id: string
}

async function handleSetOrderOutCount() {
  const tableData: ITableData[] = getDataSource()
  console.log(tableData, 'tableData')
  const hasEmpty = tableData.some((item) => !item.quantity)
  if (hasEmpty) {
    createMessage.warning('存在 确认出货数量 未填写，请检查')
  } else {
    try {
      setModalProps({ confirmLoading: true })
      for (const item of tableData) {
        //接口定义类型是接受数组,这里传对象
        await setOrderOutCount({ ...item, work_id: Number(item.work_id), doc_id: propsData.value.id! })
      }
      await closeModal()
      createMessage.success('设置成功')
      changeOkLoading(false)
      emits('success')
    } catch (e) {
      changeOkLoading(false)
      throw new Error(`${e}`)
    }
  }
}

async function handleSetVerifyCount() {
  try {
    const tableData = getDataSource()
    const hasEmpty = tableData.some((item) => !item.pkg_quantity)
    if (hasEmpty) {
      createMessage.warning('存在 待出库包裹数量 未填写，请检查')
    } else {
      setModalProps({ confirmLoading: true })
      const { msg } = await verifyOutWarehouse({ doc_id: propsData.value.id, status: 1 })
      if (msg === 'success') {
        createMessage.success('审核成功')
        const { msg: verifyMsg } = await setVerifyCount({
          doc_id: propsData.value.id,
          verify: tableData.map((item) => ({ work_id: item.work_id, pkg_quantity: item.pkg_quantity }))
        })
        if (verifyMsg === 'success') {
          await closeModal()
          createMessage.success('设置待出库包裹数量成功')
          emits('success')
        }
      }
    }
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  }
}

async function handleSubmit() {
  try {
    await changeOkLoading(true)
    verify.value ? await handleSetOrderOutCount() : await handleSetVerifyCount()
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  } finally {
    changeOkLoading(false)
  }
}
</script>

<style scoped lang="less"></style>
