<template>
  <div>
    <BasicDrawer
      v-bind="$attrs"
      @register="registerDrawer"
      destroyOnClose
      title="出库单"
      width="90%"
      @ok="handleVerify"
      @close="handleInit"
    >
      <BasicForm @register="registerForm">
        <template #FilesSlot>
          <Upload
            v-model:file-list="filesList"
            action="/api/oss/putImg2Stocking"
            :custom-request="handleFileRequest"
            :disabled="propsData.type === 'view' || [1, 2].includes(propsData.record?.status)"
            :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
          >
            <a-button type="primary">
              <upload-outlined />
              Upload
            </a-button>
          </Upload>
        </template>
        <template #relate_detail="{ model }">
          <FormItemRest>
            <VxeBasicTable
              :ref="(el) => (VxeTableRef = el)"
              class="!p-0 mt-1"
              :data="model?.relate_detail ?? []"
              v-bind="gridOptions"
              :editConfig="{ trigger: 'click', mode: 'cell', showStatus: true, enabled: true, showIcon: true }"
              :edit-rules="validRules"
            />
          </FormItemRest>
        </template>
      </BasicForm>
    </BasicDrawer>
    <VerifyModal @register="registerVerifyModal" @success="handleVerifyConfirm" @cancel="handleCancel" />
  </div>
</template>

<script lang="ts" setup name="OutWarehouseDrawer">
import { ref, watch } from 'vue'
import { Upload, Form, message } from 'ant-design-vue'
import type { UploadFile } from 'ant-design-vue'
import { UploadOutlined } from '@ant-design/icons-vue'
import { cloneDeep } from 'lodash-es'
import { BasicDrawer, DrawerInstance, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { addOutWarehouse, verifyOutOrder } from '/@/api/erp/outWarehouse'
import { useMessage } from '/@/hooks/web/useMessage'
// import { useMapStoreWithOut } from '/@/store/modules/commonMap'
import {
  getFormSchemas,
  treeSelectLoadedKeys,
  productItems,
  propsData,
  VxeTableRef,
  gridOptions,
  validRules,
  compPackagesDetails,
  compPackages
} from '../datas/pkgOutWarehouse.datas'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import VerifyModal from './VerifyOutOrder.vue'
import { useModal } from '/@/components/Modal'
import { add } from '/@/utils/math'
// import { useI18n } from '/@/hooks/web/useI18n'
import defaultUser from '/@/utils/erp/defaultUser'
import { VxeBasicTable } from '/@/components/VxeTable'
import { getPackageDetail } from '/@/api/erpFlow/packages'

// const { t } = useI18n()

const FormItemRest = Form.ItemRest
const emit = defineEmits<{ (e: 'success'): void; (e: 'register', $event: DrawerInstance): void }>()
const { createMessage } = useMessage()
// const propsData = ref<{ type: 'add' | 'view' | 'edit'; record?: Recordable }>({ type: 'add' })
// const getMapItemRequest = toRef(useMapStoreWithOut(), 'getMapItemRequest')
// const commonDeptId = ref<number>()

//编辑时的商品数据
// const productItems = ref<Recordable[]>([])
const filesList = ref<UploadFile[]>([])

const [registerVerifyModal, { openModal, closeModal, setModalProps }] = useModal()
const [registerDrawer, { changeOkLoading, closeDrawer, changeLoading }] = useDrawerInner(
  async (data: { type: 'add' | 'edit' | 'view' }) => {
    propsData.value = data
    const mapInitFunc = {
      add: handleAdd,
      edit: handleEdit,
      view: handleEdit
    }

    await mapInitFunc[data.type]?.(data)
  }
)

const [registerForm, { setFieldsValue, validate }] = useForm({
  baseColProps: { span: 12 },
  showActionButtonGroup: false,
  colon: true,
  labelAlign: 'left',
  labelCol: { style: { width: '100px', marginLeft: '10px' } },
  schemas: getFormSchemas()
})

// async function handleDelete(record, index) {
//   try {
//     console.log(index)
//     const { relate_detail } = getFieldsValue()
//     if (relate_detail.length < 2) {
//       message.error('删除失败,至少保留一个商品!!')
//       return
//     }
//     await deleteGoodsDetail(record.id)
//     relate_detail.splice(index, 1)
//     await setFieldsValue({ relate_detail })
//
//     deleteTableDataRecord(record.id)
//   } catch (err) {
//     console.log(err, '删除失败原因')
//   }
// }

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    changeOkLoading(true)
    const curFile = filesList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'outWarehouse', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({ files: filesList.value.map((item) => item.url) })
    // 判断是否所有的文件都已经上传成功，如果有uploading状态的就不修改按钮状态
    const isAllDone = filesList.value.every((item) => item.url)
    if (isAllDone) {
      changeOkLoading(false)
    }
  } catch (err) {
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}

watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val })
  }
)

async function handleOk() {
  /**
   * 新增：type: 1，正常创建
   * 编辑：type：2，
   * 删除：type：3
   */
  changeOkLoading(true)
  try {
    const data = await getSubmitData()
    // 存在productItems，但不存在formData为删除：3
    for (const item of productItems.value) {
      // if (`${item.stocking_id}-child`)
      const curItems = data.items.find((dataItems) => item.stocking_id === dataItems.stocking_id)
      if (!curItems) {
        data.items.push({ ...item, type: 3 })
      }
    }
    const result = await addOutWarehouse(data)
    emit('success')
    createMessage.success(result.msg)
    handleInit()
    closeDrawer()
    setTimeout(() => {
      changeOkLoading(false)
      changeLoading(false)
    }, 3000)
    setModalProps({ confirmLoading: false })
  } catch (e) {
    changeOkLoading(false)
    changeLoading(false)
    setModalProps({ confirmLoading: false })
    throw new Error(`${e}`)
  }
}

// function handleSetAllDept() {
//   const selectKeys = getSelectRowKeys()
//   const relate_detail = getFieldsValue().relate_detail
//   for (const item of relate_detail) {
//     if (selectKeys.includes(item.stocking_id)) {
//       item.dept_id = commonDeptId.value
//     }
//   }
//   setFieldsValue({ relate_detail })
// }

// function handleShowFilter(isVisible) {
//   // if (isVisible) {
//   //   showFilterList.value.push('name')
//   // }
//   console.log(isVisible, showFilterList.value)
// }

function handleInit() {
  treeSelectLoadedKeys.value = []
  productItems.value = []
  propsData.value = { type: 'add' }
}

async function getSubmitData(): Promise<{ doc: any; items: any[]; packageList: number[] }> {
  try {
    const values = await validate()
    // const doc_plan_out_warehouse_id = propsData.value?.record?.doc_plan_out_warehouse_id
    const {
      project_number,
      project_name,
      project_inCharge,
      delivery_incharge,
      deliverer,
      relate_detail,
      applicant,
      inCharge,
      remark,
      checkout_at,
      confirmed_at,
      id,
      files,
      shipment_inCharge,
      urgent_level,
      shipment_type
    } = values
    const commonDoc = {
      project_number,
      project_name,
      project_inCharge,
      delivery_incharge,
      deliverer,
      applicant,
      inCharge,
      remark,
      checkout_at,
      confirmed_at,
      shipment_inCharge,
      urgent_level,
      shipment_type,
      // doc_plan_out_warehouse_id,
      files: files.map((item: UploadFile) => item.url)
    }

    // 用productItems的数据判断新增、编辑、删除
    // 存在productItems也存在formData中为编辑：2
    // 不存在productItems，但存在formData为新增：1
    return {
      doc: propsData.value.type === 'add' ? commonDoc : { ...commonDoc, id },
      items: relate_detail.map((item: Recordable) => {
        const { work_id, quantity, dept_id, stocking_id, request_id, warehouse_id, name } = item
        const curItems = productItems.value.find((product) => `${product.stocking_id}-child` === item.value)
        return {
          work_id,
          request_id,
          // batch_code,
          dept_id,
          quantity,
          stocking_id,
          warehouse_id,
          name,
          status: propsData.value.type === 'add' ? 0 : item.status,
          id: ['edit'].includes(propsData.value.type) ? curItems?.id : void 0,
          type: curItems ? 2 : 1
        }
        // return propsData.value.type === 'add' ? { ...commonItems } : { ...commonItems， id }
      }),
      packageList: (propsData.value?.packings || [])
        .map((item) => item.packages)
        .flat()
        .map((item) => item.id)
    }
  } catch (e) {
    throw new Error(`${e}`)
  }
}

// 编辑提交前，验证剩余的订单是否存在没有出库的商品，且有没有存在超量出货的问题
async function handleVerify() {
  changeOkLoading(true)
  changeLoading(true)
  try {
    if (propsData.value.record?.id) {
      const formData = await getSubmitData()
      const { items } = await verifyOutOrder(propsData.value.record?.id)
      let summaryList = []
      // let requestList = []
      for (const work of items) {
        // 汇总当前的work的request商品列表
        summaryList = [...summaryList, ...work.itemsData]
      }

      for (const summary of summaryList) {
        const formTotalQuantity = formData.items.reduce(
          (total, item) => (item.request_id === summary.request_id ? add(total, item.quantity) : total),
          0
        )
        // const outCount = sub(summary.qty_received, summary.qty_stocking, 2)
        // outCount: 已出库数量（已清点出库） = 实际入库商品数 - 现有商品数
        // summary.outCount = outCount
        // unOutCount：未出库数量 = 商品需求实际数量 - 已出库数量
        // summary.unOutCount = sub(summary.qty_request_actual, outCount, 2)
        summary.formOutCount = formTotalQuantity
        summary.isPass =
          +summary.noOutWarehouseNum === +formTotalQuantity && +summary.noPurchaseNum === 0 && +summary.noInWarehouseNum === 0
      }
      const isPass = items.every((item) => {
        const workPass = item.itemsData.every((request) => request.isPass)
        item.workPass = workPass
        return workPass
      })
      // console.log(items, formData, isPass)
      if (isPass) return handleOk()
      openModal(true, { verifyData: items })
    } else {
      await handleOk()
    }
  } catch (e) {
    handleCancel()
    throw new Error(`${e}`)
  }
}

function handleVerifyConfirm() {
  closeModal()
  handleOk()
}

function handleCancel() {
  changeOkLoading(false)
  changeLoading(false)
}

async function handleAdd(data) {
  try {
    changeLoading(true)
    changeOkLoading(true)
    filesList.value = []
    const { packings } = data

    const { items } = await getPackageDetail({ packing_ids: packings.map((item) => item.id) })
    compPackages.value = items

    const [firstPackage] = packings
    await setFieldsValue({
      // 申请人和负责人默认为自己
      applicant: defaultUser!.userId,
      inCharge: defaultUser!.userId,
      // 设置项目和项目ID
      project_number: firstPackage?.project_number ?? void 0,
      project_name: firstPackage?.project_name ?? void 0,
      // 新增增加一个默认状态，然后type默认为新增，item_type，则为主/子产品
      relate_detail: cloneDeep(compPackagesDetails.value).map((item) => ({
        ...item,
        status: 0,
        type: 1,
        ...(item.type === 2 ? item.product : {}),
        work_id: item.type === 2 ? item.product.sale_work_id : item.sale_work_id
      }))
    })
  } catch (err) {
    console.log(err)
    createMessage.error('新建出库单初始化失败，请联系管理员！')
  } finally {
    changeLoading(false)
    changeOkLoading(false)
  }
}

async function handleEdit() {}
</script>
<style scoped lang="less">
:deep(.ant-col.ant-form-item-label.ant-form-item-label-left) {
  font-weight: 600;
}

.right-container-row {
  display: flex;
  justify-content: space-between;
}

.divider-title {
  font-weight: 900;
}
</style>
