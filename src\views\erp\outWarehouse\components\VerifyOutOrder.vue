<template>
  <BasicModal v-bind="$attrs" title="提示" width="50%" @register="registerModal" @ok="handleOk" @cancel="handleCancel">
    <Alert
      message="订单异常"
      description="出库涉及的销售订单存在异常，请核查，如需继续出库请点击“确定”，返回点击“取消”"
      type="warning"
      show-icon
    />
    <scroll-container style="height: 50vh">
      <div v-for="work in listData" :key="work.work_id" class="mt-5">
        <List v-if="!work.workPass" :data-source="work.itemsData">
          <template #renderItem="{ item }">
            <ListItem v-if="!item.isPass">
              <div class="flex justify-between items-center w-full">
                <div class="w-1/4 text-center"
                  >商品名称：<span class="text-[#999999]">{{ item.name }}</span></div
                >
                <div class="w-1/4 text-center"
                  >未采购数量：<span class="text-[#52c41a]">{{ item?.noPurchaseNum ?? 0 }}</span></div
                >
                <div class="w-1/4 text-center"
                  >未入库数量：<span class="text-[#52c41a]">{{ item?.noInWarehouseNum ?? 0 }}</span></div
                >
                <div class="w-1/4 text-center"
                  >未出库数量：<span class="text-[#52c41a]">{{ item?.noOutWarehouseNum ?? 0 }}</span></div
                >
                <div class="w-1/4 text-center"
                  >订单剩余需求：<span class="text-[#faad14]">{{ item?.qty_request_left ?? 0 }}</span></div
                >
                <div class="w-1/4 text-center"
                  >本次出库：<span class="text-[#1890ff]">{{ item?.formOutCount || '-' }}</span></div
                >
              </div>
            </ListItem>
          </template>
          <template #header>
            <div
              ><span class="text-[#ff0000]">{{ work.source_uniqid }}</span
              >订单存在异常</div
            >
          </template>
        </List>
      </div>
    </scroll-container>
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, ModalMethods, useModalInner } from '/@/components/Modal'
import { ScrollContainer } from '/@/components/Container'
import { List, ListItem, Alert } from 'ant-design-vue'
import { ref } from 'vue'

const emits = defineEmits<{ (e: 'success'): void; (e: 'register', $event: ModalMethods): void; (e: 'cancel'): void }>()
const listData = ref([])
const [registerModal, { changeOkLoading, closeModal }] = useModalInner((data) => {
  changeOkLoading(false)
  listData.value = data.verifyData
})

async function handleOk() {
  await changeOkLoading(true)
  emits('success')
  closeModal()
  // changeOkLoading(false)
}

function handleCancel() {
  emits('cancel')
  closeModal()
}
</script>
