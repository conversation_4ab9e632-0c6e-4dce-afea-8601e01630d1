<template>
  <div>
    <Card class="cart-item" hoverable :bodyStyle="{}">
      <!-- 标题部分start -->
      <template #title>
        <div class="title">
          <div class="title-item">
            <div class="tit-tit">{{ isInWarehouse ? '收货日期' : '出库日期' }}</div>
            <div class="date tit-content">{{ cardData.checkout_at }}</div>
          </div>
          <div class="card-tag">
            <Tag style="font-size: 16px" :color="mapStatus[cardData.status]?.color"> {{ mapStatus[cardData.status]?.label }}</Tag>
          </div>
        </div>
      </template>
      <!-- 标题部分end -->

      <!-- 卡片主体部分start -->
      <div class="card-main">
        <div class="dept-bar">
          <div class="dept-left" v-if="isInWarehouse"><span>部门:</span>{{ cardData.department }}</div>
          <div class="dept-left" v-else><span>ERP出库单号:</span>{{ cardData.strid }}</div>
        </div>

        <!-- 卡片内部表格部分start -->
        <Table :scroll="{ x: '100%', y: 300 }" :columns="detailTableColumns" :data-source="cardData.item" :pagination="false" bordered>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'name'">
              {{ mapItemStocking[record.stocking_id] ? mapItemStocking[record.stocking_id].name : '-' }}
            </template>
            <!-- <template v-if="column.dataIndex === 'puid'">
              {{ mapItemStocking[record.stocking_id] ? mapItemStocking[record.stocking_id].puid : '-' }}
            </template> -->
            <template v-if="column.dataIndex === 'dept_name'">
              {{ mapDept[record.dept_id] ? mapDept[record.dept_id] : '-' }}
            </template>
          </template>
        </Table>
        <!-- 卡片内部表格部分end -->

        <!-- 装柜要求的插槽 -->
        <slot name="request"> </slot>
      </div>
      <!-- 卡片主体部分end -->

      <template #actions>
        <span v-if="hasPermission([75])" @click="handleEmits('viewClick')"><FormOutlined /> 详情</span>
        <span v-if="showEditBtn" @click="handleEmits('detailClick')"><FormOutlined />编辑</span>
        <span v-if="cardData.status === 0 && hasPermission([78])">
          <Popconfirm title="通过审核后无法重置，且部分数据无法编辑；确定通过审核吗" @confirm="handleEmits('approve')"
            ><CheckOutlined /> 通过审核</Popconfirm
          >
        </span>
        <!--        <span v-if="cardData.status === 2 && hasPermission([80])" @click="handleEmits('setOrderCount')">-->
        <!--					<FormOutlined /> 订单出货数量-->
        <!--				</span>-->
        <span v-if="cardData.status === 0 && hasPermission([77])">
          <Popconfirm title="删除后将无法恢复数据，确定删除吗" @confirm="handleEmits('deleteClick')"><DeleteOutlined /> 删除</Popconfirm>
        </span>
      </template>
    </Card>
  </div>
</template>

<script setup lang="ts" name="WarehouseOrderCard">
import { Card, Popconfirm, Table, Tag } from 'ant-design-vue'
import { FormOutlined, DeleteOutlined, CheckOutlined } from '@ant-design/icons-vue'
import { detailTableColumns } from '../datas/datas'
import { OutWarehouseParams } from '/@/api/erp/modle/types'
import { mapDept, mapItemStocking } from '../datas/datas'
import { usePermission } from '/@/hooks/web/usePermission'
import { computed } from 'vue'
import { mapStatus } from '../datas/datas'

const { hasPermission } = usePermission()
const props = withDefaults(
  defineProps<{
    isInWarehouse: boolean
    cardData: OutWarehouseParams
  }>(),
  {
    isInWarehouse: false // 默认值为true
  }
)
const showEditBtn = computed<boolean>(
  () => (props.cardData.status === 0 && hasPermission([76])) || (props.cardData.status === 1 && hasPermission([79]))
)

const emits = defineEmits(['setOrderCount', 'detailClick', 'deleteClick', 'approve', 'viewClick'])

type Tmodal = 'setOrderCount' | 'detailClick' | 'deleteClick' | 'approve' | 'viewClick'

function handleEmits(model: Tmodal) {
  emits(model)
}
</script>

<style scoped lang="less">
.cart-item {
  font-size: 14px;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .tit-tit {
      text-align: left;
    }
    .tit-content {
      font-size: 13px;
    }
    .date {
      color: #808080;
    }
    .no {
      color: #f81d22;
    }
  }
  .card-main {
    .dept-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .dept-left {
        flex: 1;
        text-align: left;
      }
      .dept-right {
        display: flex;
        justify-content: space-between;
        color: #1890ff;
        //width: 40%;
        .img:hover,
        .detail:hover {
          color: #0561b8;
        }
      }
    }
    .card-table {
      text-align: center;
      tr {
        td:last-child {
          max-width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .info {
      text-align: left;
      margin: 12px 0;
      .info-key-value {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .key {
          font-weight: 600;
        }
      }
    }
    .status-bar {
      color: #f81d22;
      font-size: 16px;
      font-weight: 600;
      text-align: left;
    }
    .status-bar.active {
      color: #87d068;
    }
    .download-btn {
      display: flex;
      justify-content: flex-start;
      font-size: 20px;

      .btn {
        color: #1890ff;
        &:hover {
          color: #0561b8;
        }
      }
    }
  }
}
</style>
