<template>
  <BasicModal @register="registerModal" defaultFullscreen title="包裹信息" @ok="closeModal">
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicTable, useTable } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'

const [registerModal, { closeModal }] = useModalInner((data) => {
  const { record } = data
  setTableData(record)
})
const columns = [
  {
    title: '装箱单号',
    dataIndex: 'packing_strid',
    resizable: true
  },
  {
    title: '包裹箱号',
    dataIndex: 'strid',
    resizable: true
  },
  {
    title: '是否已出库',
    dataIndex: 'is_out',
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        2: { label: '已出库', color: 'green' },
        1: { label: '出库中', color: 'green' },
        0: { label: '未出库', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },

  {
    title: '是否已入库',
    dataIndex: 'is_in',
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        2: { label: '已入库', color: 'green' },
        1: { label: '入库中', color: 'green' },
        0: { label: '未入库', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否作废',
    dataIndex: 'is_cancel',
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        1: { label: '已作废', color: 'red' },
        0: { label: '未作废', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否已备货',
    dataIndex: 'is_stock',
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        1: { label: '是', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否拼货',
    dataIndex: 'is_join',
    resizable: true,
    customRender: ({ text }) => {
      const mapStatus = {
        1: { label: '是', color: 'error' },
        0: { label: '否', color: 'success' }
      }
      return mapStatus[text] ? useRender.renderTag(mapStatus[text].label, mapStatus[text].color) : text
    }
  }
  // {
  //   title: '仓库',
  //   dataIndex: 'warehouse_name',
  //   resizable: true
  // },
  // {
  //   title: '仓位',
  //   dataIndex: 'warehouse_item_name',
  //   resizable: true
  // }
]
const [registerTable, { setTableData }] = useTable({
  columns,
  showIndexColumn: false,
  rowKey: 'id',
  immediate: false
})
</script>
