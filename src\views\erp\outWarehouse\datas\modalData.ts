import { BasicColumn } from '/@/components/Table'
import { mapDept } from '../datas/datas'
import { ColumnType } from 'ant-design-vue/es/table'
import { FormSchema } from '/@/components/Form'
import { Rule } from 'ant-design-vue/lib/form'
const mapSrc: { [key: number]: string } = {
  1: '采购',
  2: '转换',
  3: '盘点',
  4: '入库'
}

export const leftTableColumns: BasicColumn[] = [
  {
    title: '所属订单',
    dataIndex: 'from_at',
    width: 200
  },
  {
    title: '所属订单商品名称',
    dataIndex: 'name',
    width: 150
  },
  {
    title: '所属订单商品批次号',
    dataIndex: 'batch_code',
    width: 200,
    resizable: true
  },
  // {
  //   title: '出库数量',
  //   dataIndex: 'quantity',
  //   resizable: true,
  //   width: 150
  // },
  {
    title: '关联仓库商品',
    dataIndex: 'stocking',
    width: 250,
    customRender: ({ record }) => (record.stocking ? record.stocking.label : '')
  },
  {
    title: '部门',
    dataIndex: 'dept_id',
    width: 250,
    customRender: ({ record }) => (record.dept_id ? mapDept.value[record.dept_id] : '')
  }
  // {
  //   title: '部门id',
  //   dataIndex: 'dept_id',
  //   width: 100
  // }
]

export const rightContainerTableColumns: ColumnType[] = [
  {
    title: '商品名称',
    dataIndex: 'name',
    width: 200,
    key: 'name',
    customFilterDropdown: true,
    // filterDropdownVisible: showFilterList.value.includes('name'),
    onFilter: (val: string, record) => {
      console.log(val, record)
      return record.name.includes(val)
    }
    // onFilterDropdownVisibleChange: (visible: boolean) => {
    //   if (visible) {
    //     showFilterList.value.push('name')
    //   } else {
    //     showFilterList.value = showFilterList.value.filter((item) => item !== 'name')
    //   }
    // }
  },
  {
    title: '商品编码',
    dataIndex: 'puid',
    width: 250,
    key: 'puid'
  },
  {
    title: '现有数量',
    dataIndex: 'qty_stocking',
    width: 150,
    key: 'qty_stocking'
  },
  {
    title: '商品来源',
    dataIndex: 'src',
    width: 150,
    customRender: ({ text }) => mapSrc[text],
    key: 'src'
  }
  // {
  //   title: '仓库id',
  //   dataIndex: 'warehouse_id'
  // }
]

export const UploadSchemas: FormSchema[] = [
  {
    field: 'files',
    label: '附件上传',
    component: 'Upload',
    slot: 'Files',
    required: true,
    rules: [
      {
        required: true,
        validator: async (_rule: Rule, value: string) => {
          if (!value || value.length === 0) return Promise.reject('请上传附件')
          return Promise.resolve()
        }
        // trigger: 'change'
      }
    ],
    colProps: {
      span: 24
    }
  }
]
