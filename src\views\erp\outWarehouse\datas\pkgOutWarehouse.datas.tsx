import { getAccountList } from '/@/api/commonUtils/index'
import { h, ref, reactive, computed } from 'vue'
import { DefaultOptionType } from 'ant-design-vue/es/vc-tree-select/TreeSelect'
import { ArrowRightOutlined } from '@ant-design/icons-vue'
// import { useMapStoreWithOut } from '/@/store/modules/commonMap'
// import { storeToRefs } from 'pinia'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { FormSchema, BasicColumn, BasicTableProps } from '/@/components/Table'
// import { Rule } from 'ant-design-vue/lib/form'
import { VxeTablePropTypes } from 'vxe-table'
export const compStatus = ref<number>(0)
export const treeData = ref<Recordable[]>([])
export const treeSelectLoadedKeys = ref<string[]>([])
export const productItems = ref<Recordable[]>([])
export const propsData = ref<{ type: 'add' | 'view' | 'edit'; record?: Recordable; bookOutWarehouse?: any }>({ type: 'add' })
export const VxeTableRef = ref(null)
export const statusOptions: {
  mapOrderStatus: { [key: number]: DefaultOptionType }
  mapDetailStatus: { [key: number]: DefaultOptionType[] }
  mapDetailItemStatus: { [key: number]: string }
} = {
  mapOrderStatus: {
    0: {
      value: 0,
      label: '未审核'
    },
    1: {
      value: 1,
      label: '已审核&出库中'
    },
    2: {
      value: 2,
      label: '已出库'
    }
  },
  mapDetailStatus: {
    0: [
      {
        label: '未出库',
        value: 0
      },
      {
        label: '准备出库',
        value: 1
      },
      {
        label: '已出库',
        value: 2
      }
    ],
    1: [
      {
        label: '准备出库',
        value: 1
      },
      {
        label: '已出库',
        value: 2
      }
    ],
    2: [
      {
        label: '已出库',
        value: 2
      }
    ]
  },
  mapDetailItemStatus: {
    0: '未出库',
    1: '准备出库',
    2: '已出库'
  }
}

// export const compPackages = computed(() => {
//   const { packings } = propsData.value
//   if (!packings) return []
//   return packings.map((packages) => packages.packages).flat()
// })

export const compPackages = ref([])

export const compPackagesDetails = computed(() => {
  if (compPackages.value.length === 0) return []
  return compPackages.value.map((details) => details.items).flat()
})

export const validRules = ref<VxeTablePropTypes.EditRules<{}>>({})

export const productColumns: BasicColumn[] = [
  {
    title: '所属订单',
    field: 'from_at',
    width: 200,
    slots: {
      default: ({ row }) => row?.source_uniqid ?? '-'
    }
    // customRender: ({ record }) => {
    //   return record.form_at ? record.form_at : record?.source_uniqid
    // }
  },
  {
    title: '所属订单商品名称',
    field: 'name',
    width: 150
    // customRender: ({ record }) => {
    //   return record.name ? record.name : mapItemRequest.value[record.request_id]?.name
    // }
  },
  {
    title: '产品编码',
    field: 'puid',
    width: 150,
    resizable: true
  },
  {
    title: '出库状态',
    field: 'status',
    ifShow: propsData.value.type === 'edit',
    slots: {
      default: ({ row }) => useRender.renderTag(statusOptions.mapDetailItemStatus[row.status])
    }
    // customRender: ({ value }) => {
    //   return useRender.renderTag(statusOptions.mapDetailItemStatus[value])
    // }
  },
  // {
  //   title: '所属订单商品批次号',
  //   field: 'batch_code',
  //   width: 200,
  //   resizable: true
  // },
  // {
  //   title: '出库状态',
  //   field: 'status',
  //   width: 150,
  //   resizable: true
  // },
  {
    title: '库存数量',
    field: 'qty_stocking',
    width: 150,
    resizable: true
  },
  // {
  //   title: '库存可出库数量',
  //   field: 'qty_residue',
  //   width: 150,
  //   resizable: true
  // },
  {
    title: '出库数量',
    field: 'quantity',
    resizable: true,
    width: 150
  },
  {
    title: '部门ID',
    field: 'dept_id',
    width: 100
  },
  {
    title: '部门',
    field: 'department',
    width: 250
  },
  {
    title: '订单需求数量',
    field: 'qty_request',
    width: 100,
    resizable: true
  },
  {
    title: '已采购数量',
    field: 'qty_purchased',
    width: 100,
    resizable: true
  }
]

export const getFormSchemas = (): FormSchema[] => [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false
  },
  {
    field: 'project_number',
    label: '项目id',
    component: 'Input',
    // ifShow: isBook,
    show: false
  },
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input',
    // ifShow: isBook,
    show: false
  },
  {
    field: 'status',
    label: '出库状态',
    component: 'RadioButtonGroup',
    colProps: {
      span: 24
    },
    defaultValue: 0,
    render: ({ model }) =>
      h('div', {}, [
        h('span', {}, statusOptions.mapOrderStatus[model.status]?.label ?? '未出库'),
        h(ArrowRightOutlined, { style: 'margin: 0 10px' }),
        h('span', {}, statusOptions.mapOrderStatus[compStatus.value]?.label ?? '未出库')
      ])
  },
  {
    field: 'shipment_type',
    label: '出库类型',
    // required: true,
    component: 'Select',
    // dynamicDisabled: type === 'view' || status != 0,
    // dynamicDisabled: ({ values }) => {
    //   return (type === 'view' || status != 0) && values.shipment_type
    // },
    componentProps: {
      options: [
        { label: '送出装柜', value: 1 },
        { label: '送货', value: 2 },
        { label: '装柜', value: 3 },
        { label: '自提', value: 4 },
        { label: '工厂出货', value: 5 }
      ]
    }
  },
  {
    field: 'urgent_level',
    label: '紧急程度',
    // required: true,
    // dynamicDisabled: type === 'view' || status != 0,
    // dynamicDisabled: ({ values }) => {
    //   return (type === 'view' || status != 0) && values.urgent_level
    // },
    component: 'Select',
    componentProps: {
      options: [
        { label: '一般', value: 1 },
        { label: '紧急', value: 2 },
        { label: '非常紧急', value: 3 }
      ]
    }
  },
  {
    field: 'shipment_inCharge',
    label: '仓库出货人',
    component: 'PagingApiSelect',
    // required: true,
    // dynamicDisabled: type === 'view' || status != 0,
    // dynamicDisabled: ({ values }) => {
    //   return (type === 'view' || status != 0) && values.shipment_inCharge
    // },
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
        // disabled: type === 'view' || status != 0
      }
    }
  },
  {
    field: 'applicant',
    label: '申请人',
    component: 'ApiSelect',
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        // disabled: type === 'view' || status != 0
        disabled: true
      }
    },
    required: true,
    colProps: {
      span: 12
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'ApiSelect',
    colProps: {
      span: 12
    },
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        // disabled: type === 'view' || status != 0
        disabled: true
      }
    },
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'project_inCharge',
    label: '项目经理',
    // dynamicDisabled: type === 'view' || status != 0,
    // ifShow: isBook,
    required: false,
    component: 'PagingApiSelect',
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
        // disabled: type === 'view' || status != 0
      }
    }
  },
  {
    field: 'delivery_incharge',
    label: '交付经理',
    // dynamicDisabled: type === 'view' || status != 0,
    // ifShow: isBook,
    required: false,
    component: 'PagingApiSelect',
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
        // disabled: type === 'view' || status != 0
      }
    }
  },
  {
    field: 'deliverer',
    label: '发货人',
    // dynamicDisabled: type === 'view' || status != 0,
    // ifShow: isBook,
    required: false,
    component: 'PagingApiSelect',
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
        // disabled: type === 'view' || status != 0
      }
    }
  },
  {
    field: 'confirmed_at',
    label: '确认时间',
    component: 'DatePicker',
    required: true,
    colProps: {
      span: 12
    },
    componentProps: {
      // disabled: type === 'view' || status != 0,
      // showTime: true,
      style: {
        width: '100%'
      },
      valueFormat: 'YYYY-MM-DD'
      // options: typeOptions
    }
  },
  {
    field: 'checkout_at',
    label: '出库时间',
    component: 'DatePicker',
    componentProps: {
      // disabled: type === 'view' || status != 0,
      // showTime: true,
      style: {
        width: '100%'
      },
      valueFormat: 'YYYY-MM-DD'
    },
    colProps: {
      span: 12
    },
    required: true
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    componentProps: {
      // disabled: type === 'view' || status != 0
    },
    colProps: {
      span: 12
    }
  },
  {
    field: 'files',
    label: '附件',
    component: 'Upload',
    slot: 'FilesSlot',
    componentProps: {
      // disabled: type === 'view' || status != 0
    }
    // rules: [
    //   {
    //     required: true,
    //     validator: async (_rule: Rule, value: string) => {
    //       if (!value || value.length === 0) return Promise.reject('请上传附件')
    //       return Promise.resolve()
    //     }
    //     // trigger: 'change'
    //   }
    // ]
  },
  {
    field: 'relate_detail',
    label: '出库商品明细',
    component: 'Input',
    slot: 'relate_detail',
    colProps: { span: 24 },
    rules: [{ required: true, validator: validDetail }]
  }
]

function validDetail(_rule: any, value: any[]) {
  if (!value || value.length === 0) return Promise.reject('请选择出库商品')
  // console.log(value.quantity)
  const validRes = value.every((item) => item.quantity >= 0)
  return validRes ? Promise.resolve() : Promise.reject('请完善明细信息！')
}

export const gridOptions = reactive<BasicTableProps>({
  id: 'VxeTable',
  loading: false,
  keepSource: true,
  // editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
  columns: productColumns,
  height: 500,
  proxyConfig: null,
  toolbarConfig: null
  // checkboxConfig: {
  //   trigger: 'row'
  // }
})
