<template>
  <BasicModal @register="registerModal" v-bind="$attrs" width="40%" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { printAgrSchemas } from '../datas/printModal'

const propData = ref()

const emit = defineEmits(['add-success', 'update-success', 'register'])

const product_info = ref()

const [registerModal, { changeOkLoading, changeLoading, closeModal }] = useModalInner(async (data) => {
  await resetFields()
  product_info.value = {}
  propData.value = data
  updateSchema(printAgrSchemas(product_info))
  if (data?.isUpdate) setFieldsValue(data.record)
})

const [registerForm, { resetFields, validate, setFieldsValue, updateSchema }] = useForm({
  baseColProps: { span: 12 },
  showActionButtonGroup: false,
  labelCol: { span: 6 },
  schemas: printAgrSchemas(product_info),
  colon: true
})

async function handleSubmit() {
  await changeOkLoading(true)
  changeLoading(true)
  try {
    const formData = await validate()
    console.log(formData, 'formData', product_info.value)
    if (propData.value.isUpdate) {
      emit('update-success', { ...formData, product_info: product_info.value, key: propData.value.record.key })
    } else {
      emit('add-success', { ...formData, product_info: product_info.value, key: Date.now() })
    }

    closeModal()
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
    changeOkLoading(false)
  }
}
</script>
