<template>
  <BasicModal @register="registerModal" v-bind="$attrs" width="50%" @ok="handleSubmit">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button @click="handleAdd" type="primary"><PlusOutlined />新增</a-button>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :stop-button-propagation="true" />
        </template>
      </template>
    </BasicTable>
    <PrintAgrFormModal @register="registerFormModal" @add-success="handleAddSuccess" @update-success="handleUpdateSuccess" />
  </BasicModal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { BasicModal, useModalInner, useModal } from '/@/components/Modal'
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import type { EditRecordRow } from '/@/components/Table'
import { useGo } from '/@/hooks/web/usePage'
import { localCache } from '/@/utils/cache/cache'
import { columns, IStorgeObj } from '../datas/printModal'
import PrintAgrFormModal from './PrintAgrFormModal.vue'

const go = useGo()

const propData = ref()

const [registerFormModal, { openModal, setModalProps }] = useModal()

// 存储已输入的数据
const storge = ref<IStorgeObj[]>([])

const [registerTable, { setTableData, getDataSource, updateTableDataRecord, insertTableDataRecord, deleteTableDataRecord }] = useTable({
  showIndexColumn: true,
  columns,
  dataSource: [],
  pagination: false,
  striped: true,
  canResize: false,
  rowKey: 'key',
  actionColumn: {
    width: 230,
    title: '操作',
    dataIndex: 'action'
  }
})

const [registerModal, { changeOkLoading, changeLoading }] = useModalInner(async (data) => {
  await setTableData([])
  propData.value = data
})

function handleAdd() {
  setModalProps({ title: '添加' })
  openModal(true, {
    isUpdate: false
  })
}

function createActions(record: EditRecordRow): Recordable[] {
  return [
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record)
    },
    {
      icon: 'ant-design:delete-outlined',
      color: 'error',
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        placement: 'left',
        confirm: handleDelete.bind(null, record)
      }
    }
  ]
}

function handleEdit(record: EditRecordRow) {
  setModalProps({ title: '编辑' })
  openModal(true, { isUpdate: true, record })
}

async function handleDelete(record: EditRecordRow) {
  await deleteTableDataRecord(record.key)
}

async function handleAddSuccess(params) {
  await insertTableDataRecord(params)
}

async function handleUpdateSuccess(params) {
  await updateTableDataRecord(params.key, params)
}

async function handleSubmit() {
  await changeOkLoading(true)
  changeLoading(true)
  try {
    storge.value = await getDataSource()
    const date = Date.now()
    //存储到localstorage 感觉这样子会插入很多,不如把puid传到page页面,然后再请求
    localCache.setCache(`agr${date}`, storge.value)
    const url = `/erp/print?page=agr&date=${date}&pageType=${propData.value.type}`

    go(url)

    // closeModal()
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
    changeOkLoading(false)
  }
}
</script>
