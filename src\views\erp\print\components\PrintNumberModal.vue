<template>
  <BasicModal @register="registerModal" v-bind="$attrs" width="15%" @ok="handleSubmit" :minHeight="60">
    <BasicForm @register="registerForm" />
    <PrintDrawer @register="registerPrintDrawer" />
    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button type="primary" @click="debounceHandleSubmit">生成打印码</a-button>
    </template>
  </BasicModal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import type { FormSchema } from '/@/components/Form'
// import { useGo } from '/@/hooks/web/usePage'
import PrintDrawer from '../../print/components/PrintDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { debounce } from 'lodash-es'

const [registerPrintDrawer, { openDrawer: openPrintDrawer }] = useDrawer()

// const go = useGo()

const propData = ref()
const schemas: FormSchema[] = [
  {
    field: 'PKG',
    label: '数量',
    component: 'InputNumber',
    componentProps: {
      min: 0.01,
      precision: 2
    },
    required: true
  }
]

const [registerForm, { resetFields, validate }] = useForm({
  baseColProps: { span: 24 },
  showActionButtonGroup: false,
  labelCol: { span: 5 },
  schemas,
  colon: true
})

const [registerModal, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  await resetFields()
  propData.value = data
  console.log(data, 'data')
})

const debounceHandleSubmit = debounce(handleSubmit, 500)
async function handleSubmit() {
  await changeOkLoading(true)
  try {
    const formData = await validate()

    openPrintDrawer(true, {
      ...propData.value,
      pkg: formData.PKG
    })
    closeModal()
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeOkLoading(false)
  }
}
</script>
