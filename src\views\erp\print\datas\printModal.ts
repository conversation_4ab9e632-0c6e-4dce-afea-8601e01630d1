import type { FormSchema, BasicColumn } from '/@/components/Table'

import { getAgrItemRequest } from '/@/api/erp/purchaseOrder'

export const printAgrSchemas = (product_info): FormSchema[] => [
  {
    field: 'puid',
    label: '产品编码',
    itemProps: {
      validateTrigger: 'blur'
    },
    component: 'PagingApiSelect',
    componentProps: ({}) => {
      return {
        searchMode: true,
        searchParamField: 'puid',
        immediate: false,
        resultField: 'items',
        api: getAgrItemRequest,
        pagingMode: true,
        returnParamsField: 'puid',
        selectProps: {
          allowClear: true,
          fieldNames: { key: 'puid', value: 'puid', label: 'puid' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'puid'
        },
        onChange: async (_, shall) => {
          // formActionType.setFieldsValue({ product_info: shall })
          product_info.value = shall
        }
      }
    },
    required: true
  },
  {
    field: 'quantity',
    label: '数量',
    component: 'InputNumber',
    componentProps: {
      min: 0.01,
      precision: 2
    },
    required: true
  },
  {
    field: 'number',
    label: '打印份数',
    component: 'InputNumber',
    componentProps: {
      min: 1
    },
    required: true
  }
]

export const columns: BasicColumn[] = [
  {
    dataIndex: 'puid',
    title: 'puid',
    width: 180,
    resizable: true
  },
  {
    dataIndex: 'quantity',
    title: '数量',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'number',
    title: '打印份数',
    width: 120,
    resizable: true
  }
]

export interface IStorgeObj {
  pkg: number
  quantity: number
  purchase_strid: string
}
