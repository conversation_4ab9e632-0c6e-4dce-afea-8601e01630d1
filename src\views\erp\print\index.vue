<template>
  <div>
    <a-button @click="debounceHandlePrint" type="primary" class="mr-4">预览</a-button>
    <a-button @click="debounceHandlePrintToPDF">导出pdf</a-button>

    <div class="list">
      <template v-if="info && info.list">
        <div
          class="print-content"
          :style="{ width: info.list.length > 1 ? '300px' : '260px' }"
          v-for="(item, index) of info.list"
          :key="index"
          :id="'agr-print' + index"
          style="page-break-after: always"
        >
          <div class="content">
            <div class="left">
              <template v-if="info">
                <div>{{ item.product_info.uniqid }}</div>

                <div><PERSON></div>

                <!-- 这是agr的替换号 -->
                <div>{{ item.product_info.remark }}</div>
                <div>
                  quantity:<span class="number">{{ item.quantity }}</span>
                </div>
              </template>
            </div>

            <div class="right">
              <template v-if="info">
                <QrCode :value="`${item.product_info.uniqid};${item.product_info.puid};${item.quantity}`" :width="120" />
              </template>
            </div>
          </div>

          <div class="bottom">
            <!-- 这是agr的批号 -->
            <span>{{ item.product_info.puid }}</span>

            <span class="purchase" v-if="item.product_info.work.purchase_strid">{{ item.product_info.work.purchase_strid }}</span>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import printJS from 'print-js'
import { QrCode } from '/@/components/Qrcode'
import { useLoading } from '/@/components/Loading'
import { localCache } from '/@/utils/cache/cache'
import { debounce } from 'lodash-es'
const [openFullLoading, closeFullLoading] = useLoading({
  tip: '加载中...'
})

const route = useRoute()

const info = ref()

onMounted(() => {
  info.value = route.query

  info.value.list = localCache.getCache(`agr${route.query.date}`)
})

function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

const debounceHandlePrint = debounce(handlePrint, 500)
async function handlePrint() {
  openFullLoading()

  const arr = []

  for (let i = 0; i < info.value.list.length; i++) {
    const imageToPrint = document.getElementById('agr-print' + i) // 获取需要打印的内容
    for (let j = 0; j < info.value.list[i].number; j++) {
      await captureAndPushImageToArr(imageToPrint, arr)
    }
  }

  await delay(1000) // 等待一段时间确保图片加载完成
  closeFullLoading()

  printJS({
    printable: arr,
    type: 'image',
    imageStyle: 'width:100%;'
  })
}

const debounceHandlePrintToPDF = debounce(handlePrintToPDF, 500)
async function handlePrintToPDF() {
  openFullLoading()

  const arr = []

  for (let i = 0; i < info.value.list.length; i++) {
    const imageToPrint = document.getElementById('agr-print' + i)
    for (let j = 0; j < info.value.list[i].number; j++) {
      await captureAndPushImageToArr(imageToPrint, arr, 'print')
    }
  }

  await delay(1000) // 等待一段时间确保图片加载完成
  closeFullLoading()
  // 动态导入 jsPDF 包
  const { default: jsPDF } = await import('jspdf')
  // 将图片数组转换为 PDF
  const pdf = new jsPDF({
    unit: 'mm',
    format: [100, 100] // 设置纸张大小为60*60mm
  })
  for (let i = 0; i < arr.length; i++) {
    pdf.addImage(arr[i], 'PNG', 0, 0, 100, 80) // 使用75*75mm纸张大小
    if (i !== arr.length - 1) {
      pdf.addPage()
    }
  }

  pdf.save('exported.pdf')
}

async function captureAndPushImageToArr(imageToPrint, arr, type = 'pdf') {
  // 动态导入 html2canvas 包
  const html2canvas = await import('html2canvas')
  const canvas = await html2canvas.default(imageToPrint, {
    dpi: type === 'print' ? 150 : 160, //分辨率
    scale: 1.8,
    useCORS: true //允许canvas画布内 可以跨域请求外部链接图片, 允许跨域请求
    // scrollY: imageToPrint.offsetHeight // 关键代码，截取长度
  })
  const imageData = canvas.toDataURL('image/png')
  const img = new Image()
  img.src = imageData
  arr.push(img.src)
  canvas.remove()
}
</script>

<style lang="less" scoped>
.list {
  display: flex;
}
.print-content {
  height: 200px;
  width: 260px;
  box-sizing: border-box;
  //   border: 1px solid #000;
  padding: 5px 5px 20px;
  font-size: 14px;
  font-weight: 700;
  position: relative;
  //   transform: rotate(-90deg);
  .content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      flex: 1;
      word-break: break-all;
    }
    .right {
      width: 120px;
    }
  }
  .bottom {
    display: flex;
    justify-content: flex-end;
    .purchase {
      font-family: 'Arial';
      border: 1px solid #000;
      padding-bottom: 8px;
    }
  }

  .number {
    font-size: 30px;
    font-weight: 700;
  }
}
</style>
