import { h } from 'vue'
import type { BasicColumn, FormSchema } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { isNull, isUnDef } from '/@/utils/is'

export const mapStatus = {
  0: { label: '待审批', color: '#08979c', status: 'process' },
  1: { label: '执行中', color: '#b7eb8f', status: 'process' },
  2: { label: '可备货', color: '#f50', status: 'process' },
  3: { label: '备货中', color: '#f50', status: 'process' },
  4: { label: '已入库', color: '#b7eb8f', status: 'process' },
  5: { label: '出库中', color: '#f50', status: 'process' },
  15: { label: '已完成', color: '#b7eb8f', status: 'finish' },
  16: { label: '已取消', color: 'red', status: 'error' }
}

export const mapProductStatus = {
  0: { label: '待确认', color: '#08979c' },
  1: { label: '确认', color: '#b7eb8f' },
  2: { label: '生产中', color: '#f50' },
  3: { label: '生产完成', color: '#b7eb8f' },
  4: { label: '质检', color: '#f50' },
  5: { label: '入库', color: '#b7eb8f' },
  6: { label: '出库', color: '#b7eb8f' }
}

export const mapType = {
  1: { label: 'Comfimrd', color: 'green' },
  2: { label: 'Process', color: 'orange' },
  3: { label: 'Finished', color: 'green' },
  4: { label: 'Warehouse', color: 'green' },
  5: { label: 'QC', color: 'orange' },
  6: { label: 'Warehouse', color: 'green' },
  7: { label: 'Process', color: 'orange' },
  8: { label: 'Comfirmed', color: 'green' },
  9: { label: 'Warehouse', color: 'green' }
}

export const searchSchemas: FormSchema[] = [
  {
    field: 'source_uniqid',
    label: '销售订单',
    component: 'Input'
  }
]

export const columns: BasicColumn[] = [
  {
    title: '商品',
    dataIndex: 'product',
    width: '25%',
    resizable: true,
    align: 'left'
  },
  {
    title: '订单实际需求数量',
    dataIndex: 'qty_request_actual',
    width: '25%',
    resizable: true
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: '25%',
    resizable: true,
    customRender: ({ value }) => (value ? h('span', null, value) : '')
  },
  {
    //商品状态
    title: '状态',
    dataIndex: 'status',
    width: '25%',
    resizable: true,
    customRender: ({ value }) =>
      isNull(value) || isUnDef(value) ? '' : useRender.renderTag(mapProductStatus[value].label, mapProductStatus[value].color)
  }
]

export const childColumns: BasicColumn[] = [
  {
    title: '类型描述',
    dataIndex: 'type',
    width: 200,
    resizable: true,
    customRender: ({ value }) => (isNull(value) || isUnDef(value) ? '' : useRender.renderTag(mapType[value].label, mapType[value].color))
  },
  {
    title: '状态描述-英文',
    dataIndex: 'type_desc_english',
    width: 150,
    resizable: true
  },
  {
    title: '状态描述-中文',
    dataIndex: 'type_desc_chinese',
    width: 150,
    resizable: true
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    width: 100,
    resizable: true
  }
]
