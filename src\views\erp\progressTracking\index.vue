<template>
  <PageWrapper contentBackground>
    <template #headerContent>
      <BasicForm @register="registerForm" :showAdvancedButton="true" :autoSubmitOnEnter="true" :autoAdvancedLine="1" />
    </template>
    <template v-if="saleList.length > 0">
      <Collapse v-loading="loading" :bordered="false">
        <template #expandIcon="{ isActive }">
          <caret-right-outlined :rotate="isActive ? 90 : 0" />
        </template>
        <CollapsePanel
          v-for="item in saleList"
          :key="item.id"
          style="background: #f7f7f7; border-radius: 4px; margin-bottom: 10px; border: 0; overflow: hidden"
        >
          <template #header>
            <div class="flex items-center">
              <div>
                订单号：{{ item.source_uniqid }} - 开单时间：{{ item.created_at }} - 部门: {{ item.department }} - 客户：{{
                  item.client_name
                }}
                - 创建人: {{ item.creator }} - 方案负责人: {{ item.program_incharge_name }} - 项目负责人: {{ item.inCharge_name }}
              </div>
            </div>
          </template>
          <BasicTable
            :searchInfo="{ work_id: item.id }"
            :canResize="false"
            :columns="columns"
            :showIndexColumn="false"
            :isTreeTable="true"
            @register="registerTable"
          >
            <template #toolbar>
              <div class="progress-bar">
                <div class="left">
                  <div class="status" :style="{ color: mapStatus[item.status].color }">{{ mapStatus[item.status].label }}</div>
                  <div class="time">预计交货日期: {{ item.delivery_at }}</div>
                  <!-- <a-button type="primary" @click="handleCirculate(item.id)" disabled>一键催办</a-button> -->
                </div>
                <div class="right">
                  <Steps :current="item.status" :status="mapStatus[item.status].status" progressDot>
                    <template v-for="toolItem in Object.keys(mapStatus)" :key="toolItem">
                      <Step :title="mapStatus[toolItem].label" />
                    </template>
                  </Steps>
                </div>
              </div>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'product'">
                <div class="product">
                  <TableImg :size="60" :simpleShow="true" :imgList="record.imgs" :margin="1" />
                  <div class="product-info">
                    <div class="name">{{ record.name }}</div>
                    <div class="puid">产品唯一码: {{ record.puid }}</div>
                  </div>
                </div>
              </template>
            </template>
            <template #expandedRowRender="{ record }">
              <BasicTable @register="registerChildTable" :searchInfo="{ request_id: record.id }" />
            </template>
          </BasicTable>
        </CollapsePanel>
      </Collapse>
    </template>

    <div class="p-4 text-right">
      <Pagination
        v-if="saleList.length > 0"
        v-model:current="paging.page"
        :total="pagingTotal"
        v-model:pageSize="paging.pageSize"
        show-size-changer
      />
    </div>
  </PageWrapper>
</template>

<script setup lang="ts" name="progressTracking">
import { ref, reactive, onMounted, watch } from 'vue'
import { Pagination, Collapse, CollapsePanel, Steps, Step } from 'ant-design-vue'
import { CaretRightOutlined } from '@ant-design/icons-vue'
import { BasicTable, useTable, TableImg } from '/@/components/Table'
import { PageWrapper } from '/@/components/Page'
import { BasicForm, useForm } from '/@/components/Form'
import { useLoading } from '/@/components/Loading'
import { getSalesOrderList, getSalesOrderListReq } from '/@/api/erp/sales'
import { getProgressTracking } from '/@/api/erp/progressTracking'
import { columns, searchSchemas, childColumns, mapStatus } from './datas/datas'

const [openFullLoading, closeFullLoading] = useLoading({
  tip: 'loading...'
})

const paging = reactive<{ page: number; pageSize: number }>({ page: 1, pageSize: 10 })
const pagingTotal = ref<number>(0)
const loading = ref(false)
const saleList = ref<any[]>([])

const [registerForm, { getFieldsValue }] = useForm({
  labelWidth: 100,
  schemas: searchSchemas,
  baseColProps: { span: 8 },
  labelCol: { span: 4 },
  actionColOptions: { span: 24 },
  colon: true,
  submitButtonOptions: {
    loading
  },
  submitFunc: async () => {
    paging.page = 1
    await initData()
  },
  submitOnReset: true
})

onMounted(() => {
  initData()
})

watch([() => paging.page, () => paging.pageSize], (data, oldData) => {
  const [page, pageSize] = data
  const [oldPage, oldPageSize] = oldData
  if (page !== oldPage || pageSize !== oldPageSize) {
    saleList.value = []
    initData()
  }
})

async function initData() {
  try {
    openFullLoading()
    loading.value = true
    const formData = getFieldsValue()
    //请求第一层接口
    const { items, total } = await getSalesOrderList({ ...paging, ...formData })
    pagingTotal.value = total
    saleList.value = items
  } catch (e) {
    throw new Error(JSON.stringify(e))
  } finally {
    closeFullLoading()
    loading.value = false
  }
}

const [registerTable] = useTable({
  api: getSalesOrderListReq,
  rowKey: 'id',
  pagination: false,
  showIndexColumn: false,
  canResize: false
})

const [registerChildTable] = useTable({
  api: getProgressTracking,
  columns: childColumns,
  showIndexColumn: false,
  pagination: false,
  canResize: false
})

// async function handleCirculate(work_id) {
//   try {
//     loading.value = true
//     await remind(work_id)
//     await initData()
//   } catch (e) {
//     throw new Error(JSON.stringify(e))
//   } finally {
//     loading.value = false
//   }
// }
</script>
<style lang="less" scoped>
.progress-bar {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  border: 1px solid #eee;
  border-radius: 10px;
  padding: 20px;
  .left {
    .status {
      font-size: 30px;
      font-weight: 700;
      margin-bottom: 6px;
    }
    .second-title {
      color: #999;
      font-size: 14px;
      margin-bottom: 6px;
    }
    .time {
      font-weight: 600;
      font-size: 18px;
      margin-bottom: 6px;
    }
  }
  .right {
    margin-left: 200px;
  }
}
.product {
  display: flex;
  align-items: center;
  .vben-basic-table-img.flex.items-center.mx-auto {
    margin-left: 0;
    margin-right: 8px;
  }
  .name {
    font-weight: 700;
    margin-bottom: 6px;
  }
  .puid {
    color: #999;
    font-size: 13px;
  }
}
</style>
