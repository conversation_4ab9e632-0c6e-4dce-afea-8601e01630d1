<template>
  <BasicModal @register="register" title="商品添加" @ok="addsubmit" :min-height="400" @close="close">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
//updateWorksAudit

import { updateWorksAudit } from '/@/api/erp/purchaseOrder'
import { schemasaddmodel } from '../datas/modal'
import { ref } from 'vue'
//From

const [registerForm, { validate, resetFields }] = useForm({
  showActionButtonGroup: false,
  schemas: schemasaddmodel
})
//work_ids
const work_ids = ref<any>([])

//modal
const [register, { closeModal }] = useModalInner(async (data) => {
  resetFields()
  console.log(data)
  work_ids.value = data
  console.log(work_ids.value)
})
const emit = defineEmits(['success', 'register', 'close'])
async function addsubmit() {
  const valid = await validate()
  console.log(valid)
  const res = await updateWorksAudit({ work_ids: work_ids.value, is_audit: 1, audit_at: valid.audit_at })
  console.log(res)

  if (res.news == 'success') {
    emit('success')
    closeModal()
    work_ids.value = []
  }
}
function close() {
  work_ids.value = []
  emit('close')
}
</script>
