<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="90%" @ok="handleSubmit" title="AGR导入" show-footer>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <div class="toolbar-btn">
          <div class="right">
            <a-button class="mr-4" @click="openImpExcelModal" type="primary"> <UploadOutlined /> 上传excel文件</a-button>
            <a-button @click="handleCompare">重新对比</a-button>
          </div>
        </div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'replacement'">
          <Input v-model:value="record.replacement" />
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <BasicTable @register="registerChildTable" :data-source="record.result" v-if="record.result.length > 0" />
      </template>
    </BasicTable>
    <ImpExcelModal @register="registerUploadModal" :dataCallBackFn="handleUploadData" ref="ImpExcelModalRef" />
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { Input } from 'ant-design-vue'
import { UploadOutlined } from '@ant-design/icons-vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { ImpExcelModal } from '/@/components/Excel'
import { useModal } from '/@/components/Modal'
import { BasicTable, useTable } from '/@/components/Table'
import { importAgr, submitAgr } from '/@/api/erp/purchaseOrder'
import { transformData2Import, columns, childColumns } from '../datas/importModal'
import { useMessage } from '/@/hooks/web/useMessage'

const [registerUploadModal, { openModal }] = useModal()

const [registerDrawer, { changeOkLoading, changeLoading, closeDrawer }] = useDrawerInner(async () => {
  setTableData([])
})

const [registerTable, { setTableData, getDataSource }] = useTable({
  title: '对比结果',
  columns,
  showIndexColumn: false,
  pagination: {
    size: 'small',
    pageSize: 10,
    pageSizeOptions: ['10', '100', '500', '1000']
  }
})

//树形内嵌
const [registerChildTable, {}] = useTable({
  showIndexColumn: false,
  pagination: false,
  maxHeight: 100,
  bordered: true,
  columns: childColumns,
  resizeHeightOffset: -500
})

function openImpExcelModal() {
  openModal(true, {
    sheetName: 'Sheet1',
    headerRow: 1,
    startCell: 'A2',
    endCell: `F20000`
  })
}

const ImpExcelModalRef = ref<InstanceType<typeof ImpExcelModal>>()
//上传
async function handleUploadData(excelData: any) {
  try {
    ImpExcelModalRef.value?.changeLoading(true)
    const cookedData = transformData2Import(excelData)

    // //每次最多200条数据
    // const chunkSize = 200
    // const totalChunks = Math.ceil(cookedData.length / chunkSize)

    // for (let i = 0; i < totalChunks; i++) {
    //   const start = i * chunkSize
    //   const end = Math.min(start + chunkSize, cookedData.length)
    //   const chunk = cookedData.slice(start, end)

    //   const {
    //     items: { data }
    //   } = await importAgr({ data: chunk })
    //   console.log(data, 'data')
    // }

    //一次性都传过去
    const {
      items: { data }
    } = await importAgr({ data: cookedData })
    await setTableData(data)
    // reload()
    ImpExcelModalRef.value?.changeLoading(false)
    return Promise.resolve('OK')
  } catch (err) {
    console.log(err)
    ImpExcelModalRef.value?.changeLoading(false)
    return Promise.reject('Fail')
  }
}
const { createMessage } = useMessage()
async function handleCompare() {
  try {
    await changeOkLoading(true)
    changeLoading(true)
    const {
      items: { data }
    } = await importAgr({
      data: getDataSource().map((item) => {
        delete item.result
        delete item.key
        return item

        //   const { result,key, ...rest } = item;
        //   return rest;
      })
    })
    createMessage.success('重新对比成功')
    setTableData(data)
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeOkLoading(false)
    changeLoading(false)
  }
}

async function handleSubmit() {
  try {
    await changeOkLoading(true)
    changeLoading(true)
    await submitAgr({
      data: getDataSource().map((item) => {
        delete item.key
        return item

        //   const { result,key, ...rest } = item;
        //   return rest;
      })
    })
    setTimeout(() => {}, 500)
    await closeDrawer()
    changeOkLoading(false)
    changeLoading(false)
  } catch (err) {
    changeOkLoading(false)
    changeLoading(false)
    throw new Error(`${err}`)
  }
}
</script>
