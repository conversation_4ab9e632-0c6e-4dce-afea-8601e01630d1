<template>
  <BasicModal @register="register" title="附件上传" @ok="handleOk" width="800px">
    <BasicForm @register="registerform">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template></BasicForm
    ></BasicModal
  >
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { UploadSchemas } from '../datas/modal'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { UploadFile, Upload, message } from 'ant-design-vue'
import { ref, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { GetupdateFiles } from '/@/api/erp/purchaseOrder'
import { isNull } from '/@/utils/is'

//id
const init_id = ref()
const [register, { closeModal, changeLoading, changeOkLoading }] = useModalInner(async (data) => {
  console.log(data)
  try {
    await changeLoading(true)
    resetFields()
    filesList.value = data?.files?.map((file) => ({ name: file, url: file, uid: Math.random() * 100000 })) ?? []
    init_id.value = data.id
  } catch (err) {
    console.error(err)
  } finally {
    changeLoading(false)
  }
})
const [registerform, { setFieldsValue, getFieldsValue, validate, resetFields }] = useForm({
  schemas: UploadSchemas,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelCol: { span: 5 }
})

//附件
const filesList = ref<UploadFile[]>([])

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val.map((item) => item.url) })
  }
)

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  console.log(file)

  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  filesList.value = filesList.value!.map((item) => {
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: item.name
    }
  })
  await setFieldsValue({
    files: filesList.value.map((item) => item.url)
  })
}

// 提交
const emit = defineEmits(['success', 'register'])
async function handleOk() {
  try {
    await changeOkLoading(true)
    await validate()
    const formdata = await getFieldsValue()
    console.log(filesList.value)
    const params = {
      id: init_id.value,
      files: formdata.files.filter((item) => !isNull(item))
    }

    const res: any = await GetupdateFiles(params)
    console.log(res)
    if (res.news == 'success') {
      emit('success')
      message.success('附件上传成功')
      await closeModal()
      filesList.value = []
    }
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  }
}
</script>
