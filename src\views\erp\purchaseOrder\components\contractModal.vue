<template>
  <BasicModal @register="register" title="合同导出" @ok="handleOk" width="800px" :minHeight="350">
    <BasicForm @register="registerform" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { ContractSchemas } from '../datas/modal'
import { ref } from 'vue'
import { explodeContract } from '/@/api/erp/purchaseOrder'
import { message } from 'ant-design-vue'
const ids = ref()
//id
const [register, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  console.log(data)
  resetFields()
  ids.value = data.ids
  resetSchema(ContractSchemas(data.supplier_id))
})
const [registerform, { validate, resetFields, resetSchema }] = useForm({
  // schemas: ContractSchemas(),
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelCol: { span: 5 }
})

// 提交
const emit = defineEmits(['success', 'register'])
async function handleOk() {
  const vali = await validate()
  const params = {
    ...vali,
    ids: ids.value
  }
  console.log(vali)
  try {
    changeOkLoading(true)
    try {
      await explodeContract(true, params)
    } catch (err: any) {
      if (err.message === 'Reflect.has called on non-object') {
        const response = await explodeContract(false, params)
        // const blob = new Blob([response.data], { type: response.headers['content-type'] })
        const downloadLink = document.createElement('a')
        downloadLink.href = URL.createObjectURL(response)
        switch (vali.type) {
          case 1:
            downloadLink.download = `采购合同(不含税).docx`
            break
          case 2:
            downloadLink.download = `采购合同(含税).docx`
            break
          case 3:
            downloadLink.download = `灯饰购货合同.docx`
            break
        }

        // 模拟点击下载链接
        downloadLink.click()

        // 清理临时资源
        URL.revokeObjectURL(downloadLink.href)
        message.success('导出成功')
        emit('success')
        closeModal()
        resetFields()
        setTimeout(() => {
          changeOkLoading(false)
        }, 500)
      }
      changeOkLoading(false)
      throw new Error(`${e}`)
    }
  } catch (e) {
    throw new Error(`${e}`)
  }
}
</script>
