<template>
  <BasicDrawer @register="registerDrawer" @ok="handleOk">
    <Card title="原单信息" :bordered="false">
      <Descriptions :column="3">
        <DescriptionsItem label="日期">{{ init_record.created_at }}</DescriptionsItem>
        <DescriptionsItem label="关联销售单号">{{ init_record.source_uniqid }}</DescriptionsItem>
        <DescriptionsItem label="供应商">{{ init_record.supplier_name }}</DescriptionsItem>
        <DescriptionsItem label="原单我司签约主体">{{ init_record.contracting_party || '-' }}</DescriptionsItem>
        <DescriptionsItem label="原单含税金额">{{ allTotalPriceArr[1] }}</DescriptionsItem>
        <DescriptionsItem label="原单开票税点">{{ itemdata[0]?.tax_point }}</DescriptionsItem>
        <DescriptionsItem label="原单税金">{{ allTotalPriceArr[0] }}</DescriptionsItem>
        <DescriptionsItem label="原单成本总价">{{ allTotalPriceArr[3] }}</DescriptionsItem>
        <DescriptionsItem label="原单开票加收税点">{{ init_record.add_point || 0 }}</DescriptionsItem>
        <DescriptionsItem label="原单开票税点加收金额">{{ allTotalPriceArr[2] }}</DescriptionsItem>
        <DescriptionsItem label="原单发票类型">{{ invoicetype[init_record.invoice_type]?.label }}</DescriptionsItem>
        <DescriptionsItem label="部门">{{ init_record.dept_name }}</DescriptionsItem>
        <!-- <DescriptionsItem label="是否Gbuilder">{{ init_record.is_gbuilder == 0 ? '否' : '是' }}</DescriptionsItem> -->
      </Descriptions>
    </Card>
    <Card :bordered="false">
      <template #title>
        <div
          >公私转换
          <span style="color: red; font-size: 20px; margin-left: 5px"
            >1、3月18号之前旧有采购订单已经是含税的，不可以使用公私转换单 2、每张采购订单只能使用使用一次公私转换单</span
          ></div
        >
      </template>
      <BasicForm @register="registerForm" @field-value-change="handleFieldChange" />
      <VxeBasicTable
        v-bind="gridOptions"
        ref="tableRef"
        show-footer
        :footer-span-method="footerSpanMethod"
        :footer-method="footerMethod"
        :expand-config="{
          visibleMethod({ row }) {
            if (row.items_sub.length == 0) {
              return false
            }
            return true
          },
          isFooter: true,
          accordion: true
        }"
      >
        <template #ActionHeader="{ column }">
          <div class="text-center">{{ column.title }}</div>
          <div class="text-center">
            <Button type="primary" size="small" @click="handleBatchDelete">批量删除</Button>
          </div>
        </template>
        <template #Action="{ row }">
          <TableAction :actions="createActions(row)" />
        </template>
        <template #Imgs="{ row, column }">
          <template v-if="column.field === 'imgs'">
            <div class="vxe-img">
              <TableImg v-if="isArray(row.imgs) && row.imgs.length > 0" :size="60" :simpleShow="true" :imgList="row.imgs" />
            </div>
          </template>
        </template>
        <template #QtyPurchased="{ column, row }">
          <template v-if="column.field === 'qty_purchased_actual'">
            <InputNumber
              v-model:value="row.qty_purchased_actual"
              :disabled="init_record?.is_check == 2 || row.items_sub.length > 0"
              :min="0.01"
              :precision="2"
              :max="row.pur_order_qty"
              @change="handleFieldChange()"
            />
          </template>
        </template>
        <template #expandContent="{ row: rowsplit }">
          <VxeBasicTable v-bind="gridOptionssplit" ref="tablesplitRef" :columns="tablecolum" :data="rowsplit.items_sub">
            <template #Imgs="{ row, column }">
              <template v-if="column.field === 'imgs'">
                <div class="vxe-img">
                  <TableImg v-if="isArray(row.imgs) && row.imgs.length > 0" :size="60" :simpleShow="true" :imgList="row.imgs" />
                </div>
              </template>
            </template>
          </VxeBasicTable>
        </template>
        <template #expandHeader> 查看子产品 </template>
      </VxeBasicTable>
    </Card>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, reactive, ref, watchEffect } from 'vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { Descriptions, DescriptionsItem, Card, InputNumber, message, Button } from 'ant-design-vue'
import { add, sub } from '/@/utils/math'
import { invoicetype, schemas, columns, tablecolum } from '../datas/public.data'
import { BasicTableProps, VxeBasicTable, VxeGridInstance, VxeTablePropTypes } from '/@/components/VxeTable'
import { cloneDeep, isArray, isUndefined } from 'lodash-es'
import { ActionItem, TableAction, TableImg } from '/@/components/Table'
import { getPurchaseDetail, purchasecreateMaleSelf } from '/@/api/erp/purchaseOrder'
import Decimal from 'decimal.js'

const init_record = ref<any>({})
const itemdata = ref<any>([])
const tableRef = ref<VxeGridInstance>()
const footergettable = ref<any>([])
const footercheecktable = ref<any>([])
const loading = ref<boolean>(false)
const gridDataSource = ref<any>([])

const gridOptions = reactive<BasicTableProps>({
  id: 'VxeTable',
  loading,
  // keepSource: true,
  // editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
  rowConfig: {
    keyField: 'id'
  },
  columns: [],
  height: '500px',
  data: gridDataSource,
  toolbarConfig: {
    enabled: false
  },
  tableStyle: { padding: '0!important' },
  proxyConfig: null,
  headerAlign: 'center',
  'scroll-y': { enabled: true, gt: 20 }
})
//子产品表格
const gridOptionssplit = reactive<BasicTableProps>({
  id: 'VxeTable',
  loading,
  // keepSource: true,
  // editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
  rowConfig: {
    keyField: 'id'
  },
  // data: gridDataSource,
  toolbarConfig: {
    enabled: false
  },
  tableStyle: { padding: '0!important' },
  proxyConfig: null,
  headerAlign: 'center',
  'scroll-y': { enabled: true, gt: 20 }
})

const [registerDrawer, { changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  console.log(data)
  resetFields()
  init_record.value = data.record
  itemdata.value = data.record.item
  gridDataSource.value = []
  gridOptions.columns = columns.filter((item) => isUndefined(item.ifShow) || item.ifShow)
  getitemDetail(data.record)
  setFieldsValue({ supplier_id: data.record.supplier_id })
})

const [registerForm, { getFieldsValue, setFieldsValue, validate, resetFields }] = useForm({
  labelWidth: 150,
  actionColOptions: { span: 24 },
  schemas,
  // baseColProps: { span: 24 },
  showActionButtonGroup: false
})

const computePriceFn = () => {
  if (itemdata.value) {
    // 应付
    const cost = itemdata.value.reduce((erp, item) => {
      return add(
        erp,
        new Decimal(item.unit_price ?? 0)
          .mul(item.qty_purchased_actual ?? 0)
          .toDecimalPlaces(2)
          .toNumber(),
        2
      )
    }, 0)

    //税金
    const taxamount = itemdata.value.reduce((erp, item) => {
      return add(
        erp,
        new Decimal(item.unit_price ?? 0)
          .div(1 + (item.tax_point || 0) / 100)
          .mul((item.tax_point || 0) / 100)
          .mul(item.qty_purchased_actual || 0)
          .toDecimalPlaces(2)
          .toNumber()
      )
    }, 0)
    //开票税点加价金额
    const addpoint = new Decimal(cost ?? 0)
      .div(1 + (init_record.value?.add_point || 0) / 100)
      .mul((init_record.value?.add_point || 0) / 100)
      .toDecimalPlaces(2)
      .toNumber()
    //成本
    const cost_price = itemdata.value.reduce((erp, item) => {
      return add(
        erp,
        new Decimal(item.unit_price ?? 0)
          .div(1 + (init_record.value?.add_point || 0) / 100)
          .mul(item.qty_purchased_actual ?? 0)
          .toDecimalPlaces(2)
          .toNumber(),
        4
      )
    }, 0)

    return [taxamount, cost, addpoint, cost_price]
  } else {
    return [0, 0, 0, 0]
  }
}

const allTotalPriceArr = computed(computePriceFn)
//公转私金额计算
function handleFieldChange() {
  const tabledata = tableRef.value?.getTableData()
  const formdata = getFieldsValue()
  console.log(tabledata)
  console.log(formdata)

  tabledata?.fullData.forEach((item: any) => {
    item.unit_amount =
      formdata.change_type == 2
        ? new Decimal(item.unit_price ?? 0)
            .div(1 + (init_record.value?.add_point || 0) / 100)
            .toDecimalPlaces(4)
            .toNumber()
        : new Decimal(item.unit_price ?? 0)
            .mul(1 + (formdata.add_point || 0) / 100)
            .toDecimalPlaces(4)
            .toNumber()
    item.unit_amount_new = new Decimal(item.unit_amount ?? 0)
      .mul(item.qty_purchased_actual ?? 0)
      .toDecimalPlaces(2)
      .toNumber()
  })
  gridDataSource.value = cloneDeep(tabledata?.fullData)
  /**
   * 金额计算  公转私 新采购单价*数量 私转公 原采购单价*数量 成本总价 新采购金额总和
   */
  const totalcost = tabledata?.fullData.reduce((erp, item) => {
    const cost = new Decimal(item.unit_amount_new).toNumber()
    return add(erp, cost, 2)
  }, 0)
  //税金
  const tax_amount = tabledata?.fullData.reduce((erp, item) => {
    const cost =
      formdata.change_type == 2
        ? new Decimal(item.unit_amount_new ?? 0)
            .mul(formdata.tax_rate || 0)
            .div(100)
            .toNumber()
        : new Decimal(item.unit_price_new ?? 0)
            .mul(item.tax_rate || 0)
            .div(100)
            .toNumber()
    return add(erp, cost, 2)
  }, 0)

  const amount = tabledata?.fullData.reduce((erp, item) => {
    const cost = formdata.change_type == 2 ? new Decimal(item.unit_amount_new).toNumber() : new Decimal(item.unit_price_new).toNumber()
    return add(erp, cost, 2)
  }, 0)
  //加收税金
  const add_point_amount = tabledata?.fullData.reduce((erp, item) => {
    const cost =
      formdata.change_type == 2
        ? new Decimal(item.unit_amount_new)
            .mul(item.add_point || 0)
            .div(100)
            .toNumber()
        : new Decimal(item.unit_price)
            .mul(formdata.add_point || 0)
            .mul(item.qty_purchased_actual || 0)
            .div(100)
            .toNumber()
    return add(erp, cost, 2)
  }, 0)

  const amount_cost = tabledata?.fullData.reduce((erp, item) => {
    console.log(item.unit_amount_new)
    return erp + new Decimal(item.unit_amount_new).toNumber()
  }, 0)

  setFieldsValue({
    cost: totalcost,
    tax_amount: tax_amount,
    amount: formdata.change_type == 2 ? amount : sub(totalcost, add_point_amount, 2),
    add_point_amount,
    tax_rate: formdata.change_type == 1 ? formdata.tax_rate : 0,
    add_point: formdata.change_type == 1 ? formdata.add_point : 0,
    amount_cost: formdata.change_type == 2 ? amount_cost : sub(totalcost, add_point_amount, 2)
  })
}

//表格
async function getitemDetail(record) {
  const { items } = await getPurchaseDetail({ doc_id: record.id, pageSize: 9999 })
  console.log(items)
  const newitems = items
    .filter((item) => item.is_purorder !== 1)
    .map((item) => {
      return {
        ...item,
        unit_price_new: new Decimal(item.unit_price).mul(item.qty_purchased_actual).toDecimalPlaces(2).toNumber()
      }
    })

  gridDataSource.value = cloneDeep(newitems)
}

const createActions = (record): ActionItem[] => [
  {
    label: '删除',
    color: 'error',
    onClick: () => {
      tableRef.value?.remove([record])
    }
  }
]

function handleBatchDelete() {
  const selectedRow = tableRef.value?.getCheckboxRecords()
  if (selectedRow && selectedRow?.length === 0) return message.error('请勾选对应商品')
  tableRef.value?.removeCheckboxRow()
}

const footerSpanMethod: VxeTablePropTypes.FooterSpanMethod = ({ $rowIndex, _columnIndex }) => {
  if ($rowIndex === 0) {
    if (_columnIndex === 1) {
      return {
        rowspan: 1,
        colspan: 30
      }
    }
  } else if ($rowIndex === 1) {
    if (_columnIndex === 1) {
      return {
        rowspan: 1,
        colspan: 30
      }
    }
  }
}
watchEffect(() => {
  footergettable.value = tableRef.value?.getTableData().fullData?.length
  footercheecktable.value = tableRef.value?.getCheckboxRecords(true).map((item) => item.id)?.length
  tableRef.value?.updateFooter()
})

const footerMethod: VxeTablePropTypes.FooterMethod = ({ columns }) => {
  const footerData = [
    columns.map((column, columnIndex) => {
      if (columnIndex === 1) {
        return `显示商品种类数量: ${footergettable.value}`
      }
      return null
    }),
    columns.map((column, columnIndex) => {
      if (columnIndex === 1) {
        return `勾选商品数量: ${footercheecktable.value}`
      }
      return null
    })
  ]
  return footerData
}

//提交
async function handleOk() {
  try {
    await changeOkLoading(true)
    const formdata = await validate()
    const tabledata = tableRef.value?.getTableData()
    if (!tabledata?.fullData.length) {
      message.error('请选择商品')
      return
    }
    console.log(tableRef.value?.columns)
    const params = {
      doc: {
        ...formdata,
        id: init_record.value.id,
        work_id: init_record.value.work_id,
        strid: init_record.value.strid,

        dept_id: init_record.value.dept_id,
        add_point_amount_org: allTotalPriceArr.value[2],
        org_amount: allTotalPriceArr.value[1]
      },

      items: tabledata.fullData.map((item) => {
        const temporary = {}
        for (let key of columns as any) {
          if (key.field !== 'action') {
            temporary[`${key.field}`] = item[`${key.field}`]
          }
          if (key.field === 'qty_purchased_actual') {
            temporary[`${key.field}`] = Number(item[`${key.field}`])
          }
        }
        // 添加 org_amount 字段，值为 unit_price * qty_purchased_actual
        // temporary[`org_amount`] = Number(item[`unit_price`]) * Number(item[`qty_purchased_actual`])
        return temporary
      })
    }
    console.log(params)

    const { news } = await purchasecreateMaleSelf(params)
    if (news) {
      setTimeout(() => {
        closeDrawer()
        changeOkLoading(false)
      }, 1000)
    }
  } catch (e) {
    changeOkLoading(false)
    console.log(e)
  } finally {
    changeOkLoading(false)
  }
}
</script>
