<template>
  <BasicModal @register="registermodal" title="订单金额追加" @ok="addhandle" width="600px">
    <BasicForm @register="registerform" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicForm, useForm } from '/@/components/Form'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { updateFormSchema } from '../datas/modal'
import { ref } from 'vue'
import { addPurchase } from '/@/api/erp/purchaseOrder'
import { message } from 'ant-design-vue'
const record = ref()
const emit = defineEmits(['success', 'register', 'registerform'])
const [registermodal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(async (data) => {
  resetFields()
  console.log(data)

  resetSchema(await updateFormSchema(checkval, data.record.supplier_id))
  record.value = data.record
  setFieldsValue({ strid: data.record.strid })
})
const [registerform, { getFieldsValue, resetFields, resetSchema, setFieldsValue, validate }] = useForm({
  showActionButtonGroup: false,
  layout: 'vertical',
  labelWidth: 200
})
const account_code = ref()
//赋值
function checkval(val) {
  console.log(val)
  account_code.value = val.account_code
}
//提交
async function addhandle() {
  try {
    await changeLoading(true)
    await changeOkLoading(true)
    await validate()
    const fromvalue = await getFieldsValue()
    delete fromvalue.accout_name
    const params = {
      ...fromvalue,
      dept_id: record.value.dept_id,
      supplier_id: record.value.supplier_id,
      work_id: record.value.work_id,
      account_code: account_code.value,
      order: 2
    }
    console.log(params)

    const res = await addPurchase(params)
    console.log(res)
    if (res.news) {
      message.success('补款成功')
      await closeModal()
      emit('success')
    } else {
      message.error('补款失败')
    }
    changeLoading(false)
    changeOkLoading(false)
  } catch (err) {
    console.log('提交金额追加错误', err)
    changeLoading(false)
    changeOkLoading(false)
    throw new Error(`${err}`)
  }
}
</script>
