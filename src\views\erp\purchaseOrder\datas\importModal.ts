import type { BasicColumn, FormSchema } from '/@/components/Table'
export function transformData2Import(data: Recordable[]): any[] {
  const fieldMap = {
    article: 'Article',
    replacement: '替换号',
    sin: '订单号',
    price: '单价',
    quantity: '采购入库数量',
    supplier: '供应商名称'
  }

  return data.map((obj) => {
    const cookedData: any = {
      article: '',
      replacement: '',
      sin: '',
      price: undefined,
      quantity: undefined,
      supplier: ''
    }
    for (const key in cookedData) {
      if (fieldMap[key]) {
        cookedData[key] = obj[fieldMap[key]]
      }
    }
    // 去除空格
    cookedData.article = String(cookedData.article ? cookedData.article : '').replace(/\s/g, '')
    cookedData.replacement = String(cookedData.replacement ? cookedData.replacement : '').replace(/\s/g, '')
    cookedData.supplier = String(cookedData.supplier ? cookedData.supplier : '').replace(/\s/g, '')
    cookedData.sin = String(cookedData.sin ? cookedData.sin : '').replace(/\s/g, '')

    // console.log(cookedData, 'cookedData')
    return cookedData
  })
}

export const columns: BasicColumn[] = [
  {
    title: 'Article',
    dataIndex: 'article',
    width: 120,
    resizable: true
  },
  {
    title: '替换号',
    dataIndex: 'replacement',
    width: 120,
    resizable: true
  },
  {
    title: '订单号',
    dataIndex: 'sin',
    width: 120,
    resizable: true
  },
  {
    title: '单价',
    dataIndex: 'price',
    width: 120,
    resizable: true
  },
  {
    title: '采购入库数量',
    dataIndex: 'quantity',
    width: 120,
    resizable: true
  },
  {
    title: '供应商',
    dataIndex: 'supplier',
    width: 120,
    resizable: true
  }
]

export const childColumns: BasicColumn[] = [
  {
    title: '销售单',
    dataIndex: 'sale_order',
    children: [
      {
        title: '订单号',
        dataIndex: 'work_source_uniqid',
        width: 120,
        resizable: true
      },
      {
        title: '数量',
        dataIndex: 'work_quantity',
        width: 100,
        resizable: true
      }
    ]
  },
  {
    title: '采购单',
    dataIndex: 'purchase_order',
    children: [
      {
        title: '采购单号',
        dataIndex: 'purchase_number',
        width: 120,
        resizable: true
      },
      {
        title: '采购数量',
        dataIndex: 'purchase_quantity',
        width: 100,
        resizable: true
      }
    ]
  },
  {
    title: '入库单',
    dataIndex: 'in_warehouse',
    children: [
      {
        title: '入库单号',
        dataIndex: 'in_number',
        width: 120,
        resizable: true
      },
      {
        title: '入库数量',
        dataIndex: 'in_quantity',
        width: 100,
        resizable: true
      }
    ]
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'article',
    label: 'Article',
    component: 'Input'
  },
  {
    field: 'replacement',
    label: '替换号',
    component: 'Input'
  },
  {
    field: 'sin',
    label: '订单号',
    component: 'Input'
  },
  {
    field: 'price',
    label: '单价',
    component: 'Input'
  },
  {
    field: 'quantity',
    label: '采购入库数量',
    component: 'Input'
  },
  {
    field: 'supplier',
    label: '供应商名称',
    component: 'Input'
  }
]
