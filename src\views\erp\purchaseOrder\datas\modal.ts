import { FormSchema } from '/@/components/Form'
import { getStaffList } from '/@/api/baseData/staff'
import { getCategory } from '/@/api/financialDocuments/otherIncome'
import { BasicColumn } from '/@/components/Table'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
// import { getAccountList, getDeptList } from '/@/api/commonUtils'

import { Rule } from 'ant-design-vue/lib/form'
import { getSupplier } from '/@/api/baseData/supplier'
import { mul } from '/@/utils/math'
import { getRmbquot } from '/@/api/erp/sales'

export async function updateFormSchema(hand: Function, supplierid: number): Promise<FormSchema[]> {
  const modalSchemas: FormSchema[] = [
    {
      field: 'is_finance',
      label: '金额追加类型',
      component: 'Select',
      defaultValue: 0,
      componentProps: () => {
        return {
          options: [
            { label: '正常支出', value: 0 },
            { label: '财务支出', value: 1 }
          ]
        }
      },
      colProps: { span: 24 }
    },
    {
      field: 'strid',
      label: '采购单号',
      component: 'Input',
      dynamicDisabled: true,
      colProps: { span: 24 }
    },
    {
      field: 'amount',
      label: '追加金额',
      component: 'InputNumber',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        min: 0,
        precision: 4
      }
    },
    {
      field: 'supplier_item_id',
      label: '银行账户',
      component: 'PagingApiSelect',
      required: true,
      componentProps: ({ formModel }) => {
        return {
          fieldNames: {
            label: 'account',
            value: 'id'
          },
          api: async () => {
            const { items } = await getSupplier({ id: supplierid })
            return items[0].item
          },
          selectProps: {
            placeholder: '请选择',
            showSearch: true,
            allowClear: true,
            optionFilterProp: 'bank_name'
          },
          resultField: 'items',
          onChange(val, shall) {
            if (!val) return
            console.log(shall)

            formModel.accout_name = shall.account_name
          }
        }
      },
      colProps: {
        span: 24
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'accout_name',
      label: '账户名称',
      component: 'Input',
      dynamicDisabled: true,
      colProps: {
        span: 24
      }
    },
    {
      field: 'applicant',
      label: '追加金额申请人',
      component: 'ApiSelect',
      required: true,
      colProps: { span: 24 },
      componentProps: () => {
        return {
          api: getStaffList,
          selectProps: {
            fieldNames: { key: 'key', value: 'id', label: 'name' },
            showSearch: true,
            placeholder: '请选择',
            allowClear: true,
            optionFilterProp: 'name'
          },
          resultField: 'items'
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'inCharge',
      label: '追加款单负责人',
      component: 'ApiSelect',
      required: true,
      colProps: { span: 24 },
      componentProps: () => {
        return {
          api: getStaffList,
          selectProps: {
            fieldNames: { key: 'key', value: 'id', label: 'name' },
            showSearch: true,
            placeholder: '请选择',
            allowClear: true,
            optionFilterProp: 'name'
          },
          resultField: 'items'
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'account_name',
      label: '追加金额科目',
      component: 'ApiSelect',
      required: true,
      colProps: { span: 24 },
      componentProps: () => {
        return {
          api: getCategory,
          resultField: 'items',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'account_name',
              label: 'account_name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'account_name',
            allowClear: true
          },
          onChange: (_, shall) => {
            hand(shall)
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'exchange_rate',
      label: '汇率',
      component: 'ApiSelect',
      defaultValue: '1.000000',
      required: true,
      componentProps: ({ formModel }) => {
        return {
          api: getRmbquot,
          resultField: 'items',
          selectProps: {
            fieldNames: { value: 'fBuyPri', label: 'name' },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            onChange(val, shall) {
              if (val == '1.000000') {
                formModel.fg_amount = undefined
                formModel.total_price = undefined
              }
              formModel.rate = val
              formModel.total_price = mul(val, formModel.fg_amount || 0, 2)
              formModel.currency = shall.name.split('-')[0]
            }
          }
        }
      },
      colProps: { span: 24 },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'rate',
      label: '汇率比例',
      component: 'InputNumber',
      componentProps: ({ formModel }) => {
        return {
          precision: 4,
          min: 0,
          max: 100,
          onChange(val) {
            formModel.total_price = mul(val, formModel.fg_amount || 0, 2)
          }
        }
      },
      colProps: { span: 24 },
      defaultValue: '1.000000'
    },
    {
      field: 'currency',
      label: '币种',
      component: 'Input',
      show: false,
      colProps: { span: 24 }
    },
    {
      field: 'remark',
      label: '追加金额备注',
      required: true,
      component: 'InputTextArea',
      colProps: { span: 24 }
    },
    {
      field: 'desc',
      label: '追加金额描述',
      component: 'InputTextArea',
      colProps: { span: 24 }
    }
  ]
  return modalSchemas
}
export const UploadSchemas: FormSchema[] = [
  {
    field: 'files',
    label: '附件上传',
    component: 'Upload',
    slot: 'Files',
    required: true,
    rules: [
      {
        required: true,
        validator: async (_rule: Rule, value: string) => {
          if (!value || value.length === 0) return Promise.reject('请上传附件')
          return Promise.resolve()
        }
        // trigger: 'change'
      }
    ],
    colProps: {
      span: 24
    }
  }
]
export const ContractSchemas = (supplierid?: string): FormSchema[] => [
  {
    field: 'type',
    label: '合同类型',
    component: 'Select',
    required: true,
    componentProps: {
      options: [
        { value: 1, label: '采购合同(不含税)' },
        { value: 2, label: '采购合同(含税)' },
        { value: 3, label: ' 灯饰购货合同' }
      ]
    },
    colProps: {
      span: 24
    }
  },
  {
    field: 'a_sign_date',
    label: '甲方签订日期',
    component: 'DatePicker',
    required: true,
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    colProps: {
      span: 24
    }
  },
  {
    field: 'b_sign_date',
    label: '乙方签订日期',
    component: 'DatePicker',
    required: true,
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    colProps: {
      span: 24
    }
  },
  {
    field: 'b_supplier_name',
    label: '乙方供应商名称',
    component: 'Input',
    required: true,
    colProps: {
      span: 24
    }
  },
  {
    field: 'b_contacts',
    label: '乙方联系人',
    component: 'Input',
    required: true,
    colProps: {
      span: 24
    }
  },
  {
    field: 'b_address',
    label: '乙方地点',
    component: 'Input',
    required: true,
    colProps: {
      span: 24
    }
  },
  {
    field: 'address',
    label: '交货地点',
    helpMessage: '甲方指定/乙方指定',
    component: 'Input',
    required: true,
    colProps: {
      span: 24
    }
  },
  {
    field: 'supplier_item_id',
    label: '银行账户',
    component: 'PagingApiSelect',
    required: true,
    componentProps: {
      fieldNames: {
        label: 'account_name',
        value: 'id'
      },

      api: async () => {
        const { items } = await getSupplier({ id: supplierid })
        return items[0].item
      },
      selectProps: {
        placeholder: '请选择',
        showSearch: true,
        allowClear: true,
        optionFilterProp: 'bank_name'
      },
      resultField: 'items'
    },
    colProps: {
      span: 24
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'a_contact_way',
    label: '甲方联系方式',
    component: 'Input',
    colProps: {
      span: 24
    },
    required({ model }) {
      return model.type == 3 ? true : false
    },
    show({ model }) {
      return model.type == 3 ? true : false
    }
  },
  {
    field: 'b_contact_way',
    label: '乙方联系方式',
    component: 'Input',
    colProps: {
      span: 24
    },
    required({ model }) {
      return model.type == 3 ? true : false
    },
    show({ model }) {
      return model.type == 3 ? true : false
    }
  },
  {
    field: 'a_credit_code',
    label: '甲方信用代码',
    component: 'Input',
    colProps: {
      span: 24
    },
    required({ model }) {
      return model.type == 3 ? true : false
    },
    show({ model }) {
      return model.type == 3 ? true : false
    }
  },
  {
    field: 'b_credit_code',
    label: '乙方信用代码',
    component: 'Input',
    colProps: {
      span: 24
    },
    required({ model }) {
      return model.type == 3 ? true : false
    },
    show({ model }) {
      return model.type == 3 ? true : false
    }
  },
  {
    field: 'a_sign_name',
    label: '甲方签约代表',
    component: 'Input',
    colProps: {
      span: 24
    },
    required({ model }) {
      return model.type == 3 ? true : false
    },
    show({ model }) {
      return model.type == 3 ? true : false
    }
  },
  {
    field: 'b_sign_name',
    label: '已方签约代表',
    component: 'Input',
    colProps: {
      span: 24
    },
    required({ model }) {
      return model.type == 3 ? true : false
    },
    show({ model }) {
      return model.type == 3 ? true : false
    }
  }
]

/** 采购订单关联下的其他支出单 */
export const columns: BasicColumn[] = [
  {
    title: '其他支出单号',
    dataIndex: 'strid',
    width: 100
  },
  {
    title: '其他支出单金额',
    dataIndex: 'cost',
    width: 50,
    customRender: ({ text }) => `￥ ${formateerNotCurrency.format(text)}`
  }
]

export const schemasaddmodel: FormSchema[] = [
  {
    field: 'audit_at',
    required: true,
    component: 'DatePicker',
    label: '结算日期',
    colProps: {
      span: 24
    },
    componentProps: {
      style: {
        width: '100%'
      },
      valueFormat: 'YYYY-MM-DD'
    }
  }
]
