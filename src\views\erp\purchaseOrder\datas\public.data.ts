import { getSupplier } from '/@/api/baseData/supplier'
import { cpgetList } from '/@/api/erp/purchaseOrder'
import { FormSchema } from '/@/components/Form'
import { VxeGridPropTypes } from 'vxe-table'
export const invoicetype = {
  1: {
    label: '增值税专用发票',
    value: 1
  },
  2: {
    label: '普通发票',
    value: 2
  },
  3: {
    label: '不开票',
    value: 3
  }
}

async function getSupplierFn(record) {
  const { items } = await getSupplier({ id: record })
  return items[0].item
}

export const schemas: FormSchema[] = [
  {
    field: 'supplier_id',
    label: 'supplier_id',
    component: 'Input',
    show: false
  },
  {
    field: 'change_type',
    label: '转换类型',
    required: true,
    component: 'Select',
    componentProps: ({ formModel }) => {
      return {
        options: [
          {
            label: '公转私',
            value: 2
          },
          {
            label: '私转公',
            value: 1
          }
        ],
        onChange(value) {
          formModel.invoice_type = value === 2 ? 3 : undefined
        }
      }
    },
    colProps: { span: 8 }
  },
  {
    field: 're_clause',
    label: '原款项是否退回',
    component: 'Select',
    required: true,
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    },
    colProps: { span: 8 },
    show(renderCallbackParams) {
      return renderCallbackParams.model.change_type ? true : false
    }
  },
  {
    field: 'invoice_type',
    label: '新发票类型',
    component: 'Select',
    required: true,
    componentProps: {
      options: Object.keys(invoicetype).map((key) => {
        return {
          label: invoicetype[key].label,
          value: invoicetype[key].value
        }
      })
    },
    colProps: { span: 8 },
    dynamicDisabled(renderCallbackParams) {
      return renderCallbackParams.model.change_type === 2
    },
    show(renderCallbackParams) {
      return renderCallbackParams.model.change_type ? true : false
    }
  },
  {
    field: 'contracting_party',
    label: '我司签约主体',
    component: 'PagingApiSelect',
    required: true,
    componentProps: {
      api: cpgetList,
      selectProps: {
        fieldNames: { key: 'key', value: 'name', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        optionFilterProp: 'name'
      },
      resultField: 'items'
    },
    colProps: { span: 8 },
    show(renderCallbackParams) {
      return renderCallbackParams.model.change_type ? true : false
    }
  },
  // {
  //   field: 'is_gbuilder',
  //   label: '是否Gbuilder',
  //   component: 'Select',
  //   required: true,
  //   componentProps: {
  //     options: [
  //       {
  //         label: '是',
  //         value: 1
  //       },
  //       {
  //         label: '否',
  //         value: 0
  //       }
  //     ]
  //   },
  //   colProps: { span: 8 },
  //   show(renderCallbackParams) {
  //     return renderCallbackParams.model.change_type ? true : false
  //   }
  // },
  {
    field: 'amount_cost',
    label: '转换后立刻付款金额',
    required: true,
    component: 'InputNumber',
    componentProps: ({ formModel }) => {
      return {
        max: formModel.amount,
        precision: 2
      }
    },
    colProps: { span: 8 },
    show(renderCallbackParams) {
      return renderCallbackParams.model.change_type ? true : false
    },
    dynamicDisabled: true
  },
  {
    field: 'cost',
    label: '含税金额',
    required: true,
    component: 'InputNumber',
    defaultValue: 0,
    colProps: { span: 8 },
    componentProps: {
      precision: 2
    },
    dynamicDisabled: true,
    show(renderCallbackParams) {
      return renderCallbackParams.model.change_type ? true : false
    }
  },
  {
    field: 'tax_rate',
    label: '开票税点',
    component: 'InputNumber',
    colProps: { span: 8 },
    defaultValue: 0,
    componentProps: {
      addonAfter: '%',
      precision: 2,
      min: 0,
      max: 100
    },
    required(renderCallbackParams) {
      return renderCallbackParams.model.change_type == 1 ? true : false
    },
    dynamicDisabled(renderCallbackParams) {
      return renderCallbackParams.model.change_type == 2 ? true : false
    },
    show(renderCallbackParams) {
      return renderCallbackParams.model.change_type ? true : false
    }
  },
  {
    field: 'tax_amount',
    label: '税金',
    required: true,
    defaultValue: 0,
    component: 'InputNumber',
    colProps: { span: 8 },
    dynamicDisabled: true,
    show(renderCallbackParams) {
      return renderCallbackParams.model.change_type ? true : false
    }
  },
  {
    field: 'amount',
    label: '成本总价',
    defaultValue: 0,
    required: true,
    component: 'InputNumber',
    colProps: { span: 8 },
    dynamicDisabled: true,
    show(renderCallbackParams) {
      return renderCallbackParams.model.change_type ? true : false
    }
  },
  {
    field: 'add_point',
    label: '开票加收税点',
    component: 'InputNumber',
    defaultValue: 0,
    colProps: { span: 8 },
    componentProps: {
      addonAfter: '%',
      precision: 2,
      min: 0,
      max: 100
    },
    required(renderCallbackParams) {
      return renderCallbackParams.model.change_type == 1 ? true : false
    },
    dynamicDisabled(renderCallbackParams) {
      return renderCallbackParams.model.change_type == 2 ? true : false
    },
    show(renderCallbackParams) {
      return renderCallbackParams.model.change_type ? true : false
    }
  },
  {
    field: 'add_point_amount',
    label: '开票加收税点金额',
    defaultValue: 0,
    required: true,
    component: 'InputNumber',
    colProps: { span: 8 },
    dynamicDisabled: true,
    show(renderCallbackParams) {
      return renderCallbackParams.model.change_type ? true : false
    }
  },
  {
    field: 'account',
    label: '旧货款付款账号',
    component: 'Input',
    colProps: { span: 8 },
    required: true,
    show(renderCallbackParams) {
      return renderCallbackParams.model.change_type && renderCallbackParams.model.re_clause == 1 ? true : false
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.change_type && renderCallbackParams.model.re_clause == 1 ? true : false
    }
  },
  {
    field: 'bank',
    label: '旧货款收款开户行',
    component: 'Input',
    colProps: { span: 8 },
    required: true,
    show(renderCallbackParams) {
      return renderCallbackParams.model.change_type && renderCallbackParams.model.re_clause == 1 ? true : false
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.change_type && renderCallbackParams.model.re_clause == 1 ? true : false
    }
  },
  {
    field: 'account_name',
    label: '旧货款收款账户名称',
    component: 'Input',
    colProps: { span: 8 },
    required: true,
    show(renderCallbackParams) {
      return renderCallbackParams.model.change_type && renderCallbackParams.model.re_clause == 1 ? true : false
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.change_type && renderCallbackParams.model.re_clause == 1 ? true : false
    }
  },
  {
    field: 'from_account_name',
    label: '付款账号名称',
    component: 'PagingApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: getSupplierFn.bind(null, formModel.supplier_id),
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        selectProps: {
          fieldNames: { key: 'id', value: 'account_name', label: 'account_name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          onChange(_, shall) {
            if (!shall) return
            formModel.from_account = shall.account
            formModel.from_bank = shall.bank
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: { span: 8 },
    required: true,
    show(renderCallbackParams) {
      return renderCallbackParams.model.change_type ? true : false
    }
  },
  {
    field: 'from_account',
    label: '付款账号',
    component: 'Input',
    colProps: { span: 8 },
    required: true,
    dynamicDisabled: true,
    show(renderCallbackParams) {
      return renderCallbackParams.model.change_type ? true : false
    }
  },
  {
    field: 'from_bank',
    label: '付款账号开户行',
    component: 'Input',
    colProps: { span: 8 },
    required: true,
    dynamicDisabled: true,
    show(renderCallbackParams) {
      return renderCallbackParams.model.change_type ? true : false
    }
  }
]

export const columns: VxeGridPropTypes.Columns[] = [
  {
    width: 50,
    type: 'checkbox'
  },
  {
    width: 100,
    type: 'expand',
    slots: { content: 'expandContent', header: 'expandHeader' }
  },
  {
    title: '操作',
    field: 'action',
    width: 150,
    // fixed: 'right',
    slots: { default: 'Action', header: 'ActionHeader' }
  },
  {
    title: '商品名称',
    field: 'name',
    width: 150,
    resizable: true
  },
  {
    title: '产品图片',
    field: 'imgs',
    width: 100,
    resizable: true,
    slots: { default: 'Imgs' }
  },
  {
    title: '产品编码',
    field: 'puid',
    width: 150,
    resizable: true
  },
  {
    title: '采购数量',
    field: 'qty_purchased_actual',
    width: 100,
    resizable: true,
    slots: { default: 'QtyPurchased' }
  },
  {
    title: '原采购单价',
    field: 'unit_price',
    width: 150,
    resizable: true
  },
  {
    title: '原采购金额',
    field: 'unit_price_new',
    width: 150,
    resizable: true
  },
  {
    title: '新采购单价',
    field: 'unit_amount',
    width: 150,
    resizable: true
  },
  {
    title: '新采购金额',
    field: 'unit_amount_new',
    width: 150,
    resizable: true
  },
  {
    title: 'purchase_id',
    field: 'purchase_id',
    width: 150,
    resizable: true,
    ifShow: false
  }
]

//子产品tablecolum
export const tablecolum: VxeGridPropTypes.Columns[] = [
  // {
  //   title: 'id',
  //   field: 'id',
  //   width: 100,
  //   resizable: true,
  //   ifShow: false
  // },
  // {
  //   title: 'request_id',
  //   field: 'request_id',
  //   width: 100,
  //   resizable: true,
  //   ifShow: false
  // },
  // {
  //   title: 'work_id',
  //   field: 'work_id',
  //   width: 100,
  //   resizable: true,
  //   ifShow: false
  // },
  {
    title: '产品名称',
    field: 'name',
    width: 200,
    resizable: true
  },
  {
    title: '采购数量',
    field: '',
    width: 100,
    resizable: true
  },
  {
    title: '采购单位',
    field: 'unit',
    width: 100,
    resizable: true
  },
  {
    title: '采购单价',
    field: 'unit_price',
    width: 100,
    resizable: true
  },
  {
    title: '图片',
    field: 'imgs',
    width: 100,
    resizable: true,
    slots: { default: 'Imgs' }
  }
  // {
  //   title: 'type',
  //   field: 'type',
  //   width: 100,
  //   resizable: true,
  //   ifShow: false
  // }
]
