import { FormSchema } from '/@/components/Table'
import { reactive, ref } from 'vue'
import { uploadApi } from '/@/api/sys/upload'
import { getStaffList } from '/@/api/baseData/staff'
import { getDeptTree } from '/@/api/admin/dept'
import { getPurchaseDetail } from '/@/api/erp/purchaseOrder'
import { getRelatePurchaseList } from '/@/api/erp/inWarehouse'
import { BasicTableProps } from '@/components/VxeTable'

export const loading = ref(false)
export const columns = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    title: '名称',
    field: 'name'
  },
  {
    title: '质检状态',
    field: 'qc_status',
    slots: { default: 'QcStatus' }
  },
  {
    title: '产品状态',
    field: 'status',
    slots: { default: 'GoodsStatus' }
  },
  {
    title: '产品图片',
    field: 'imgs',
    slots: { default: 'Imgs' }
  },
  // {
  //   title: '数量',
  //   field: 'qty_request'
  // },
  {
    title: '未质检数量',
    field: 'qr_num_left'
  },
  {
    title: '质检数量',
    field: 'qr_num',
    slots: { default: 'QcNum' }
  },
  {
    title: '产品编码',
    field: 'puid'
  }
]
export const gridOptions = reactive<BasicTableProps>({
  id: 'VxeTable',
  loading: loading,
  keepSource: true,
  editConfig: { trigger: 'click', mode: 'cell', showStatus: true },
  columns: columns,
  height: 700,
  proxyConfig: null,
  toolbarConfig: null
  // checkboxConfig: {
  //   checkMethod: (data) => {
  //     return data?.row?.qc_status === 0
  //   }
  // }
})

export const getSchemas = (type: 'add' | 'edit' | 'detail', { setFieldsValue, tableRef }): FormSchema[] => [
  {
    label: '质检单ID',
    field: 'id',
    component: 'Input',
    ifShow: type === 'edit',
    show: false
  },
  {
    label: '质检单work_id',
    field: 'work_id',
    component: 'Input',
    ifShow: type === 'edit',
    show: false
  },
  {
    label: '采购单workId',
    field: 'purchase_work_id',
    component: 'Input',
    ifShow: ({ model }) => {
      return model.type === 1
    },
    show: false
  },
  {
    label: '质检类型',
    field: 'type',
    component: 'Select',
    componentProps: {
      options: [
        { label: '采购质检', value: 1 },
        { label: '库存质检', value: 2 }
      ]
    },
    dynamicDisabled: true
  },
  {
    label: '关联采购单',
    field: 'doc_purchase_id',
    component: 'PagingApiSelect',
    componentProps: {
      resultField: 'items',
      api: getRelatePurchaseList,
      searchMode: true,
      pagingMode: true,
      searchParamField: 'strid',
      // immediate: true,
      selectProps: {
        disabled: true,
        fieldNames: { key: 'id', value: 'id', label: 'strid' },
        showSearch: true,
        placeholder: '请选择'
      },
      // params: {
      //   is_wait: 1
      // },
      pagingSize: 20,
      returnParamsField: 'id',
      async onChange(val: number) {
        // 编辑操作，不请求，直接从详情中获取
        if (['edit', 'detail'].includes(type)) return
        try {
          loading.value = true
          const { items } = await getPurchaseDetail({ doc_id: val, pageSize: 500, is_have_qr: 1 })
          await setFieldsValue({
            items: items.map((item) => ({ ...item, qr_num: item.qr_num_left }))
          })
          await tableRef.value.toggleAllCheckboxRow()
          loading.value = false
        } catch (err) {
          throw new Error(err)
        }
      }
    },
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    },
    ifShow: ({ model }) => {
      return model.type === 1
    }
  },
  {
    label: '销售订单workId',
    field: 'sale_work_id',
    component: 'Input',
    ifShow: ({ model }) => {
      return model.type === 2 && ['add', 'edit'].includes(type)
    },
    show: false
  },
  {
    field: 'double_check',
    label: '是否多次质检',
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    },
    required: true,
    dynamicDisabled: ['detail'].includes(type)
  },
  {
    field: 'result',
    label: '质检状态',
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        { label: '通过', value: 1 },
        { label: '不通过', value: 0 }
      ]
    },
    required: true,
    dynamicDisabled: ['detail'].includes(type)
  },
  {
    field: 'qr_type',
    label: '质检方式',
    component: 'Select',
    componentProps: {
      options: [
        { value: 1, label: '线上质检' },
        { value: 2, label: '到厂质检' },
        { value: 3, label: '到仓质检' },
        { value: 4, label: '他人质检' }
      ]
    },
    required: true,
    dynamicDisabled: ['detail'].includes(type)
  },
  {
    field: 'inCharge',
    label: '质检人',
    component: 'PagingApiSelect',
    componentProps: {
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      returnParamsField: 'id',
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        disabled: ['detail'].includes(type)
      }
    },
    required: true
  },
  {
    field: 'content',
    label: '质检内容',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '颜色是否和订单一致Is the color consistent with the order',
          value: '颜色是否和订单一致Is the color consistent with the order'
        },
        {
          label: '外观是否和订单一致Is the appearance consistent with the order',
          value: '外观是否和订单一致Is the appearance consistent with the order'
        },
        {
          label: '质检测试Quality Test',
          value: '质检测试Quality Test'
        },
        {
          label: '防水测试water-proof test',
          value: '防水测试water-proof test'
        },
        {
          label: '零部件完整性检查Component Integrity Check',
          value: '零部件完整性检查Component Integrity Check'
        },
        {
          label: '组装视频Assembly video',
          value: '组装视频Assembly video'
        },
        {
          label: '性能测试Performance Testing',
          value: '性能测试Performance Testing'
        },
        {
          label: '整洁度检查Cleanliness check',
          value: '整洁度检查Cleanliness check'
        },
        {
          label: '加工方式是否一致Whether the processing method is consistent',
          value: '加工方式是否一致Whether the processing method is consistent'
        },
        {
          label: '包装检查Packaging inspection',
          value: '包装检查Packaging inspection'
        }
      ],
      mode: 'multiple'
    },
    required: true,
    dynamicDisabled: ['detail'].includes(type)
  },
  {
    field: 'qr_stage',
    label: '质检时期',
    component: 'Select',
    componentProps: {
      options: [
        { label: '生产中', value: 1 },
        { label: '生产完成', value: 2 },
        { label: '包装后', value: 3 }
      ]
    },
    required: true,
    dynamicDisabled: ['detail'].includes(type)
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      // immediate: false,
      // lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        placeholder: '请选择',
        showSearch: true,
        optionFilterProp: 'name',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    },
    dynamicDisabled: true,
    required: true
  },
  {
    field: 'images',
    label: '图片',
    component: 'Upload',
    slot: 'Images',
    rules: [{ required: true, message: '请上传图片' }]
  },
  {
    field: 'videos',
    label: '视频',
    component: 'Upload',
    componentProps: {
      api: uploadApi,
      maxSize: 200000,
      accept: ['video/*'] // 接受视频文件
    },
    dynamicDisabled: ['detail'].includes(type)
  },
  {
    field: 'items',
    label: '质检产品',
    component: 'Select',
    slot: 'Items',
    required: true
  }
]
