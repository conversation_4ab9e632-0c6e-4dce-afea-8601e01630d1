<template>
  <div>
    <PageWrapper>
      <BasicTable :data-cachekey="routePath" @register="registerTable">
        <!--        <template #toolbar>-->
        <!--          <a-button type="primary" @click="handleAdd">新建</a-button>-->
        <!--        </template>-->
        <template #form-advanceBefore>
          <Button type="primary" class="mr-8px" @click="handleBeforeExport" :loading="isBtnLoding" v-if="hasPermission(585)"
            >条件导出EXCEL</Button
          >
        </template>
        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
          </template>
          <template v-if="column.dataIndex === 'images'">
            <TableImg :imgList="record.images" :simpleShow="true" />
          </template>
        </template>
      </BasicTable>
    </PageWrapper>
    <QcDrawer @register="registerDrawer" @success="reload" />
    <UpdateAtModal @register="registerUpdateAtModal" @success="reload" />
    <UploadModal @register="registerUploadModal" @relaod="reload" />
  </div>
</template>

<script lang="tsx" setup>
import { ActionItem, BasicTable, TableAction, TableImg, useTable } from '/@/components/Table'
import { PageWrapper } from '/@/components/Page'
import { columns, searchSchemas } from './datas/datas'
import { getQcList, nullifyQc, setQcStatus } from '/@/api/erp/qc'
import { QualityDetectionItem } from '/@/api/erp/modle/types'
import QcDrawer from './components/QcDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { useMessage } from '/@/hooks/web/useMessage'
import { usePermission } from '/@/hooks/web/usePermission'
import UpdateAtModal from '/@/views/revisitManage/revisitLog/components/UpdateAtModal.vue'
import * as propertyConst from '/@/views/revisitManage/revisitLog/datas/const'
import { useModal } from '/@/components/Modal'
import { Textarea, Button } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import UploadModal from './components/UploadModal.vue'
import { ref } from 'vue'

const route = useRoute()
const { path: routePath } = route
const { hasPermission } = usePermission()
const { createMessage } = useMessage()
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerUpdateAtModal, { openModal, setModalProps }] = useModal()
const isBtnLoding = ref(false)

const [registerTable, { reload, getForm, setLoading }] = useTable({
  title: '质检单',
  showIndexColumn: false,
  showTableSetting: true,
  isTreeTable: true,
  rowKey: 'id',
  columns: columns,
  api: getQcList,
  actionColumn: { width: 200, title: '操作', dataIndex: 'action' },
  useSearchForm: true,
  formConfig: {
    schemas: searchSchemas,
    labelWidth: 170,
    baseColProps: { span: 6 },
    fieldMapToTime: [
      ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['sale_audit_at', ['sale_audit_at_start', 'sale_audit_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  }
})

function createActions(record: QualityDetectionItem): ActionItem[] {
  return [
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      disabled: record.status !== 0 || record.is_cancel !== 0,
      ifShow: hasPermission(263)
    },
    {
      label: '审核',
      icon: 'ant-design:file-search-outlined',
      popConfirm: {
        title: '确定通过审核？',
        placement: 'left',
        confirm: handleApprove.bind(null, record)
      },
      disabled: record.status !== 0 || record.is_cancel === 1,
      ifShow: hasPermission(264)
    }
  ]
}

function createDropDownActions(record) {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '查看详情',
      onClick: handleView.bind(null, record),
      ifShow: hasPermission(265)
    },
    // {
    //   icon: 'ant-design:delete-outlined',
    //   label: '删除',
    //   popConfirm: {
    //     title: '删除后将无法恢复数据，确定删除吗',
    //     placement: 'left',
    //     confirm: handleDel.bind(null, record),
    //     disabled: record.status !== 0
    //   },
    //   // disabled: record.status !== 0,
    //   ifShow: hasPermission(266)
    // },
    {
      label: '申请延期',
      onClick: () => {
        setModalProps({ title: '申请延期' })
        openModal(true, { work_id: record.work_id, type: propertyConst.QCAPPLYDELAYLABLE, id: record.sale_work_id })
      },
      tooltip: '需求生产完成日期默认会在填写的交付日期上减五天',
      ifShow: hasPermission([288]),
      disabled: record.type !== 1 || record.is_cancel !== 0
      // disabled: record.follow_up_at ? new Date(record.follow_up_at).getTime() > new Date().getTime() : true
      // color: record.follow_up_at ? (new Date(record.follow_up_at).getTime() > new Date().getTime() ? 'success' : 'error') : 'warning'
    },
    {
      label: '作废',
      icon: 'material-symbols:closed-caption-disabled-outline',
      popConfirm: {
        title: (
          <div class="w-100">
            <Textarea autoSize={false} v-model:value={record.nullifyRemark} placeholder="请输入作废备注" allow-clear />
          </div>
        ),
        placement: 'left',
        confirm: handleNullify.bind(null, record),
        disabled: record.is_cancel === 1
      },
      ifShow: hasPermission(296)
    },
    {
      icon: 'ant-design:upload-outlined',
      color: 'error',
      label: '附件更改',
      onClick: handleUppload.bind(null, record),
      ifShow: hasPermission([581])
    }
  ]
}

function handleView(record) {
  setDrawerProps({ title: '查看质检单', showFooter: false })
  openDrawer(true, { record, type: 'detail' })
}

function handleEdit(record) {
  setDrawerProps({ title: '编辑质检单', showFooter: true })
  openDrawer(true, { record, type: 'edit' })
}

// async function handleDel(record) {
//   try {
//     const { msg } = await delQc({ id: record.id })
//     if (msg === 'success') {
//       createMessage.success('删除成功')
//       reload()
//     }
//   } catch (err) {
//     throw new Error(err)
//   }
// }

async function handleApprove(record) {
  try {
    const { msg } = await setQcStatus({ id: record.id, status: 1 })
    if (msg === 'success') {
      createMessage.success('审核成功')
      reload()
    }
  } catch (err) {
    throw new Error(err)
  }
}

async function handleNullify(record) {
  try {
    if (!record.nullifyRemark) return createMessage.error('请先填写作废备注')
    const { msg } = await nullifyQc({ id: record.id, cancel_remark: record.nullifyRemark })
    if (msg === 'success') {
      createMessage.success('作废成功')
      reload()
    }
  } catch (err) {
    throw new Error(err)
  }
}

// 旧erp数据上传附件
const [registerUploadModal, { openModal: openModalUplad }] = useModal()
function handleUppload(record) {
  openModalUplad(true, record)
}

const handleBeforeExport = async () => {
  try {
    isBtnLoding.value = true
    setLoading(true)
    const params = getForm()?.getFieldsValue()
    const response = await getQcList({ is_excel: 1, ...params, pageSize: 10000 }, 'blob', { isTransformResponse: false })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    downloadLink.download = `质检-${+new Date()}.xlsx`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    createMessage.success('导出成功')
  } catch (err) {
    createMessage.error('导出失败')
    setLoading(false)
    throw new Error(err)
  } finally {
    setLoading(false)
    isBtnLoding.value = false
  }
}
</script>
