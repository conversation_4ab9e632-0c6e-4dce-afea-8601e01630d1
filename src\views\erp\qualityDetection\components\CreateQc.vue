<template>
  <div>
    <BasicForm @register="registerForm">
      <template #deptSlot="{ model }">
        <Select v-model:value="model.dept_id" :options="deptOptions" :disabled="true" />
      </template>
      <template #Images>
        <FormItemRest>
          <Upload
            v-model:file-list="imgFileList"
            accept="image/*"
            action="/api/oss/putImg2Stocking"
            list-type="picture-card"
            :custom-request="handleFileRequest"
            multiple
            :capture="null"
            @change="handleRemove"
          >
            <template #itemRender="{ file, originNode }">
              <div style="width: 104px; height: 104px; position: relative">
                <!-- 保留原有的 antd 展示（包括预览和删除） -->
                <component :is="originNode" class="mt-3" />
                <div v-if="file.status === 'error'" style="color: red; text-align: center; font-size: 12px; margin-top: 4px">
                  上传失败，请删除
                </div>
              </div>
            </template>
            <div>
              <plus-outlined />
              <div style="margin-top: 8px">Upload</div>
            </div>
          </Upload>
        </FormItemRest>
      </template>
      <template #Items="{ model }">
        <FormItemRest>
          <Alert type="info" show-icon>
            <template #description>
              请勾选 <span class="text-[#ff0000] font-bold" style="font-size: 15px">待质检</span> 的数据作为质检产品
            </template>
          </Alert>
          <VxeBasicTable ref="tableRef" class="!p-0" :data="model.items" v-bind="gridOptions" :checkboxConfig="checkboxConfig">
            <template #Imgs="{ row }">
              <Row :gutter="15">
                <Col v-for="img in row.imgs" :key="img" :span="4">
                  <Image :src="img" :alt="row.uniqid" />
                </Col>
              </Row>
            </template>
            <template #QcNum="{ rowIndex }">
              <InputNumber
                v-if="model.items && model.items[rowIndex]"
                :max="model.items[rowIndex].qr_num_left"
                :min="0.01"
                v-model:value="model.items[rowIndex].qr_num"
                :precision="2"
              />
            </template>
            <template #GoodsStatus="{ row }">
              {{ t(`qc.${props.qcType === 1 ? 'mapPurchaseStatusQc' : 'mapStockStatusQc'}.${row.status}`) }}
            </template>
            <template #QcStatus="{ row }">
              {{ [0, 1, 2].includes(row.qc_status) ? t(`qc.mapQcStatus.${row.qc_status}`) : row.qc_status }}
            </template>
          </VxeBasicTable>
        </FormItemRest>
      </template>
    </BasicForm>
    <Teleport to="#qc-drawer-submit">
      <a-button :loading="btnLoading" type="primary" @click="handleSubmit">提交</a-button>
    </Teleport>
  </div>
</template>
<script setup lang="ts">
import { getSchemas, gridOptions } from '/@/views/erp/qc/datas/drawer.datas'
import { BasicForm, useForm } from '/@/components/Form'
import { VxeBasicTable, VxeGridInstance, VxeTablePropTypes } from '/@/components/VxeTable'
import { Upload, UploadFile, Form, UploadChangeParam, Select, Row, Col, Alert, message, InputNumber } from 'ant-design-vue'
import { commonImgUpload } from '/@/api/commonUtils/upload'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { ref, reactive, onMounted } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { useI18n } from '/@/hooks/web/useI18n'
import { isNull, pick, isUndefined } from 'lodash-es'
import { useMessage } from '/@/hooks/web/useMessage'
import { Image } from 'ant-design-vue'
// import { sub } from '/@/utils/math'

const { createMessage } = useMessage()
const { t } = useI18n()
const tableRef = ref<VxeGridInstance>(null)
const props = defineProps({
  qcType: {
    type: Number,
    required: true
  },
  selectData: {
    type: Array,
    required: true
  }
})
const emits = defineEmits(['submit'])
const deptOptions = ref<any[]>([])
const FormItemRest = Form.ItemRest
//附件
const imgFileList = ref<UploadFile[]>([])
const uploadQueue = ref<UploadFile[]>([])
const isUploading = ref(false)

const btnLoading = ref<boolean>(false)

const checkboxConfig = reactive<VxeTablePropTypes.CheckboxConfig<any>>({
  trigger: 'default',
  checkMethod: (data) => {
    return [0, 2].includes(data?.row?.qc_status) && data?.row?.status === 2
  }
})

// 处理文件上传队列
async function processUploadQueue() {
  if (!uploadQueue.value || uploadQueue.value.length === 0) {
    isUploading.value = false
    btnLoading.value = false
    return
  }

  isUploading.value = true
  const file = uploadQueue.value[0]

  try {
    if (!file) {
      uploadQueue.value.shift()
      processUploadQueue()
      return
    }

    const result = await commonImgUpload(file as any, 'qualityDetection', file)

    if (result && result.path) {
      // 更新文件状态
      const index = imgFileList.value.findIndex((item) => item?.uid === file?.uid)
      if (index !== -1) {
        imgFileList.value[index] = {
          ...imgFileList.value[index],
          url: result.path,
          status: 'done'
        }
      } else {
        // 如果找不到对应文件，添加到列表
        imgFileList.value.push({
          ...file,
          url: result.path,
          status: 'done'
        })
      }

      // 更新表单值
      try {
        await setFieldsValue({
          images: imgFileList.value.filter((item) => item?.url).map((item) => item.url)
        })
      } catch (error) {
        console.error('更新表单值失败:', error)
      }
    } else {
      // 标记当前文件为 error
      const index = imgFileList.value.findIndex((item) => item?.uid === file?.uid)
      if (index !== -1) {
        imgFileList.value[index] = {
          ...imgFileList.value[index],
          status: 'error'
        }
      } else if (typeof file === 'object') {
        imgFileList.value.push({
          name: file.name,
          uid: file.uid,
          status: 'error'
        } as UploadFile)
      }

      message.error(`${file?.name || '文件'} 上传失败，请删除后重新上传`)
    }
  } catch (err: any) {
    // 标记当前文件为 error
    const index = imgFileList.value.findIndex((item) => item?.uid === file?.uid)
    if (index !== -1) {
      imgFileList.value[index] = {
        ...imgFileList.value[index],
        status: 'error'
      }
    } else if (typeof file === 'object') {
      imgFileList.value.push({
        name: file.name,
        uid: file.uid,
        status: 'error'
      } as UploadFile)
    }

    message.error(`${file?.name || '文件'} 上传失败，请删除后重新上传: ${err?.message || '未知错误'}`)
  }

  // 无论成功失败都 shift 并继续下一个
  uploadQueue.value.shift()
  isUploading.value = uploadQueue.value.length > 0
  btnLoading.value = isUploading.value
  if (isUploading.value) {
    processUploadQueue()
  }
}

async function handleFileRequest({ file, onSuccess, onError }: UploadRequestOption) {
  try {
    if (!file) {
      message.error('无效的文件')
      return
    }

    // 确保 uploadQueue 存在
    if (!uploadQueue.value) {
      uploadQueue.value = []
    }

    // 确保 imgFileList 存在
    if (!imgFileList.value) {
      imgFileList.value = []
    }

    // 将文件添加到上传队列
    uploadQueue.value.push(file as UploadFile)

    // 如果当前没有正在上传的文件，开始处理队列
    if (!isUploading.value) {
      btnLoading.value = true
      processUploadQueue()
    }

    // 立即返回成功，实际上传在队列中处理
    onSuccess!('pending')
  } catch (err: any) {
    message.error(`处理文件失败，请删除失败文件后重新上传: ${err?.message || '未知错误'}`)
    if (onError) onError(err)
  }
}

async function handleRemove({ fileList }: UploadChangeParam) {
  await setFieldsValue({ images: fileList.map((item) => item.url) })
}

const [registerForm, { setFieldsValue, validate, resetSchema }] = useForm({
  showActionButtonGroup: false,
  labelCol: { span: 2 },
  baseColProps: { span: 24 },
  colon: true
})

async function addInit({ qcType, purchaseRecord, stockingRecord }) {
  await resetSchema(getSchemas('add', { setFieldsValue, tableRef }))
  const [purchaseRecordItem] = purchaseRecord
  const mapFn = {
    // 采购质检：1，库存质检：2
    1: purchaseRecord ? setPurchaseQcOrder.bind(null, { qcType, purchaseRecord: purchaseRecordItem }) : null,
    2: stockingRecord ? setStockQcOrder.bind(null, { qcType, stockingRecord }) : null
  }
  mapFn[qcType]?.()
}

async function setPurchaseQcOrder({ qcType, purchaseRecord }) {
  await setFieldsValue({
    type: qcType,
    doc_purchase_id: purchaseRecord?.id ?? null,
    purchase_work_id: purchaseRecord.work_id,
    dept_id: purchaseRecord.dept_id
  })
}

async function setStockQcOrder({ qcType, stockingRecord }) {
  const { dept_id, work_id } = stockingRecord[0]
  await setFieldsValue({
    type: qcType,
    items: stockingRecord.map((item) => ({
      ...item,
      item_stocking_id: item.id,
      qr_num: item.qr_num_left,
      sale_work_id: item.work_id
    })),
    sale_work_id: work_id,
    dept_id: dept_id
  })
  await tableRef.value.setAllCheckboxRow(true)
}

onMounted(() => {
  addInit({ qcType: props.qcType, purchaseRecord: props.selectData, stockingRecord: props.selectData })
})

async function handleSubmit() {
  try {
    const values = await validate()
    const isEmptyImg = values.images.some((item) => isUndefined(item) || isNull(item))
    if (isEmptyImg) {
      return createMessage.error('请等待图片上传完成，再提交！')
    }
    if (tableRef.value.getCheckboxRecords(true).length === 0) {
      return createMessage.error('请勾选待质检的数据作为质检产品')
    }
    let params = { ...values, items: tableRef.value.getCheckboxRecords(true) }
    // 根据不同的type获取保留的字段
    const fields = getParams('add', props.qcType)
    params.items = params.items.map((item) => pick(item, fields))
    // console.log(params)
    emits('submit', params, btnLoading)
  } catch (err) {
    throw new Error(err)
  }
}

/**
 * 根据不同的type，获取保留的字段
 * @param {add | edit} type 新增or编辑
 * @param {number} qcType 1：采购，2：库存
 * @returns string[] 各自类型的保留参数
 */
function getParams(type: 'add' | 'edit', qcType: number) {
  const mapQcType = {
    1: {
      add: ['request_id', 'purchase_id', 'qr_num'],
      edit: ['request_id', 'purchase_id', 'qr_num', 'id']
    },
    2: {
      add: ['request_id', 'purchase_id', 'item_stocking_id', 'qr_num', 'sale_work_id'],
      edit: ['request_id', 'purchase_id', 'item_stocking_id', 'qr_num', 'sale_work_id', 'id']
    }
  }
  return mapQcType[qcType][type]
}
</script>

<style scoped>
::v-deep(.ant-upload-list-picture-card-container) {
  margin-top: 20px !important;
}
::v-deep(.ant-upload.ant-upload-select-picture-card) {
  margin-top: 20px !important;
}
</style>
