<template>
  <div>
    <BasicTable class="p-4" @register="registerTable">
      <template #expandedRowRender="{ record }">
        <BasicTable
          :key="record.status"
          :ref="(el) => (expandedRowRefs[record.id] = el)"
          class="p-4 expand-row-table"
          :api="getItemsList.bind(null, { pageSize: 9999, strid: record.strid })"
          :columns="childRenColumns"
          rowKey="id"
          :canResize="false"
          :rowSelection="rowSelection"
        >
          <template #toolbar>
            <Button :disabled="pushing" @click="endProduction(record.id)">批量设置完成生产</Button>
          </template>
        </BasicTable>
      </template>
    </BasicTable>
    <Teleport to="#qc-drawer-submit">
      <a-button type="primary" disabled>提交</a-button>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { childRenColumns, columnsFn } from '/@/views/erp/PurchaseTracking/datas/datas'
import { getPurchaseOrderList } from '/@/api/erp/purchaseOrder'
import { BasicTable, TableActionType, useTable } from '/@/components/Table'
import { useVModel } from '@vueuse/core'
import { getItemsList, setStatus } from '/@/api/erp/PurchaseTracking'
import { ref } from 'vue'
import { Button } from 'ant-design-vue'
import { useMessage } from '/@/hooks/web/useMessage'

const pushing = ref<boolean>(false)
const { createMessage } = useMessage()
const expandedRowRefs = ref<{ [key: string]: TableActionType | Nullable<any> }>({})
const props = withDefaults(
  defineProps<{
    selectData: any
    rawData: any
  }>(),
  {
    selectData: {},
    rawData: {}
  }
)

const rowSelection = ref({
  getCheckboxProps: (record) => {
    return { disabled: record.status == 2 }
  }
})
console.log(props.rawData)

const emits = defineEmits(['options-change', 'change', 'update:selectData'])

const selectData = useVModel(props, 'selectData', emits)

const [registerTable, { reload: purchaseOrderReload }] = useTable({
  // title: '采购商品跟踪',
  api: async (params) => getPurchaseOrderList({ ...params }),
  columns: columnsFn(),
  searchInfo: {
    is_have_qr: 1,
    basic_work_id: props.rawData.id
    // strid: props.rawData.source_uniqid
  },
  showIndexColumn: false,
  showTableSetting: false,
  rowKey: 'id',
  scroll: { y: '55vh' },
  pagination: {
    // size: 'small',
    pageSize: 10,
    pageSizeOptions: ['10', '20', '100']
  },
  rowSelection: {
    fixed: true,
    type: 'radio',
    getCheckboxProps: (record) => {
      return { disabled: [0, 1].includes(record.qc_status) }
    },
    onChange: (_selectedRowKeys, selectedRows) => {
      selectData.value = selectedRows
      console.log(selectData.value)
    }
  }
})

/** 点击批量设置完成状态 */
async function endProduction(parentId) {
  try {
    pushing.value = true
    const tableAction = expandedRowRefs.value[parentId]?.tableAction
    const selectedRow = tableAction?.getSelectRows().map((item) => ({ id: item.id }))
    if (selectedRow?.length === 0) return createMessage.error('请勾选商品！')
    await setStatus({ ids: selectedRow, status: 2 })
    tableAction?.reload()
    tableAction?.clearSelectedRowKeys()
    createMessage.success('操作成功！')
    purchaseOrderReload()
  } catch (error) {
    console.log(error)
    createMessage.error('操作失败！')
  } finally {
    pushing.value = false
  }
}
</script>

<style scoped lang="less">
.expand-row-table {
  :deep(.ant-table) {
    margin: 0 !important;
  }
}
</style>
