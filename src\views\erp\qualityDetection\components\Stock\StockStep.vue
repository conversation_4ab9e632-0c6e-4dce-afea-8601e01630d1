<template>
  <div>
    <BasicTable class="p-4" @register="registerTable" />
  </div>
</template>

<script setup lang="ts">
import { BasicTable, useTable } from '/@/components/Table'
import { columns } from '/@/views/erp/Inventory/datas/data'
import { getstockList } from '/@/api/erp/inventory'
import { useVModel } from '@vueuse/core'

const props = withDefaults(
  defineProps<{
    selectData: any
    rawData: any
  }>(),
  {
    selectData: {},
    rawData: {}
  }
)

const emits = defineEmits(['options-change', 'change', 'update:selectData'])

const selectData = useVModel(props, 'selectData', emits)

const [registerTable] = useTable({
  // title: '库存货物',
  showIndexColumn: false,
  useSearchForm: false,
  // formConfig,
  columns: columns.filter((item) => !['id', 'unit_price', 'unit_price_right', 'warehouse_id'].includes(item.dataIndex)),
  showTableSetting: false,
  api: (params) => getstockList({ ...params, source_uniqid: props.rawData.source_uniqid, is_have_qr: 1 }),
  scroll: { y: '55vh' },
  rowSelection: {
    fixed: 'left',
    preserveSelectedRowKeys: true,
    getCheckboxProps: (record) => {
      return { disabled: !(record.status !== 0 && record.qc_status !== 1) }
    },
    onChange: (_selectedRowKeys, selectedRows) => {
      selectData.value = selectedRows
      console.log(selectData.value)
    }
  },
  rowKey: 'id',
  pagination: { pageSize: 10 }
})
</script>
