<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="查询未采购商品" width="70%" destroyOnClose>
    <BasicTable
      v-if="record.source_uniqid"
      size="small"
      class="p-4 expand-row-table"
      @register="registerChildrenTable"
      :api="getItemsList.bind(null, { source_uniqid: record.source_uniqid })"
    >
      <template #bodyCell="{ text, column, record }">
        <template v-if="column.key === 'imgs'">
          <TableImg :imgList="text" :simpleShow="true" />
        </template>
        <template v-if="column.key === 'status'">
          <Tag :color="statusMap[record.status]?.color"> {{ statusMap[record.status]?.text }}</Tag>
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script setup lang="ts">
import { useModalInner, BasicModal } from '/@/components/Modal'
import { BasicTable, TableImg, useTable } from '/@/components/Table'
import { childRenColumns, statusMap } from '/@/views/erp/UnPurchaseTracking/datas/datas'
import { getItemsList } from '/@/api/erp/UnPurchaseTracking'
import { Tag } from 'ant-design-vue'
import { ref } from 'vue'

const record = ref({})
const [registerModal] = useModalInner((data) => {
  record.value = data.record
})

/** 注册子表格 */
const [registerChildrenTable, {}] = useTable({
  showIndexColumn: false,
  columns: childRenColumns.filter(
    (item) => !['unit_price_org', 'unit_price', 'total_amount', 'amountNotPurchased'].includes(item.dataIndex)
  ),
  showTableSetting: false,
  useSearchForm: false,
  isTreeTable: false,
  rowKey: 'id',
  canResize: false,
  pagination: {
    pageSize: 10,
    pageSizeOptions: ['10', '20', '100']
  }
})
</script>

<style scoped lang="less">
.expand-row-table {
  :deep(.ant-table) {
    margin: 0 !important;
  }
}
</style>
