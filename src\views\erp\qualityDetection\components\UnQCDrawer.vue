<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="未质检商品" width="90%" destroyOnClose>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column: productColumn, record: productRecord }">
        <template v-if="productColumn.dataIndex === 'product'">
          <div class="product">
            <TableImg :size="60" :simpleShow="true" :imgList="productRecord.imgs" :margin="1" />
            <div class="product-info">
              <div class="name">{{ productRecord.name }}</div>
              <div class="puid">产品编号: {{ productRecord.puid }}</div>
            </div>
          </div>
        </template>
      </template></BasicTable
    >
  </BasicDrawer>
</template>

<script setup lang="ts">
import { getSalesOrderListReq } from '/@/api/erp/sales'
import { useDrawerInner, BasicDrawer } from '/@/components/Drawer'
import { BasicTable, useTable, TableImg } from '/@/components/Table'
import { productColumns } from '../datas/unQCDrawer'

const [registerDrawer, { changeLoading }] = useDrawerInner(async (params) => {
  try {
    changeLoading(true)
    await setProps({ searchInfo: params })
    await reload()
  } catch (e) {
    console.error(e)
  } finally {
    changeLoading(false)
  }
})

/** 注册子表格 */
const [registerTable, { reload, setProps }] = useTable({
  showIndexColumn: false,
  columns: productColumns,
  showTableSetting: false,
  useSearchForm: false,
  isTreeTable: false,
  rowKey: 'id',
  api: getSalesOrderListReq,
  immediate: false,
  canResize: false,
  pagination: {
    pageSize: 10,
    pageSizeOptions: ['10', '20', '100']
  }
})
</script>

<style scoped lang="less">
.product {
  display: flex;

  .vben-basic-table-img.flex.items-center.mx-auto {
    margin-left: 0;
    margin-right: 8px;
  }

  .product-info {
    flex: 1;
    .name {
      font-weight: 700;
      margin-bottom: 6px;
      word-wrap: break-word; /* 将长单词断行 */
      word-break: break-all; /* 强制断行，适用于没有空格的长字符串 */
      white-space: normal; /* 允许文本换行 */
    }

    .puid {
      color: #999;
      font-size: 13px;
      word-wrap: break-word; /* 将长单词断行 */
      word-break: break-all; /* 强制断行，适用于没有空格的长字符串 */
      white-space: normal; /* 允许文本换行 */
    }
  }
}
</style>
