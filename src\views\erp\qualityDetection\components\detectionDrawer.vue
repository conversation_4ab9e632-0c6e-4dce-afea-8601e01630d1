<template>
  <BasicDrawer
    v-bind="$attrs"
    title="生成质检报告"
    @register="registerDrawer"
    width="90%"
    show-footer
    @ok="handleSubmit"
    @close="handleClose"
  >
    <div v-if="propsData.type" class="pl-25 pr-25 pt-10 pb-10">
      <Steps :current="curStep">
        <Step v-for="item in stepArr" :key="item.key" :title="item.title" />
      </Steps>
    </div>
    <component
      v-if="compCurComponent"
      :is="compCurComponent"
      v-bind="compCurParams"
      v-model:selectData="selectData"
      :qc-type="compQcType"
      @submit="handleSubmit"
    />
    <template #footer>
      <span id="qc-drawer-submit"></span>
      <a-button type="default" @click="handleClose">取消</a-button>
      <a-button :disabled="curStep === 0" type="default" @click="curStep -= 1">上一步</a-button>
      <a-button :disabled="curStep === stepArr.length - 1 || selectData.length === 0" type="primary" @click="curStep += 1">下一步</a-button>
      <!--      <a-button :disabled="curStep !== stepArr.length - 1" type="primary" @click="handleSubmit">确定</a-button>-->
    </template>
  </BasicDrawer>
</template>

<script setup lang="ts" name="DetectionDrawer">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { QualityDetectionItem } from '/@/api/erp/modle/types'
import { ref, computed, markRaw } from 'vue'
import { Steps, Step } from 'ant-design-vue'
import { useI18n } from '/@/hooks/web/useI18n'
import { useMessage } from '/@/hooks/web/useMessage'
import CreateQc from './CreateQc.vue'
import PurchaseSelectComp from './Purchase/PurchaseStep.vue'
import StockSelectComp from './Stock/StockStep.vue'
import { qrnupdate } from '/@/api/erp/qc'

const { t } = useI18n()
const { createMessage } = useMessage()
//部门选项
const emits = defineEmits(['success', 'register'])
const curStep = ref<number>(0)

const propsData = ref<{ record?: QualityDetectionItem; type: 'purchase' | 'stock' | '' }>({ type: '' })

const mapStepTitle = computed(() => (propsData.value.type ? t(`qc.mapQcStepTitle.${propsData.value.type}`) : ''))

const stepArr = ref<{ title: string; step: number; key: number }[]>([])

const comp = {
  purchase: [PurchaseSelectComp, CreateQc],
  stock: [StockSelectComp, CreateQc]
}

const mapComp = ref({})

const selectData = ref([])

const compCurComponent = computed(() =>
  propsData.value.type && comp[propsData.value.type] ? markRaw(comp[propsData.value.type][curStep.value]) : null
)

const compCurParams = computed(() =>
  propsData.value.type && mapComp.value[propsData.value.type] ? mapComp.value[propsData.value.type][[curStep.value]]?.params : {}
)

const compQcType = computed(() => {
  const mapType = {
    purchase: 1,
    stock: 2
  }
  return mapType[propsData.value.type]
})

const [registerDrawer, { changeOkLoading, closeDrawer, changeLoading }] = useDrawerInner(
  async (data: { record: QualityDetectionItem; type: 'purchase' | 'stock' }) => {
    try {
      changeOkLoading(false)
      changeLoading(true)

      propsData.value = data
      handleInit(data)
      changeLoading(false)
    } catch (e) {
      throw new Error(`${e}`)
    }
  }
)

function handleClose() {
  closeDrawer()
  curStep.value = 0
  propsData.value = { type: '' }
  selectData.value = []
}

function handleInit({ record }) {
  stepArr.value = [
    { title: mapStepTitle.value, step: 1, key: 0 },
    { title: '填写生成质检单数据', step: 2, key: 1 }
  ]

  mapComp.value = {
    purchase: [{ params: { rawData: record } }, { params: {} }],
    stock: [{ params: { rawData: record } }, { params: {} }]
  }
}

async function handleSubmit(params, btnLoading) {
  btnLoading.value = true
  try {
    const { msg } = await qrnupdate(params)
    if (msg === 'success') {
      // changeOkLoading(false)
      createMessage.success('生成质检报告成功')
      emits('success', { order_id: propsData.value.record?.id })
      setTimeout(() => {
        btnLoading.value = false
      }, 200)
      handleClose()
    } else {
      changeOkLoading(false)
    }
  } catch (e) {
    changeOkLoading(false)
    throw new Error(e)
  }
}

// watchEffect(() => {
//   console.log(selectData.value)
// })
</script>
