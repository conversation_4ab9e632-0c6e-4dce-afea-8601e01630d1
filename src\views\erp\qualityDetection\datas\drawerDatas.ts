import { BasicColumn, FormSchema } from '/@/components/Table'

import { getAccountList } from '/@/api/commonUtils'
import { ref } from 'vue'
import { uploadApi } from '/@/api/sys/upload'

export const qcItemRequestTag = ref<number[]>([]) // 已经质检过的商品id集合
export const columns: BasicColumn[] = [
  {
    title: '名称',
    dataIndex: 'name',
    width: 150,
    resizable: true
  },
  // {
  //   title: '已通过质检',
  //   dataIndex: 'is_pass',
  //   width: 100,
  //   customRender: ({ record }) => {
  //     return h(
  //       Tag,
  //       {
  //         color: qcItemRequestTag.value.includes(record.id) ? 'success' : 'warning'
  //       },
  //       () => (qcItemRequestTag.value.includes(record.id) ? '是' : '否')
  //     )
  //   }
  // },
  {
    title: '产品图片',
    dataIndex: 'imgs',
    width: 100,
    resizable: true
  },
  {
    title: '数量',
    dataIndex: 'qty_request',
    width: 100,
    resizable: true
  },
  {
    title: '已质检数量',
    dataIndex: 'total_qc_num'
  },
  {
    title: '剩余可质检数量',
    dataIndex: 'qc_num_left'
  },
  {
    title: '本次质检数量',
    dataIndex: 'qc_num',
    width: 100,
    resizable: true
  },
  {
    title: '批号',
    dataIndex: 'batch_code',
    width: 200,
    resizable: true
  },
  {
    title: '产品唯一码',
    dataIndex: 'uniqid',
    width: 200,
    resizable: true
  },
  {
    title: '总价',
    dataIndex: 'total_amount',
    width: 150,
    resizable: true
  }
]

export const getSchemas = (type: 'add' | 'edit' | 'detail'): FormSchema[] => [
  {
    label: 'order_id',
    field: 'id',
    component: 'Input',
    show: false
  },
  {
    field: 'double_check',
    label: '是否多次质检',
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    },
    required: true,
    dynamicDisabled: ['edit', 'detail'].includes(type)
  },
  {
    field: 'result',
    label: '质检结果',
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        { label: '通过', value: '通过' },
        { label: '不通过', value: '不通过' }
      ]
    },
    required: true,
    dynamicDisabled: ['edit', 'detail'].includes(type)
  },
  {
    field: 'qc_type_id',
    label: '质检方式',
    component: 'Select',
    componentProps: {
      options: [
        { value: 1, label: '线上质检' },
        { value: 2, label: '到厂质检' },
        { value: 3, label: '到仓质检' },
        { value: 4, label: '他人质检' }
      ]
    },
    required: true,
    dynamicDisabled: ['detail'].includes(type)
  },
  {
    field: 'content',
    label: '质检内容',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '颜色是否和订单一致Is the color consistent with the order',
          value: '颜色是否和订单一致Is the color consistent with the order'
        },
        {
          label: '外观是否和订单一致Is the appearance consistent with the order',
          value: '外观是否和订单一致Is the appearance consistent with the order'
        },
        {
          label: '质检测试Quality Test',
          value: '质检测试Quality Test'
        },
        {
          label: '防水测试water-proof test',
          value: '防水测试water-proof test'
        },
        {
          label: '零部件完整性检查Component Integrity Check',
          value: '零部件完整性检查Component Integrity Check'
        },
        {
          label: '组装视频Assembly video',
          value: '组装视频Assembly video'
        },
        {
          label: '性能测试Performance Testing',
          value: '性能测试Performance Testing'
        },
        {
          label: '整洁度检查Cleanliness check',
          value: '整洁度检查Cleanliness check'
        },
        {
          label: '加工方式是否一致Whether the processing method is consistent',
          value: '加工方式是否一致Whether the processing method is consistent'
        },
        {
          label: '包装检查Packaging inspection',
          value: '包装检查Packaging inspection'
        }
      ],
      mode: 'multiple'
    },
    required: true,
    dynamicDisabled: ['detail'].includes(type)
  },
  {
    field: 'qc_stage_id',
    label: '质检时期',
    component: 'Select',
    componentProps: {
      options: [
        { label: '生产中', value: 1 },
        { label: '生产完成', value: 2 },
        { label: '包装后', value: 3 }
      ]
    },
    required: true,
    dynamicDisabled: ['detail'].includes(type)
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'Select',
    slot: 'deptSlot',
    required: true
  },
  {
    field: 'user_id',
    label: '质检人',
    component: 'ApiSelect',
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        // disabled: ['detail'].includes(type)
        disabled: true
      }
    },
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'images',
    label: '图片',
    component: 'Upload',
    slot: 'Images',
    rules: [{ required: true, message: '请上传图片' }]
  },
  {
    field: 'videos',
    label: '视频',
    component: 'Upload',
    componentProps: {
      api: uploadApi,
      maxSize: 200000,
      accept: ['video/*'], // 接受视频文件
      onChange(list: string[]) {
        console.log(list, 'list')
      }
    },
    dynamicDisabled: ['detail'].includes(type)
  },
  {
    field: 'items',
    label: '质检产品',
    component: 'Select',
    slot: 'Items',
    required: true
  }
]
