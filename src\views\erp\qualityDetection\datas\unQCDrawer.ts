import { h } from 'vue'
import { BasicColumn } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { mapProductStatus } from '/@/views/revisitManage/revisitLog/datas/datas'
import { isEqual } from 'lodash-es'

export const productColumns: BasicColumn[] = [
  {
    title: '商品',
    dataIndex: 'product',
    width: 180,
    align: 'left',
    resizable: true
  },
  // {
  //   title: '图片',
  //   dataIndex: 'imgs',
  //   width: 120,
  //   resizable: true,
  //   customRender: ({ value }) => (isNullOrUnDef(value) ? '' : useRender.renderImg(value, { simpleShow: true }))
  // },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 180,
    customRender: ({ value }) => (isNullOrUnDef(value) ? '' : h('span', null, value)),
    resizable: true
  },
  {
    title: '订单实际需求数量',
    dataIndex: 'qty_request_actual',
    width: 180,
    resizable: true
  },
  {
    //商品状态
    title: '状态',
    dataIndex: 'status',
    width: 200,
    resizable: true,
    customRender: ({ value }) =>
      isNullOrUnDef(value) ? '' : useRender.renderTag(mapProductStatus[value].label, mapProductStatus[value].color)
  },
  {
    title: 'Comfimrd',
    dataIndex: 'comfimrd',
    width: 120,
    resizable: true,
    customRender: ({ value, record }) =>
      useRender.renderTag(value ?? '', isEqual(Number(record.qty_request_actual), Number(value)) ? 'green' : 'red')
  },
  {
    title: 'QC',
    dataIndex: 'qc',
    width: 120,
    resizable: true,
    customRender: ({ value, record }) =>
      useRender.renderTag(value ?? '', isEqual(Number(record.qty_request_actual), Number(value)) ? 'green' : 'red')
  }
]
