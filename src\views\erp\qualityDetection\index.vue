<template>
  <div class="p-4">
    <BasicTable :data-cachekey="routePath" v-model:expandedRowKeys="expandedRowKeys" @register="registerTable">
      <template #toolbar>
        <a-button v-if="hasPermission(500)" type="primary" @click="handleBatchSetStatus">批量修改质检状态</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="createActions(record)"
            :drop-down-actions="createParentDropDownActions(record)"
            :stopButtonPropagation="true"
          />
        </template>
        <template v-if="column.dataIndex === 'source_uniqid'">
          <a-button v-if="hasPermission([151])" class="table-ellipsis-btn" type="link" @click="handlePreviewReport(record)">
            {{ record.source_uniqid }}
          </a-button>
          <div v-else>{{ record.source_uniqid }}</div>
        </template>
      </template>
      <template #expandedRowRender="{ record, expanded }">
        <div class="extend-table-container">
          <BasicTable
            v-if="expanded"
            :ref="(el) => (expandedRowRefs[record.id] = el)"
            :api="(params) => qrngetList({ ...params, basic_work_id: record.id })"
            rowKey="id"
            size="small"
            :showIndexColumn="true"
            :pagination="true"
            :scroll="{ y: 400, x: 0 }"
            :columns="childrenColumns"
            :actionColumn="{
              width: 200,
              title: '操作',
              dataIndex: 'action'
            }"
          >
            <template #bodyCell="{ record: reportRecord, column: reportColumn }">
              <template v-if="reportColumn.dataIndex === 'images'">
                <TableImg :imgList="reportRecord.images" :simpleShow="true" />
              </template>
              <!--            <template v-if="reportColumn.dataIndex === 'product'">-->
              <!--              {{ reportRecord.item.map((item) => item.name).join('，') || '-' }}-->
              <!--            </template>-->
              <template v-if="reportColumn.dataIndex === 'action'">
                <TableAction
                  :actions="createReportActions(reportRecord, record)"
                  :drop-down-actions="createDropDownActions(reportRecord, record)"
                  :stopButtonPropagation="true"
                />
              </template>
            </template>
          </BasicTable>
        </div>
      </template>
      <template #footer v-if="hasPermission([444]) || hasPermission([445])">
        <div class="footer">
          <span style="color: green" class="mr-8 font-weight-bold size-20px" v-if="hasPermission([444])">
            质检提成金额合计：{{ formateerNotCurrency.format(qc_commission) }}
          </span>
          <span style="color: red" class="mr-8 font-weight-bold" v-if="hasPermission([445])">
            销售单总应收金额合计：{{ formateerNotCurrency.format(receivable) }}</span
          >
        </div>
      </template>
    </BasicTable>
    <QcDrawer @register="registerDrawer" @success="handleSuccess" />
    <DetectionDrawer @register="registerDetectionDrawer" @success="handleSuccess" />
    <UpdateAtModal @register="registerUpdateAtModal" @success="handleSuccess" />
    <UnPurchase @register="registerUnPurchase" />
    <UnQCDrawer @register="registerUnQCDrawer" />
    <VerifyDrawer @register="registerSalesDrawer" />
  </div>
</template>

<script setup lang="tsx" name="qualityDetection">
import { BasicTable, useTable, TableAction, ActionItem, TableActionType } from '/@/components/Table'
import { batchSetStatus, getQualityDetectionList } from '/@/api/erp/qualityDetection'
import { columns, searchFormSchema } from './datas/datas'
import { columns as childrenColumns } from '/@/views/erp/qc/datas/datas'
import { useDrawer } from '/@/components/Drawer'
import QcDrawer from '/@/views/erp/qc/components/QcDrawer.vue'
import { QualityDetectionItem } from '/@/api/erp/modle/types'
import { ref, unref } from 'vue'
import TableImg from '/@/components/Table/src/components/TableImg.vue'
import { usePermission } from '/@/hooks/web/usePermission'
import { qrngetList, nullifyQc, qrnsetQcAbnormalRemark, setQcStatus } from '/@/api/erp/qc'
import DetectionDrawer from './components/detectionDrawer.vue'
import * as propertyConst from '/@/views/revisitManage/revisitLog/datas/const'
import { useMessage } from '/@/hooks/web/useMessage'
import UpdateAtModal from '/@/views/revisitManage/revisitLog/components/UpdateAtModal.vue'
import { useModal } from '/@/components/Modal'
import UnPurchase from '/@/views/erp/qualityDetection/components/UnPurchase.vue'
import { Textarea } from 'ant-design-vue'
import { isEmpty } from 'lodash-es'
import UnQCDrawer from './components/UnQCDrawer.vue'
import VerifyDrawer from '/@/views/erp/saleOrder/components/VerifyDrawer.vue'
import { add } from '/@/utils/math'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { useRoute } from 'vue-router'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'
import JSZip from 'jszip'

const route = useRoute()
const { path: routePath } = route
const [registerUnPurchase, { openModal: openUnPurchaseModal }] = useModal()
const [registerUnQCDrawer, { openDrawer: openUnQCDrawer }] = useDrawer()
const { createMessage } = useMessage()
const { hasPermission } = usePermission()
const expandedRowRefs = ref<{ [key: string]: TableActionType | Nullable<any> }>({})
const expandedRowKeys = ref<number[]>([])

const pageSearchInfo = ref({})
//
const qc_commission = ref(0)
const receivable = ref(0)

pageSearchInfo.value = {}
if (window.history.state?.searchParams) {
  pageSearchInfo.value = window.history.state.searchParams
}

const [registerSalesDrawer, { openDrawer: openSalesDrawer, setDrawerProps: setSalesDrawerProps }] = useDrawer()

const [registerUpdateAtModal, { openModal, setModalProps }] = useModal()

const [registerTable, { reload: reloadQualityTable, getForm, getSelectRows }] = useTable({
  title: '质检',
  api: getQualityDetectionList,
  showIndexColumn: false,
  columns,
  rowKey: 'id',
  searchInfo: {
    type: 3
  },
  actionColumn: {
    width: 450,
    title: '操作',
    helpMessage: '采购单只能做采购质检单(采购入库不能做库存质检单)，库存转换或盘点入库的库存只能做库存质检单',
    dataIndex: 'action'
  },
  rowSelection: {},
  useSearchForm: true,
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas: searchFormSchema,
    fieldMapToTime: [
      ['deliver_at', ['deliver_at_start', 'deliver_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['out_at', ['out_at_start', 'out_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['audit_at', ['audit_at_start', 'audit_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  },
  expandIconColumnIndex: -1,
  beforeFetch: async (params) => {
    let pageParams = {}
    if (!isEmpty(pageSearchInfo.value)) {
      const form = getForm()
      pageParams = {
        ...pageSearchInfo.value
      }
      await form.setFieldsValue(pageParams)
      pageSearchInfo.value = {}
    }
    return {
      ...params,
      ...pageParams
    }
  },
  afterFetch: async (data) => {
    qc_commission.value = data.reduce((acc, cur) => {
      return add(acc, cur.qc_commission, 2)
    }, 0)
    receivable.value = data.reduce((acc, cur) => {
      return add(acc, cur.receivable, 2)
    }, 0)
  }
})

const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerDetectionDrawer, { openDrawer: openDetectionDrawer }] = useDrawer()

function createActions(record: QualityDetectionItem): ActionItem[] {
  return [
    {
      label: '未采购商品',
      onClick: handleCheckUnPurchase.bind(null, record),
      badgeProps: {
        count: +record.no_purchase_num
      }
    },
    {
      label: '生成采购质检单',
      onClick: handlePurchaseQc.bind(null, record),
      ifShow: hasPermission(267),
      disabled: +record.purchase_no_qc_num === 0,
      badgeProps: {
        count: +record.purchase_no_qc_num
      }
    },
    {
      label: '生成库存质检单',
      onClick: handleStockQc.bind(null, record),
      ifShow: hasPermission(268),
      disabled: +record.stock_no_qc_num === 0,
      badgeProps: {
        count: +record.stock_no_qc_num
      }
    }
  ]
}

function createParentDropDownActions(record) {
  return [
    {
      label: '导出',
      onClick: handleExportReport.bind(null, record),
      ifShow: hasPermission(391)
    },
    {
      label: '填写质检异常原因',
      // onClick: handleAbnormal.bind(null, record),
      popConfirm: {
        title: (
          <div class="w-100">
            <Textarea key={record.id} autoSize={false} v-model:value={record.abnormalReason} placeholder="请输入质检异常原因" allow-clear />
          </div>
        ),
        placement: 'left',
        confirm: handleAbnormal.bind(null, record),
        disabled: +record.status !== 15
      },
      ifShow: hasPermission(395)
    },

    {
      label: '查看未质检商品',
      onClick: function (record) {
        openUnQCDrawer(true, { work_id: record.id })
      }.bind(null, record),
      ifShow: hasPermission(416)
    },
    {
      label: '查看销售单详情',
      onClick: () => {
        setSalesDrawerProps({ title: '销售单详情' })
        openSalesDrawer(true, record)
      },
      ifShow: hasPermission(428)
    },
    {
      label: '销售单附件下载',
      disabled: !record.files,
      onClick: () => {
        if (record.files) {
          const fileUrls = Array.isArray(record.files) ? record.files : [record.files]
          downloadAndZipFiles(fileUrls)
        }
      },
      ifShow: hasPermission(428)
    }
  ]
}

function createReportActions(reportRecord: QualityDetectionItem, record): ActionItem[] {
  return [
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, reportRecord, record),
      disabled: reportRecord.status !== 0 || reportRecord.is_cancel === 1,
      ifShow: hasPermission(263)
    },
    {
      label: '审核',
      icon: 'ant-design:file-search-outlined',
      popConfirm: {
        title: '确定通过审核？',
        placement: 'left',
        confirm: handleApprove.bind(null, reportRecord, record)
      },
      disabled: reportRecord.status !== 0 || reportRecord.is_cancel === 1,
      ifShow: hasPermission(264)
    }
    // {
    //   icon: 'ant-design:eye-outlined',
    //   label: '详情',
    //   onClick: handleView.bind(null, reportRecord),
    //   ifShow: hasPermission(265)
    // }
  ]
}

function createDropDownActions(reportRecord, record) {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '查看详情',
      onClick: handleView.bind(null, reportRecord, record),
      ifShow: hasPermission(265)
    },
    // {
    //   icon: 'ant-design:delete-outlined',
    //   label: '删除',
    //   popConfirm: {
    //     title: '删除后将无法恢复数据，确定删除吗',
    //     placement: 'left',
    //     confirm: handleDel.bind(null, reportRecord, record),
    //     disabled: reportRecord.status !== 0
    //   },
    //   // disabled: reportRecord.status !== 0,
    //   ifShow: hasPermission(266)
    // },
    {
      label: '申请延期',
      onClick: () => {
        setModalProps({ title: '申请延期' })
        openModal(true, {
          work_id: reportRecord.work_id,
          type: propertyConst.QCAPPLYDELAYLABLE,
          id: reportRecord.sale_work_id,
          order_id: record.id
        })
      },
      tooltip: '需求生产完成日期默认会在填写的交付日期上减五天',
      ifShow: hasPermission([288]),
      disabled: reportRecord.type !== 1 || reportRecord.is_cancel === 1
      // color: reportRecord.follow_up_at ? (new Date(reportRecord.follow_up_at).getTime() > new Date().getTime() ? 'success' : 'error') : 'warning'
    },
    {
      label: '作废',
      icon: 'material-symbols:closed-caption-disabled-outline',
      popConfirm: {
        title: (
          <div class="w-100">
            <Textarea
              key={reportRecord.id}
              autoSize={false}
              v-model:value={reportRecord.nullifyRemark}
              placeholder="请输入作废备注"
              allow-clear
            />
          </div>
        ),
        placement: 'left',
        confirm: handleNullify.bind(null, reportRecord, record),
        disabled: reportRecord.status !== 1 || reportRecord.is_cancel === 1
      },
      ifShow: hasPermission(296)
    }
  ]
}

function handlePreviewReport(record: QualityDetectionItem) {
  expandedRowKeys.value.includes(record.id)
    ? (expandedRowKeys.value = expandedRowKeys.value.filter((item) => item !== record.id))
    : expandedRowKeys.value.push(record.id)
}

function getTableAction(recordId: number) {
  const tableAction = unref(expandedRowRefs)[recordId]?.tableAction
  return tableAction || {}
}

function handleDetectionReport(record: QualityDetectionItem) {
  record.order_id
    ? getTableAction(record.order_id)?.reload && getTableAction(record.order_id)?.reload()
    : getTableAction(record.id)?.reload && getTableAction(record.id)?.reload()
}

function handleSuccess(record: QualityDetectionItem) {
  reloadQualityTable()
  handleDetectionReport(record)
}

function handleView(record) {
  setDrawerProps({ title: '查看质检单', showFooter: false })
  openDrawer(true, { record, type: 'detail' })
}

function handlePurchaseQc(record) {
  // setDetectionDrawerProps({ title: '生成采购质检单' })
  openDetectionDrawer(true, { type: 'purchase', record })
}

function handleStockQc(record) {
  // setDetectionDrawerProps({ title: '生成库存质检单' })
  openDetectionDrawer(true, { type: 'stock', record })
}

async function handleApprove(reportRecord, record) {
  try {
    const { msg } = await setQcStatus({ id: reportRecord.id, status: 1 })
    if (msg === 'success') {
      createMessage.success('审核成功')
      handleSuccess({ order_id: record.id })
      // reload()
    }
  } catch (err) {
    throw new Error(err)
  }
}

function handleEdit(reportRecord, record) {
  setDrawerProps({ title: '编辑质检单', showFooter: true })
  openDrawer(true, { record: reportRecord, type: 'edit', order_id: record.id })
}

// async function handleDel(reportRecord, record) {
//   try {
//     const { msg } = await delQc({ id: reportRecord.id })
//     if (msg === 'success') {
//       createMessage.success('删除成功')
//       handleSuccess({ order_id: record.id })
//       // reload()
//     }
//   } catch (err) {
//     throw new Error(err)
//   }
// }

async function handleNullify(reportRecord, record) {
  try {
    if (!reportRecord.nullifyRemark) return createMessage.error('请先填写作废备注')
    const { msg } = await nullifyQc({ id: reportRecord.id, cancel_remark: record.nullifyRemark })
    if (msg === 'success') {
      createMessage.success('作废成功')
      handleDetectionReport({ order_id: record.id })
      // reload()
    }
  } catch (err) {
    throw new Error(err)
  }
}

function handleCheckUnPurchase(record) {
  openUnPurchaseModal(true, { record })
}

async function handleExportReport(record) {
  try {
    const response = await qrngetList({ is_excel: 1, basic_work_id: record.id }, 'blob', { isTransformResponse: false })

    // const blob = new Blob([response.data], { type: response.headers['content-type'] })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    downloadLink.download = `质检报告-${+new Date()}.xlsx`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    createMessage.success('导出成功')
  } catch (err) {
    console.log(err)
    createMessage.error('导出失败')
  }
}
async function handleAbnormal(record) {
  try {
    const { msg } = await qrnsetQcAbnormalRemark({ work_id: record.id, qc_abnormal_remark: record.abnormalReason })
    if (msg === 'success') {
      handleSuccess(record)
      createMessage.success('质检异常原因设置成功')
      return
    }
    createMessage.success('质检异常原因设置失败')
  } catch (err) {
    createMessage.success('质检异常原因设置失败')
  }
}

async function handleBatchSetStatus() {
  try {
    if (getSelectRows().length < 1) return createMessage.error('请先选中数据')
    const ids = getSelectRows().map((item) => item.id)
    const { news } = await batchSetStatus({ ids })
    if (news === 'success') {
      createMessage.success('设置成功')
      await reloadQualityTable()
    }
  } catch (err) {
    createMessage.error('设置失败')
  }
}

async function downloadAndZipFiles(files: string[]) {
  try {
    const zip = new JSZip()

    // 下载所有文件
    const downloadPromises = files.map(async (fileUrl, index) => {
      const response = await fetch(fileUrl)
      const blob = await response.blob()
      const fileName = `附件${index + 1}${fileUrl.substring(fileUrl.lastIndexOf('.'))}`
      zip.file(fileName, blob)
    })

    await Promise.all(downloadPromises)

    // 生成zip文件
    const content = await zip.generateAsync({ type: 'blob' })

    // 创建下载链接
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(content)
    downloadLink.download = '销售订单附件.zip'

    // 触发下载
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)

    createMessage.success('附件下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    createMessage.error('附件下载失败')
  }
}
</script>

<style scoped lang="less">
.extend-table-container {
  :deep(.ant-table) {
    margin: 0 !important;
  }
}
</style>
