<template>
  <div>
    <BasicTable :data-cachekey="routePath" @register="registerTable">
      <template #toolbar>
        <Tooltip>
          <template #title>选中销售订单生成收款单</template>
          <a-button v-if="hasPermission([53])" @click="debouncedGenerate" :disabled="generateBtnStatus" class="mr-8px">生成收款单</a-button>
        </Tooltip>
        <a-button :loading="exportLoading" v-if="hasPermission(392)" type="primary" @click="handleExport(1)">导出显示的销售单</a-button>
        <a-button :loading="exportLoading" v-if="hasPermission(393)" type="primary" @click="handleExport(2)"
          >导出显示销售单下的收款单</a-button
        >
      </template>
      <template #form-receivable="{ model }">
        <FormItemRest>
          <div style="display: flex; align-items: center">
            <InputNumber v-model:value="model.receivable1" placeholder="应收金额初始值" style="height: 35px" :precision="4" />
            <span class="iconify" data-icon="ant-design:swap-outlined" style="width: 32px; padding: 0 3px 0 3px"></span>
            <InputNumber v-model:value="model.receivable2" placeholder="应收金额最大值" style="height: 35px" :precision="4" />
          </div>
        </FormItemRest>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>

      <template #expandedRowRender="{ expanded, record: { id } }">
        <div class="extend-table-container">
          <!--        <BasicTable @register="registerChildTable" :search-info="{ work_id: id }">-->
          <BasicTable
            v-if="expanded"
            :scroll="{ y: 400, x: 0 }"
            size="small"
            :ref="(el) => (expandedRowRefs[id] = el)"
            rowKey="id"
            :api="(params) => getReceiptList({ ...params, work_id: id, ...child })"
            :showIndexColumn="false"
            :canResize="false"
            :columns="childColumns(handleReloadChild)"
          >
            <template #bodyCell="{ record, column }">
              <template v-if="column.dataIndex === 'files'">
                <div v-if="isArray(record?.files)">
                  <div v-for="(newVal, index) in record?.files" :key="index">
                    <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a>
                  </div>
                </div>
                <!--              <TableImg :imgList="record.files" :simpleShow="true" />-->
              </template>
            </template>
          </BasicTable>
        </div>
      </template>
    </BasicTable>
    <PreviewFile @register="registerPreviewModal" />
    <GenerateModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script setup lang="ts" name="/erp/paySettle">
import { ref } from 'vue'
import { Tooltip, message, InputNumber, Form } from 'ant-design-vue'
import { debounce, isArray } from 'lodash-es'
import { usePermission } from '/@/hooks/web/usePermission'
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import type { ActionItem } from '/@/components/Table'
import { useModal } from '/@/components/Modal'
import { getReceiptList } from '/@/api/financialDocuments/receiptOrder'
import { getSalesOrderList, receiptExport, setSalesStatus } from '/@/api/erp/sales'
import GenerateModal from '../saleOrder/components/GenerateModal.vue'
import type { IRecord } from '../saleOrder/datas/types'
import { columns, searchFormSchema, childColumns } from './datas/datas'
import { useMessage } from '/@/hooks/web/useMessage'
import { createImgPreview } from '/@/components/Preview'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { useRoute } from 'vue-router'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'

const route = useRoute()
const { path: routePath } = route
const exportLoading = ref(false)
const { createMessage } = useMessage()
const FormItemRest = Form.ItemRest
/** 注册modal */
const [registerModal, { openModal }] = useModal()
const [registerPreviewModal, { openModal: openPreviewModal }] = useModal()
const { hasPermission } = usePermission()
const expandedRowRefs = ref({})
//耳机列表收款时间搜索
const child = {
  collection_at_start: null,
  collection_at_end: null,
  bind_fund_start: null,
  bind_fund_end: null,
  doc_fund_created_at_start: null,
  doc_fund_created_at_end: null
}

const [registerTable, { getSelectRows, clearSelectedRowKeys, reload, getForm, setLoading }] = useTable({
  api: getSalesOrderList,
  rowKey: 'id',
  title: '收款结算',
  showIndexColumn: false,
  isTreeTable: true,
  columns,
  searchInfo: {
    from: 2
  },
  useSearchForm: true,
  showTableSetting: true,
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas: searchFormSchema,
    fieldMapToTime: [
      ['receivable', ['receivable1', 'receivable2']],
      ['submited_at', ['submited_at_start', 'submited_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['collection_at', ['collection_at_start', 'collection_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['bind_fund', ['bind_fund_start', 'bind_fund_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['doc_fund_created_at', ['doc_fund_created_at_start', 'doc_fund_created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  },
  rowSelection: {
    type: 'checkbox',
    onChange: handleCheckboxChange,
    getCheckboxProps: (record) => {
      if (record.status === 0 || record.status === 16 || record.is_audit == 1 || record.parent_id !== null) {
        return { disabled: true }
      } else {
        return { disabled: false }
      }
    }
  },
  actionColumn: {
    width: 100,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  beforeFetch: (params) => {
    if (params.collection_at_start || params.collection_at_end) {
      child.collection_at_start = params.collection_at_start
      child.collection_at_end = params.collection_at_end
    } else if (params.bind_fund_start || params.bind_fund_end) {
      child.bind_fund_start = params.bind_fund_start
      child.bind_fund_end = params.bind_fund_end
    } else if (params.doc_fund_created_at_start || params.doc_fund_created_at_end) {
      child.doc_fund_created_at_start = params.doc_fund_created_at_start
      child.doc_fund_created_at_end = params.doc_fund_created_at_end
    } else {
      child.collection_at_start = null
      child.collection_at_end = null
      child.bind_fund_start = null
      child.bind_fund_end = null
      child.doc_fund_created_at_start = null
      child.doc_fund_created_at_end = null
    }
    return params
  }
})

// const [registerChildTable, { reload: reloadChildTable, setColumns: setChildColumns }] = useTable({
//   api: getReceiptList,
//   columns: childColumns(),
//   showIndexColumn: false,
//   pagination: false,
//   canResize: false,
//   afterFetch: async (data) => {
//     await setChildColumns(childColumns(reloadChildTable))
//     return data
//   }
// })

function createActions(record): ActionItem[] {
  return [
    {
      icon: 'clarity:success-line',
      label: '生效',
      popConfirm: {
        okText: '确定',
        title: '确定将订单状态设置成生效状态吗',
        cancelText: '取消',
        placement: 'left',
        confirm: handleChangeStatus.bind(null, record, 1)
      },
      disabled: record.status !== 0 || record.parent_id !== null,
      ifShow: hasPermission([211])
    }
  ]
}

async function handleChangeStatus(record: IRecord, status: number) {
  console.log(status)

  try {
    const result = await setSalesStatus({ id: record.id, status: status })
    console.log(result)
    reload()
  } catch (err) {
    throw new Error(`${err}`)
  }
}

/** 生成收款单 */
const debouncedGenerate = debounce(handleGenerate, 200)
async function handleGenerate() {
  let clientId: Array<number> = []
  let show = false
  const work_ids = ref<any>([])
  try {
    getSelectRows().forEach((item) => {
      clientId.push(item.client_id)
      work_ids.value.push(item.id)
      return item.id
    })

    for (let item of getSelectRows()) {
      if (item.payment_type && item.payment_type !== 1) {
        message.warning('该采购订单已完成付款单最后一笔款或全款生成')
        show = true
        break
      }
    }
    if (!show) {
      if (new Set(clientId).size == 1) {
        openModal(true, { selectRowsData: getSelectRows(), work_ids: work_ids.value })
      } else {
        message.error('不同客户不能生成同一张收款单！')
      }
    }
  } catch (error) {
    message.success('生成收款单失败!')
    throw new Error(`${error}`)
  }
}

function handleSuccess() {
  clearSelectedRowKeys()
  reload()
}

/** 选中 */
const generateBtnStatus = ref(true)
function handleCheckboxChange() {
  if (getSelectRows().length == 0) {
    generateBtnStatus.value = true
  } else {
    generateBtnStatus.value = false
  }
}

// 预览
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openPreviewModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}

function handleReloadChild() {
  for (const key of Object.keys(expandedRowRefs.value)) {
    expandedRowRefs.value[key]?.tableAction?.reload()
  }
}

async function handleExport(type: 1 | 2) {
  try {
    exportLoading.value = true
    setLoading(true)
    const formData = getForm().getFieldsValue()
    const response = await receiptExport({ ...formData, excel_type: type, pageSize: 2000 })

    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    const downloadTitle = {
      1: `销售单-${+new Date()}.xlsx`,
      2: `收款单-${+new Date()}.xlsx`
    }
    downloadLink.download = downloadTitle[type]

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    createMessage.success('导出成功')
  } catch (err) {
    console.log(err)
    createMessage.error('导出失败')
  } finally {
    setLoading(false)
    exportLoading.value = false
  }
}
</script>

<style scoped lang="less">
.extend-table-container {
  :deep(.ant-table) {
    margin: 0 !important;
  }
}
</style>
