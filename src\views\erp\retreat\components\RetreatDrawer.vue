<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    title="新增"
    :show-footer="!['detail'].includes(propsData.type)"
    @ok="handleSubmit"
    width="90%"
    destroyOnClose
  >
    <BasicForm @register="registerForm" @field-value-change="handleFieldChange">
      <template #ProductSlot="{ model }">
        <FormItemRest>
          <Alert message="勾选订单商品作为退货商品" type="info" show-icon />
          <BasicTable @register="registerTable" v-model:expandedRowKeys="expandedRowKeys" :expandIconColumnIndex="-1">
            <template #bodyCell="{ record, column, index }">
              <template v-if="!['detail'].includes(propsData.type)">
                <template v-if="column && column.dataIndex === 'quantity' && model.product_info.length > 0">
                  <FormItemRest>
                    <InputNumber
                      v-if="model.product_info[index]"
                      v-model:value="model.product_info[index].quantity"
                      :min="0"
                      :max="record.maxQuantity"
                      :precision="2"
                      :disabled="model.product_info[index].items_sub.length > 0"
                      @blur="handleQuantityChange(record, index, model.product_info)"
                    />
                  </FormItemRest>
                </template>
                <template v-if="column && column.dataIndex === 'remark' && model.product_info.length > 0">
                  <FormItemRest>
                    <Textarea v-if="model.product_info[index]" v-model:value="model.product_info[index].remark" />
                  </FormItemRest>
                </template>
                <template v-if="column && column.dataIndex === 'desc' && model.product_info.length > 0">
                  <FormItemRest>
                    <Textarea v-if="model.product_info[index]" v-model:value="model.product_info[index].desc" />
                  </FormItemRest>
                </template>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <TableAction :actions="createActions(record)" />
              </template>
            </template>
            <template #expandedRowRender="{ record: cellRecord, index: cellIndex }">
              <BasicTable
                :columns="tablecolum('retreat')"
                :ref="(el) => (expandedRowRefs[cellRecord.id] = el)"
                :can-resize="false"
                rowKey="id"
                :data-source="cellRecord.items_sub"
                :clickToRowSelect="false"
                :rowSelection="{
                  type: 'checkbox',
                  onChange: () => {
                    handleExpandChange(cellRecord, model.product_info, cellIndex)
                  }
                }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'quantity'">
                    <InputNumber
                      v-model:value="record.quantity"
                      :min="0"
                      :default-value="record.quantity_left"
                      :max="record.quantity_left"
                      :precision="0"
                      :disabled="propsData.type === 'detail'"
                      @change="handleExpandChange(cellRecord, model.product_info, cellIndex)"
                    />
                  </template>
                  <template v-if="column.key === 'imgs'">
                    <TableImg :imgList="record.imgs" :simpleShow="true" />
                  </template>
                  <template v-if="column.key === 'files'">
                    <div v-for="(newVal, index) in record.files" :key="index">
                      <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
                    >
                  </template>
                </template>
              </BasicTable>
            </template>
          </BasicTable>
        </FormItemRest>
      </template>
    </BasicForm>
    <PreviewFile @register="registerModal" />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { nextTick, ref, unref, watch } from 'vue'
import { Form, InputNumber, message, Alert, Textarea } from 'ant-design-vue'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { getRetreatDetail, retreatAdd, retreatUpdate } from '/@/api/erp/retreat'
import { ActionItem, BasicTable, TableActionType, useTable, TableAction, TableImg } from '/@/components/Table'
import { getItemRequest } from '/@/api/commonUtils'
import { getPurchaseDetail } from '/@/api/erp/purchaseOrder'
import defaultUser from '/@/utils/erp/defaultUser'
import { columns, createSchemas, selectRowKeys } from '../datas/createDrawerDrawer'
import { add, div, mul } from '/@/utils/math'
import { tablecolum } from '../datas/createDrawerDrawer'
import { useMessage } from '/@/hooks/web/useMessage'
import { useModal } from '/@/components/Modal'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { createImgPreview } from '/@/components/Preview'

const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()
const emit = defineEmits(['success', 'register'])

const FormItemRest = Form.ItemRest

const propsData = ref<{ type: 'add' | 'edit' | 'detail'; record?: Recordable }>({ type: 'add' })

const retreatType = ref<'1' | '2' | '3'>()
const expandedRowKeys = ref<number[]>([])
const expandedRowRefs = ref<{ [key: string]: TableActionType | Nullable<any> }>({})
//子产品sku
const calculatesku = ref<any>([])

const quantitymax = ref(false)
const proporecord = ref()

const [
  registerTable,
  {
    setColumns,
    setTableData,
    getSelectRowKeys,
    setSelectedRowKeys,
    clearSelectedRowKeys,
    deleteSelectRowByKey,
    getSelectRows,
    getDataSource,
    updateTableDataRecord
  }
] = useTable({
  showIndexColumn: false,
  columns,
  dataSource: [],
  pagination: false,
  striped: true,
  rowKey: 'id',
  rowSelection: {
    type: 'checkbox',
    onChange: TableSelect,
    getCheckboxProps: (record) => {
      const retreatType = getFieldsValue().type
      return {
        disabled: record.quantity === 0 || (![0, 2, 4, 3].includes(record.qc_status) && [1, 2].includes(retreatType))
      }
    }
  },
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action'
  },
  clickToRowSelect: false
})

const [registerDrawer, { changeLoading, changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  expandedRowKeys.value = []
  propsData.value = data
  try {
    changeLoading(true)
    await resetFields()
    clearSelectedRowKeys()
    await updateSchema(createSchemas(propsData.value.type, { handleOrderChange, handleRetreatTypeChange }))
    await setFieldsValue({ product_info: [] })
    setTableData([])

    if (['detail', 'edit'].includes(unref(propsData).type)) {
      const { items } = await getRetreatDetail({ id: data.record.id })
      proporecord.value = items.item
      await setFieldsValue({ ...items, product_info: items.items })
      const newdata = items.items.map((item) => ({
        ...item,
        maxQuantity: item.quantity_max,
        total_price: mul(item.quantity, +item.unit_price)
      }))
      await setTableData(newdata)
      setSelectedRowKeys(items.items.map((item) => item.id))
    } else {
      setFieldsValue({
        applicant: defaultUser!.userId,
        inCharge: defaultUser!.userId
      })
    }
  } catch (e) {
    console.log('加载抽屉出错', e)
    throw new Error(`${e}`)
  } finally {
    changeLoading(false)
  }
})

const [registerForm, { resetFields, setFieldsValue, validate, getFieldsValue, updateSchema, clearValidate }] = useForm({
  schemas: createSchemas(propsData.value.type, { handleOrderChange, handleRetreatTypeChange }),
  baseColProps: { span: 24 },
  colon: true,
  showActionButtonGroup: false,
  labelWidth: 120
})

async function handleRetreatTypeChange(type: '1' | '2') {
  try {
    retreatType.value = type

    if (['add'].includes(propsData.value.type)) {
      clearSelectedRowKeys()
      // 清空表单的商品和表格的数据
      setTableData([])
      await setFieldsValue({ product_info: [] })

      await setFieldsValue({ sale_order_id: undefined, purchase_order_id: undefined, dept_id: void 0 })
      // 清除设置默认值的验证提示
      await clearValidate('dept_id')
      // 下面的ifShow还要判断上是否详情和编辑(未做)
      await updateSchema([
        {
          field: 'sale_order_id',
          ifShow: type == '1'
        },
        {
          field: 'purchase_order_id',
          ifShow: type !== '1'
        }
        // {
        //   field: 'doc_in_id',
        //   ifShow: type == '3'
        // }
      ])
    } else {
      const mapType = {
        1: '销售单号',
        2: '采购单号'
        // 3: '入库单号'
      }
      //编辑和详情时
      await updateSchema({
        field: 'type_strid',
        label: mapType[type]
      })
      columns.map((item) => {
        if (item.dataIndex == 'unit_price_tax' || item.dataIndex == 'tax_point') {
          item.defaultHidden = type === '2' ? false : true
        }
      })
      setColumns(columns)
    }
  } catch (err) {
    console.log('退货类型切换时出错', err)
    throw new Error(`${err}`)
  }
}

const work_id = ref<number>()
//关联单下拉框change
async function handleOrderChange(val: number, workId?: number, shall?: any) {
  console.log('handleOrderChange执行')
  changeLoading(true)
  if (['detail', 'edit'].includes(unref(propsData).type)) return
  await setFieldsValue({ dept_id: shall.dept_id })
  // setTableData([])
  clearSelectedRowKeys()
  if (workId) work_id.value = workId

  try {
    switch (retreatType.value) {
      case '1':
        //代码很冗余,未抽取
        const saleOrderResult = await getItemRequest({ work_id: val, pageSize: 99999 })
        await setFieldsValue({
          product_info: saleOrderResult.items
            .filter((item) => item.qty_request_left > 0)
            .map((item) => ({ ...item, quantity: item.qty_request_left, remark: '', desc: '' }))
        })
        calculatesku.value = saleOrderResult.items.map((item) => {
          return {
            id: item.id,
            sku: item.items_sub?.map((val) => {
              return {
                id: val.id,
                quantityPerProduct: val.proportion,
                ratioInProduct: div(val.proportion_org, 100, 2)
              }
            })
          }
        })
        setTableData(
          saleOrderResult.items
            .filter((item) => item.qty_request_left > 0)
            .map((item) => ({
              ...item,
              maxQuantity: item.qty_request_left,
              quantity: item.qty_request_left,
              total_price: mul(item.qty_request_left, +item.unit_price),
              remark: '',
              desc: ''
            }))
        )
        break
      case '2':
        const purchaseOrderResult = await getPurchaseDetail({ doc_id: val, pageSize: 99999 })
        await setFieldsValue({
          product_info: purchaseOrderResult.items
            .filter((item) => item.qty_wait_received > 0)
            .map((item) => ({ ...item, quantity: item.qty_wait_received, remark: '', desc: '' }))
        })
        calculatesku.value = purchaseOrderResult.items.map((item) => {
          return {
            id: item.id,
            sku: item.items_sub?.map((val) => {
              return {
                id: val.id,
                ratioInProduct: div(val.proportion, 100, 2)
              }
            })
          }
        })
        setTableData(
          purchaseOrderResult.items
            .filter((item) => item.qty_wait_received > 0)
            .map((item) => ({
              ...item,
              maxQuantity: item.qty_wait_received,
              quantity: item.qty_wait_received,
              total_price: mul(item.qty_wait_received, +item.unit_price),
              remark: '',
              desc: ''
            }))
        )
        break
      // case '3':
      //   const inWarehouseResult = await getPackageList({ purchase_work_ids: [workId], pageSize: 99999 })
      //   await setFieldsValue({
      //     product_info: inWarehouseResult.items
      //       .filter(
      //         (item) =>
      //           item.is_out == 0 && item.is_in == 2 && item.is_cancel == 0 && item.is_stock == 0 && item.is_old == 0 && item.is_retreat == 0
      //       )
      //       .map((item) => ({ ...item }))
      //   })

      //   setColumns(inwharecolumns)
      //   setTableData(
      //     inWarehouseResult.items
      //       .filter(
      //         (item) =>
      //           item.is_out == 0 && item.is_in == 2 && item.is_cancel == 0 && item.is_stock == 0 && item.is_old == 0 && item.is_retreat == 0
      //       )
      //       .map((item) => ({
      //         ...item
      //       }))
      //   )
      //   break
    }
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
}

async function handleSubmit() {
  await changeOkLoading(true)
  try {
    await validate()
    const formData = getFieldsValue()
    const selectRow = getSelectRowKeys()

    if (selectRow.length === 0) {
      changeOkLoading(false)
      return message.error('至少需要选择一个商品!')
    }
    const { applicant, inCharge, dept_id, remark, product_info, type, tax_amount } = formData

    //商品信息
    let saleOrderItems
    let purchaseOrderItems
    // let inWarehouseOrderItems

    if (propsData.value.type === 'add') {
      saleOrderItems = product_info.filter((item) => selectRow.includes(item.id)).map((item) => mapProductInfo(item, 'add').saleOrderItem)

      purchaseOrderItems = product_info
        .filter((item) => selectRow.includes(item.id))
        .map((item) => mapProductInfo(item, 'add').purchaseOrderItem)

      // inWarehouseOrderItems = product_info
      //   .filter((item) => selectRow.includes(item.id))
      //   .map((item) => mapProductInfo(item, 'add').inWarehouseOrderItem)
    } else {
      saleOrderItems = product_info.map((item) => ({
        ...mapProductInfo(item, 'edit').saleOrderItem,
        action: selectRow.includes(item.id) ? 2 : 3,
        id: item.id
      }))

      purchaseOrderItems = product_info.map((item) => ({
        ...mapProductInfo(item, 'edit').purchaseOrderItem,
        action: selectRow.includes(item.id) ? 2 : 3,
        id: item.id
      }))

      // inWarehouseOrderItems = product_info.map((item) => ({
      //   ...mapProductInfo(item, 'edit').inWarehouseOrderItem,
      //   action: selectRow.includes(item.id) ? 2 : 3,
      //   id: item.id
      // }))
    }

    const params: Recordable = {
      work_id: propsData.value.type === 'add' ? unref(work_id) : undefined,
      doc: {
        id: propsData.value.type === 'edit' && propsData.value.record?.id ? propsData.value.record.id : undefined,
        type,
        applicant,
        inCharge,
        dept_id,
        tax_amount: Number(tax_amount),
        remark: remark ?? '',
        //下面计算总价的数量可能为undefined
        total_price: product_info
          .filter((item) => selectRow.includes(item.id))
          .reduce((prev, curr) => prev + curr.unit_price * curr.quantity, 0)
          .toFixed(2)
      },
      is_change_sale: 0
    }

    if (retreatType.value === '1') {
      params.items = saleOrderItems
    } else if (retreatType.value === '2') {
      params.items = purchaseOrderItems
    }
    if (propsData.value.type === 'add') await retreatAdd(params)

    if (propsData.value.type === 'edit') await retreatUpdate(params)
    await closeDrawer()
    setTimeout(() => {
      changeOkLoading(false)
    }, 2000)
    emit('success')
  } catch (err) {
    console.log('提交退货单出错:', err)
    changeOkLoading(false)
    throw new Error(`${err}`)
  }
}

function mapProductInfo(item, type) {
  const items_sub = ref([])
  const formData = getFieldsValue()

  if (item.items_sub?.length > 0) {
    const tableAction = unref(expandedRowRefs)[item.id]?.tableAction
    const items = tableAction?.getSelectRows()
    if (!items || items.length == 0) {
      message.error('请勾选子产品退货商品')
      throw new Error('请勾选子产品退货商品')
    }

    if (item.quantity == item.qty_request_left) {
      quantitymax.value = true
    } else {
      quantitymax.value = false
    }
    items_sub.value = items?.map((val) => {
      return {
        work_id: unref(work_id) ?? val.sale_work_id,
        id: val.id,
        request_id: val.request_id,
        request_sub_id: formData.type === 1 ? val.id : val.request_sub_id,
        purchase_id: formData.type === 2 ? val.item_purchase_id : undefined,
        purchase_sub_id: formData.type === 2 ? val.id : undefined,
        quantity: quantitymax.value ? Number(val.quantity_left) : Number(val.quantity),
        proportion: val.proportion,
        remark: val.remark,
        desc: val.desc,
        name: val.name,
        imgs: val.imgs ?? [],
        files: val.files ?? []
      }
    })
  }

  const commonProperties = {
    remark: item.remark ?? '',
    desc: item.desc ?? '',
    unit: item.unit,
    unit_price: item.unit_price,
    imgs: item.imgs ?? []
  }

  const saleOrderItem = {
    name: item.name,
    work_id: item.work_id,
    request_id: type === 'add' ? item.id : item.request_id,
    quantity: item.quantity,
    items_sub: items_sub.value,
    ...commonProperties
  }

  const purchaseOrderItem = {
    purchase_id: type === 'add' ? item.id : item.purchase_id,
    name: item.name,
    work_id: item.work_id,
    request_id: item.request_id,
    quantity: item.quantity,
    warehouse_id: item.warehouse_id,
    stocking_id: item.stocking_id,
    items_sub: items_sub.value,
    unit_price_tax: Number(item.unit_price_tax),
    ...commonProperties
  }

  const inWarehouseOrderItem = {
    name: item.name,
    work_id: item.work_id,
    request_id: item.request_id,
    quantity: item.quantity,
    purchase_id: item.purchase_id,
    warehouse_id: item.warehouse_id,
    stocking_id: type === 'add' ? item.id : item.stocking_id,
    ...commonProperties
  }

  return { saleOrderItem, purchaseOrderItem, inWarehouseOrderItem }
}

function createActions(record: any): ActionItem[] {
  return [
    {
      label: '查看子产品',
      disabled: record.items_sub?.length == 0 ? true : false,
      onClick: handleViewRelate.bind(null, record)
    }
  ]
}

//ctions
function handleViewRelate(record) {
  expandedRowKeys.value.includes(record.id)
    ? (expandedRowKeys.value = expandedRowKeys.value.filter((item) => item !== record.id))
    : expandedRowKeys.value.push(record.id)
  nextTick(() => {
    const tableAction = unref(expandedRowRefs)[record.id]?.tableAction
    tableAction?.setSelectedRowKeys(record.items_sub.map((item) => item.id))
  })
}

//展示
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  console.log(prefix)
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}

async function handleQuantityChange(record, index, quantity) {
  if (record.items_sub.length > 0) {
    const quantityboolon = ref(false)
    const tableAction = unref(expandedRowRefs)[record.id]?.tableAction
    const quantitys = ref()
    if (tableAction?.getSelectRows().length > 0) {
      const data = tableAction?.getSelectRows()
      quantitys.value = data.reduce((acc, cur) => {
        return add(acc, mul(cur.quantity, cur.proportion, 2))
      }, 0)

      if (data.length == record.items_sub.length) {
        let allMatch = data.every((item) => {
          return item.quantity == item.quantity_left
        })
        quantityboolon.value = allMatch
      }
    }
    quantity[index].quantity = quantityboolon.value == false ? quantitys.value : record.qty_request_left
  }
  return quantity
}

//展开更改
async function handleExpandChange(row, quantity, cellIndex?) {
  const purchases = ref([])
  const tableAction = unref(expandedRowRefs)[row.id]?.tableAction
  const quantityboolon = ref(false)
  const quantitys = ref()
  const fatherids = getSelectRows().map((item) => item.id)

  if (tableAction?.getSelectRows().length == 0) {
    deleteSelectRowByKey(row.id)
    return
  }

  purchases.value = tableAction?.getSelectRows().map((item) => {
    return {
      id: item.id,
      quantity: Number(item.quantity)
    }
  })
  if (purchases.value.length > 0) {
    const data = tableAction?.getSelectRows()

    const numbers = tableAction?.getSelectRows()[0].request_id
    quantitys.value = data.reduce((acc, cur) => {
      return add(acc, mul(cur.quantity, cur.proportion, 2))
    }, 0)

    if (data.length == row.items_sub.length) {
      let allMatch = data.every((item) => {
        return item.quantity == item.quantity_left
      })
      quantityboolon.value = allMatch
    }
    const show = fatherids.includes(numbers)
    if (show == false) {
      fatherids.push(numbers)
    }
    setSelectedRowKeys([...fatherids])
  }
  quantity[cellIndex].quantity =
    quantityboolon.value == false ? quantitys.value : row.qty_request_left ? row.qty_request_left : row.quantity_max

  updateTableDataRecord(quantity[cellIndex].id, quantity[cellIndex])
  return quantity
}
watch(
  () => selectRowKeys.value,
  (newValue, oldValue) => {
    const added = newValue.filter((item) => !oldValue.includes(item))
    const removed = oldValue.filter((item) => !newValue.includes(item))
    const dataSource = getDataSource()
    if (added.length > 0) {
      const addsoure = dataSource.filter((item) => added.includes(item.id))
      const tableAction = unref(expandedRowRefs)[addsoure[0]?.id]?.tableAction
      if (tableAction) {
        tableAction?.setSelectedRowKeys(addsoure[0].items_sub.map((item) => item.id))
      }
    } else if (removed.length > 0) {
      const remove = dataSource.filter((item) => removed.includes(item.id))
      const tableAction = unref(expandedRowRefs)[remove[0]?.id]?.tableAction
      if (tableAction) {
        tableAction?.clearSelectedRowKeys()
      }
    }
  },
  { deep: true }
)

function handleFieldChange(key, value) {
  console.log(key, value)
  if (key === 'type') {
    console.log(columns)
    columns.map((item) => {
      if (item.dataIndex == 'unit_price_tax' || item.dataIndex == 'tax_point') {
        item.defaultHidden = value === 2 ? false : true
      }
    })
    setColumns(columns)
  }
}

async function TableSelect() {
  selectRowKeys.value = getSelectRowKeys()
  await publictaxamunt()
}
async function publictaxamunt() {
  const selectdata = await getSelectRows()
  const tax_amount = selectdata.reduce((acc, cur) => {
    return add(acc, Number((Number(cur.unit_price_tax) * Number(cur.quantity)).toFixed(2)), 2)
  }, 0)
  //税金tax_point
  const taxamount1 = selectdata.reduce((erp, item) => {
    return add(
      erp,
      Number(
        Number(Number(item.unit_price || 0) / (1 + (item.tax_point || 0) / 100)) *
          ((Number(item.tax_point) || 0) / 100) *
          Number(Number(item.quantity || 0).toFixed(2))
      ),
      2
    )
  }, 0)
  const cost = selectdata.reduce((erp, item) => {
    return add(erp, Number((Number(item.unit_price) * Number(item.quantity)).toFixed(2)), 2)
  }, 0)

  const addpoint = (cost / (1 + (Number(selectdata[0]?.add_point) || 0) / 100)) * ((Number(selectdata[0]?.add_point) || 0) / 100)
  await setFieldsValue({ tax_amount: tax_amount, tax_amount1: taxamount1, tax_amount2: addpoint, app_point: selectdata[0]?.add_point })
}
</script>
