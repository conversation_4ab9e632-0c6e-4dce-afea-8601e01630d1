import { Rule } from 'ant-design-vue/lib/form'
import type { DefaultOptionType } from 'ant-design-vue/es/vc-tree-select/TreeSelect'

import { type FormSchema, type BasicColumn, TableImg } from '/@/components/Table'
import { getDeptSelectTree } from '/@/api/admin/dept'
import { getStaffList } from '/@/api/baseData/staff'
import { getWorkList } from '/@/api/commonUtils'
// import { getRetreatInWarehouseSelect } from '/@/api/erp/retreat'
import { getRelatePurchaseList } from '/@/api/erp/inWarehouse'
const saleStore = useSaleOrderStore()
import { isArray } from 'lodash-es'
import { h, ref } from 'vue'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { message, Popover } from 'ant-design-vue'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { useI18n } from '/@/hooks/web/useI18n'
import Icon from '/@/components/Icon'

const { t } = useI18n()
//已选择的商品
export const selectRowKeys = ref<any[]>([])

export const createSchemas = (
  type: 'add' | 'edit' | 'detail',
  handleFn?: { handleOrderChange?: Function; handleRetreatTypeChange?: Function }
): FormSchema[] => [
  {
    field: 'strid',
    label: '退货单号',
    component: 'Input',
    dynamicDisabled: true,
    ifShow: ['detail', 'edit'].includes(type),
    colProps: {
      span: 8
    }
  },
  {
    field: 'type_strid',
    label: '销售单号',
    component: 'Input',
    dynamicDisabled: true,
    ifShow: ['detail', 'edit'].includes(type),
    colProps: {
      span: 8
    }
  },

  {
    field: 'type',
    label: '退货类型',
    component: 'Select',
    required: true,
    componentProps: () => {
      return {
        disabled: ['detail', 'edit'].includes(type),
        options: [
          {
            value: 1,
            label: '销售退货'
          },
          {
            value: 2,
            label: '采购退货'
          }
          // {
          //   value: 3,
          //   label: '入库退货'
          // }
        ],
        onChange: async (val: number) => {
          console.log('change1 类型change')
          handleFn!.handleRetreatTypeChange!(String(val))
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 8
    }
  },
  {
    field: 'sale_order_id',
    label: '关联销售订单',
    component: 'ApiSelect',
    componentProps: {
      resultField: 'items',
      api: getWorkList,
      searchMode: true,
      searchParamField: 'source_uniqid',
      immediate: false,
      selectProps: {
        allowClear: true,
        fieldNames: { value: 'id', label: 'source_uniqid' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'source_uniqid'
      },
      params: {
        item_left: 1,
        types: [3, 27],
        status: saleStore.retreatSaleStatus,
        pageSize: '999999',
        auth: 2
      },
      onChange: async (val: number, shall) => {
        console.log('销售单change')
        if (!val) return
        // id是work_id
        try {
          handleFn!.handleOrderChange!(val, shall.id, shall)
        } catch (e) {
          throw new Error(`${e}`)
        }
      }
    },
    required: true,
    ifShow: false,
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 8
    }
  },
  {
    field: 'purchase_order_id',
    label: '关联采购订单',
    component: 'ApiSelect',
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    },
    ifShow: false,
    componentProps: {
      api: getRelatePurchaseList,
      searchMode: true,
      searchParamField: 'strid',
      immediate: false,
      selectProps: {
        fieldNames: { work_id: 'work_id', supplier_id: 'supplier_id', value: 'doc_id', label: 'strid' },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        // mode: 'multiple',
        optionFilterProp: 'strid'
      },
      params: { is_wait: 1 },
      resultField: 'items',
      onChange: async (val: number, shall) => {
        console.log('采购单change')
        if (!val) return
        try {
          handleFn!.handleOrderChange!(val, shall.work_id, shall)
        } catch (e) {
          throw new Error(`${e}`)
        }
      }
    },
    colProps: {
      span: 8
    }
  },
  // {
  //   field: 'doc_in_id',
  //   label: '关联入库单',
  //   component: 'ApiSelect',
  //   colProps: { span: 24 },
  //   required: true,
  //   ifShow: false,
  //   componentProps: () => {
  //     return {
  //       api: getRelatePurchaseList,
  //       searchMode: true,
  //       searchParamField: 'strid',
  //       immediate: false,
  //       selectProps: {
  //         fieldNames: { work_id: 'work_id', supplier_id: 'supplier_id', value: 'work_id', label: 'strid' },
  //         showSearch: true,
  //         placeholder: '请选择',
  //         allowClear: true,
  //         // mode: 'multiple',
  //         optionFilterProp: 'strid'
  //       },
  //       resultField: 'items',
  //       onChange: async (val: number, shall) => {
  //         console.log('入库单change')
  //         if (!val) return
  //         try {
  //           handleFn!.handleOrderChange!(val, shall.work_id, shall)
  //         } catch (e) {
  //           throw new Error(`${e}`)
  //         }
  //       }
  //     }
  //   },
  //   itemProps: {
  //     validateTrigger: 'blur'
  //   }
  // },
  {
    field: 'tax_amount',
    label: '不含税退货总金额',
    component: 'Input',
    dynamicDisabled: true,
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.type === 2
    },
    required: true,
    colProps: {
      span: 8
    }
  },
  {
    field: 'tax_amount1',
    label: '税金',
    component: 'Input',
    dynamicDisabled: true,
    colProps: {
      span: 8
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.type === 2
    }
  },
  {
    field: 'tax_amount2',
    label: '开票税点加收金额',
    component: 'Input',
    dynamicDisabled: true,
    colProps: {
      span: 8
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.type === 2
    }
  },
  {
    field: 'app_point',
    label: '开票加收税点',
    component: 'Input',
    dynamicDisabled: true,
    colProps: {
      span: 8
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.type === 2
    }
  },
  {
    field: 'applicant',
    label: '申请人',
    required: true,
    component: 'ApiSelect',
    componentProps({ formActionType }) {
      return {
        api: getStaffList,
        resultField: 'items',
        selectProps: {
          // disabled: ['detail'].includes(type),
          disabled: true,
          allowClear: true,
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        },
        params: {
          pageSize: 9999
        },
        onChange: async () => {
          try {
            await formActionType?.validateFields!(['applicant'])
          } catch (e) {
            throw new Error(`${e}`)
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 8
    }
  },
  {
    field: 'inCharge',
    label: '负责人',
    required: true,
    component: 'ApiSelect',
    componentProps: ({ formActionType }) => ({
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        // disabled: ['detail'].includes(type),
        disabled: true,
        allowClear: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      },
      params: {
        pageSize: 9999
      },
      onChange: async () => {
        try {
          await formActionType?.validateFields!(['inCharge'])
        } catch (e) {
          throw new Error(`${e}`)
        }
      }
    }),
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 8
    }
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    required: true,
    componentProps: {
      // disabled: ['detail'].includes(type),
      disabled: true,
      api: getDeptSelectTree,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'id', value: 'id', label: 'name' },
        placeholder: '请选择',
        filterTreeNode: (search: string, item: DefaultOptionType) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 8
    }
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    dynamicDisabled: ['detail'].includes(type),
    colProps: {
      span: 8
    }
  },
  {
    field: 'product_info',
    label: '商品信息',
    component: 'Input',
    required: true,
    slot: 'ProductSlot',
    rules: [{ required: true, validator: validateProductInfo }]
  }
]

function validateProductInfo(_rule: Rule, value: any[]) {
  if (!value || value.length === 0) return Promise.reject('请先选择关联的入库单')
  const validResult = value.filter((item) => selectRowKeys.value.includes(item.id)).every((item) => item.quantity > 0)
  if (!validResult) {
    message.error('退货数量必须大于0')
    return Promise.reject('退货数量必须大于0')
  }
  return Promise.resolve()
}

export const columns: BasicColumn[] = [
  {
    dataIndex: 'name',
    title: '产品名称',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'imgs',
    title: '产品图片',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      if (text) {
        if (isArray(text)) return h(TableImg, { imgList: text, simpleShow: true })
      }
      return ''
    }
  },
  // {
  //   dataIndex: 'qty_total',
  //   title: '采购入库商品总数量',
  //   width: 120,
  //   resizable: true
  // },
  // {
  //   dataIndex: 'qty_residue',
  //   title: '采购入库剩余可用商品数量',
  //   width: 120,
  //   resizable: true
  // },
  {
    dataIndex: 'qc_status',
    title: '质检状态',
    width: 100,
    resizable: true,
    // helpMessage: ['非未质检状态的商品无法进行退货', '请先对质检单进行作废操作', '再执行退货操作'],
    customRender: ({ text }) => {
      const slots = {
        content: () => (
          <span>
            请先将相关的质检单执行 <span style="color: red">作废</span> 操作，再进行退货
          </span>
        )
      }
      return (
        <div>
          {useRender.renderTag(t(`tag.qcStatusTag.${text}.label`), t(`tag.qcStatusTag.${text}.color`))}
          {text === 0 ? null : (
            <Popover title="已质检无法退货" v-slots={slots}>
              <Icon icon="clarity:warning-standard-solid" color="red" class="ml-2" />
            </Popover>
          )}
        </div>
      )
    }
  },
  {
    dataIndex: 'unit',
    title: '单位',
    width: 100,
    resizable: true
  },
  {
    dataIndex: 'unit_price',
    title: '含税单价',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'unit_price_tax',
    title: '不含税单价',
    width: 120,
    resizable: true,
    defaultHidden: true
  },
  {
    dataIndex: 'maxQuantity',
    title: '剩余可退货数量',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'quantity',
    title: '退货数量',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'tax_point',
    title: '税点',
    width: 120,
    resizable: true,
    defaultHidden: true
  },
  {
    dataIndex: 'total_price',
    title: '总价',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'remark',
    title: '备注',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'desc',
    title: '描述',
    width: 120,
    resizable: true
  }
]

//子产品tablecolum
export const tablecolum = (type?: string): BasicColumn[] => [
  {
    title: 'id',
    dataIndex: 'id',
    width: 100,
    resizable: true,
    defaultHidden: true
  },
  {
    title: 'request_id',
    dataIndex: 'request_id',
    width: 100,
    resizable: true,
    defaultHidden: true
  },
  {
    title: 'work_id',
    dataIndex: 'work_id',
    width: 100,
    resizable: true,
    defaultHidden: true
  },
  {
    title: '产品名称',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  {
    title: '产品数量',
    dataIndex: 'quantity',
    width: 100,
    resizable: true
  },
  // {
  //   title: '占比',
  //   dataIndex: 'proportion',
  //   width: 100,
  //   resizable: true,
  //   customRender: ({ text }) => {
  //     return text ? text + '%' : '-'
  //   }
  // },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 100,
    resizable: true
  },
  {
    title: '剩余可退货数量',
    dataIndex: 'quantity_left',
    width: 100,
    resizable: true,
    ifShow: type == 'retreat'
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 100,
    resizable: true
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 100,
    resizable: true
  },
  {
    title: '图片',
    dataIndex: 'imgs',
    width: 100,
    resizable: true
  },
  {
    title: '附件',
    dataIndex: 'files',
    width: 250,
    resizable: true
  },
  {
    title: 'type',
    dataIndex: 'type',
    width: 100,
    resizable: true,
    defaultHidden: true
  }
]
