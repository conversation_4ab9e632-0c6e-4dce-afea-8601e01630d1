import { h } from 'vue'
import type { BasicColumn, FormSchema } from '/@/components/Table'
import { Tag } from 'ant-design-vue'
import { getDeptTree } from '/@/api/admin/dept'
import { getClientList, getErpSupplier } from '/@/api/commonUtils'
import { getStaffList } from '/@/api/baseData/staff'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { DIVIDER_SCHEMA, GET_STATUS_SCHEMA } from '/@/const/status'

export const mapStatus = {
  0: { label: '待执行', color: 'blue' },
  1: { label: '执行中', color: '#108ee9' },
  15: { label: '已完成', color: '#87d068' }
}

export const mapRetreatOrderType = {
  1: { label: '销售退货', color: 'pink' },
  2: { label: '采购退货', color: 'orange' },
  3: { label: '入库退货', color: 'blue' }
}

export const columns: BasicColumn[] = [
  {
    title: '退货单号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '单号',
    dataIndex: 'type_strid',
    width: 200,
    resizable: true,
    customRender: ({ record }) => {
      const firstCol = h('div', {}, `${mapRetreatOrderType[record.type].label}单号:`)
      const secondCol = h('div', {}, `${record.type_strid}`)

      return h('div', {}, [firstCol, secondCol])
    }
  },
  {
    title: '名称',
    dataIndex: 'name',
    width: 350,
    resizable: true
  },
  {
    title: '退货类型',
    dataIndex: 'type',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(mapRetreatOrderType[value].label, mapRetreatOrderType[value].color)
    }
  },
  {
    title: '是否费用订单',
    dataIndex: 'is_consume',
    width: 120,
    resizable: true,
    customRender: ({ record }) => {
      return useRender.renderTag(record.is_consume ? '是' : '否', record.is_consume ? 'red' : 'green')
    }
  },
  {
    title: '订单状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ value }) => {
      return h(Tag, { color: mapStatus[value].color }, () => mapStatus[value].label)
    },
    resizable: true
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    width: 120,
    resizable: true
  },
  {
    title: '退货总金额(含税)',
    dataIndex: 'total_price',
    width: 200,
    resizable: true
  },
  {
    title: '退货总金额(不含税)',
    dataIndex: 'tax_amount',
    width: 200,
    resizable: true
  },
  {
    title: '客户',
    dataIndex: 'client_name',
    width: 120,
    resizable: true
  },
  {
    title: '申请人',
    dataIndex: 'applicant_name',
    width: 120,
    resizable: true
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name',
    width: 120,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 120,
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 200,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 400,
    resizable: true
  }
]
const status_schema = GET_STATUS_SCHEMA(
  Object.keys(mapStatus).map((item) => ({
    value: item,
    label: mapStatus[item].label
  }))
)

export const searchFormSchema: FormSchema[] = [
  status_schema,
  DIVIDER_SCHEMA,
  {
    field: 'strid',
    label: '退货单号',
    component: 'Input'
  },
  {
    field: 'source_uniqid',
    label: '销售单号',
    component: 'Input'
  },
  {
    field: 'type',
    label: '退货类型',
    component: 'Select',
    componentProps: {
      options: [
        { label: '销售退货', value: 1 },
        { label: '采购退货', value: 2 },
        { label: '入库退货', value: 3 }
      ]
    }
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      immediate: false,
      lazyLoad: true,
      api: getDeptTree,
      treeSelectProps: {
        fieldNames: { children: 'children', value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择部门',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  {
    field: 'supplier_id',
    label: '供应商',
    component: 'PagingApiSelect',
    componentProps: () => {
      return {
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        api: getErpSupplier,
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择供应商',
          allowClear: true
        },
        resultField: 'items'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'PagingApiSelect',
    componentProps: () => {
      return {
        api: getStaffList,
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择申请人',
          allowClear: true
        },
        resultField: 'items'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'applicant',
    label: '申请人',
    component: 'PagingApiSelect',
    componentProps: () => {
      return {
        api: getStaffList,
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择申请人',
          allowClear: true
        },
        resultField: 'items'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  // {
  //   field: 'status',
  //   label: '状态',
  //   component: 'Select',
  //   componentProps: () => {
  //     const keyArr = Object.keys(mapStatus)
  //     const options = keyArr.map((item) => ({
  //       value: item,
  //       label: mapStatus[item].label
  //     }))
  //     return {
  //       options
  //     }
  //   }
  // },
  {
    field: 'client_id',
    label: '客户',
    component: 'PagingApiSelect',
    componentProps: {
      api: getClientList,
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      },
      resultField: 'items'
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'check_at',
    label: '审核日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea'
  }
]
