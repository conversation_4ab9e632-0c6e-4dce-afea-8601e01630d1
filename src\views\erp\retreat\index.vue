<template>
  <div>
    <BasicTable :data-cachekey="routePath" @register="registerTable" class="p-4">
      <template #toolbar>
        <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleCreate"> 新增退货</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
      </template>
    </BasicTable>
    <RetreatDrawer @register="registerDrawer" @success="reload" />
  </div>
</template>

<script setup lang="ts" name="/erp/retreat">
import { deleteRetreat, getRetreatList, setRetreatStatus } from '/@/api/erp/retreat'
import { useDrawer } from '/@/components/Drawer'
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import type { ActionItem } from '/@/components/Table'
import { usePermission } from '/@/hooks/web/usePermission'
import RetreatDrawer from './components/RetreatDrawer.vue'
import { columns, searchFormSchema } from './datas/datas'
import { useRoute } from 'vue-router'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'

const route = useRoute()
const { path: routePath } = route
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const { hasPermission } = usePermission()
const [registerTable, { reload, deleteTableDataRecord }] = useTable({
  title: '退货单',
  api: getRetreatList,
  columns,
  showIndexColumn: false,
  useSearchForm: true,
  rowKey: 'id',
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas: searchFormSchema,
    fieldMapToTime: [
      ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['check_at', ['check_at_start', 'check_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  },
  actionColumn: {
    width: 170,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  showTableSetting: true
})

function createActions(record: Recordable): ActionItem[] {
  let buttonList: ActionItem[] = [
    // {
    //   // icon: 'ant-design:eye-outlined',
    //   label: '执行中',
    //   onClick: handleSetStatus.bind(null, record, 1),
    //   disabled: record.status !== 0,
    //   ifShow: hasPermission([146])
    // },
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: record.type == 3
    },
    {
      // icon: 'ant-design:eye-outlined',
      label: '完成',
      color: 'success',
      popConfirm: {
        okText: '确定',
        title: '确定将退货单状态设置成完成状态吗',
        cancelText: '取消',
        placement: 'left',
        confirm: handleSetStatus.bind(null, record, 15)
      },
      disabled: record.status !== 0,
      ifShow: hasPermission([147]) && record.type !== 3
    }
  ]

  return buttonList
}

function createDropDownActions(record: Recordable): ActionItem[] {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission([143]) && record.type !== 3
    },
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      disabled: record.status !== 0 || record.type == 3,
      ifShow: hasPermission([144]) && record.type !== 3
    },
    {
      icon: 'ant-design:delete-outlined',
      color: 'error',
      label: '删除',
      // disabled: record.status !== 0,
      popConfirm: {
        title: '是否确认删除',
        placement: 'left',
        confirm: handleDelete.bind(null, record.id),
        disabled: record.status !== 0 || record.type == 3
      },
      ifShow: hasPermission([145]) && record.type !== 3
    }
  ]
}
function handleCreate() {
  setDrawerProps({ title: '新增退货单' })
  openDrawer(true, {
    type: 'add'
  })
}

function handleDetail(record: Recordable) {
  console.log(record)
  setDrawerProps({ title: '退货单详情' })
  openDrawer(true, { record, type: 'detail' })
}

async function handleSetStatus(record: Recordable, status: number) {
  const res = await setRetreatStatus({ id: record.id, status })
  if (res.msg) {
    reload()
  }
}

function handleEdit(record: Recordable) {
  setDrawerProps({ title: '编辑退货单' })
  openDrawer(true, { record, type: 'edit' })
}

async function handleDelete(id: number) {
  await deleteRetreat({ id })
  deleteTableDataRecord(id)
}
</script>
