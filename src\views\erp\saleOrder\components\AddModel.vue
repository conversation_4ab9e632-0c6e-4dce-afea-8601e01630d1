<template>
  <BasicModal @register="register" @ok="addsubmit" :min-height="400" @close="close">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
//updateWorksAudit

import { projectwksetDesignerAt, updateWorksAudit } from '/@/api/erp/sales'
import { WorksAuditschemas } from '../datas/Modal'
import { ref } from 'vue'
const types = ref()
//From
const [registerForm, { validate, resetFields, resetSchema }] = useForm({
  showActionButtonGroup: false,
  // schemas: WorksAuditschemas(),
  baseColProps: {
    span: 24
  }
})
//work_ids
const work_ids = ref<any>([])

//modal
const [register, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  changeOkLoading(false)
  resetSchema(WorksAuditschemas(data.type))
  resetFields()
  work_ids.value = data.data
  types.value = data.type
})
const emit = defineEmits(['success', 'register', 'close'])
async function addsubmit() {
  try {
    changeOkLoading(true)

    const values = await validate()

    types.value == 'account'
      ? await updateWorksAudit({ ...values, work_ids: work_ids.value, is_audit: 1 })
      : await projectwksetDesignerAt({ ...values, work_id: work_ids.value })
    emit('success')
    await closeModal()
    work_ids.value = []
  } catch (err) {
    changeOkLoading(false)
    console.error(err)
  }
}
function close() {
  work_ids.value = []
  emit('close')
}
//提交
</script>
