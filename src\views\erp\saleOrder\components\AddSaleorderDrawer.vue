<template>
  <div>
    <BasicDrawer v-bind="$attrs" @register="registerDrawer" @ok="addsubmit" width="90%" title="添加补货订单" show-footer>
      <div class="mt-2">
        <Card title="补货订单" :bordered="false">
          <BasicForm @register="registerForm" />
        </Card>
        <Card title="补货商品" class="mt-2" :bordered="false">
          <template #extra>
            <Button type="primary" @click="handleAddProduct">添加商品</Button>
          </template>
          <BasicTable @register="registertable" :beforeEditSubmit="beforeEditSubmit">
            <template #bodyCell="{ text, column, record }">
              <template v-if="column.key === 'action'">
                <TableAction
                  :actions="[
                    {
                      icon: 'ant-design:delete-outlined',
                      color: 'error',
                      tooltip: '删除此商品',
                      popConfirm: {
                        title: '是否确认删除',
                        placement: 'left',
                        confirm: handleDelete.bind(null, record)
                      }
                    }
                  ]"
                />
              </template>
              <template v-if="column.key === 'imgs'">
                <TableImg :size="60" :simpleShow="true" :imgList="text" />
              </template>
              <!-- <template v-if="column.key === 'unit_price'"> {{ record.unit_price }} CNY </template>
              <template v-if="column.key === 'total_amount'"> {{ record.total_amount }} CNY </template>
              <template v-if="column.key === 'qty_request'"> {{ record.qty_request }} {{ record.unit }} </template> -->
            </template>
          </BasicTable>
        </Card>
      </div>
    </BasicDrawer>
    <AddModel @register="registermodal2" ref="addmodelRef" @success="onAddProduct" />
  </div>
</template>
<script lang="ts" setup name="AddSaleorderDrawer">
import { ref, unref } from 'vue'
import { Card, Button, message } from 'ant-design-vue'
import { useModal } from '/@/components/Modal'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { useMessage } from '/@/hooks/web/useMessage'
import { getaddSalesOrder, getReplenish } from '/@/api/erp/sales'
// import { getcustomerList } from '/@/api/financialDocuments/otherIncome'
import { BasicTable, useTable, TableAction, TableImg } from '/@/components/Table'
import AddModel from './AddModel.vue'
import { schemas, columns } from '../datas/Modal'
import { getStaffList } from '/@/api/baseData/staff'
import type { IDelRecord } from '../datas/types'

const propsData = ref()

//分类
// const tabeldata: any = ref([])
// const datarecord = ref()
// const price = ref()

const [registerDrawer, { changeLoading, changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  StaffListFn()
  resetFields()
  reload()
  propsData.value = data
  changeLoading(true)
  // console.log(data.record, 'data.record')
  try {
    await resetFields()
    await setFieldsValue(data.record)
    await setFieldsValue({ auditor_name: Number(data.record.auditor) })
    console.log(data, 'data')
    const { items } = await getReplenish({ work_id: data?.record?.id })
    setTableData(items)
  } finally {
    changeLoading(false)
  }
})

//商品添加model
const [registermodal2, { openModal }] = useModal()
//From
const [registerForm, { validateFields, resetFields, setFieldsValue, getFieldsValue }] = useForm({
  schemas,
  showActionButtonGroup: false
})
//Table
const [registertable, { getDataSource, deleteTableDataRecord, setTableData, getColumns, reload }] = useTable({
  columns,
  bordered: true,
  actionColumn: {
    width: 160,
    title: '操作',
    dataIndex: 'action'
  }
})
function handleAddProduct() {
  openModal(true, { action: 'add', data: propsData.value?.record })
}

function onAddProduct(productFormData) {
  // console.log(productFormData, 'productFormData')
  setTableData([...getDataSource(), productFormData])
}

async function handleDelete(record: IDelRecord) {
  deleteTableDataRecord(record.key)
}

// 格式化提交的数据
function formatSubmit() {
  // getDataSource()有我们不需要的属性,所以需要清除
  const dataSource = getDataSource()?.map((item) => {
    let temporary = {}
    for (let colName of getColumns()) {
      if (colName.key !== 'action') {
        temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
      }
    }
    return temporary
  })
  return dataSource
}

//退出
const emit = defineEmits(['success', 'register'])

//提交
async function addsubmit() {
  await changeOkLoading(true)
  const { record } = unref(propsData)
  const formData = await validateFields()
  // console.log(formData, 'formData')
  const params = {
    ...formData,
    work_id: record.id,
    name: record.name,
    client_name: record.client_name,
    items: getDataSource() ?? []
  }

  console.log(getDataSource(), 'getDataSource()')

  const adddata = await getFieldsValue()
  const data = await formatSubmit()
  if (!data) return
  data.forEach((item: any) => {
    delete item.undefined
  })
  const client_name = ref()
  statusdata.value.forEach((item) => {
    console.log(item)
    if (item.id == adddata.client_id) {
      client_name.value = item.name
    }
  })
  // const msg = ref()

  const res: any = await getaddSalesOrder(params)
  console.log(res, 'res')
  if (res.news == 'success') {
    message.success('补货单添加成功')
    closeDrawer()
    emit('success')
  }
  await changeOkLoading(false)
}

const addmodelRef = ref<InstanceType<typeof AddModel>>()

async function beforeEditSubmit({ record, index, key, value }) {
  console.log('单元格数据正在准备提交', { record, index, key, value })
  const recenumber = await getDataSource()
  const number = recenumber.reduce((per, arr) => {
    return per + arr.total_amount
  }, 0)
  setFieldsValue({ receivable: number })
  return await feakSave({ value })
}

const { createMessage } = useMessage()
// 模拟将指定数据保存
function feakSave({ value }) {
  createMessage.loading({
    content: `正在保存数据`,
    key: '_save_fake_data',
    duration: 0
  })
  return new Promise((resolve) => {
    setTimeout(() => {
      if (value === '') {
        createMessage.error({
          content: '保存失败：不能为空',
          key: '_save_fake_data',
          duration: 2
        })
        resolve(false)
      } else {
        createMessage.success({
          content: `数据已成功保存`,
          key: '_save_fake_data',
          duration: 2
        })
        resolve(true)
      }
    }, 2000)
  })
}

//员工
const statusdata = ref()
async function StaffListFn() {
  const a = await getStaffList()
  statusdata.value = unref(a.items)
}
</script>
