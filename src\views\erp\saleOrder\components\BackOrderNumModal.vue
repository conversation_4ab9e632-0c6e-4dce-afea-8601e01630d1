<!-- eslint-disable max-len -->
<template>
  <BasicModal @register="registerModal" width="80%" title="填写收货日期" :bodyStyle="{ height: '700px' }" @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #Files>
        <Upload
          v-model:file-list="FilesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
        <div style="color: red; font-weight: 700; font-size: 18px"> 采购下单表及图纸文件上传</div>
      </template>
    </BasicForm>
    <div style="color: red; font-weight: 700; font-size: 18px" v-if="!['/car/', '/sp/', '/sptest/'].includes(pathname)">
      勾选完成【产品拆分】的产品（或不需要【产品拆分】的产品），勾选产品可直接进行采购
      <a
        style="color: #0000ff"
        href="https://img.gbuilderchina.com/erp/%E5%85%B3%E4%BA%8E%E9%94%80%E5%94%AE%E8%AE%A2%E5%8D%95%E5%A4%87%E8%B4%A7%E5%8A%9F%E8%83%BD%E6%9B%B4%E6%96%B0%E8%AF%B4%E6%98%8E.docx"
        >备货功能操作说明（点击下载）</a
      >
    </div>
    <BasicTable @register="registerTable" @selection-change="selectionchange" v-if="!['/car/', '/sp/', '/sptest/'].includes(pathname)">
      <template #headerCell="{ column }">
        <template v-if="searchConfig[column.dataIndex]">
          <Popover trigger="click" :title="searchConfig[column.dataIndex].title">
            <template #content>
              <Input
                allowClear
                style="width: 70%"
                v-model:value="searchValues[column.dataIndex]"
                :placeholder="searchConfig[column.dataIndex].placeholder"
                @change="handleallowClear"
              />
              <Button
                style="margin-left: 5px"
                type="primary"
                @click="handleBatchEdit(searchValues[column.dataIndex], searchConfig[column.dataIndex].field)"
              >
                确定
              </Button>
            </template>
            <span style="margin-right: 10px">
              {{ column.customTitle }}
              <EditOutlined />
            </span>
          </Popover>
        </template>
        <template v-else>{{ column.customTitle }}</template>
      </template>
      <template #bodyCell="{ text, column }">
        <template v-if="column.key === 'imgs'">
          <TableImg :imgList="text" :simpleShow="true" />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { getSalesOrderListReq, setSalesStatus } from '/@/api/erp/sales'
import dayjs, { Dayjs } from 'dayjs'
import { Upload, UploadFile } from 'ant-design-vue'
import { PlusOutlined, EditOutlined } from '@ant-design/icons-vue'
import { UploadRequestOption } from 'ant-design-vue/lib/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { BasicTable, useTable, TableImg } from '/@/components/Table'
import { backordercolimns } from '../datas/Modal'
import { Popover, Input, Button } from 'ant-design-vue'
import { cloneDeep } from 'lodash-es'
import { message } from 'ant-design-vue'

const emit = defineEmits(['success', 'register'])
const disableddate = ref<any>([])
const propDatas = ref()
//附件
const FilesList = ref<UploadFile[]>([])
const pathname = window.location.pathname
console.log(pathname)

// 搜索配置
const searchConfig = {
  name: {
    title: '搜索选择产品名称',
    placeholder: '请输入产品名称',
    field: 'name'
  },
  puid: {
    title: '搜索选择产品编号',
    placeholder: '请输入产品编号',
    field: 'puid'
  }
}

// 搜索值的统一管理
const searchValues = ref({
  name: '',
  puid: ''
})

//原始table
const originaltable = ref<any[]>([])
//全部勾选
const initallselectdata = ref<any[]>([])
//商品是否有拆分完成的
const splitboolen = ref()
//是否全部都是拆分完成
const splitlength = ref(false)

const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(async (data) => {
  console.log(data)

  resetFields()
  splitlength.value = false
  propDatas.value = data
  FilesList.value = data?.files?.map((item) => ({ url: item, name: item, uid: item })) || []
  if (!['/car/', '/sp/', '/sptest/'].includes(pathname)) {
    setLoading(true)
    const { items } = await getSalesOrderListReq({ work_id: data.id, pageSize: 9999, from: 1 })
    console.log(items)
    // 保存原始数据
    originaltable.value = cloneDeep(items)
    splitboolen.value = items.some((item) => item.is_finish_split == 1)
    const splititems = items.filter((item) => item.is_finish_split == 0)
    if (splititems.length == 0) {
      splitlength.value = true
    }
    setTableData(splititems)
    setLoading(false)
  }
  const today = dayjs()
  const futureDate = today.add(10, 'day')
  const todayString = today.format('YYYY-MM-DD')
  const futureDateString = futureDate.format('YYYY-MM-DD')
  disableddate.value = [todayString, futureDateString]
})

/** 注册from */
const [registerForm, { validate, resetFields, setFieldsValue }] = useForm({
  labelWidth: 140,
  schemas: [
    {
      field: 'delivery_at',
      label: '交货日期',
      component: 'DatePicker',
      // required: true,
      componentProps: {
        valueFormat: 'YYYY-MM-DD',
        style: { width: '100%' }
      }
    },
    {
      field: 'urgent_level',
      label: '紧急状态',
      component: 'Select',
      required: true,
      defaultValue: 1,
      componentProps: {
        options: [
          {
            label: '一般',
            value: 1
          },
          {
            label: '紧急',
            value: 2
          },
          {
            label: '非常紧急',
            value: 3
          }
        ]
      }
    },
    {
      field: 'purchase_est_finish_at',
      label: '采购需求日期',
      component: 'DatePicker',
      required: true,
      defaultValue: dayjs().add(3, 'day').format('YYYY-MM-DD'),
      componentProps: () => {
        return {
          valueFormat: 'YYYY-MM-DD',
          disabledDate: (current: Dayjs) => {
            if (!disableddate.value || !Array.isArray(disableddate.value) || disableddate.value.length !== 2) {
              return false
            }
            const tooLate = disableddate.value[0] && current.diff(dayjs(disableddate.value[0]), 'days') > 30
            const tooEarly = disableddate.value[1] && dayjs(disableddate.value[1]).diff(current, 'days') > 10
            return tooLate || tooEarly
          },
          style: { width: '100%' }
        }
      }
    },
    {
      field: 'files',
      label: '附件',
      required: !['/sp/', '/sptest/'].includes(pathname),
      component: 'Upload',
      itemHelpMessage: '采购下单表及图纸文件上传',
      slot: 'Files'
      // required: true
    }
  ],
  showSubmitButton: false,
  showResetButton: false,
  baseColProps: {
    span: 8
  }
})

//注册表格
const [registerTable, { setTableData, getSelectRowKeys, getDataSource, setSelectedRowKeys, setLoading, clearSelectedRowKeys }] = useTable({
  columns: backordercolimns,
  showIndexColumn: false,
  rowSelection: {},
  canResize: false,
  pagination: false,
  // pagination: {
  //   size: 'small',
  //   pageSize: 10,
  //   pageSizeOptions: ['10', '20', '100', '500', '1000']
  // },
  rowKey: 'id'
})
//附件上传
async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  FilesList.value = FilesList.value!.map((item) => {
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: item.name
    }
  })
  await setFieldsValue({
    files: FilesList.value.map((item) => item.url)
  })
}

watch(
  () => FilesList.value,
  async (val) => {
    await setFieldsValue({ files: val?.map((item) => item.url) })
  }
)

async function handleSubmit() {
  try {
    await changeLoading(true)
    await changeOkLoading(true)

    // 1. 表单验证
    const values = await validate()
    const selectedRows = ref()
    // 2. 验证必填字段
    if (!values.purchase_est_finish_at) {
      changeOkLoading(false)
      return message.error('请选择采购需求日期')
    }

    // 3. 验证选中的数据
    if (!['/car/', '/sp/', '/sptest/'].includes(pathname)) {
      console.log(123)

      selectedRows.value = await getSelectRowKeys()
      const datas = await getDataSource()
      if (datas.length > 0 && splitboolen.value === false) {
        if (!selectedRows.value || selectedRows.value.length === 0) {
          changeOkLoading(false)
          return message.error('请选择要处理的产品')
        }
      }
    }
    console.log(selectedRows.value)

    // 4. 构建提交参数
    const params = {
      id: propDatas.value.id,

      status: 2,

      ...values,
      is_finish_split: !['/car/', '/sp/', '/sptest/'].includes(pathname)
        ? selectedRows.value.length == 0
          ? splitlength.value == true
            ? 1
            : 0
          : 1
        : 1,

      items: !['/car/', '/sp/', '/sptest/'].includes(pathname)
        ? selectedRows.value.length == 0
          ? undefined
          : selectedRows.value.map((item) => {
              return {
                request_id: item
              }
            })
        : undefined
    }
    console.log(params)

    // 5. 提交数据
    await setSalesStatus(params)

    // // 6. 成功处理
    emit('success')
    await closeModal()
  } catch (err) {
    console.error(err)
    message.error(`${err.errorFields[0].errors}保存失败`)
  } finally {
    changeLoading(false)
    !['/car/', '/sp/', '/sptest/'].includes(pathname) ? clearSelectedRowKeys() : undefined
    changeOkLoading(false)
  }
}

// 搜索相关函数
async function handleBatchEdit(data, field) {
  if (!data && !field) return

  const alldata = await getDataSource()
  const searchResult = fuzzySearchWithRegex(data, alldata, field)
  setTableData(searchResult)

  // 恢复当前搜索结果中已选中项的勾选状态
  const selectedIds = initallselectdata.value.map((item) => item.id)
  const currentIds = searchResult.filter((item) => selectedIds.includes(item.id)).map((item) => item.id)
  setSelectedRowKeys(currentIds)

  // 如果有选中项但当前搜索结果中没有显示，给出提示
}

function fuzzySearchWithRegex(query, items, searchField) {
  if (!query) {
    const selectedIds = initallselectdata.value.map((item) => item.id)
    setSelectedRowKeys(selectedIds)
    return originaltable.value
  }

  const regex = new RegExp(query, 'i')
  return items.filter((item) => {
    const fieldValue = item[searchField]
    return fieldValue ? regex.test(fieldValue.toString()) : false
  })
}

async function handleallowClear({ target }) {
  if (target.value === '') {
    setTableData(originaltable.value)
    const selectedIds = initallselectdata.value.map((item) => item.id)
    setSelectedRowKeys(selectedIds)
  }
}

function selectionchange({ keys, rows }) {
  // 获取当前表格数据的ID列表
  const currentTableIds = getDataSource().map((item) => item.id)
  // 1. 处理取消选中的情况
  const currentUnselectedIds = currentTableIds.filter((id) => !keys.includes(id))
  // 2. 从总选中数据中移除当前取消选中的项
  initallselectdata.value = initallselectdata.value.filter((item) => !currentUnselectedIds.includes(item.id))
  // 3. 添加新选中的项
  const existingIds = initallselectdata.value.map((item) => item.id)
  const newSelectedRows = rows.filter((row) => !existingIds.includes(row.id))
  // 4. 更新总选中数据
  initallselectdata.value = [...initallselectdata.value, ...newSelectedRows]
}
</script>
