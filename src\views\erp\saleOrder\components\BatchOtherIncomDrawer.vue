<template>
  <BasicDrawer @register="registerDrawer" v-bind="$attrs" showFooter width="90%" destroyOnClose @close="handleClose" @ok="onSubmit">
    <!-- 其他支出单表单start -->
    <BasicTitle>其他收入单基础信息</BasicTitle>
    <BasicForm @register="registerBaseInfoForm" style="margin-bottom: 10px">
      <template #FilesSlot>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg2Stocking"
          :custom-request="handleFileRequest"
          :multiple="true"
          :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
        >
          <a-button type="primary">
            <upload-outlined />
            Upload
          </a-button>
        </Upload>
      </template>
    </BasicForm>
    <!-- 其他支出单表单end -->

    <!-- 审核表单start -->
    <BasicTitle>填写收款信息</BasicTitle>
    <BasicForm @register="registerAuditForm" style="margin-bottom: 10px" />
    <!-- 审核表单end -->

    <!-- 其他收入明细start -->
    <BasicTitle>其他收入明细</BasicTitle>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleAdd">新增一行</a-button>

        <Dropdown :disabled="!!currentEditKeyRef">
          <a class="ant-dropdown-link" @click.prevent>
            <a-button>
              <template #icon><download-outlined /> </template>明细文件 <download-outlined />
            </a-button>
          </a>
          <template #overlay>
            <Menu @click="handleMenuClick">
              <MenuItem key="upload"><upload-outlined /> 导入明细</MenuItem>
              <MenuItem key="export"><download-outlined /> 模板</MenuItem>
            </Menu>
          </template>
        </Dropdown>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <!-- 其他收入明细end -->
    <ImpExcelModal @register="registerUploadModal" :dataCallBackFn="handleUploadData" ref="ImpExcelModalRef" />
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { cloneDeep, throttle } from 'lodash-es'
import { Upload, message, Dropdown, Menu, MenuItem } from 'ant-design-vue'
import type { UploadFile } from 'ant-design-vue'
import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { UploadOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import type { ActionItem, EditRecordRow } from '/@/components/Table'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTitle } from '/@/components/Basic/index'
import { BasicForm, useForm } from '/@/components/Form'
import {
  baseInfoSchema,
  baseFormConfig,
  auditSchema,
  columnsFn,
  currentEditKeyRef,
  insertInitValue,
  filterArrayByKeys,
  childrenColumns
} from '../datas/OtherIncomeDrawer'
import { batchCreateOtherIncome } from '/@/api/financialDocuments/otherIncome'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { ImpExcelModal } from '/@/components/Excel'
import { useModal } from '/@/components/Modal'
import { validTableData } from '../datas/fn'
import { add } from '/@/utils/math'
import { onExpExcelTemplate } from '/@/utils/exportTemplate'
import { excelHeader } from '/@/views/financialDocuments/otherIncomeExpend/datas/OtherIncomeDrawer.data'
import { IMP_EXCEL_END } from '/@/views/credential/credential/datas/importModal'
import { transformData2Import } from '/@/views/financialDocuments/otherIncomeExpend/datas/importModal'

//附件
const filesList = ref<UploadFile[]>([])
// 存储编辑前的record
const beforeRecord = ref()

const [registerDrawer, { closeDrawer, changeOkLoading, changeLoading }] = useDrawerInner(async (data) => {
  try {
    await changeLoading(true)
    await changeOkLoading(false)
    await setColumns(
      columnsFn({
        updateTableDataRecord,
        setColumns,
        assignment
      })
    )
    //设置表格初始值
    await setTableData(
      data.map((item, index) => ({ ...insertInitValue, ...item, key: index + 1, sale_work_id: item.id, children: undefined }))
    )
    //设置表单初始值
    calcAmount()
  } catch (err) {
    message.error(err)

    console.error(err)
  } finally {
    changeLoading(false)
  }
})

const [registerBaseInfoForm, { setFieldsValue: setBaseFieldsValue, validate: baseValidate, getFieldsValue: getBaseFieldsValue }] = useForm({
  ...baseFormConfig,
  schemas: baseInfoSchema
})
const [registerAuditForm, { validate: auditValidate, getFieldsValue: getAuditFieldsValue }] = useForm({
  ...baseFormConfig,
  schemas: auditSchema
})

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setBaseFieldsValue({ files: val?.map((item) => item.url) ?? [] })
  }
)
//附件上传
async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'otherIncome')
  onSuccess!(result.path)
  filesList.value = filesList.value!.map((item) => {
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: item.name
    }
  })
  await setBaseFieldsValue({
    files: filesList.value.map((item) => item.url)
  })
}

const [registerTable, { setTableData, deleteTableDataRecord, updateTableDataRecord, setColumns, insertTableDataRecord, getDataSource }] =
  useTable({
    title: '',
    showIndexColumn: false,
    columns: columnsFn(),
    canResize: false,
    isTreeTable: false,
    rowKey: 'key',
    actionColumn: {
      width: 250,
      title: '操作',
      dataIndex: 'action'
    }
  })

async function handleAdd() {
  const record = {
    key: Date.now(),
    ...insertInitValue,
    account_name: null,
    account_code: null
  }
  const tableData = await getDataSource()
  await setTableData([...tableData, record])
  // handleEdit(record)
}

//execl上传
const [registerUploadModal, { openModal }] = useModal()
function handleMenuClick({ key }) {
  if (key === 'export') {
    onExpExcelTemplate(excelHeader)
  } else if (key === 'upload') {
    // openAgrDrawer(true, {})
    openModal(true, {
      sheetName: 'Sheet1',
      headerRow: 1,
      startCell: 'A2',
      endCell: `B${IMP_EXCEL_END}`
    })
  }
}
const ImpExcelModalRef = ref<InstanceType<typeof ImpExcelModal>>()
async function handleUploadData(data) {
  ImpExcelModalRef.value?.changeLoading(true)
  const hide = message.loading('正在导入数据，请稍后...', 0)
  try {
    const newData = transformData2Import(data)

    if (newData.length === 0) {
      message.error('没有可导入的数据！')
      return Promise.reject('Fail')
    } else if (newData.length > 0) {
      if (newData.some((item) => item.amount <= 0 || !item.amount)) {
        message.error('导入文件中有数据的收入金额t不为数字或小于0')
        return Promise.reject('Fail')
      }
    }
    //看导入前table是否有数据
    const oldTableData = await getDataSource()
    //下面冗余的代码需要优化
    if (oldTableData.length === 0) {
      const newTableData = newData.map((item, index) => ({
        ...insertInitValue,
        ...item,
        key: index + 1
      }))

      await setTableData(newTableData)
      calcAmount()
      return Promise.resolve('OK')
    } else if (oldTableData.length > newData.length) {
      const newTableData = oldTableData.map((item, index) => ({
        ...insertInitValue,
        ...item,
        ...newData[index]
      }))
      await setTableData(newTableData)
      calcAmount()
      return Promise.resolve('OK')
    } else if (oldTableData.length < newData.length) {
      //下面的key会不会重复呢,但是用时间戳的话,也可能会和后面的重复,除非新增按钮增加一个节流
      const newTableData = newData.map((item, index) => ({
        ...insertInitValue,
        key: index + 1,
        ...item,
        ...oldTableData[index]
      }))
      await setTableData(newTableData)
      calcAmount()
      return Promise.resolve('OK')
    }
    //除了上面三种情况,应该其他就是失败的
    return Promise.reject('Fail')
  } catch (err) {
    return Promise.reject('Fail')
  } finally {
    hide()
    ImpExcelModalRef.value?.changeLoading(false)
  }
}

function createActions(record: EditRecordRow): ActionItem[] {
  if (!record.editable) {
    return [
      {
        label: '编辑',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        onClick: handleEdit.bind(null, record)
      },
      {
        label: '复制',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        onClick: throttleHandleCopy.bind(null, record)
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          placement: 'left',
          confirm: handleDelete.bind(null, record)
        },
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false
      }
    ]
  } else {
    return [
      {
        icon: 'ant-design:check-outlined',
        tooltip: '保存',
        onClick: handleSave.bind(null, record)
      },
      {
        icon: 'ant-design:close-outlined',
        color: 'error',
        tooltip: '取消',
        onClick: handleCancel.bind(null, record)
      }
    ]
  }
}

async function handleEdit(record: EditRecordRow) {
  currentEditKeyRef.value = record.key

  beforeRecord.value = cloneDeep(record)
  record.onEdit?.(true, false)
  if (record.corres_type === 5) {
    await setColumns(childrenColumns({ updateTableDataRecord, setColumns, assignment }))
  } else {
    await setColumns(columnsFn({ updateTableDataRecord, setColumns, assignment }))
  }
}

//使用节流
const throttleHandleCopy = throttle(handleCopy, 300)

async function handleCopy(record: EditRecordRow) {
  await insertTableDataRecord({ ...record, key: Date.now() })
}

async function handleDelete(record: EditRecordRow) {
  await deleteTableDataRecord(record.key)
}

async function handleSave(record: EditRecordRow) {
  // 校验

  message.loading({ content: '正在保存...', duration: 0, key: 'saving' })
  const valid = await record.onValid?.()
  if (valid) {
    try {
      if (!record.account_name) {
        message.error({ content: '明细当中支出科目为必填项,请完成填写' })
        throw new Error()
      }
      if (!record.amount) {
        message.error({ content: '明细当中支出金额为必填项,请完成填写' })
        throw new Error()
      }
      if (!record.department) {
        message.error({ content: '明细当中支出部门为必填项,请完成填写' })
        throw new Error()
      }
      const data = cloneDeep(record.editValueRefs)
      console.log(data)

      // 保存之后提交编辑状态
      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
      }

      calcAmount()
      message.success({ content: '数据已保存', key: 'saving' })
    } catch (error) {
      message.error({ content: '保存失败', key: 'saving' })
    }
  } else {
    message.error({ content: '请填写正确的数据', key: 'saving' })
  }
}

async function calcAmount() {
  const dataSource = await getDataSource()
  setBaseFieldsValue({
    amount: dataSource.reduce((pre, item) => add(pre, item.amount, 2), 0)
  })
}

function handleCancel(record: EditRecordRow) {
  currentEditKeyRef.value = ''
  record.onEdit?.(false, false)
  updateTableDataRecord(record.key, beforeRecord.value)
}

function handleClose() {
  setTableData([])
  currentEditKeyRef.value = ''
  closeDrawer()
}

async function onSubmit() {
  try {
    await changeOkLoading(true)
    await baseValidate()
    await auditValidate()

    const tableData = await getDataSource()
    if (validTableData(tableData)) throw new Error('验证表格不通过')
    const baseFormData = await getBaseFieldsValue()
    const auditFormData = await getAuditFieldsValue()
    if (baseFormData.amount !== auditFormData.amount) {
      return message.error('基础信息的总金额与收款信息的本次应收金额不一致')
    }
    await batchCreateOtherIncome({
      doc: baseFormData,
      items: filterArrayByKeys(tableData),
      status_data: auditFormData
    })
    handleClose()
  } catch (err) {
    changeOkLoading(false)
    console.error(err)
  }
}

//赋值
async function assignment(shall: any) {
  console.log(shall)

  if (!shall) return
  if (shall.value === 5) {
    await setColumns(childrenColumns({ updateTableDataRecord, setColumns, assignment }))
  } else {
    await setColumns(columnsFn({ updateTableDataRecord, setColumns, assignment }))
  }
}
</script>
