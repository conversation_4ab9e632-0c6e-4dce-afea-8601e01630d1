<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="新增" show-footer @ok="handleSubmit" width="90%">
    <BasicForm @register="registerForm">
      <template #ProductSlot="{ model }">
        <FormItemRest>
          <Alert
            v-if="propsData.way !== 'afterSale'"
            :message="
              propsData.way == 'new'
                ? '勾选订单商品作为退货商品并且会作为新销售订单的商品'
                : '勾选订单商品作为退货商品: 售后单时不生成退货单'
            "
            :description="
              propsData.way == 'new'
                ? `勾选商品中当前退货数量即为下次新销售订单的商品数量 , 
                查看子产品--子产品: 请勾选子产品进行退货, 当主产品全额退货时,子产品仅能勾选退货数量默认全部`
                : `查看子产品--子产品: 请勾选子产品进行退货, 当主产品全额退货时,子产品仅能勾选退货数量默认全部`
            "
            :type="propsData.way == 'new' ? 'error' : 'info'"
          />
          <BasicTable @register="registerTable" v-model:expandedRowKeys="expandedRowKeys" :expandIconColumnIndex="-1">
            <template #bodyCell="{ record, column, index }">
              <template v-if="column && column.dataIndex === 'quantity' && model.product_info.length > 0">
                <FormItemRest>
                  <InputNumber
                    v-if="model.product_info[index]"
                    v-model:value="model.product_info[index].quantity"
                    :min="0.01"
                    :precision="2"
                    :default-value="1"
                    :max="record.maxQuantity"
                    :disabled="model.product_info[index].items_sub.length > 0"
                    @blur="handleChange(record, model.product_info, index)"
                  />
                </FormItemRest>
              </template>
              <template v-if="column && column.dataIndex === 'remark' && model.product_info.length > 0">
                <FormItemRest>
                  <Textarea v-if="model.product_info[index]" v-model:value="model.product_info[index].remark" />
                </FormItemRest>
              </template>
              <template v-if="column && column.dataIndex === 'desc' && model.product_info.length > 0">
                <FormItemRest>
                  <Textarea v-if="model.product_info[index]" v-model:value="model.product_info[index].desc" />
                </FormItemRest>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <TableAction :actions="createActions(record)" />
              </template>
            </template>
            <template #expandedRowRender="{ record: cellRecord, index: cellIndex }">
              <BasicTable
                :columns="tablecolum('retreat')"
                :ref="(el) => (expandedRowRefs[cellRecord.id] = el)"
                :can-resize="false"
                :data-source="cellRecord.items_sub"
                :clickToRowSelect="false"
                rowKey="id"
                :rowSelection="{
                  type: 'checkbox',
                  onChange: () => {
                    handleExpandChange(cellRecord, model.product_info, cellIndex)
                  }
                }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'quantity'">
                    <InputNumber
                      v-model:value="record.quantity"
                      :min="0.01"
                      :default-value="record.quantity_left"
                      :max="record.quantity_left"
                      :precision="0"
                      @change="handleExpandChange(cellRecord, model.product_info, cellIndex)"
                    />
                  </template>
                  <template v-if="column.key === 'imgs'">
                    <TableImg :imgList="record.imgs" :simpleShow="true" />
                  </template>
                  <template v-if="column.key === 'files'">
                    <div v-for="(newVal, index) in record.files" :key="index">
                      <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
                    >
                  </template>
                </template>
              </BasicTable>
            </template>
          </BasicTable>
        </FormItemRest>
      </template>
    </BasicForm>
  </BasicDrawer>
  <EditDrawer @register="registerEditDrawer" @success="emit('success')" />
  <PurchaseDrawer @register="registerPurchaseDrawer" @success="Pursuccess" />
  <PreviewFile @register="registerModal" />
</template>

<script lang="ts" setup>
import { nextTick, ref, unref, watch } from 'vue'
import { Form, InputNumber, message, Textarea, Alert } from 'ant-design-vue'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicDrawer, useDrawer, useDrawerInner } from '/@/components/Drawer'
import { retreatAdd } from '/@/api/erp/retreat'
import { ActionItem, BasicTable, TableActionType, useTable, TableAction, TableImg } from '/@/components/Table'
import { getItemRequest } from '/@/api/commonUtils'
import { getPurchaseDetail } from '/@/api/erp/purchaseOrder'
import defaultUser from '/@/utils/erp/defaultUser'
import { columns, createSchemas, selectRowKeys } from '../datas/createRetreatDrawer'
import type { IRecord } from '../datas/types'
import { add, mul } from '/@/utils/math'
import EditDrawer from './EditDrawer.vue'
import { addAfterSale } from '/@/api/erp/sales'
import PurchaseDrawer from '../../purchaseOrder/components/purchaseDrawer.vue'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { tablecolum } from '../datas/drawer.data'
import { useMessage } from '/@/hooks/web/useMessage'
import { useModal } from '/@/components/Modal'
import { createImgPreview } from '/@/components/Preview'

const emit = defineEmits(['success', 'register'])
const FormItemRest = Form.ItemRest

const propsData = ref<{ type: 1 | 2; record?: IRecord; items?: { doc_id: number }; way?: string }>({ type: 1 })

const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()
const expandedRowKeys = ref<number[]>([])
const expandedRowRefs = ref<{ [key: string]: TableActionType | Nullable<any> }>({})
const quantitymax = ref(false)

//采购退货生成新采购订单
const [registerPurchaseDrawer, { openDrawer, setDrawerProps }] = useDrawer()

const [
  registerTable,
  {
    setTableData,
    getSelectRowKeys,
    clearSelectedRowKeys,
    getSelectRows,
    setSelectedRowKeys,
    deleteSelectRowByKey,
    getDataSource,
    setColumns,
    updateTableDataRecord
  }
] = useTable({
  showIndexColumn: false,
  columns,
  dataSource: [],
  pagination: false,
  striped: true,
  rowKey: 'id',
  rowSelection: {
    type: 'checkbox',
    onChange: TableSelect,
    getCheckboxProps: (record) => {
      return { disabled: record.quantity === 0 }
    }
  },
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action'
  },
  clickToRowSelect: false
})

const [registerDrawer, { changeLoading, changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  console.log(data)

  propsData.value = data
  expandedRowKeys.value = []
  try {
    changeLoading(true)
    await resetFields()
    await clearSelectedRowKeys()
    await updateSchema(createSchemas(handleOrderChange, propsData.value.way))
    await setFieldsValue({ product_info: [] })
    setTableData([])
    await updateSchema([
      {
        field: 'source_uniqid',
        ifShow: data.type === 1
      },
      {
        field: 'strid',
        ifShow: data.type === 2
      }
    ])
    setFieldsValue({
      type: data.type,
      [data.type === 1 ? 'source_uniqid' : 'strid']: data.type === 1 ? data.record.source_uniqid : data.record.strid,
      dept_id: data.record.dept_id,
      applicant: defaultUser!.userId,
      inCharge: defaultUser!.userId
    })
    await clearValidate('dept_id')
    columns.map((item) => {
      if (item.dataIndex == 'unit_price_tax' || item.dataIndex == 'tax_point') {
        item.defaultHidden = data.type == 2 ? false : true
      }
      if (item.dataIndex == 'maxQuantity') {
        item.defaultHidden = propsData.value.way == 'afterSale' ? true : false
      }
      if (item.dataIndex == 'quantity') {
        item.title = propsData.value.way == 'afterSale' ? '售后数量' : '退货数量'
      }
      if (item.dataIndex == 'remark') {
        item.title = propsData.value.way == 'afterSale' ? '售后备注' : '退货备注'
      }
      if (item.dataIndex == 'desc') {
        item.title = propsData.value.way == 'afterSale' ? '售后描述' : '退货描述'
      }
    })
    setColumns(columns)
  } catch (e) {
    console.log('加载抽屉出错', e)
    throw new Error(`${e}`)
  } finally {
    changeLoading(false)
  }
})

const [registerForm, { resetFields, setFieldsValue, validate, getFieldsValue, updateSchema, clearValidate }] = useForm({
  schemas: createSchemas(handleOrderChange),
  baseColProps: { span: 24 },
  colon: true,
  showActionButtonGroup: false,
  labelWidth: 120
})
//退货再建但是跳转到编辑
const [registerEditDrawer, { openDrawer: openEditDrawer, setDrawerProps: setEditDrawerProps }] = useDrawer()

async function handleOrderChange() {
  changeLoading(true)

  clearSelectedRowKeys()

  try {
    switch (propsData.value.type) {
      case 1:
        const saleOrderResult = await getItemRequest({ work_id: propsData.value.record?.id, pageSize: 999 })
        if (propsData.value.way !== 'afterSale') {
          await setFieldsValue({
            product_info: saleOrderResult.items
              .filter((item) => item.qty_request_left > 0)
              .map((item) => ({ ...item, quantity: item.qty_request_left, remark: '', desc: '' }))
          })
          setTableData(
            saleOrderResult.items
              .filter((item) => item.qty_request_left > 0)
              .map((item) => ({
                ...item,
                maxQuantity: item.qty_request_left,
                quantity: item.qty_request_left,
                remark: '',
                desc: ''
              }))
          )
        } else {
          await setFieldsValue({
            product_info: saleOrderResult.items
              .filter((item) => Number(item.qty_request_actual) > 0)
              .map((item) => ({ ...item, quantity: item.qty_request_actual, remark: '', desc: '' }))
          })
          setTableData(
            saleOrderResult.items
              .filter((item) => Number(item.qty_request_actual) > 0)
              .map((item) => ({
                ...item,
                maxQuantity: item.qty_request_actual,
                quantity: item.qty_request_actual,
                remark: '',
                desc: ''
              }))
          )
        }
        break
      case 2:
        const purchaseOrderResult = await getPurchaseDetail({ doc_id: propsData.value.items[0]?.doc_id })
        await setFieldsValue({
          product_info: purchaseOrderResult.items.map((item) => ({ ...item, quantity: item.qty_wait_received, remark: '', desc: '' }))
        })
        setTableData(
          purchaseOrderResult.items.map((item) => ({
            ...item,
            maxQuantity: item.qty_wait_received,
            quantity: item.qty_wait_received,
            remark: '',
            desc: ''
          }))
        )
        break
    }
  } catch (err) {
    console.error(err, 'OrderChange时错误')
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
}

function mapProductInfo(item) {
  console.log(item.quantity)

  console.log(item)
  const items_sub = ref([])
  if (item.items_sub?.length > 0) {
    const tableAction = unref(expandedRowRefs)[item.id]?.tableAction
    const items = tableAction?.getSelectRows()
    if (!items || items.length == 0) {
      message.error('请勾选子产品商品')
      throw new Error('请勾选子产品退货商品')
    }

    if (item.quantity == item.qty_request_left) {
      quantitymax.value = true
    } else {
      quantitymax.value = false
    }
    items_sub.value = items?.map((val) => {
      return {
        work_id: propsData.value.way == 'afterSale' ? undefined : unref(propsData).record![unref(propsData).type === 1 ? 'id' : 'work_id'],
        request_id: propsData.value.way == 'afterSale' ? undefined : val.request_id,
        request_sub_id: propsData.value.type === 1 ? val.id : val.request_sub_id,
        purchase_id: propsData.value.type === 2 ? val.item_purchase_id : undefined,
        purchase_sub_id: propsData.value.type === 2 ? val.id : undefined,
        quantity: quantitymax.value ? Number(val.quantity_left) : Number(val.quantity),
        proportion: val.proportion,
        proportion_org: val.proportion_org,
        unit: val.unit,
        remark: val.remark,
        desc: val.desc,
        name: val.name,
        imgs: val.imgs ?? [],
        files: val.files ?? []
      }
    })
  }
  // const items_addAfterSale

  const commonProperties = {
    remark: item.remark ?? '',
    desc: item.desc ?? '',
    unit: item.unit,
    unit_price: item.unit_price,
    imgs: item.imgs ?? []
  }

  const saleOrderItem = {
    name: item.name,
    work_id: item.work_id,
    request_id: item.id,
    quantity: item.quantity,
    items_sub: items_sub.value,
    ...commonProperties
  }

  const purchaseOrderItem = {
    purchase_id: item.id,
    name: item.name,
    work_id: item.work_id,
    request_id: item.request_id,
    quantity: item.quantity,
    warehouse_id: item.warehouse_id,
    stocking_id: item.stocking_id,
    items_sub: items_sub.value,
    unit_price_tax: Number(item.unit_price_tax),
    ...commonProperties
  }

  return { saleOrderItem, purchaseOrderItem }
}

async function handleSubmit() {
  try {
    await changeOkLoading(true)
    await validate()
    const formData = getFieldsValue()
    const selectRow = getSelectRowKeys()

    if (selectRow.length === 0) {
      message.error('至少需要选择一个商品!')
      changeOkLoading(false)
      return
    }
    const { applicant, inCharge, dept_id, remark, product_info, type, tax_amount } = formData

    //商品信息
    let saleOrderItems
    let purchaseOrderItems

    saleOrderItems = product_info.filter((item) => selectRow.includes(item.id)).map((item) => mapProductInfo(item).saleOrderItem)

    purchaseOrderItems = product_info.filter((item) => selectRow.includes(item.id)).map((item) => mapProductInfo(item).purchaseOrderItem)

    const params: Recordable = {
      work_id: unref(propsData).record![unref(propsData).type === 1 ? 'id' : 'work_id'],
      doc: {
        type,
        applicant,
        inCharge,
        dept_id,
        remark: remark ?? '',
        tax_amount: Number(tax_amount),
        total_price: product_info
          .filter((item) => selectRow.includes(item.id))
          .reduce((prev, curr) => prev + curr.unit_price * curr.quantity, 0)
          .toFixed(2)
      },
      is_change_sale: 0
    }
    if (propsData.value.type === 1) {
      params.items = saleOrderItems
    } else if (propsData.value.type === 2) {
      params.items = purchaseOrderItems
    }

    if (propsData.value.way == 'purchase') {
      params.is_change_purchase = 1
    } else if (propsData.value.way == 'new') {
      params.is_change_sale = 1
    }
    if (propsData.value.way == 'new') {
      params['is_change_sale'] = 1
    }
    console.log(params)

    const res = propsData.value.way == 'afterSale' ? await addAfterSale(params) : await retreatAdd(params)
    if (res.work_id) {
      openEditDrawer(true, { record: { id: res.work_id }, type: 'edit' })
      setEditDrawerProps({
        maskClosable: false,
        closable: false,
        closeFunc: async () => {
          return false
        }
      })
    }

    await closeDrawer()
    setTimeout(() => {
      changeOkLoading(false)
    }, 2000)
    emit('success')
    if (params.doc.type !== 2 || propsData.value.way !== 'purchase') {
      await closeDrawer()
      setTimeout(() => {
        changeOkLoading(false)
      }, 2000)
      emit('success')
    } else if (propsData.value.way == 'purchase') {
      const clonedate = await getSelectRows()
      clonedate.forEach((item) => {
        params.items.forEach((val) => {
          if (item.request_id == val.request_id) {
            item.qty_purchased = val.quantity
          }
        })
      })
      const sales_id = params.items[0].work_id
      openDrawer(true, { news: { clonedate, id: sales_id }, isUpdate: true, type: 'add' })
      setDrawerProps({ title: '新增采购单' })
    }
  } catch (err) {
    console.log('提交退货单出错:', err)
    changeOkLoading(false)
    throw new Error(`${err}`)
  }
}

//采购退货后采购订单生成完成退出
async function Pursuccess() {
  await closeDrawer()
  setTimeout(() => {
    changeOkLoading(false)
  }, 2000)
  emit('success')
}

function createActions(record: any): ActionItem[] {
  return [
    {
      label: '查看子产品',
      disabled: record.items_sub?.length == 0 ? true : false,
      onClick: handleViewRelate.bind(null, record)
    }
  ]
}

//ctions
function handleViewRelate(record) {
  expandedRowKeys.value.includes(record.id)
    ? (expandedRowKeys.value = expandedRowKeys.value.filter((item) => item !== record.id))
    : expandedRowKeys.value.push(record.id)
  nextTick(() => {
    const tableAction = unref(expandedRowRefs)[record.id]?.tableAction
    tableAction?.setSelectedRowKeys(record.items_sub.map((item) => item.id))
  })
}

//展示
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  console.log(prefix)
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
//主产品更改带动子产品数量
async function handleChange(record, quantity, cellIndex) {
  console.log(record)
  console.log(quantity)
  if (record.items_sub.length > 0) {
    console.log('1')

    const quantityboolon = ref(false)
    const tableAction = unref(expandedRowRefs)[record.id]?.tableAction
    const quantitys = ref()
    if (tableAction?.getSelectRows().length > 0) {
      const data = tableAction?.getSelectRows()
      quantitys.value = data.reduce((acc, cur) => {
        return add(acc, mul(cur.quantity, cur.proportion, 2))
      }, 0)

      if (data.length == record.items_sub.length) {
        let allMatch = data.every((item) => {
          return item.quantity === item.quantity_left
        })
        quantityboolon.value = allMatch
      }
    }
    quantity[cellIndex].quantity =
      quantityboolon.value == false ? quantitys.value : propsData.value.type == 1 ? record.qty_request_left : record.qty_wait_received
  }

  return quantity
}

//展开更改
async function handleExpandChange(row, quantity, cellIndex?) {
  const purchases = ref([])
  const tableAction = unref(expandedRowRefs)[row.id]?.tableAction
  const quantitys = ref()
  const quantityboolon = ref(false)
  const fatherids = getSelectRows().map((item) => item.id)
  console.log(fatherids)

  if (tableAction?.getSelectRows().length == 0) {
    deleteSelectRowByKey(row.id)
    return
  }
  purchases.value = tableAction?.getSelectRows().map((item) => {
    return {
      id: item.id,
      quantity: Number(item.quantity)
    }
  })
  if (purchases.value.length > 0) {
    const data = tableAction?.getSelectRows()
    const numbers =
      propsData.value.type === 1 ? tableAction?.getSelectRows()[0].request_id : tableAction?.getSelectRows()[0].item_purchase_id
    quantitys.value = data.reduce((acc, cur) => {
      return add(acc, mul(cur.quantity, cur.proportion, 2))
    }, 0)

    if (data.length == row.items_sub.length) {
      let allMatch = data.every((item) => {
        return item.quantity == item.quantity_left
      })
      quantityboolon.value = allMatch
    }
    const show = fatherids.includes(numbers)
    if (show == false) {
      fatherids.push(numbers)
    }

    setSelectedRowKeys([...fatherids])
  }

  quantity[cellIndex].quantity =
    quantityboolon.value == false ? quantitys.value : propsData.value.type === 1 ? row.qty_request_left : row.qty_wait_received
  updateTableDataRecord(quantity[cellIndex].id, quantity[cellIndex])
  return quantity
}
watch(
  () => selectRowKeys.value,
  (newValue, oldValue) => {
    const added = newValue.filter((item) => !oldValue.includes(item))
    const removed = oldValue.filter((item) => !newValue.includes(item))
    const dataSource = getDataSource()
    if (added.length > 0) {
      const addsoure = dataSource.filter((item) => added.includes(item.id))
      const tableAction = unref(expandedRowRefs)[addsoure[0]?.id]?.tableAction
      if (tableAction) {
        tableAction?.setSelectedRowKeys(addsoure[0].items_sub.map((item) => item.id))
      }
    } else if (removed.length > 0) {
      const remove = dataSource.filter((item) => removed.includes(item.id))
      const tableAction = unref(expandedRowRefs)[remove[0]?.id]?.tableAction
      if (tableAction) {
        tableAction?.clearSelectedRowKeys()
      }
    }
  },
  { deep: true }
)
async function TableSelect() {
  selectRowKeys.value = getSelectRowKeys()
  await publictaxamunt()
}

async function publictaxamunt() {
  const selectdata = await getSelectRows()
  const tax_amount = selectdata.reduce((acc, cur) => {
    return add(acc, Number((Number(cur.unit_price_tax) * Number(cur.quantity)).toFixed(2)), 2)
  }, 0)
  //税金tax_point
  const taxamount1 = selectdata.reduce((erp, item) => {
    return add(
      erp,
      Number(
        Number(Number(item.unit_price || 0) / (1 + (item.tax_point || 0) / 100)) *
          ((Number(item.tax_point) || 0) / 100) *
          Number(Number(item.quantity || 0).toFixed(2))
      ),
      2
    )
  }, 0)
  const cost = selectdata.reduce((erp, item) => {
    return add(erp, Number((Number(item.unit_price) * Number(item.quantity)).toFixed(2)), 2)
  }, 0)
  const addpoint = (cost / (1 + (Number(selectdata[0]?.add_point) || 0) / 100)) * ((Number(selectdata[0]?.add_point) || 0) / 100)
  await setFieldsValue({ tax_amount: tax_amount, tax_amount1: taxamount1, tax_amount2: addpoint, app_point: selectdata[0]?.add_point || 0 })
}
</script>
