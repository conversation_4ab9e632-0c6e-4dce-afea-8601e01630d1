<template>
  <div>
    <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="编辑销售订单" show-footer @ok="handleSubmit" width="90%">
      <BasicForm @register="registerForm">
        <template #filesSlot>
          <Upload
            v-model:file-list="filesList"
            action="/api/oss/putImg2Stocking"
            :custom-request="handleFileRequest"
            :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
          >
            <a-button type="primary" :disabled="type === 'split'">
              <upload-outlined />
              Upload
            </a-button>
          </Upload>
        </template>
        <template #relateOrderSlot="{ model }">
          <FormItemRest>
            <BasicTable @register="registerDrawerTable" v-model:expandedRowKeys="expandedRowKeys" :expandIconColumnIndex="-1">
              <template #headerCell="{ column }">
                <template v-if="column.dataIndex === 'action'">
                  <span>{{ column.customTitle }}</span>
                  <div>
                    <a-button type="primary" size="small" @click="handleBatchDel">批量删除选中产品</a-button>
                  </div>
                </template>
                <template v-else>{{ column.customTitle }}</template>
              </template>
              <template #bodyCell="{ column, index, record }">
                <template v-if="column.key === 'imgs'">
                  <div class="flex items-center justify-center">
                    <TableImg :imgList="record.imgs" :simpleShow="true" />
                  </div>
                </template>
                <template v-if="column.dataIndex === 'name' && model.relate_order[index]">
                  <Input v-model:value="model.relate_order[index].name" :disabled="type === 'split'" />
                </template>
                <template v-if="column.dataIndex === 'unit' && model.relate_order[index]">
                  <Input v-model:value="model.relate_order[index].unit" :disabled="type === 'split'" />
                </template>
                <template v-if="column.dataIndex === 'unit_price' && model.relate_order[index]">
                  <InputNumber
                    v-model:value="model.relate_order[index].unit_price"
                    :precision="6"
                    :min="ordertype == 2 ? 0 : afterSale == 1 ? 0 : 0.01"
                    :max="ordertype == 2 ? 0 : 999999999999"
                    @change="handleReChange(record)"
                    :disabled="type === 'split'"
                  />
                </template>
                <template v-if="column.dataIndex === 'qty_request' && model.relate_order[index]">
                  <InputNumber
                    v-model:value="model.relate_order[index].qty_request"
                    :precision="2"
                    :min="0.01"
                    @change="handleReChange(record, model.relate_order[index].qty_request)"
                    :disabled="type === 'split'"
                  />
                </template>
                <template v-if="column.dataIndex === 'desc' && model.relate_order[index]">
                  <Input v-model:value="model.relate_order[index].desc" :disabled="type === 'split'" />
                </template>
                <template v-if="column.dataIndex === 'width'">
                  <Input v-model:value="model.relate_order[index].width" :disabled="type === 'split'" />
                </template>
                <template v-if="column.dataIndex === 'length'">
                  <Input v-model:value="model.relate_order[index].length" :disabled="type === 'split'" />
                </template>
                <template v-if="column.dataIndex === 'height'">
                  <Input v-model:value="model.relate_order[index].height" :disabled="type === 'split'" />
                </template>
                <template v-if="column.dataIndex === 'remark' && model.relate_order[index]">
                  <Input v-model:value="model.relate_order[index].remark" :disabled="type === 'split'" />
                </template>
                <template v-if="column.dataIndex === 'is_finish_split' && model.relate_order[index]">
                  <Select
                    :options="[
                      {
                        label: '是',
                        value: 1
                      },
                      {
                        label: '否',
                        value: 0
                      }
                    ]"
                    v-model:value="model.relate_order[index].is_finish_split"
                    :disabled="model.relate_order[index].is_finish_split === 2"
                  />
                </template>
                <!--              <template v-if="column.dataIndex === 'puid' && model.relate_order[index]">-->
                <!--                <Input v-model:value="model.relate_order[index].puid" />-->
                <!--              </template>-->
                <!--              <template v-if="column.dataIndex === 'batch_code' && model.relate_order[index]">-->
                <!--                <Input v-model:value="model.relate_order[index].batch_code" />-->
                <!--              </template>-->
                <template v-if="column.dataIndex === 'action'">
                  <TableAction :actions="createActions(record)" />
                </template>
              </template>
              <template #expandedRowRender="{ record: cellRecord }">
                <BasicTable
                  :columns="tablecolum()"
                  :ref="(el) => (expandedRowRefs[cellRecord.id] = el)"
                  :can-resize="false"
                  :data-source="cellRecord.items_sub"
                  :show-index-column="false"
                  :actionColumn="{
                    width: 200,
                    title: '操作',
                    dataIndex: 'action'
                  }"
                >
                  <template #toolbar>
                    <Button type="primary" @click="handleAdd(cellRecord, 'add')">添加</Button>
                  </template>
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'imgs'">
                      <TableImg :imgList="record.imgs" :simpleShow="true" />
                    </template>
                    <template v-if="column.key === 'files'">
                      <div v-for="(newVal, index) in record.files" :key="index">
                        <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
                      >
                    </template>
                    <template v-if="column.key === 'action'">
                      <TableAction :actions="splitActions(record, cellRecord)" />
                    </template>
                  </template>
                </BasicTable>
              </template>
            </BasicTable>
          </FormItemRest>
        </template>
      </BasicForm>
    </BasicDrawer>
    <ChooseImg @register="registerChooseImgModal" @change="handleSelected" />
    <splitModal @register="registersplitmodal" @handle-add="handletableAdd" />
    <PreviewFile @register="registerModal" />
  </div>
</template>

<script setup lang="ts">
import { ref, unref, watch } from 'vue'
import { Form, Input, InputNumber, Upload, UploadFile, Button, message, Select } from 'ant-design-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { UploadOutlined } from '@ant-design/icons-vue'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable, TableImg, TableAction, TableActionType, ActionItem } from '/@/components/Table'
import { editSalesOrder, getSalesOrderDetail, getSalesOrderListReq } from '/@/api/erp/sales'
import { getDrawerTableColumns, getSchemasList, tablecolum } from '../datas/drawer.data'
import { div, mul, add } from '/@/utils/math'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import ChooseImg from './ChooseImg.vue'
import { useModal } from '/@/components/Modal'
import { createImgPreview } from '/@/components/Preview'
import splitModal from './splitModal.vue'
import { useMessage } from '/@/hooks/web/useMessage'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { cloneDeep } from 'lodash-es'

const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

// 标准四舍五入函数，解决第三位为5时的精度问题
function formatAmount(num: number): string {
  if (isNaN(num)) return '0.00'
  // 使用 Math.round 确保标准四舍五入
  const rounded = Math.round(num * 100) / 100
  return rounded.toFixed(2)
}
const filesList = ref<UploadFile[]>([])
const expandedRowKeys = ref<number[]>([])
const expandedRowRefs = ref<{ [key: string]: TableActionType | Nullable<any> }>({})
const emit = defineEmits(['register', 'success'])

const [registerChooseImgModal, { openModal: openChooseImgModal }] = useModal()

const FormItemRest = Form.ItemRest

const id = ref()
const totalSplittable = ref<any>([])
const type = ref('')
//订单类型,决定商品金额
const ordertype = ref()
const afterSale = ref()

const [registerDrawer, { changeLoading, changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  changeLoading(true)
  totalSplittable.value = []
  expandedRowKeys.value = []
  type.value = data.type

  try {
    await resetFields()
    setTableData([])
    id.value = data.record.id
    ordertype.value = data.record.type

    const detailData = await getSalesOrderDetail({ work_id: data.record.id })

    const { items, works } = await getSalesOrderListReq({ work_id: data.record.id, pageSize: 999 })
    afterSale.value = works?.is_after_sale
    filesList.value = data.record.files?.map((item) => ({ url: item, name: item, uid: item }))
    resetSchema(getSchemasList(updateSchema, data.type))
    await setFieldsValue({
      ...detailData,
      relate_order: items,
      client: {
        value: detailData.client_id,
        label: detailData.client_name,
        is_finish_split: data.type == 'split' ? 1 : null
      }
    })
    setTableData(items)
    items.forEach((item: any) => {
      if (item.items_sub?.length > 0) {
        totalSplittable.value.push(...item.items_sub)
      }
    })
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
})

const [registerForm, { setFieldsValue, resetFields, validate, getFieldsValue, resetSchema, updateSchema }] = useForm({
  schemas: getSchemasList(),
  baseColProps: { span: 12 },
  colon: true,
  showActionButtonGroup: false,
  labelWidth: 120
})

const [
  registerDrawerTable,
  { setTableData, updateTableDataRecord, deleteTableDataRecord, getSelectRowKeys, clearSelectedRowKeys, getDataSource }
] = useTable({
  showIndexColumn: false,
  columns: getDrawerTableColumns(),
  dataSource: [],
  pagination: false,
  canResize: false,
  rowKey: 'id',
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
    // slots: { customRender: 'action' },
    fixed: 'right'
    // ifShow: false
  },
  rowSelection: {}
})

watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val })
  }
)
async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  filesList.value = filesList.value!.map((item, idx) => {
    const url = item.url || item.response
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: url ? url.match(/[^\/]+$/)[0] : idx
    }
  })
  await setFieldsValue({ files: filesList.value.map((item) => item.url) })
}

async function handleReChange(record, qty_request?) {
  const formData = await getFieldsValue()
  //这里应该还要判断每行的单价和数量是不是有值
  const total_amount = formData.relate_order.reduce((pre, cur) => {
    return pre + cur.unit_price * cur.qty_request
  }, 0)
  console.log(total_amount)

  setFieldsValue({ receivable: formatAmount(total_amount) })
  updateTableDataRecord(record.id, { total_amount: formatAmount(total_amount) })
  if (qty_request) {
    //sku
    const tableAction = unref(expandedRowRefs)[record.id]?.tableAction
    console.log(totalSplittable.value)
    const table = totalSplittable.value.filter((item) => item.request_id === record.id)
    console.log(table)

    table.forEach((item) => {
      console.log(item)
      item.proportion = div(mul(div(qty_request, item.quantity, 6), item.proportion_org, 6), 100, 6)
    })
    tableAction?.setTableData(table)
    console.log(tableAction)
  }
}

async function handleSubmit() {
  try {
    await changeOkLoading(true)

    const data = await handleCalculate(totalSplittable.value)
    console.log(data)

    const formData = await validate()
    const { client } = formData
    delete formData.client
    const params = {
      ...formData,
      files: formData.files?.map((item: UploadFile) => item.url),
      relate_order: undefined,
      id: unref(id),
      client_id: client.value,
      client_name: client.label,
      items: formData.relate_order.map((item) => ({
        name: item.name ?? '',
        id: item.id,
        unit: item.unit ?? '',
        unit_price: item.unit_price ?? '',
        qty_request: item.qty_request ?? '',
        desc: item.desc ?? '',
        remark: item.remark ?? '',
        imgs: item.imgs ?? [],
        width: item.width ?? void 0,
        height: item.height ?? void 0,
        length: item.length ?? void 0,
        is_finish_split: item.is_finish_split ?? void 0
        // puid: item.puid ?? '',
        // batch_code: item.batch_code ?? ''
      })),
      items_sub: data
    }

    await editSalesOrder(params)
    await closeDrawer()
    changeOkLoading(false)
    emit('success')
  } catch (err) {
    changeOkLoading(false)
    throw new Error(`${err}`)
  }
}

function createActions(record) {
  return [
    {
      // icon: 'ant-design:file-add-outlined',
      // color: 'success',
      label: '编辑图片',
      ifShow: type.value !== 'split',
      onClick: handleChooseImg.bind(null, record)
    },
    {
      label: '查看子产品',
      ifShow: type.value == 'split' && Number(record.qty_request_left) == Number(record.qty_request),
      onClick: handleViewRelate.bind(null, record)
    },
    {
      label: '删除',
      ifShow: type.value !== 'split',
      onClick: handleDel.bind(null, record)
    }
  ]
}

function handleChooseImg(record) {
  openChooseImgModal(true, { imgList: record.imgs, id: id.value, record })
}

function handleSelected(record, selected) {
  updateTableDataRecord(record.id, { imgs: [selected.url] })
}

//ctions
function handleViewRelate(record) {
  expandedRowKeys.value.includes(record.id)
    ? (expandedRowKeys.value = expandedRowKeys.value.filter((item) => item !== record.id))
    : expandedRowKeys.value.push(record.id)
}

//handleAdd
const [registersplitmodal, { openModal: opensplitModal }] = useModal()
function handleAdd(record, type) {
  opensplitModal(true, { record, type, qty_request: record.qty_request })
}

function splitActions(record: any, cellRecord: any): ActionItem[] {
  return [
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record, cellRecord, 'edit')
    },
    {
      label: '删除',
      onClick: handledeletd.bind(null, record)
    }
  ]
}

function handleEdit(record, cellRecord, type) {
  opensplitModal(true, { record: { ...record, sname: cellRecord.name }, type, qty_request: cellRecord.qty_request })
}

//子产品添加列表
async function handletableAdd(e) {
  console.log(e)

  const tableAction = unref(expandedRowRefs)[e.request_id]?.tableAction
  if (e.key) {
    tableAction.updateTableDataRecord(e.key, { ...e })

    const index = e.id
      ? totalSplittable.value.findIndex((obj) => obj.id === e.id)
      : totalSplittable.value.findIndex((obj) => obj.key === e.key)
    if (index !== -1) {
      totalSplittable.value.splice(index, 1, e)
      console.log(totalSplittable.value)
    } else {
      totalSplittable.value.push(e)
    }
  } else {
    const clonedDataSource = cloneDeep(tableAction.getDataSource())
    clonedDataSource.push(e)
    const dataSource = clonedDataSource
    tableAction.setTableData(dataSource)
    const tabledata = tableAction.getDataSource()
    totalSplittable.value.push(tabledata[tabledata.length - 1])
  }
}

//子产品删除
async function handledeletd(record: any) {
  const tableAction = unref(expandedRowRefs)[record.request_id]?.tableAction
  const tableData = tableAction.getDataSource()
  const newData = tableData.filter((item) => item.key !== record.key)
  tableAction.setTableData(newData)
  if (!record.id) {
    totalSplittable.value = totalSplittable.value.filter((item) => {
      return item.key !== record.key
    })
  } else {
    const updatedItems = totalSplittable.value.map((item: any) => {
      if (item.id === record.id) {
        record['type'] = 3
        return (item = record)
      }
      return item
    })
    totalSplittable.value = updatedItems
  }
}

//计算子产品占比
async function handleCalculate(data) {
  // 用于存储分组后的数据
  const groupedData = {}
  // 分组数据
  data.forEach((item) => {
    if (!groupedData[item.request_id]) {
      groupedData[item.request_id] = []
    }
    groupedData[item.request_id].push(item)
  })

  // 调整每个分组的 qroportion 总和不超过 100
  for (const requestId in groupedData) {
    let groups = groupedData[requestId]

    let group = groupedData[requestId].filter((item) => item.type !== 3)

    if (group.length > 0) {
      let totalQroportion = group.reduce((sum, item) => add(sum, item.proportion_org, 2), 0)
      const datasource = await getDataSource()

      if (totalQroportion !== 100) {
        datasource.forEach((item) => {
          if (item.id == requestId) {
            message.error(`主产品'${item.name}' 下的子产品占比总和不为100%`)
            throw new Error(`主产品'${item.name}' 下的子产品占比总和必须为100`)
          }
        })
      } else {
        datasource.forEach((item) => {
          if (item.id == requestId) {
            item.is_finish_split = 1
          }
        })
      }
    }

    // 更新分组后的数据
    groupedData[requestId] = groups
  }

  // 将分组后的数据重新合并为数组
  const result = ref<any>([])
  for (const key in groupedData) {
    result.value.push(...groupedData[key])
  }
  result.value.forEach((item) => {
    delete item.key
    delete item.sname
  })
  return result.value
}

//展示
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  console.log(prefix)
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}

async function handleDel(record) {
  deleteTableDataRecord(record.id)
  const tableData = getDataSource()
  const total_amount = tableData?.reduce((pre, cur) => {
    return pre + cur.unit_price * cur.qty_request
  }, 0)
  setFieldsValue({ receivable: formatAmount(total_amount), relate_order: tableData })
}

function handleBatchDel() {
  deleteTableDataRecord(getSelectRowKeys())
  clearSelectedRowKeys()
  const tableData = getDataSource()
  const total_amount = tableData?.reduce((pre, cur) => {
    return pre + cur.unit_price, cur.qty_request
  }, 0)
  setFieldsValue({ receivable: formatAmount(total_amount), relate_order: tableData })
}
</script>
