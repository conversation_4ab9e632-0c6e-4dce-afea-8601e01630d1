<template>
  <BasicModal @register="register" title="财务特批" @ok="addsubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { financeApproved } from '/@/api/erp/sales'
const emit = defineEmits(['register', 'success'])
const id = ref()
const [registerForm, { validate, resetFields }] = useForm({
  showActionButtonGroup: false,
  colon: true,
  baseColProps: {
    span: 12
  },
  schemas: [
    {
      field: 'is_finance_approved',
      label: '是否财务特批',
      component: 'Select',
      required: true,
      componentProps: {
        options: [{ label: '是', value: 1 }]
      }
    }
  ]
})

const [register, { closeModal }] = useModalInner(async (data) => {
  resetFields()
  id.value = data.id
})

async function addsubmit() {
  try {
    const values = await validate()
    await financeApproved({ id: id.value, ...values })
    emit('success')
    closeModal()
  } catch (err) {
    console.error(err)
  }
}
</script>
