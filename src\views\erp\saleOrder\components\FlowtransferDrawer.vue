<template>
  <div>
    <BasicDrawer @register="refundDrawer" width="90%" title="流水调拨" destroyOnClose show-footer @ok="handleSubmit">
      <BasicForm @register="registerForm" />
      <Alert message="请勾选确定并按+号展开编辑金额" type="info" show-icon />
      <BasicTable @register="registerTable" :data-source="paymentList" @selection-change="selectchange">
        <template #expandedRowRender="{ record, index }">
          <BasicTable
            @register="childtabel"
            :showIndexColumn="false"
            :pagination="false"
            :maxHeight="300"
            :actionColumn="{ width: 110, title: '操作', dataIndex: 'action' }"
            :data-source="record.fund"
            :columns="childColumns()"
          >
            <template #bodyCell="{ column, record: childrecord, index: childrIndex }">
              <template v-if="column.key === 'action'">
                <TableAction :actions="createActions(childrecord, index, childrIndex, record)" />
              </template>
            </template>
          </BasicTable>
        </template>
      </BasicTable>
    </BasicDrawer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Alert, message } from 'ant-design-vue'
import { cloneDeep, debounce } from 'lodash-es'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicTable, useTable, EditRecordRow, ActionItem, TableAction } from '/@/components/Table'

import { getFormSchemas, childColumns } from '../datas/drawer.data'
import { paymentList, tableColumn } from '../datas/fn'
import { getPaymentList } from '/@/api/financialDocuments/refund'
import { postfundAllot } from '/@/api/erp/sales'
import { postpfundAllot } from '/@/api/erp/purchaseOrder'
import { add, sub } from '/@/utils/math'

const emit = defineEmits(['success', 'register'])
const currentEditKeyRef = ref('')

/** 注册From */
const [registerForm, { setFieldsValue, validate, resetSchema, resetFields, updateSchema }] = useForm({
  baseColProps: { span: 12 },
  showActionButtonGroup: false,
  colon: true,
  labelAlign: 'right',
  labelCol: { style: { width: '150px', marginLeft: '10px' } }
})

/** 注册表格 */
const [registerTable, { getSelectRowKeys, setColumns, getSelectRows }] = useTable({
  pagination: false,
  showIndexColumn: false,
  scroll: { y: '65vh' },
  rowKey: 'id',
  rowSelection: {
    type: 'checkbox'
  }
})

const [childtabel, { getDataSource: getchildDataSource }] = useTable()

function createActions(record: EditRecordRow, index: number, childrIndex: number, father: any): Recordable[] {
  let editButtonList: ActionItem[] = [
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      disabled: sub(record.amount_allot, record.total_amount_fund) == 0,
      onClick: handleUpdate.bind(null, record)
    }
  ]

  if (!record.editable) {
    return editButtonList
  }
  return [
    {
      label: '保存',
      onClick: handleSave.bind(null, record, index, childrIndex, father)
    },
    {
      label: '取消',
      onClick: handleEditCancel.bind(null, record)
    }
  ]
}
//调拨触发的请求
const type = ref()
const propsdata = ref()
/** 抽屉进来时触发 */
const [refundDrawer, { setDrawerProps, closeDrawer, changeOkLoading }] = useDrawerInner(async (data) => {
  console.log(data)
  propsdata.value = data.record
  await resetFields()
  paymentList.value = []
  currentEditKeyRef.value = ''
  try {
    const detaild = await getPaymentList({ work_id: data.type == 1 ? data.record.id : data.record.work_id })
    console.log(detaild)
    paymentList.value = detaild
    type.value = data.type
    changetyppe.value = true
    await setColumns(tableColumn(data.type))
    await resetSchema(getFormSchemas(data.type, handchange))
    await updateSchema({
      field: 'work_id',
      componentProps: {
        defaultOptions:
          data.type == 1
            ? [
                {
                  source_uniqid: data.record.source_uniqid,
                  id: data.record.id
                }
              ]
            : [
                {
                  purchase_strid: data.record.strid,
                  id: data.record.work_id
                }
              ]
      }
    })
    await setFieldsValue({
      work_id: data.type == 1 ? data.record.id : data.record.work_id
    })
  } catch (err: any) {
    throw new Error(err)
  }
})

/** 编辑 */
async function handleUpdate(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  await record.onEdit?.(true, false)
}

/** 保存 */
async function handleSave(record: EditRecordRow, index: number, childrIndex: number, father: any) {
  const valid = await record.onValid?.()

  if (valid) {
    const childrecord = await getchildDataSource()
    const totalamountfund = childrecord.reduce((prev, next) => {
      return add(prev, next.amount_fund || 0, 2)
    }, 0)
    if (totalamountfund > Number(father.amount_fdocw)) {
      return message.error(`${type.value === 1 ? '收款单' : '付款单'}:${father.strid} 本次回退金额之和应小于或等于款单分配金额`)
    }
    const pass = record.onEdit?.(false, true)
    if (pass) {
      currentEditKeyRef.value = ''
    }
  }

  // 这里只能用这种办法，不知道为什么用 #Payment={'model,field'}绑定的值拿不到本次回退金额的值
  paymentList.value[index].fund[childrIndex] = cloneDeep(record)

  const rowdata = await getSelectRows()
  const arramont = ref<any>([])
  rowdata.forEach((item) => {
    arramont.value.push(...item.fund)
  })
  console.log(arramont.value)
  const total = arramont.value.reduce((prev, next) => {
    return add(prev, next.amount_fund || 0, 2)
  }, 0)

  setFieldsValue({
    amount: total
  })
}

/** 取消编辑 */
async function handleEditCancel(record: EditRecordRow) {
  await record.onEdit?.(false, false)
  currentEditKeyRef.value = ''
}

/** 确定 */
// 选择条件
const changetyppe = ref(true)
const handleSubmit = debounce(_handleSubmit, 200)
async function _handleSubmit() {
  try {
    changeOkLoading(true)
    const value = await validate()
    if (getSelectRowKeys().length == 0) {
      changeOkLoading(false)
      message.error('请勾选退款款单!')
      return
    }
    if (value.work_id == value.to_work_id) {
      changeOkLoading(false)
      message.error('不能选择相同的款单进行流水调拨!')
      return
    }

    let fdocLists = paymentList.value
      .map((item) => {
        if (getSelectRowKeys()?.includes(item.id)) {
          const hasPositiveAmount = item.fund.some((val) => Number(val.amount_fund) > 0)
          if (!hasPositiveAmount) {
            throw new Error('至少有一个回退金额大于0!')
          }
          if (item.fund.length == 0) {
            throw new Error('请选择流水款项!')
          }
          return {
            id: item.id,
            funds: item.fund
              .filter((val) => Number(val.amount_fund))
              .map((val) => {
                return {
                  fund_id: val.fund_id,
                  amount_fund: val.amount_fund
                }
              })
          }
        }
      })
      .filter((item) => item)

    if (changetyppe.value == true) {
      type.value == 1
        ? await postfundAllot({
            ...value,
            fdocLists: fdocLists
          })
        : await postpfundAllot({
            ...value,
            fdocLists: fdocLists
          })
      closeDrawer()
      message.success('编辑成功!')
      emit('success')
      setTimeout(() => {
        changeOkLoading(false)
      }, 1000)
    } else {
      changeOkLoading(false)
      return message.error(type.value == 1 ? '款单客户必须一致!' : '款单供应商必须一致!')
    }
  } catch (e: any) {
    changeOkLoading(false)
    setDrawerProps({ confirmLoading: false })
    message.error(e?.message)
  }
}

/** 勾选 */
function selectchange({ rows }) {
  console.log(rows)
  const arramont = ref<any>([])
  rows.forEach((item) => {
    arramont.value.push(...item.fund)
  })
  console.log(arramont.value)
  const total = arramont.value.reduce((prev, next) => {
    return add(prev, next.amount_fund || 0, 2)
  }, 0)

  setFieldsValue({
    amount: total
  })
}
/** 选取提示 */
function handchange(shall) {
  console.log(shall)
  if (type.value == 1) {
    if (shall.client_id !== propsdata.value.client_id) {
      changetyppe.value = false
      return message.error('款单客户必须一致!')
    } else {
      changetyppe.value = true
    }
  }
  if (type.value == 2) {
    if (shall.supplier_id !== propsdata.value.supplier_id) {
      changetyppe.value = false
      return message.error('款单供应商必须一致!')
    } else {
      changetyppe.value = true
    }
  }
}
</script>
