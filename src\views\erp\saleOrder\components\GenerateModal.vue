<!-- eslint-disable max-len -->
<template>
  <BasicModal @register="registerModal" width="70%" defaultFullscreen @ok="handleOk">
    <template #title>
      <div>
        <div style="display: flex">
          <div style="margin-right: 10px"> 请填写以下信息 : </div>
          <a
            style="color: red"
            href="https://img.gbuilderchina.com/erp/%E6%94%B6%E6%AC%BE%E6%96%B9%E5%BC%8F%E5%AF%B9%E5%BA%94%E5%BD%95%E5%85%A5%E6%94%B6%E6%AC%BE%E5%8D%95%E7%9A%84%E5%A1%AB%E5%86%99%E8%A6%81%E6%B1%82.docx"
            >收款方式对应录入收款单的填写要求说明文档（点击下载）</a
          >
        </div>
        <div>邀约附件上传条件为,需要有老客户邀约人存在,或者为产品部自建渠道,且为当前项目第一次创建付款单才可上传邀约附件</div>
      </div>
    </template>
    <BasicForm v-model:model="basicFormModel" @register="registerForm">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
      <template #Files2>
        <Upload
          v-model:file-list="filesList2"
          action="/api/oss/putImg"
          list-type="picture-card"
          accept="image/*"
          :custom-request="handleoldFileRequest"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
    </BasicForm>
    <Alert message="多条单据生成付款单时,请往下滑动滚轮" show-icon description="明细填入佣金是会自动生成其他收入单" />
    <Card title="本次应收金额">
      <template #extra>
        <div class="text-base w-180">
          当前输入本次应收金额汇总：
          <span class="text-blue-500 font-bold text-xl">{{ compRowPrice }}</span>
        </div>
      </template>
      <template v-for="item in cloneDeepSelectRowsData" :key="item.strid">
        <Descriptions class="mb-5">
          <DescriptionsItem :label-style="{ width: '40%' }">
            <template #label>
              <div :style="{ color: '#909399' }">
                <div>销售单号 : {{ item.source_uniqid }}</div>
                <div>订单金额 : {{ item.total_price }} ¥</div>
                <div>订单部门 : {{ item.department }}</div>
              </div>
            </template>
            <Form :model="item" ref="formRef" :rules="formRulesFn()" layout="inline">
              <FormItem has-feedback name="no_amount" label="去佣货款金额">
                <Tooltip placement="top">
                  <template #title>
                    <span>本次收款分配去佣货款金额，不是填写总金额，填的是本次收款分配金额</span>
                  </template>
                  <InputNumber
                    :disabled="!isCNY"
                    :formatter="(value) => `¥${value}`"
                    v-model:value="item.no_amount"
                    :min="0.01"
                    :precision="2"
                  />
                </Tooltip>
              </FormItem>
              <FormItem has-feedback name="sale_type" label="销售类型" style="width: 300px !important">
                <Select :options="sale_type_options" v-model:value="item.sale_type" />
              </FormItem>

              <FormItem has-feedback name="commission" label="佣金">
                <Tooltip placement="top">
                  <template #title>
                    <span>
                      若本次收款中包含中间商自己的佣金(需要我们二次转出)则需要填写，若不包含佣金全部是我们的货款直接入账即不用填写
                    </span>
                  </template>
                  <InputNumber
                    :formatter="(value) => `¥${value}`"
                    v-model:value="item.commission"
                    :precision="2"
                    :disabled="warehousedisa || !isCNY"
                  />
                </Tooltip>
              </FormItem>
              <FormItem has-feedback name="commission" label="含佣金额">
                <div>{{ add(item.no_amount, item.commission, 2) }}</div>
              </FormItem>
              <FormItem name="payment_type" label="款项类型">
                <RadioGroup v-model:value="item.payment_type" :options="radioOpt" :disabled="warehousedisa" />
              </FormItem>

              <template v-if="!isCNY">
                <FormItem
                  class="!mt-2"
                  name="foreign_currency_amount"
                  label="去佣货款外汇金额"
                  :rules="[{ required: true, message: '请输入外汇金额' }]"
                >
                  <InputNumber
                    v-model:value="item.foreign_currency_amount"
                    :precision="4"
                    @change="(val) => handleInputForeign(val, item)"
                  />
                </FormItem>
                <FormItem class="!mt-2" name="foreign_currency_commission" label="佣金外汇">
                  <InputNumber
                    v-model:value="item.foreign_currency_commission"
                    :precision="4"
                    @change="(val) => handleInputCommissionForeign(val, item)"
                  />
                </FormItem>
              </template>
              <Tooltip placement="top">
                <template #title>
                  <span class="text-[#ffff00]"
                    >交付日期一旦确认，将影响整个生产流程，此日期将作为生产、质检、发货的依据，请务必谨慎填写！</span
                  >
                </template>
                <FormItem
                  class="!mt-2"
                  name="deliver_at"
                  label="订单交付日期"
                  :required="warehousedisa ? false : [1, 3].includes(item.payment_type)"
                  v-if="[1, 3].includes(item.payment_type)"
                >
                  <DatePicker v-model:value="item.deliver_at" value-format="YYYY-MM-DD 23:59:59" format="YYYY-MM-DD" />
                </FormItem>
              </Tooltip>
              <Tooltip placement="top">
                <template #title>
                  <span>本张订单的收款总金额不对等的原因</span>
                </template>
                <FormItem class="!mt-2" has-feedback name="amout_remark" label="备注" v-if="item.amout_remark_show">
                  <Textarea :formatter="(value) => `¥${value}`" v-model:value="item.amout_remark" />
                </FormItem>
              </Tooltip>
            </Form>
          </DescriptionsItem>
        </Descriptions>
      </template>
    </Card>
  </BasicModal>
</template>

<script setup lang="ts">
import { addBatch } from '/@/api/financialDocuments/receiptOrder'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, Rule, useForm } from '/@/components/Form'
import {
  Card,
  Descriptions,
  DescriptionsItem,
  InputNumber,
  message,
  Form,
  FormItem,
  UploadFile,
  Upload,
  RadioGroup,
  Tooltip,
  Alert,
  DatePicker,
  Textarea,
  Select
} from 'ant-design-vue'
import { ref, watch, computed, unref } from 'vue'
import { cloneDeep, isNull, isUndefined } from 'lodash-es'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { PlusOutlined } from '@ant-design/icons-vue'
import { add, mul, sub } from '/@/utils/math'
import { useMessage } from '/@/hooks/web/useMessage'
import { rejectList } from '/@/api/erp/sales'
import { generschemas } from '../datas/Modal'
import { useUserStore } from '/@/store/modules/user'
import { omit } from 'lodash-es'
// import { sale_type_options } from '../datas/generateModal'
const userStore = useUserStore()

const { createMessage } = useMessage()
const emit = defineEmits(['register', 'handleSubmit', 'success'])
//出库单生成收款单禁止
const warehousedisa = ref(false)
//出库单生成收款单出库单id
const doc_out_warehouse_id = ref()

const basicFormModel = ref({})

const cloneDeepSelectRowsData = ref<Array<any>>([])
const compRowPrice = computed(() => {
  if (basicFormModel.value.rate != 1) {
    const total = cloneDeepSelectRowsData.value.reduce(
      (total, work) => add(add(work?.foreign_currency_amount ?? 0, work?.foreign_currency_commission ?? 0), total),
      0
    )
    return mul(total, basicFormModel.value.rate, 2)
  }
  return cloneDeepSelectRowsData.value.reduce((total, work) => add(add(work.no_amount, work.commission || 0), total), 0)
})
// const selectRowsData = ref<Array<any>>([])
const formRef = ref()

const sale_type_options = [
  {
    label: '出口销售',
    value: 1
  },
  {
    label: '国内销售（个体）',
    value: 2
  },
  {
    label: '国内销售（中间商）',
    value: 3
  },
  {
    label: '国内销售（翻译）',
    value: 4
  }
]
const radioOpt = [
  {
    label: '定金',
    value: 1
  },
  {
    label: '最后一笔款',
    value: 2
  },
  {
    label: '全款',
    value: 3
  }
]
/** 校验 */
const formRulesFn = (): any => {
  return {
    no_amount: [
      {
        required: true,
        trigger: 'change',
        validator: async (_rule: Rule, value: string) => {
          // Number(value) > Number(selectRowsData.value[index].no_amount) ||
          if (Number(value) <= 0) {
            return Promise.reject('输入的收款金额必须大于0！')
          } else {
            return Promise.resolve()
          }
        }
      }
    ],
    sale_type: [
      {
        required: true,
        trigger: 'change'
      }
    ],
    payment_type: [
      {
        required: true,
        trigger: 'change'
      }
    ],
    amout_remark: [
      {
        required: true,
        trigger: 'change',
        validator: async (_rule: Rule, value: string) => {
          // Number(value) > Number(selectRowsData.value[index].no_amount) ||
          if (!value) {
            return Promise.reject('当前款项类型为【最后一笔款】或【全款】，请说明收款金额不足原因')
          } else {
            return Promise.resolve()
          }
        }
      }
    ]
  }
}

/** 注册from */
const [registerForm, { validate, resetFields, setFieldsValue, resetSchema, updateSchema }] = useForm({
  labelWidth: 150,
  // schemas: generschemas,
  showSubmitButton: false,
  showResetButton: false,
  actionColOptions: {
    span: 24
  }
})
const olduser = ref(false)

/** 注册Modal */
const [registerModal, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  console.log(data)
  // const hasType27 = data.selectRowsData.some((item) => item.type === 27)
  warehousedisa.value = data.hasOwnProperty('type')

  if (data.hasOwnProperty('type')) {
    doc_out_warehouse_id.value = data.doc_out_warehouse_id
    olduser.value = false
  } else {
    //olduser老客户邀约人   self_chnl是否产品部自建渠道  is_send_olduser是否已发送群聊
    olduser.value = data.selectRowsData.some(
      (item) => item.is_send_olduser !== 1 && ((item.olduser !== null && item.olduser !== undefined) || item.self_chnl !== 0)
    )
  }
  console.log(olduser)

  resetSchema(generschemas(warehousedisa.value, olduser.value))

  resetFields()
  // selectRowsData.value = data.selectRowsData
  await setFieldsValue({ dept_id: userStore.getUserInfo?.deptId })

  const { items } = await rejectList({ work_ids: data.work_ids })
  if (items.length > 0) {
    const imgsdata = typeof items.files === 'string' ? JSON.parse(items.files) : items.files
    const imgsdata2 = typeof items.files === 'string' ? JSON.parse(items.files2) : items.files2
    filesList.value = (
      typeof imgsdata === 'string'
        ? [{ url: imgsdata, uid: +new Date().toString(), name: imgsdata }]
        : (imgsdata as string[])?.map((item: string, idx: number) => ({ url: item, uid: idx.toString(), name: item }))
    ) as UploadFile[]
    filesList2.value = (
      typeof imgsdata2 === 'string'
        ? [{ url: imgsdata2, uid: +new Date().toString(), name: imgsdata2 }]
        : (imgsdata2 as string[])?.map((item: string, idx: number) => ({ url: item, uid: idx.toString(), name: item }))
    ) as UploadFile[]
    cloneDeepSelectRowsData.value = cloneDeep(data.selectRowsData).map((item) => ({
      ...item,
      no_amount: null,
      log_no_amount: item.no_amount,
      commission: null
      // deliver_at: null
    }))
    cloneDeepSelectRowsData.value.forEach((item) => {
      items.work?.forEach((work) => {
        if (work.work_id === item.id) {
          item.no_amount = Number(work.amount)
          item.payment_type = work.payment_type
          item.commission = work.commission || 0
        }
      })
    })
    await setFieldsValue({ ...items, notes_account: items.from_account, notes_bank: items.from_bank })
  } else {
    filesList.value = []
    filesList2.value = []

    const customPaymentType = {
      outwarehouse: 1
    }

    // 将no_amount的默认值去除，使用另外的字段记录之前的no_amount
    cloneDeepSelectRowsData.value = cloneDeep(data.selectRowsData).map((item) => ({
      ...item,
      no_amount: data.hasOwnProperty('type') ? add(+item.total_price, 0, 2) : null,
      log_no_amount: item.no_amount,
      commission: null,
      // 如果有type，先判断customPaymentType对象里面有没有对应的值，如果有就用对应的值，否则默认3（健哥ERP群）
      payment_type: data.hasOwnProperty('type') ? (customPaymentType?.[data.type] ? customPaymentType[data.type] : 3) : null
      // deliver_at: null
    }))
  }
  console.log(cloneDeepSelectRowsData.value)
})

const isCNY = computed(() => unref(basicFormModel)?.rate == 1)

function handleInputForeign(val, record) {
  const foreign = basicFormModel.value.rate
  if (!foreign) return createMessage.error('获取汇率失败')

  record.no_amount = mul(val, foreign, 2)
}

function handleInputCommissionForeign(val, record) {
  const foreign = basicFormModel.value.rate
  if (!foreign) return createMessage.error('获取汇率失败')

  record.commission = mul(val, foreign, 2)
}

watch(
  () => basicFormModel.value.rate,
  (val) => {
    for (let record of cloneDeepSelectRowsData.value) {
      if (!isCNY.value) {
        record.commission = null
        record.no_amount = null

        if (record.foreign_currency_commission) {
          record.commission = mul(val, record.foreign_currency_commission, 2)
        }

        if (record.foreign_currency_amount) {
          record.no_amount = mul(val, record.foreign_currency_amount, 2)
        }
      } else {
        record.foreign_currency_commission = null
        record.foreign_currency_amount = null
      }
    }
  }
)

/** 点击确认 */
const handleOk = async () => {
  changeOkLoading(true)
  try {
    let data
    try {
      data = await validate()
    } catch (_e) {
      changeOkLoading(false)
      createMessage.error('请填写生成内容')
      return
    }
    if (warehousedisa.value) {
      data['doc_out_warehouse_id'] = doc_out_warehouse_id.value
    }
    const isEmptyFile = data.files.some((item) => isUndefined(item) || isNull(item))
    if (olduser.value) {
      const isEmptyFiles = data?.files2.some((item) => isUndefined(item) || isNull(item))
      if (isEmptyFiles) {
        return createMessage.error('请等待文件上传完成，再提交！')
      }
    }
    if (isEmptyFile) {
      return createMessage.error('请等待文件上传完成，再提交！')
    }
    let works: Array<{
      work_id: number
      amount: number
      commission: number
      payment_type: number
      amout_remark: string
      doc_out_warehouse_id: string
      sale_type: number
    }> = []

    // 校验
    try {
      for (let item of formRef.value) {
        await item.validate()
      }
    } catch (_e) {
      changeOkLoading(false)
      createMessage.error('请完善本次应收的 货款金额 和 款项类型 以及 备注')
      return
    }

    console.log(cloneDeepSelectRowsData.value)

    if (!cloneDeepSelectRowsData.value[0].client_id) {
      changeOkLoading(false)
      return createMessage.error('当前订单无客户信息，无法生成收款单')
    }

    cloneDeepSelectRowsData.value.forEach((item) => {
      works.push({
        work_id: item.id,
        amount: item.no_amount,
        payment_type: item.payment_type,
        commission: item.commission,
        amout_remark: item.amout_remark,
        foreign_currency_amount: item.foreign_currency_amount,
        foreign_currency_commission: item.foreign_currency_commission,
        deliver_at: item.deliver_at,
        sale_type: item.sale_type,
        exchange_rate: data.rate,
        currency: data.currency
      })
      data.client_id = item.client_id
      works = works.map((item: any) => {
        let work = item
        if (!work.commission) work = omit(work, 'commission')
        if (!work.foreign_currency_amount) work = omit(work, 'foreign_currency_amount')
        if (!work.foreign_currency_commission) work = omit(work, 'foreign_currency_commission')
        if (isCNY.value) {
          work = omit(work, ['foreign_currency_commission', 'foreign_currency_amount'])
        }

        return work
      })
    })

    //判断汇总金额和填写的总金额是否相等
    // let worksPrice = works.reduce((total, work) => add(add(work.amount, work.commission || 0), total), 0)
    // 外汇总价判断
    // if (!unref(isCNY)) {
    //   const total = works.reduce(
    //     (total, work) => add(add(work?.foreign_currency_amount ?? 0, work?.foreign_currency_commission ?? 0), total),
    //     0
    //   )
    //   worksPrice = mul(total, data.rate, 2)
    // }
    if (+data.total_price !== +unref(compRowPrice)) {
      changeOkLoading(false)
      return createMessage.error('填入的金额与汇总总金额不一致！请检查金额！')
    }
    const res = await addBatch({ ...data, works, clause: 1 })
    if (res.code == 0) {
      closeModal()
      emit('success')
      setTimeout(() => {
        changeOkLoading(false)
      }, 3000)
      message.success('成功生成收款单！')
    } else {
      throw new Error(`${res.message}`)
    }
    console.log(data, works)
  } catch (error: any) {
    changeOkLoading(false)
    console.log(error)
    message.error(error.message)
    throw new Error(`${error}`)
  }
}

//附件
const filesList = ref<UploadFile[]>([])
//邀约附件
const filesList2 = ref<UploadFile[]>([])

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val?.map((item) => item.url) })
  }
)
//附件
watch(
  () => filesList2.value,
  async (val) => {
    await setFieldsValue({ files2: val?.map((item) => item.url) })
  }
)

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    console.log(file)
    changeOkLoading(true)
    const curFile = filesList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({
      files: filesList.value.map((item) => item.url)
    })
  } catch (err) {
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  } finally {
    changeOkLoading(false)
  }
}
async function handleoldFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    console.log(file)
    changeOkLoading(true)
    const curFile = filesList2.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList2.value = filesList2.value!.filter((item) => item.url)
      return
    }
    filesList2.value = filesList2.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({
      files2: filesList2.value.map((item) => item.url)
    })
  } catch (err) {
    if (err.code === 'ERR_NETWORK') filesList2.value = filesList2.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList2.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  } finally {
    changeOkLoading(false)
  }
}

//款项类型更改
watch(
  () => cloneDeepSelectRowsData.value,
  (newval) => {
    console.log(newval[0].payment_type)
    newval.forEach((item) => {
      if (sub(item.receivable_left, item.received_actual, 2) !== add(item.no_amount, item.commission, 2) && item.payment_type > 1) {
        Reflect.set(item, 'amout_remark_show', true)
      } else {
        Reflect.set(item, 'amout_remark_show', false)
        item.amout_remark = ''
      }
    })
    const hasDeposit = newval.some((item) => +item.payment_type === 1)

    updateSchema({ field: 'deliver_at', required: hasDeposit })
  },
  {
    deep: true
  }
)
</script>
<style lang="less" scoped>
:deep(.ant-picker),
:deep(.ant-select) {
  width: 100%;
}

:deep(.ant-descriptions-item-content) {
  display: inline-block;
}

:deep(.ant-descriptions-item) {
  padding: 0;
}
</style>
