<template>
  <BasicModal @register="register" title="附件上传" @ok="handleOk" width="800px">
    <BasicForm @register="registerform">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template></BasicForm
    >
    <BasicTable @register="registertable" v-if="types == 'box'" :search-info="{ work_id: init_id }">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'files'">
          <div v-for="(newVal, index) in record.files" :key="index">
            <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ newVal.name ?? `附件${index + 1}` }}</a></div
          >
        </template>
      </template>
    </BasicTable>
    <PreviewFile @register="registerpreModal" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { UploadSchemas } from '../datas/Modal'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { UploadFile, Upload, message } from 'ant-design-vue'
import { ref, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { projectpackinguploadFiles, projectwkgetSaleFilesList, updateFiles } from '/@/api/erp/sales'
import { BasicTable, useTable } from '/@/components/Table'
import { createImgPreview } from '/@/components/Preview'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { nextTick } from 'vue'

//id
const init_id = ref()
const types = ref()
const [registerpreModal, { openModal }] = useModal()

const [register, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  console.log(data)
  types.value = data.type
  filesList.value =
    data.type == 'erp' ? data.record?.files?.map((file) => ({ name: file, url: file, uid: Math.random() * 100000 })) ?? [] : []
  init_id.value = data.record?.id
  if (data.type == 'box') {
    nextTick(async () => {
      setLoading(true)
      const { items } = await projectwkgetSaleFilesList({ work_id: data.record.id })
      setTableData(items)
      setLoading(false)
    })
  }
})
const [registerform, { setFieldsValue, getFieldsValue }] = useForm({
  schemas: UploadSchemas,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelCol: { span: 5 }
})

const [registertable, { setTableData, setLoading }] = useTable({
  showIndexColumn: false,
  title: '附件列表',
  columns: [
    {
      title: '上传人',
      dataIndex: 'person_name',
      width: 100,
      resizable: true
    },
    {
      title: '上传时间',
      dataIndex: 'created_at',
      width: 100,
      resizable: true
    },
    {
      title: '附件',
      dataIndex: 'files',
      width: 100,
      resizable: true
    }
  ],
  pagination: false,
  canResize: false
})

//附件
const filesList = ref<UploadFile[]>([])

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({
      files:
        types.value == 'box'
          ? val.map((item) => {
              return {
                url: item.url,
                name: item.name
              }
            })
          : val?.map((item) => item.url)
    })
  }
)

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    console.log(file)
    changeOkLoading(true)
    const curFile = filesList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({
      files:
        types.value == 'box'
          ? filesList.value.map((item) => {
              return {
                url: item.url,
                name: item.name
              }
            })
          : filesList.value.map((item) => item.url)
    })
    // 判断是否所有的文件都已经上传成功，如果有uploading状态的就不修改按钮状态
    const isAllDone = filesList.value.every((item) => item.url)
    if (isAllDone) {
      changeOkLoading(false)
    }
  } catch (err) {
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}

const emit = defineEmits(['success', 'register'])
// 提交
async function handleOk() {
  try {
    await changeOkLoading(true)
    const formdata = await getFieldsValue()
    console.log(filesList.value)
    const params = {
      work_id: init_id.value,
      files: formdata.files
    }
    const res: any = types.value == 'erp' ? await updateFiles(params) : await projectpackinguploadFiles(params)
    console.log(res)

    if (res.news == 'success') {
      emit('success')
      await closeModal()
      message.success('附件上传成功')
      filesList.value = []
    }
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  } finally {
    changeOkLoading(false)
  }
}

async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return message.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]

  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>
