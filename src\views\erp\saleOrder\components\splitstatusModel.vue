<template>
  <BasicModal @register="register" @ok="addsubmit" :min-height="400" @close="close">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'

import { setIsFinishSplit } from '/@/api/erp/sales'
import { schemasaddmodel } from '../datas/Modal'
import { ref } from 'vue'
//From

const [registerForm, { validate, resetFields, resetSchema, updateSchema }] = useForm({
  showActionButtonGroup: false,
  // schemas: schemasaddmodel,
  baseColProps: {
    span: 24
  }
})
const work_id = ref<any>()
// const type = ref<any>()

//modal
const [register, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  changeOkLoading(false)
  resetFields()
  console.log(data)
  work_id.value = data.record.id
  // type.value = data.type
  const originalDate = new Date(data.record.is_finish_split_at)
  originalDate.setDate(originalDate.getDate() + 10)
  const newDateString = originalDate.toISOString().split('T')[0]
  const disableddate = [data.record.is_finish_split_at?.split(' ')[0], newDateString]
  console.log(disableddate)

  resetSchema(schemasaddmodel(disableddate, updateSchema))
})
const emit = defineEmits(['success', 'register', 'close'])
async function addsubmit() {
  try {
    changeOkLoading(true)

    const values = await validate()
    console.log(values)
    // type.value == 'status'
    await setIsFinishSplit({ id: work_id.value, ...values })
    // : await setPurchaseEstFinishAt({ id: work_id.value, ...values })
    closeModal()
    emit('success')
  } catch (err) {
    changeOkLoading(false)
    console.error(err)
  }
}
function close() {
  emit('close')
}
//提交
</script>
