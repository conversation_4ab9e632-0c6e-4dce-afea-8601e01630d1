// import { Rule } from 'ant-design-vue/lib/form'
import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { getStaffList } from '/@/api/baseData/staff'
import { getRmbquot } from '/@/api/erp/sales'
import { getDeptSelectTree } from '/@/api/admin/dept'
import { getcustomerList } from '/@/api/financialDocuments/otherIncome'
import { ref, h } from 'vue'
// import { useExchangeRateStore } from '/@/store/modules/exchangeRate'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { mul } from '/@/utils/math'
import dayjs, { Dayjs } from 'dayjs'
import { Rule } from 'ant-design-vue/lib/form'
import { getDept } from '/@/api/erp/systemInfo'
import { getListByDate } from '/@/api/erp/outWarehouse'
//请求汇率
//订单类型和状态
const saleStore = useSaleOrderStore()

export const schemas: FormSchema[] = [
  {
    field: 'type',
    required: true,
    component: 'Select',
    label: '订单类型',
    colProps: {
      span: 8
    },
    componentProps: { options: saleStore.mapOrderTypeOptions, disabled: true },
    labelWidth: 100
  },
  {
    field: 'name',
    required: true,
    component: 'Input',
    label: '名称',
    colProps: {
      span: 8
    },
    componentProps: {},
    labelWidth: 100
  },
  {
    field: 'status',
    required: true,
    component: 'Select',
    label: '状态',
    colProps: {
      span: 8
    },
    componentProps: { options: saleStore.mapOrderStatusOptions, disabled: true },
    labelWidth: 100
  },
  {
    field: 'client_id',
    required: true,
    component: 'ApiSelect',
    label: '客户',
    colProps: {
      span: 8
    },
    componentProps: () => {
      return {
        api: getcustomerList,
        resultField: 'items',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'id',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true
        }
      }
    },
    labelWidth: 100,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'dept_id',
    required: true,
    component: 'ApiTreeSelect',
    label: '部门',
    componentProps: {
      api: getDeptSelectTree,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        placeholder: '请选择',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    },
    colProps: {
      span: 8
    },
    labelWidth: 100
  },
  {
    field: 'currency',
    required: true,
    component: 'Input',
    label: '货币',
    colProps: {
      span: 8
    },
    componentProps: {
      disabled: true
    },
    labelWidth: 100
  },
  {
    field: 'exchange_rate',
    required: true,
    component: 'ApiSelect',
    label: '汇率',
    colProps: {
      span: 8
    },
    labelWidth: 100,
    componentProps: {
      api: getRmbquot,
      resultField: 'items',
      selectProps: {
        fieldNames: { value: 'fBuyPri', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'receivable',
    required: true,
    component: 'Input',
    label: '应收金额',
    colProps: {
      span: 8
    },
    componentProps: {
      disabled: true
    },
    labelWidth: 100
  },
  {
    field: 'inCharge',
    label: '负责人',
    required: true,
    component: 'ApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    colProps: {
      span: 8
    },
    labelWidth: 100,
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'remark',
    // required: true,
    component: 'InputTextArea',
    label: '备注',
    colProps: {
      span: 8
    },
    componentProps: {},
    labelWidth: 100
  }
]
const onenumber = ref(null)
const stype = ref(false)
export const columns: BasicColumn[] = [
  {
    title: '名称',
    dataIndex: 'name',
    width: 100
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 100
  },
  {
    title: '单价',
    dataIndex: 'unit_price',
    width: 100
  },
  {
    title: 'imgs',
    dataIndex: 'imgs',
    width: 100
  },
  {
    title: '总数',
    dataIndex: 'qty_request',
    width: 100,
    edit: true,
    editComponent: 'InputNumber',
    editComponentProps: (val) => {
      console.log(val)
      if (stype.value === false) {
        onenumber.value = val.record.qty_request
        stype.value = true
      }
      return {
        min: 0,
        max: onenumber.value,
        addonAfter: val.record.unit
      }
    }
  },
  {
    title: '总金额',
    dataIndex: 'total_amount',
    width: 100,
    customRender(opt) {
      const a = ref()
      a.value = opt.record.qty_request * opt.record.unit_price
      opt.record.total_amount = a.value
      console.log(a)
      return h('div', {}, a.value)
    }
  },
  {
    title: '商品描述',
    dataIndex: 'desc',
    editComponent: 'Input',

    width: 100,
    edit: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    // editComponent: 'Input',
    width: 100,
    edit: true
  }
]

export const schemasaddmodel = (disableddate: any, updateSchema: Function): FormSchema[] => [
  {
    field: 'is_finish_split',
    required: true,
    component: 'Select',
    label: '是否全部产品完成拆分',
    itemHelpMessage: '完成产品拆分过后,才能进行采购,如果不需要拆分产品直接选择 "是"',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ],
      onChange: async (val) => {
        await updateSchema({
          field: 'purchase_est_finish_at',
          required: val == 1
        })
      }
    }
    // show: type == 'status',
    // ifShow: type == 'status'
  },
  // {
  //   field: 'purchase_est_finish_at',
  //   label: '采购需求预计完成日期',
  //   component: 'DatePicker',
  //   defaultValue: dayjs().add(3, 'day').format('YYYY-MM-DD'),
  //   componentProps: {
  //     valueFormat: 'YYYY-MM-DD',
  //     style: { width: '100%' }
  //   },
  //   show: type == 'status',
  //   ifShow: type == 'status'
  // },
  {
    field: 'purchase_est_finish_at',
    label: '采购需求日期',
    component: 'DatePicker',
    defaultValue: dayjs(disableddate[0]).add(3, 'day').format('YYYY-MM-DD'),
    componentProps: () => {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (current: Dayjs) => {
          if (!disableddate || !Array.isArray(disableddate) || disableddate.length !== 2) {
            return false
          }
          const tooLate = disableddate[0] && current.diff(dayjs(disableddate[0]), 'days') > 10
          const tooEarly = disableddate[1] && dayjs(disableddate[1]).diff(current, 'days') > 10
          return tooLate || tooEarly
        },
        style: { width: '100%' }
      }
    }
    // show: type == 'date',
    // ifShow: type == 'date'
  }
]
//olduser 邀约显示  warehouse 是否出库单
export const generschemas = (warehouse?, olduser?): FormSchema[] => [
  // {
  //   field: 'collection_at',
  //   label: '收款日期',
  //   component: 'DatePicker',
  //   itemHelpMessage: '银行流水到账日期',
  //   componentProps: {
  //     valueFormat: 'YYYY-MM-DD'
  //   },
  //   colProps: {
  //     span: 7
  //   }
  // },
  {
    field: 'fund_at',
    label: '水单日期',
    component: 'DatePicker',
    itemHelpMessage:
      '水单日期是核算业绩的时间，是客户汇款截图上的水单日期，请如实填写，如录入收款单日期是12月2日，水单日期为11月30日收款，即填写11月30日',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    colProps: {
      span: 7
    },
    required: true
  },
  {
    field: 'notes',
    label: '对方付款人',
    itemHelpMessage: '这笔打款账户名字',
    component: 'Input',
    colProps: {
      span: 7
    },
    required: true
  },
  {
    field: 'notes_account',
    label: '对方付款人账号',
    itemHelpMessage: ['只需要填写打款账号后4位数字', '收现金的直接填写“现金”'],
    component: 'Input',
    colProps: {
      span: 7
    },
    required: true
  },
  {
    field: 'notes_bank',
    label: '对方付款银行/平台',
    itemHelpMessage: ['填写对方打款账户所在银行', '信保填写对应平台', '收现金的直接填写“现金”'],
    component: 'Input',
    colProps: {
      span: 7
    },
    required: true
  },
  {
    field: 'account',
    label: '收款账号',
    component: 'Input',
    itemHelpMessage: ['我司收款账号后4位数字', '收现金的直接填写“现金”'],
    colProps: {
      span: 7
    },
    required: true
  },
  {
    field: 'account_name',
    label: '收款银行',
    component: 'Input',
    itemHelpMessage: ['我司收款账号所在银行', '收现金的直接填写“现金” '],
    colProps: {
      span: 7
    },
    required: true
  },
  // {
  //   field: 'payment_type',
  //   label: '款项类型',
  //   component: 'RadioGroup',
  //   // defaultValue: 3,去除默认值，让用户手选
  //   componentProps: {
  //     options: [
  //       {
  //         label: '定金',
  //         value: 1
  //       },
  //       {
  //         label: '最后一笔款',
  //         value: 2
  //       },
  //       {
  //         label: '全款',
  //         value: 3
  //       }
  //     ]
  //   },
  //   colProps: {
  //     span: 11
  //   },
  //   required: true
  // },
  {
    field: 'g_remark',
    label: '携带备注',
    component: 'InputTextArea',
    itemHelpMessage: '外币收款需备注收到总外币金额，及其他注释',
    // componentProps: {
    //   autosize: { minRows: 3, maxRows: 6 }
    // },
    colProps: {
      span: 7
    }
  },
  {
    label: '本次应收汇总金额',
    field: 'total_price',
    component: 'InputNumber',
    componentProps: ({ formModel }) => {
      return {
        min: 0,
        disabled: formModel.rate !== '1.000000',
        precision: 2
      }
    },
    defaultValue: 0,
    required: true,
    itemHelpMessage: ['这笔流水金额。', '注意：必须填写与下方填写的本次应收金额总金额（货款+佣金）对应', '否则无法生成'],
    colProps: {
      span: 7
    }
  },
  // {
  //   field: 'sale_type',
  //   label: '销售类型',
  //   component: 'Select',
  //   required: !warehouse,
  //   show: !warehouse,
  //   ifShow: !warehouse,
  //   colProps: {
  //     span: 7
  //   },
  //   componentProps: {
  //     options: [
  //       {
  //         label: '出口销售',
  //         value: 1
  //       },
  //       {
  //         label: '国内销售（个体）',
  //         value: 2
  //       },
  //       {
  //         label: '国内销售（中间商）',
  //         value: 3
  //       },
  //       {
  //         label: '国内销售（翻译）',
  //         value: 4
  //       }
  //     ]
  //   }
  // },
  // {
  //   field: 'is_collection',
  //   label: '是否软装代收',
  //   required: true,
  //   itemHelpMessage: '如果当前所收款项包含门窗软件产品款项请选择是',
  //   component: 'Select',
  //   colProps: {
  //     span: 7
  //   },
  //   componentProps: {
  //     options: [
  //       {
  //         label: '不是',
  //         value: 0
  //       },
  //       {
  //         label: '是',
  //         value: 1
  //       }
  //     ]
  //   }
  // },
  {
    field: 'fg_amount',
    label: '外币金额',
    component: 'InputNumber',
    itemHelpMessage: '当汇率不为人人民币时填写外币金额算出汇总总金额',
    componentProps: ({ formModel }) => {
      return {
        precision: 4,
        min: 0,
        disabled: formModel.rate == '1.000000',
        onChange(val) {
          formModel.total_price = mul(val, formModel.rate, 2)
        }
      }
    },
    colProps: {
      span: 7
    }
  },

  {
    field: 'exchange_rate',
    label: '汇率',
    component: 'ApiSelect',
    defaultValue: '1.000000',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        api: getRmbquot,
        resultField: 'items',
        selectProps: {
          fieldNames: { value: 'fBuyPri', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          onChange(val, shall) {
            if (val == '1.000000') {
              formModel.fg_amount = undefined
              formModel.total_price = undefined
            }
            formModel.rate = val
            formModel.total_price = mul(val, formModel.fg_amount || 0, 2)
            formModel.currency = shall.name.split('-')[0]
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 7
    }
  },
  {
    field: 'rate',
    label: '汇率比例',
    component: 'InputNumber',
    componentProps: ({ formModel }) => {
      return {
        precision: 4,
        min: 0,
        max: 100,
        onChange(val) {
          formModel.total_price = mul(val, formModel.fg_amount || 0, 2)
        }
      }
    },
    colProps: {
      span: 7
    },
    defaultValue: '1.000000'
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'PagingApiSelect',
    required: true,
    componentProps: {
      api: getDept,
      resultField: 'items',
      labelField: 'name',
      valueField: 'id',
      searchMode: true,
      pagingMode: true,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择'
      }
    },
    colProps: {
      span: 7
    }
  },
  // {
  //   field: 'deliver_at',
  //   label: '项目交付日期',
  //   component: 'DatePicker',
  //   componentProps: {
  //     valueFormat: 'YYYY-MM-DD 23:59:59',
  //     format: 'YYYY-MM-DD',
  //     style: {
  //       width: '100%'
  //     }
  //   },
  //   colProps: {
  //     span: 7
  //   }
  // },
  {
    field: 'files',
    label: '水单/收据上传',
    required: true,
    itemHelpMessage: '附件必须放水单/单据，否则财务无法识别对应哪一笔流水，较大概率重填',
    component: 'Upload',
    // helpMessage: '附件必须放水单，否则财务无法识别对应哪一笔流水，较大概率重填',
    slot: 'Files',
    colProps: {
      span: 7
    }
  },
  {
    field: 'files2',
    label: '自建渠道和邀约老客户/老客户项目经理证明附件',
    itemHelpMessage:
      '若是CRM里对应项目是产品部自建业务渠道或邀约老客户到店成交的，请上传对应的自建业务渠道证明或邀约老客户截图。邀约附件必须图片',
    required: true,
    component: 'Upload',
    slot: 'Files2',
    colProps: {
      span: 7
    },
    ifShow: olduser,
    show: olduser
  },
  {
    field: 'doc_out_warehouse_id',
    label: '出库单单号',
    component: 'PagingApiSelect',
    componentProps: {
      api: getListByDate,
      resultField: 'items',
      labelField: 'name',
      valueField: 'id',
      searchMode: true,
      pagingMode: true,
      searchParamField: 'strid',
      selectProps: {
        // mode: 'multiple',
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'strid'
        },
        showSearch: true,
        placeholder: '请选择'
      }
    },
    show: warehouse,
    ifShow: warehouse,
    colProps: {
      span: 7
    }
  },
  {
    field: 'currency',
    label: '货币',
    component: 'Input',
    show: false,
    defaultValue: '人民币'
  }
]
//查看子产品
export const schemassplit: FormSchema[] = [
  {
    field: 'sname',
    label: '主产品名称',
    component: 'Input',
    colProps: {
      span: 12
    },
    componentProps: {
      disabled: true
    }
  },
  {
    field: 'name',
    label: '子产品名称',
    component: 'Input',
    colProps: {
      span: 12
    },
    required: true
  },
  {
    field: 'quantity',
    label: '产品数量',
    component: 'InputNumber',
    itemHelpMessage: '产品数量只能填整数',
    colProps: {
      span: 12
    },
    componentProps: {
      min: 0,
      precision: 0
    },
    required: true
  },
  {
    field: 'proportion_org',
    label: '产品占比',
    component: 'InputNumber',
    itemHelpMessage: '所有子产品占比总和为100%',
    componentProps: {
      min: 0,
      max: 100,
      precision: 2,
      // formatter: (val) => `${val}%`,
      addonAfter: '%',
      parser: (val) => val.replace('%', '')
    },
    colProps: {
      span: 12
    },
    required: true
  },
  {
    field: 'unit',
    label: '单位',
    required: true,
    component: 'Input',
    colProps: {
      span: 12
    }
  },
  {
    field: 'length',
    label: '长度(CM)',
    // required: true,
    component: 'Input',
    colProps: {
      span: 12
    }
  },
  {
    field: 'width',
    label: '宽度(CM)',
    // required: true,
    component: 'Input',
    colProps: {
      span: 12
    }
  },
  {
    field: 'height',
    label: '高度(CM)',
    // required: true,
    component: 'Input',
    colProps: {
      span: 12
    }
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    colProps: {
      span: 12
    }
  },
  {
    field: 'desc',
    label: '描述',
    component: 'InputTextArea',
    colProps: {
      span: 12
    }
  },
  {
    field: 'imgs',
    label: '图片组',
    component: 'Upload',
    slot: 'imgs',
    colProps: {
      span: 12
    }
    // required: true
  },
  {
    field: 'files',
    label: '附件',
    component: 'Upload',
    slot: 'files',
    colProps: {
      span: 12
    }
  }
]

export function WorksAuditschemas(type: string): FormSchema[] {
  return [
    {
      field: 'audit_at',
      label: '结算日期',
      component: 'DatePicker',
      required: true,
      componentProps: {
        style: {
          width: '100%'
        },
        valueFormat: 'YYYY-MM-DD'
      },
      show: type === 'account',
      ifShow: type === 'account'
    },
    {
      field: 'finance_remark',
      label: '财务备注',
      component: 'InputTextArea',
      show: type === 'account',
      ifShow: type === 'account'
    },
    {
      field: 'designer_orders_at',
      label: '下单日期',
      component: 'DatePicker',
      required: true,
      componentProps: {
        style: {
          width: '100%'
        },
        valueFormat: 'YYYY-MM-DD HH:mm:ss'
      },
      show: type === 'sale',
      ifShow: type === 'sale'
    }
  ]
}

export const UploadSchemas: FormSchema[] = [
  {
    field: 'files',
    label: '附件上传',
    component: 'Upload',
    slot: 'Files',
    required: true,
    rules: [
      {
        required: true,
        validator: async (_rule: Rule, value: string) => {
          if (!value || value.length === 0) return Promise.reject('请上传附件')
          return Promise.resolve()
        }
        // trigger: 'change'
      }
    ],
    colProps: {
      span: 24
    }
  }
]
export const UploadexcelSchemas: FormSchema[] = [
  {
    field: 'files',
    label: '附件上传',
    component: 'Upload',
    slot: 'Uploads',
    colProps: {
      span: 24
    }
  }
]

export const backordercolimns: BasicColumn[] = [
  {
    title: 'id',
    dataIndex: 'id',
    width: 200,
    resizable: true,
    ifShow: false
  },
  {
    title: '产品名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    resizable: true
  },
  {
    title: '产品图片',
    dataIndex: 'imgs',
    key: 'imgs',
    width: 300,
    resizable: true
  },
  {
    title: '产品编号',
    dataIndex: 'puid',
    key: 'puid',
    width: 150,
    resizable: true,
    customRender: ({ text }) => (text ? text : '-')
  },
  {
    title: '产品唯一码',
    dataIndex: 'uniqid',
    key: 'uniqid',
    resizable: true,
    width: 250
  },
  {
    title: '订单实际需求数量',
    dataIndex: 'qty_request_actual',
    key: 'qty_request_actual',
    width: 150
  },
  {
    title: '订单剩余数量',
    dataIndex: 'qty_request_left',
    key: 'qty_request_left',
    width: 150
  }
]
