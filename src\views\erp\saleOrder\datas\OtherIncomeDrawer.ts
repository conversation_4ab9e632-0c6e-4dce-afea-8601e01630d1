import { h, ref } from 'vue'
import { Tag } from 'ant-design-vue'
import type { FormSchema } from '/@/components/Form'
import { canceltypeOptions, urgent_level_options } from '/@/views/financialDocuments/otherIncomeExpend/datas/OtherIncomeDrawer.data'
import { getDept, getInchargeList } from '/@/api/erp/systemInfo'
import defaultUser from '/@/utils/erp/defaultUser'
import type { BasicColumn } from '/@/components/Table'
import { getWorkList } from '/@/api/commonUtils'
import { getCategory } from '/@/api/financialDocuments/otherIncome'
import { getWorkListFn } from '/@/views/financialDocuments/otherExpend/datas/drawer'

import { isNull, isUnDef } from '/@/utils/is'
import { cloneDeep } from 'lodash-es'

export const currentEditKeyRef = ref('')

// export const is_retax_type = ref()
export const baseInfoSchema: FormSchema[] = [
  {
    field: 'urgent_level',
    label: '紧急状态',
    required: true,
    component: 'Select',
    componentProps: {
      options: urgent_level_options
    }
  },
  // {
  //   field: 'is_retax',
  //   label: '是否退税',
  //   required: true,
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: '是', value: 1 },
  //       { label: '否', value: 0 }
  //     ],
  //     onChange(val) {
  //       is_retax_type.value = val
  //     }
  //   }
  // },
  {
    field: 'dept_id',
    label: '部门',
    component: 'PagingApiSelect',
    componentProps: {
      api: getDept,
      params: { status: 1 },
      resultField: 'items',
      labelField: 'name',
      valueField: 'id',
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    required: true
  },
  {
    field: 'inCharge',
    label: '负责人',
    required: true,
    component: 'ApiSelect',
    componentProps: () => {
      return {
        api: getInchargeList,

        resultField: 'items',
        selectProps: {
          fieldNames: {
            value: 'id',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'applicant',
    label: '申请人',
    required: true,
    component: 'Select',
    dynamicDisabled: true,
    defaultValue: defaultUser!.userId,
    componentProps: {
      options: [
        {
          value: defaultUser!.userId,
          label: defaultUser!.realName
        }
      ]
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'amount',
    label: '总金额',
    required: true,
    component: 'InputNumber',
    defaultValue: 0,
    componentProps: {
      min: 0.01,
      precision: 4,
      disabled: true
    }
  },
  {
    field: 'remark',
    label: '单据备注',
    component: 'InputTextArea'
  },
  {
    field: 'desc',
    label: '单据描述',
    component: 'InputTextArea'
  },

  {
    field: 'files',
    label: '附件',
    component: 'Upload',
    slot: 'FilesSlot'
  }
]

export const auditSchema: FormSchema[] = [
  {
    field: 'amount',
    label: '本次应收金额',
    component: 'InputNumber',
    componentProps: {
      min: 0.01,
      precision: 2,
      placeholder: '请输入本次应收金额'
    },
    required: true
  },
  {
    field: 'collection_at',
    label: '收款日期',
    helpMessage: '流水日期',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    },
    required: true
  },
  {
    field: 'notes',
    label: '对方付款人',
    required: true,
    component: 'Input'
  },
  {
    field: 'notes_account',
    label: '对方付款人账号',
    helpMessage: '账号后4位',
    component: 'Input',
    required: true
  },
  {
    field: 'notes_bank',
    label: '对方付款银行/平台',
    component: 'Input',
    required: true
  },
  {
    field: 'account',
    label: '收款账号',
    helpMessage: '账号后4位',
    component: 'Input',
    required: true
  },
  {
    field: 'account_name',
    label: '收款银行',
    component: 'Input',
    required: true
  },
  {
    field: 'payment_type',
    label: '款项类型',
    component: 'RadioGroup',
    defaultValue: 3,
    componentProps: {
      options: [
        //都不给别人选还显示另外两个...
        {
          label: '定金',
          value: 1
        },
        {
          label: '最后一笔款',
          value: 2
        },
        {
          label: '全款',
          value: 3
        }
      ],
      disabled: true
    },
    required: true
  },
  {
    field: 'g_remark',
    label: '携带备注',
    helpMessage: '写明外汇币别与金额，及其他注释内容',
    component: 'InputTextArea',
    componentProps: {
      autosize: { minRows: 3, maxRows: 6 }
    }
  }
]

export const columnsFn: (Fn?) => BasicColumn[] = (Fn) => [
  {
    title: 'sale_work_id',
    dataIndex: 'sale_work_id',
    width: 100,
    defaultHidden: true
  },
  {
    title: '关联销售单号',
    dataIndex: 'source_uniqid',
    editComponent: 'PagingApiSelect',
    editComponentProps: ({ record }) => {
      return {
        api: getWorkList,
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        params: {
          type: 3,
          status: [1, 2, 3, 4, 5, 15]
        },
        // pagingSize: 20,
        searchParamField: 'source_uniqid',
        selectProps: {
          fieldNames: {
            key: 'id',
            value: 'source_uniqid',
            label: 'source_uniqid'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'source_uniqid',
          allowClear: true,
          onChange: (value, shall) => {
            console.log(value, 'value', record)
            if (!value) {
              //设置为undefined,收入部门的disabled判断才会起效,设置为null就不会
              record.source_uniqid = undefined
              Fn.updateTableDataRecord(record.key, { sale_work_id: undefined })
            } else {
              if (Fn.updateTableDataRecord) {
                Fn.updateTableDataRecord(record.key, { department: shall.department_name, sale_work_id: shall.id })
              }
            }
          },
          style: {
            width: '100%'
          }
        }
      }
    },
    // ifShow: order == 1 || type == 'add',
    width: 250,
    resizable: true,
    editRow: true
  },
  {
    title: '收入科目名称',
    dataIndex: 'account_name',
    editComponent: 'ApiSelect',
    width: 250,
    editRow: true,
    resizable: true,
    editComponentProps: ({ record }) => {
      return {
        api: getCategory,
        resultField: 'items',
        labelField: 'account_name',
        valueField: 'account_name',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'account_name',
            label: 'account_name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'account_name',
          allowClear: true,
          onChange: (value, shall) => {
            console.log(value, shall)
            if (!value) {
              Fn.updateTableDataRecord(record.key, { account_code: undefined })
            } else if (Fn.updateTableDataRecord && value) {
              console.log('true', shall.account_code)
              Fn.updateTableDataRecord(record.key, { account_code: shall.account_code })
            }
          },
          style: {
            width: '100%'
          }
        },

        params: {
          status: 1
        }
      }
    }
  },
  {
    title: '汇率',
    dataIndex: 'rate_name',
    width: 180,
    resizable: true
  },
  {
    title: '汇率',
    dataIndex: 'rate',
    ifShow: false
  },
  {
    title: '描述',
    dataIndex: 'desc',
    editComponent: 'Textarea',
    width: 200,
    editRow: true,
    resizable: true,
    editComponentProps: {
      autosize: { minRows: 3, maxRows: 6 },
      style: {
        width: '100%'
      }
    }
  },
  {
    title: '备注',
    dataIndex: 'remark',
    editComponent: 'Textarea',
    width: 200,
    editRow: true,
    resizable: true,
    editComponentProps: {
      autosize: { minRows: 3, maxRows: 6 },
      style: {
        width: '100%'
      }
    }
  },
  {
    title: '收入金额',
    dataIndex: 'amount',
    width: 150,
    resizable: true,
    editComponent: 'InputNumber',
    editRow: true,
    editComponentProps: {
      precision: 2,
      step: 0.01,
      min: 0.01,
      style: {
        width: '100%'
      }
    }
  },
  {
    title: '收入部门',
    dataIndex: 'department',
    editComponent: 'ApiSelect',
    editComponentProps: ({ record }) => {
      return {
        api: getDept,
        params: { status: 1 },
        resultField: 'items',
        selectProps: {
          fieldNames: {
            key: 'id',
            value: 'name',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          disabled: !!record.source_uniqid
        }
      }
    },
    width: 200,
    editRow: true,
    resizable: true
  },
  {
    title: '往来单位类型',
    dataIndex: 'corres_type',
    editComponent: 'Select',
    editComponentProps: ({ record }) => {
      return {
        allowClear: true,
        options: [
          { label: '员工', value: 1 },
          { label: '部门', value: 2 },
          { label: '客户', value: 3 },
          { label: '供应商', value: 4 },
          { label: '其他', value: 5 }
        ],
        style: {
          width: '100%'
        },
        onChange: (_, shall) => {
          record.corres_pondent = null
          Fn.assignment(shall)
        }
      }
    },
    width: 200,
    editRow: true,
    resizable: true
  },
  {
    title: '往来单位',
    dataIndex: 'corres_pondent',
    editComponent: 'PagingApiSelect',
    editComponentProps: (fromat) => {
      return {
        api: getWorkListFn(fromat.record.corres_type),
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        params: {
          type: 3,
          status: [1, 3, 4, 5, 15]
        },
        selectProps: {
          fieldNames: {
            key: 'id',
            value: 'name',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          style: {
            width: '100%'
          }
        }
      }
    },
    width: 200,
    resizable: true,
    editRow: true
  },
  {
    title: '收入科目代码',
    dataIndex: 'account_code',
    width: 200,
    editComponent: 'Input',
    editRow: true,
    editDynamicDisabled: true,
    resizable: true
  },
  {
    title: '货币',
    dataIndex: 'currency',
    width: 200,
    editRow: true,
    editComponent: 'Input',
    resizable: true
  },
  {
    title: '是否取消',
    dataIndex: 'is_cancel',
    width: 100,
    customRender: ({ value }) => {
      return isUnDef(value) || isNull(value) ? '-' : h(Tag, { color: canceltypeOptions[value].color }, canceltypeOptions[value].label)
    },
    resizable: true
  },
  {
    title: '取消时间',
    dataIndex: 'cancel_at',
    width: 150,
    customRender: ({ value }) => {
      return isUnDef(value) || isNull(value) ? '-' : h('div', null, value)
    },
    resizable: true
  }
]

export const baseFormConfig = {
  labelWidth: 200,
  showActionButtonGroup: false,
  baseColProps: { span: 8 },
  colon: true
}

/**
 * 需要传递后端的明细数据
 */
export const requiredDetailTableParams = [
  'account_code',
  'account_name',
  'amount',
  'corres_pondent',
  'corres_type',
  'currency',
  'dept_id',
  'desc',
  'inCharge',
  'rate',
  'remark',
  'sale_work_id'
]
export const insertInitValue = {
  currency: '人民币',
  rate: 1,
  rate_name: '人民币-1',
  source_uniqid: null,
  account_name: null,
  department: null
}

export function filterArrayByKeys(arr, keys = requiredDetailTableParams) {
  return arr.map((item) => {
    return keys.reduce((obj, key) => {
      if (item.hasOwnProperty(key)) {
        obj[key] = ['corres_type'].includes(key) ? item[key] || undefined : item[key]
      }
      return obj
    }, {})
  })
}

export const childrenColumns = (Fn?): BasicColumn[] => {
  const newDetailsColumns: Array<any> = []
  const datas = cloneDeep(columnsFn(Fn))
  for (const item of datas) {
    if (item.dataIndex == 'corres_pondent') {
      item.editComponent = 'Input'
      item.editComponentProps = {}
    }
    newDetailsColumns.push(item)
  }
  return newDetailsColumns
}
