import { Rule } from 'ant-design-vue/lib/form'
import type { DefaultOptionType } from 'ant-design-vue/es/vc-tree-select/TreeSelect'

import type { FormSchema, BasicColumn } from '/@/components/Table'
import { getDeptSelectTree } from '/@/api/admin/dept'
import { getStaffList } from '/@/api/baseData/staff'
// import { getWorkList } from '/@/api/commonUtils'
// import { getRetreatInWarehouseSelect } from '/@/api/erp/retreat'
// import { getRelatePurchaseList } from '/@/api/erp/inWarehouse'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { message } from 'ant-design-vue'
import { ref } from 'vue'

//已选择的商品
export const selectRowKeys = ref<any[]>([])

export const createSchemas = (handleOrderChange?: Function, way?: string): FormSchema[] => [
  {
    field: 'type',
    label: way == 'afterSale' ? '单据类型' : '退货类型',
    component: 'Select',
    required: true,
    dynamicDisabled: true,
    componentProps: () => {
      return {
        options: [
          {
            value: 1,
            label: '销售退货'
          },
          {
            value: 2,
            label: '采购退货'
          }
        ]
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 8
    },
    show: way == 'afterSale' ? false : true
  },
  {
    field: 'source_uniqid',
    label: '销售单号',
    component: 'Input',
    required: true,
    dynamicDisabled: true,
    componentProps: {
      onChange: async (val: string) => {
        console.log('销售单change', val)
        if (!val) return
        // id是work_id
        try {
          handleOrderChange!()
        } catch (e) {
          throw new Error(`${e}`)
        }
      }
    },
    colProps: {
      span: 8
    }
  },
  {
    field: 'strid',
    label: '采购单号',
    component: 'Input',
    dynamicDisabled: true,
    componentProps: {
      onChange: async (val: string) => {
        console.log('采购单change', val)
        if (!val) return
        // id是work_id
        try {
          handleOrderChange!()
        } catch (e) {
          throw new Error(`${e}`)
        }
      }
    },
    colProps: {
      span: 8
    }
  },
  {
    field: 'tax_amount',
    label: '不含税退货总金额',
    component: 'Input',
    dynamicDisabled: true,
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.type === 2
    },
    colProps: {
      span: 8
    },
    required: true
  },
  {
    field: 'tax_amount1',
    label: '税金',
    component: 'Input',
    dynamicDisabled: true,
    colProps: {
      span: 8
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.type === 2
    }
  },
  {
    field: 'tax_amount2',
    label: '开票税点加收金额',
    component: 'Input',
    dynamicDisabled: true,
    colProps: {
      span: 8
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.type === 2
    }
  },
  {
    field: 'app_point',
    label: '开票加收税点',
    component: 'Input',
    dynamicDisabled: true,
    colProps: {
      span: 8
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.type === 2
    }
  },
  {
    field: 'applicant',
    label: '申请人',
    required: true,
    component: 'ApiSelect',
    componentProps({ formActionType }) {
      return {
        api: getStaffList,
        resultField: 'items',
        selectProps: {
          disabled: true,
          allowClear: true,
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        },
        params: {
          pageSize: 9999
        },
        onChange: async () => {
          try {
            await formActionType?.validateFields!(['applicant'])
          } catch (e) {
            throw new Error(`${e}`)
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 8
    }
  },
  {
    field: 'inCharge',
    label: '负责人',
    required: true,
    component: 'ApiSelect',
    componentProps: ({ formActionType }) => ({
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        disabled: true,
        allowClear: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      },
      params: {
        pageSize: 9999
      },
      onChange: async () => {
        try {
          await formActionType?.validateFields!(['inCharge'])
        } catch (e) {
          throw new Error(`${e}`)
        }
      }
    }),
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 8
    }
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    required: true,
    componentProps: {
      // disabled: ['detail'].includes(type),
      disabled: true,
      api: getDeptSelectTree,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'id', value: 'id', label: 'name' },
        placeholder: '请选择',
        filterTreeNode: (search: string, item: DefaultOptionType) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 8
    }
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    colProps: {
      span: 8
    }
  },
  {
    field: 'product_info',
    label: '商品信息',
    component: 'Input',
    required: true,
    slot: 'ProductSlot',
    rules: [
      {
        required: true,
        validator: (_rule: Rule, value: any[]) => {
          if (!value || value.length === 0) return Promise.reject('请先选择关联的入库单')
          const validResult = value.filter((item) => selectRowKeys.value.includes(item.id)).every((item) => item.quantity > 0)
          console.log(way)

          if (!validResult && way !== 'afterSale') {
            message.error('退货数量必须大于0')
            return Promise.reject('退货数量必须大于0')
          }
          return Promise.resolve()
        }
      }
    ]
  }
]

// function validateProductInfo() {}

export const columns: BasicColumn[] = [
  {
    dataIndex: 'name',
    title: '产品名称',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'imgs',
    title: '产品图片',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return useRender.renderImg(text)
    }
  },
  // {
  //   dataIndex: 'qty_total',
  //   title: '采购入库商品总数量',
  //   width: 120,
  //   resizable: true
  // },
  // {
  //   dataIndex: 'qty_residue',
  //   title: '采购入库剩余可用商品数量',
  //   width: 120,
  //   resizable: true
  // },
  {
    dataIndex: 'unit',
    title: '单位',
    width: 100,
    resizable: true
  },
  {
    dataIndex: 'unit_price',
    title: '单价',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'unit_price_tax',
    title: '不含税单价',
    width: 120,
    resizable: true,
    defaultHidden: true
  },
  {
    dataIndex: 'maxQuantity',
    title: '剩余可退货数量',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'quantity',
    title: '退货数量',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'remark',
    title: '退货备注',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'tax_point',
    title: '税点',
    width: 120,
    resizable: true,
    defaultHidden: true
  },
  {
    dataIndex: 'total_price',
    title: '总价',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'desc',
    title: '退货描述',
    width: 120,
    resizable: true
  },
  {
    title: '长度(CM)',
    dataIndex: 'length',
    width: 100
  },
  {
    title: '宽度(CM)',
    dataIndex: 'width',
    width: 100
  },
  {
    title: '高度(CM)',
    dataIndex: 'height',
    width: 100
  }
]
