import type { BasicColumn, FormSchema, TableActionType } from '/@/components/Table'
import type { FormProps } from '/@/components/Form'
import { getDeptTree } from '/@/api/admin/dept'
// import { mapOrderStatusType } from './drawer.data'
import { getStaffList } from '/@/api/baseData/staff'
// import { getcustomerList } from '/@/api/financialDocuments/otherIncome'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { Dropdown, Menu, MenuItem, message, Tag } from 'ant-design-vue'
import { h, nextTick, ref } from 'vue'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { isNull, isUndefined } from 'lodash-es'
import { useI18n } from '/@/hooks/web/useI18n'
import { isArray, isNullOrUnDef, isString } from '/@/utils/is'
// import { setIsFinishSplit } from '/@/api/erp/sales'
// import { useMessage } from '/@/hooks/web/useMessage'
import { getDept } from '/@/api/erp/systemInfo'
import { mapSaleType } from './generateModal'
import { setIsFinishSplit } from '/@/api/erp/sales'
import { DIVIDER_SCHEMA, GET_STATUS_SCHEMA, NEW_STATUS_FORMCONFIG } from '/@/const/status'

export const ALL = 'all'
export const SALE = 'sale'
export const FEE = 'fee'
export const mapTypeMenu = {
  [ALL]: [3, 27],
  [SALE]: [3],
  [FEE]: [27]
}
export const menuOptions = [
  { value: ALL, label: '全部' },
  { value: SALE, label: '销售订单' },
  { value: FEE, label: '费用订单' }
]

// const { createMessage } = useMessage()
export const tableRef = ref<Nullable<TableActionType>>(null)
const pathname = window.location.pathname

//订单类型和状态
const saleStore = useSaleOrderStore()
const { t, tm } = useI18n()

/** 结算状态 */
export const mapAudit = {
  0: { label: '未结算', color: '' },
  1: { label: '已结算', color: 'green' },
  2: { label: '待结算', color: 'skyblue' }
}
const isticket = {
  0: { label: '否', color: '' },
  1: { label: '是', color: 'green' }
}
export const columns: BasicColumn[] = [
  {
    title: '开单时间',
    dataIndex: 'submited_at',
    width: 120,
    resizable: true
  },
  {
    title: '交货日期',
    dataIndex: 'delivery_at',
    width: 120,
    resizable: true
  },
  {
    title: '可备货日期',
    dataIndex: 'stock_at',
    width: 120,
    resizable: true
  },
  {
    title: '收款次数',
    dataIndex: 'receipt_num',
    width: 100,
    resizable: true
  },
  {
    title: '项目id',
    dataIndex: 'project_number',
    width: 120,
    resizable: true
  },
  {
    title: '项目名称',
    dataIndex: 'project_name',
    width: 120,
    resizable: true
  },
  {
    title: '是否开票',
    dataIndex: 'is_ticket',
    width: 100,
    resizable: true,
    customRender: ({ record }) => {
      return h(Tag, { color: isticket[record.is_ticket].color }, () => isticket[record.is_ticket].label)
    }
  },
  {
    title: '是否完成产品拆分',
    dataIndex: 'is_finish_split',
    width: 150,
    helpMessage: '完成产品拆分过后,才能进行采购',
    resizable: true,
    customRender: ({ record }) => {
      return pathname == '/s/' ? (
        <Dropdown disabled={record.status !== 2}>
          {{
            default: () => (
              <Tag color={isticket[record.is_finish_split]?.color || 'default'}>
                {isticket[record.is_finish_split] ? isticket[record.is_finish_split].label : '-'}
              </Tag>
            ),
            overlay: () => (
              <Menu onClick={(val) => handleSetIsFinishSplit(val, record)}>
                {{
                  default: () => (
                    <>
                      <MenuItem key="1">
                        <span>是</span>
                      </MenuItem>
                      <MenuItem key="0">
                        <span>否</span>
                      </MenuItem>
                    </>
                  )
                }}
              </Menu>
            )
          }}
        </Dropdown>
      ) : (
        h(Tag, { color: isticket[record.is_finish_split].color }, () => isticket[record.is_finish_split].label)
      )
    }
  },
  {
    title: '紧急状态',
    dataIndex: 'urgent_level',
    width: 120,
    resizable: true,
    customRender({ text }) {
      const map = {
        1: { label: '一般', color: '' },
        2: { label: '紧急', color: 'green' },
        3: { label: '非常紧急', color: 'red' }
      }
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: map[text]?.color }, () => map[text]?.label)
    }
  },
  {
    title: '开票类型',
    dataIndex: 'ticket',
    width: 100,
    resizable: true
  },
  {
    title: '销售类型',
    dataIndex: 'sale_type',
    width: 120,
    resizable: true,
    customRender: ({ value }) => (value ? useRender.renderTag(mapSaleType[value]) : '-')
  },
  {
    title: '销售订单号',
    dataIndex: 'source_uniqid',
    width: 200,
    resizable: true
  },
  {
    title: '订单类型',
    dataIndex: 'type',
    width: 120,
    resizable: true,
    customRender: ({ value }) => (isNullOrUnDef(value) ? '-' : saleStore.saleType[value])
  },
  {
    title: '紧急程度',
    dataIndex: 'urgent_level',
    width: 100,
    resizable: true,
    customRender: ({ text }) =>
      text ? useRender.renderTag(t(`tag.mapUrgentLevel.${text}.alias`), t(`tag.mapUrgentLevel.${text}.color`)) : '-'
  },
  {
    title: '订单状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ value }) => {
      // return h(Tag, { color: saleStore.statusColor[record.status] }, () => saleStore.saleStatus[record.status])
      return isNullOrUnDef(value) ? '-' : useRender.renderTag(saleStore.saleStatus[value], saleStore.statusColor[value])
    }
  },
  {
    title: '结算状态',
    dataIndex: 'is_audit',
    width: 150,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : h(Tag, { color: mapAudit[value]?.color }, () => mapAudit[value]?.label)
      // return mapStatus[record.status]?.label
    }
  },
  {
    title: '是否销售改单',
    dataIndex: 'is_change_sale',
    width: 150,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : h(Tag, { color: isticket[value]?.color }, () => isticket[value]?.label)
      // return mapStatus[record.status]?.label
    }
  },
  {
    title: '是否售后单',
    dataIndex: 'is_after_sale',
    width: 150,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : h(Tag, { color: isticket[value]?.color }, () => isticket[value]?.label)
      // return mapStatus[record.status]?.label
    }
  },
  {
    title: '驳回备注',
    dataIndex: 'reject_remark',
    width: 150,
    customRender({ text }) {
      return text || '-'
    }
  },
  {
    title: '驳回日期',
    dataIndex: 'reject_at',
    width: 150,
    customRender({ text }) {
      return text || '-'
    }
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 150
  },
  {
    title: '业务部门',
    dataIndex: 'operation_department',
    width: 150,
    resizable: true
  },
  {
    title: '客户名称',
    dataIndex: 'client_name',
    width: 100,
    resizable: true
  },
  {
    title: '老客户邀约负责人',
    dataIndex: 'olduser_name',
    width: 100,
    resizable: true
  },
  {
    title: '老客户/自建渠道是否已核实',
    dataIndex: 'is_send_olduser',
    width: 200,
    resizable: true,
    customRender: ({ record }) => {
      return record.is_send_olduser == 1 ? '已核实' : '未核实'
    }
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 100,
    resizable: true
  },
  {
    title: '交付负责人',
    dataIndex: 'delivery_incharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '方案负责人',
    dataIndex: 'program_incharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '项目负责人',
    dataIndex: 'inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '审核人',
    dataIndex: 'auditor_name',
    width: 100,
    resizable: true
  },

  // {
  //   title: '入库包裹数',
  //   dataIndex: 'pkg_num',
  //   width: 100,
  //   resizable: true
  // },
  {
    title: '销售渠道',
    dataIndex: 'source',
    width: 100,
    resizable: true,
    customRender: ({ record }) => record.source || '-'
  },
  {
    title: '订单金额汇率',
    dataIndex: 'rate',
    width: 100,
    resizable: true,
    customRender: ({ record }) => record.rate || '-'
  },
  {
    title: '币种',
    dataIndex: 'currency',
    width: 100,
    resizable: true,
    customRender: ({ record }) => record.currency || '-'
  },
  {
    title: '订单金额',
    dataIndex: 'total_price',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  // {
  //   title: '原总应收金额',
  //   dataIndex: 'receivable_org',
  //   width: 100,
  //   resizable: true,
  //   customRender: ({ value }) => {
  //     return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
  //   }
  // },
  // {
  //   title: '总应收金额',
  //   dataIndex: 'receivable',
  //   width: 100,
  //   resizable: true,
  //   customRender: ({ value }) => {
  //     return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
  //   }
  // },

  {
    title: '已收金额',
    dataIndex: 'received',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value ? value : 0)
    }
  },
  {
    title: '实收金额',
    dataIndex: 'received_actual',
    width: 100,
    resizable: true,
    helpMessage: '实收金额与已收金额不对等,应是本张销售单存在退货或退款金额',
    customRender: ({ value }) => {
      return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  {
    title: '改单后订单总额',
    dataIndex: 'receivable_left',
    helpMessage: '应收金额=总应收金额-退货金额',
    width: 150,
    resizable: true,
    customRender: ({ value }) => {
      return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  {
    title: '结算金额',
    dataIndex: 'audit_amount',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  {
    title: '未收货款',
    dataIndex: 'un_audit_amount',
    width: 100,
    resizable: true,
    customRender: ({ record }) => {
      const unAuditAmount = record.is_audit !== 1 ? (Number(record.receivable_left) - Number(record.received_actual)).toFixed(4) : 0

      return unAuditAmount
    }
  },
  {
    title: '已入库金额',
    dataIndex: 'in_amount',
    width: 150,
    resizable: true
  },
  {
    title: '入库未收款金额',
    dataIndex: 'in_no_pay_amount',
    width: 150,
    resizable: true
  },
  // {
  //   title: '带单部门',
  //   dataIndex: 'take_department',
  //   width: 100,
  //   resizable: true
  // },
  // {
  //   title: '运营中心',
  //   dataIndex: 'operation_name',
  //   width: 100,
  //   resizable: true
  // },
  {
    title: '翻译人员名称',
    dataIndex: 'translate',
    width: 100,
    resizable: true
  },
  {
    title: '设计师',
    dataIndex: 'design',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      const textarr = text.map((item) => item.name)
      return text ? useRender.renderTags(textarr) : '-'
    }
  },
  {
    title: '定制全案设计师',
    dataIndex: 'design_customized_names',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? useRender.renderTags(text) : '-'
    }
  },
  {
    title: '报价跟单人员',
    dataIndex: 'quotation',
    width: 100,
    resizable: true
  },
  {
    title: '单品设计师',
    dataIndex: 'one_case_designer_name',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return text ? useRender.renderTags(text) : '-'
    }
  },
  {
    title: '全案设计师',
    dataIndex: 'full_case_designer_name',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      // return text ? useRender.renderTags(text) : '-'
      if (isArray(text)) {
        return useRender.renderTags(text)
      } else if (isString(text)) {
        return useRender.renderTag(text)
      } else {
        return '-'
      }
    }
  },
  {
    title: '全案设计部门',
    dataIndex: 'dept_designer_department',
    width: 150,
    resizable: true
  },
  {
    title: '单品设计部门',
    dataIndex: 'one_dept_designer_department',
    width: 150,
    resizable: true
  },
  {
    title: '2d设计师',
    dataIndex: 'design2d',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      const textarr = text.map((item) => item.name)
      return text ? useRender.renderTags(textarr) : '-'
    }
  },
  {
    title: '3d设计师',
    dataIndex: 'design3d',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      const textarr = text.map((item) => item.name)
      return text ? useRender.renderTags(textarr) : '-'
    }
  },
  {
    title: '迎宾员',
    dataIndex: 'welcomes_names',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? useRender.renderTags(text) : '-'
    }
  },
  {
    title: '摄影师',
    dataIndex: 'shoots_names',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? useRender.renderTags(text) : '-'
    }
  },
  {
    title: '运营人员',
    dataIndex: 'yyperson_name',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? useRender.renderTag(text) : '-'
    }
  },
  {
    title: '凤凰计划',
    dataIndex: 'phoenix_plan',
    width: 100,
    resizable: true
  },
  {
    title: '渠道来源',
    dataIndex: 'source2',
    width: 100,
    resizable: true
  },
  // {
  //   title: '退货金额',
  //   dataIndex: 'retreat_amount',
  //   width: 100,
  //   resizable: true,
  //   customRender: ({ value }) => {
  //     return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
  //   }
  // },
  {
    title: '佣金',
    dataIndex: 'commission',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  {
    title: '贸易方式',
    dataIndex: 'trade_methods',
    width: 120,
    resizable: true
  },
  {
    title: '国家',
    dataIndex: 'country',
    width: 120,
    resizable: true
  },
  {
    title: '结束时间',
    dataIndex: 'est_finished_at',
    width: 120,
    resizable: true
  },
  {
    title: '结算日期',
    dataIndex: 'audit_at',
    width: 120,
    resizable: true
  },
  {
    title: '业绩核定日期',
    dataIndex: 'ach_app_at',
    width: 120,
    resizable: true
  },
  {
    title: '水单日期',
    dataIndex: 'fund_at',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '设计师下单时间',
    dataIndex: 'designer_orders_at',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '财务特批',
    dataIndex: 'is_finance_approved',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '' : text ? '是' : '否'
    }
  }
]

export const warehouseColumns: BasicColumn[] = [
  {
    title: '开单时间',
    dataIndex: 'submited_at',
    width: 120,
    resizable: true
  },
  {
    title: '销售订单号',
    dataIndex: 'source_uniqid',
    width: 200,
    resizable: true
  },
  {
    title: '国家',
    dataIndex: 'country',
    width: 120,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 150
  }
]

export const drawerTableColumns: BasicColumn[] = [
  {
    title: '单号',
    dataIndex: 'order_no'
  },
  {
    title: '订单名称',
    dataIndex: 'order_name'
  },
  {
    title: '订单总价',
    dataIndex: 'total_price'
  }
]

export const productTableColumns: BasicColumn[] = [
  {
    title: '产品图片',
    dataIndex: 'product_img',
    width: 100
  },
  {
    title: '产品编号',
    dataIndex: 'product_no',
    width: 100
  },
  {
    title: '数量',
    dataIndex: 'product_count',
    width: 100
  },
  {
    title: '单价',
    dataIndex: 'product_price',
    width: 100
  },
  {
    title: '批号',
    dataIndex: 'batch_no',
    width: 100
  }
]
const status_schema = GET_STATUS_SCHEMA(saleStore.mapOrderStatusOptions)

const saleArr = [
  '/erp/saleOrder',
  '/erp/backorder',
  '/erpFlow/unpurchaseTrackingsplit',
  '/erpFlow/unpurchaseOrder',
  '/erpFlow/tobeconfirmed'
]

export const searchFormSchema: (tableAction?, routeName?) => FormSchema[] = (tableAction?, routeName?) => [
  {
    field: 'menuTypes',
    label: '',
    ifShow: ['/erp/saleOrder'].includes(routeName),
    defaultValue: ALL,
    component: 'RadioButtonGroup',
    componentProps: ({ formActionType }) => ({
      options: menuOptions,
      onChange: (value) => {
        tableAction?.setProps({
          searchInfo: {
            types: mapTypeMenu[value]
          }
        })

        nextTick(() => formActionType.submit())
      }
    }),

    colProps: {
      span: 6
    }
  },
  {
    ...status_schema,
    colProps: {
      span: ['/erp/saleOrder', '/erpFlow/tobeconfirmed', '/erpFlow/unpurchaseTrackingsplit'].includes(routeName) ? 12 : 24
    },
    ifShow: ['/erp/saleOrder', '/erp/backorder', '/erpFlow/unpurchaseOrder', '/erpFlow/tobeconfirmed'].includes(routeName)
  },
  {
    field: 'text',
    label: '', //未收货贷款合计:
    ifShow: saleArr.includes(routeName) && !['/erp/backorder'].includes(routeName),
    slot: 'textSlot',
    component: 'Input',
    colProps: {
      span: 6
    }
  },
  { ...DIVIDER_SCHEMA, ifShow: saleArr.includes(routeName) },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        placeholder: '请选择',
        showSearch: true,
        optionFilterProp: 'name',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  // {
  //   field: 'is_audit_dept',
  //   label: '核算部门',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: '是', value: 1 },
  //       { label: '否', value: 0 }
  //     ]
  //   }
  // },
  {
    field: 'operation',
    label: '业务部门',
    component: 'PagingApiSelect',
    componentProps: {
      api: getDept,
      // params: { status: 1, is_audit: 1, is_operate: 1 },
      params: { status: 1, is_audit: 1 },
      resultField: 'items',
      labelField: 'name',
      valueField: 'id',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        style: {
          width: '100%'
        }
      }
    }
  },
  // {
  //   field: 'take_dept_id',
  //   label: '带单部门',
  //   component: 'PagingApiSelect',
  //   componentProps: {
  //     api: getDept,
  //     params: { status: 1 },
  //     // params: { status: 1, is_audit: 1 },
  //     resultField: 'items',
  //     labelField: 'name',
  //     valueField: 'id',
  //     searchMode: true,
  //     pagingMode: true,
  //     selectProps: {
  //       fieldNames: {
  //         key: 'key',
  //         value: 'id',
  //         label: 'name'
  //       },
  //       optionFilterProp: 'name',
  //       showSearch: true,
  //       placeholder: '请选择',
  //       allowClear: true,
  //       style: {
  //         width: '100%'
  //       }
  //     }
  //   }
  // },
  // {
  //   field: 'status',
  //   label: '状态',
  //   component: 'Select',
  //   componentProps: {
  //     options: saleStore.mapOrderStatusOptions
  //   }
  // },
  {
    field: 'source_uniqid',
    label: '销售订单号',
    component: 'Input'
  },
  {
    field: 'delivery_incharge',
    label: '交付负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'program_incharge',
    label: '方案负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'inCharge',
    label: '项目负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'is_audit',
    label: '结算状态',
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        { label: '未完成结算', value: 0 },
        { label: '已完成结算', value: 1 },
        { label: '待结算', value: 2 }
      ]
    },
    colProps: {
      span: 8
    }
  },
  {
    field: 'is_change_sale',
    label: '是否销售改单',
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ]
    }
  },
  {
    field: 'is_after_sale',
    label: '是否售后单',
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ]
    }
  },
  {
    field: 'is_finish_split',
    label: '是否完成拆单',
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ]
    }
  },

  {
    field: 'client_name',
    label: '客户',
    component: 'Input'
    // componentProps: () => {
    //   return {
    //     api: getcustomerList,
    //     resultField: 'items',
    //     selectProps: {
    //       fieldNames: {
    //         key: 'key',
    //         value: 'id',
    //         label: 'name'
    //       },
    //       showSearch: true,
    //       placeholder: '请选择',
    //       optionFilterProp: 'name',
    //       allowClear: true
    //     }
    //   }
    // }
  },
  {
    field: 'creator',
    label: '创建人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'receivable',
    label: '应收金额',
    component: 'Input',
    slot: 'receivable'
  },
  {
    field: 'submited_at',
    label: '开单日期',
    component: 'SingleRangeDate',
    // defaultValue: [dayjs().add(-7, 'day').format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')],

    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'project_number',
    label: '项目id',
    component: 'Input'
  },
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input'
  },
  {
    field: 'est_finished_at',
    label: '项目结束时间',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'urgent_level',
    label: '紧急程度',
    component: 'Select',
    componentProps: {
      options: tm('tag.urgentLevelList'),
      fieldNames: { key: 'value', value: 'value', label: 'alias' }
    }
  },
  {
    field: 'ach_app_at',
    label: '业绩核定日期',
    component: 'SingleRangeDate',
    // defaultValue: [dayjs().add(-7, 'day').format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')],

    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'is_ach_app_at_null',
    label: '业绩核定日期是否为空',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'received',
    label: '已收金额',
    component: 'InputNumber',
    slot: 'received'
  },
  {
    field: 'is_pass_nopay',
    label: '入库未收款金额 > 0',
    component: 'Select',
    // labelWidth: 180,
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'is_send_olduser',
    label: '老客户/自建渠道是否已核实',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'yyperson',
    label: '运营人员',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  }
]

async function handleSetIsFinishSplit({ key }, { id }) {
  try {
    const res = await setIsFinishSplit({ id, is_finish_split: key })
    console.log(res)

    if (res.news == 'success') {
      message.success('设置成功')
      tableRef.value?.tableAction?.reload()
      return
    }
    message.error('设置失败')
  } catch (err) {
    message.error('设置失败')
  }
}

export const formConfigFn: (
  setProps?: any,
  clearSelectedRowKeys?: any,
  reload?: any,
  tableActionType?: any,
  routeName?: string
) => Partial<FormProps> = (setProps?, clearSelectedRowKeys?, reload?, tableActionType?, routeName?) => ({
  ...NEW_STATUS_FORMCONFIG,
  schemas: searchFormSchema(tableActionType, routeName),
  fieldMapToTime: [
    ['receivable', ['receivable1', 'receivable2']],
    ['received', ['received_start', 'received_end']],
    ['submited_at', ['submited_at_start', 'submited_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
    ['est_finished_at', ['est_finished_at_start', 'est_finished_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
    ['ach_app_at', ['ach_app_at_start', 'ach_app_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
  ],

  resetFunc: async () => {
    clearSelectedRowKeys?.()
    reload?.()
    setProps({
      beforeFetch: () => {
        return { page: 1, pageSize: 10 }
      }
    })

    setTimeout(() => {
      setProps({
        beforeFetch: (params) => {
          return { ...params, mapOrder: undefined }
        }
      })
    }, 0)
  },
  submitFunc: (): any => {
    clearSelectedRowKeys?.()
    reload?.()
  }
})
