export const checkboxErrMessage = '状态为已取消或未执行或结算状态为已结算的订单不可以执行本次操作'
export const closingAuditMessage = '状态为已结束才可以执行本次操作'

export const checkoutIncomeErrorMessage = '状态为已取消或未执行的订单不可以执行本次操作'
export const expenseAuditMessage = '费用类订单状态为执行中才可以执行本次操作'

//其实可以把item.status === 0 || item.status === 16放getCheckboxProps,但是难保以后不会继续增加按钮,所以还是写在这里吧
export function isCheckboxDisabled(type, selectRows) {
  if (selectRows.length === 0) {
    message.error('请勾选订单')
    return true
  }
  if (['generateReceipt'].includes(type)) {
    if (selectRows.some((item) => item.status === 0 || item.status === 16 || item.is_audit == 1)) {
      message.error(checkboxErrMessage)
      return true
    }
  } else if (['createOtherIncome'].includes(type)) {
    if (selectRows.some((item) => item.status === 0 || item.status === 16)) {
      message.error(checkoutIncomeErrorMessage)
      return true
    }
  } else if (['closingAudit'].includes(type)) {
    if (selectRows.some((item) => item.status !== 15)) {
      message.error(closingAuditMessage)
      return true
    }
  } else if (['expenseAudit'].includes(type)) {
    if (selectRows.some((item) => item.status !== 1)) {
      message.error(expenseAuditMessage)
      return true
    }
  } else {
    return true
  }
}

export function validTableData(data) {
  if (data.length === 0) {
    message.error('收入明细不能为空')
    return true
  }
  if (data.some((item) => !item.account_name || !item.account_code || !item.amount || !item.department || !item.currency?.trim())) {
    message.error('收入明细数据必填数据不完整')
    return true
  }
}
import { getRelationType } from '../../../financialDocuments/capitalFlow/datas/fn'

import { useRender } from '/@/components/Table/src/hooks/useRender'
import { ref } from 'vue'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { BasicColumn } from '/@/components/Table'
import { message } from 'ant-design-vue'

export const paymentList: any = ref([])

/** 抽屉表格字段显示 1:销售退款，2：采购退款 */
export function tableColumn(val: number): BasicColumn[] {
  return [
    {
      title: '单号',
      dataIndex: 'strid'
    },
    {
      title: '类型',
      dataIndex: 'type',
      customRender: ({ text }) => {
        return getRelationType(text)
      }
    },
    {
      title: val == 1 ? '应收金额' : '应付金额',
      dataIndex: 'amount',
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text) : '0.00'
      }
    },
    {
      title: '款单分配金额',
      dataIndex: 'amount_fdocw'
    },
    {
      title: val == 1 ? '本次已收金额' : '本次已付金额',
      dataIndex: val == 1 ? 'amount_rec' : 'amount_paid',
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text) : '0.00'
      }
    }
  ]
}

/**
 *  退款单类型
 * @param val 传值返回对应的类型，传对应的field（字符串类型）则用于筛选
 * @returns tag or options
 */
export function typeOfRefundNote(val): any {
  if (val === 'order') {
    return [
      { label: '销售退款', value: 1 },
      { label: '采购退款', value: 2 }
    ]
  } else {
    const mapValue = {
      1: { text: '销售退款', value: 1, color: '' },
      2: { text: '采购退款', value: 2, color: '' }
    }
    return mapValue[val] ? useRender.renderTag(mapValue[val]?.text, mapValue[val]?.color) : '-'
  }
}

/**
 *  款项类型
 * @param val 传值返回对应的类型，传对应的field（字符串类型）则用于筛选
 * @returns tag or options
 */
export function paymentType(val): any {
  if (val === 'type') {
    return [
      { label: '退款', value: 1 },
      { label: '不退款', value: 2 }
    ]
  } else {
    const mapValue = {
      1: { text: '退款', value: 1, color: '' },
      2: { text: '不退款', value: 2, color: '' }
    }
    return mapValue[val] ? useRender.renderTag(mapValue[val]?.text, mapValue[val]?.color) : '-'
  }
}

/**
 *  审核状态
 * @param val 传值返回对应的类型，传对应的field（字符串类型）则用于筛选
 * @returns tag or options
 */
export function auditStatus(val): any {
  if (val === 'status') {
    return [
      { label: '待审核', value: 0 },
      { label: '已审核', value: 1 },
      { label: '已结束', value: 15 }
    ]
  } else {
    const mapValue = {
      0: { text: '待审核', value: 0, color: '' },
      1: { text: '已审核', value: 1, color: 'success' },
      15: { text: '已结束', value: 15, color: 'warning' }
    }
    return mapValue[val] ? useRender.renderTag(mapValue[val]?.text, mapValue[val]?.color) : '-'
  }
}

export function checkStatus(val): any {
  const mapValue = {
    0: { label: '未审批', color: 'default' },
    1: { label: '审批通过', color: 'processing' },
    2: { label: '驳回', color: 'error' }
  }
  return mapValue[val] ? useRender.renderTag(mapValue[val]?.label, mapValue[val]?.color) : '-'
}
