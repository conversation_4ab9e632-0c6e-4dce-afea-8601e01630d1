import { BasicColumn } from '/@/components/Table'

export const commonPackingInfokey = {
  主产品编码: 'puid',
  子产品名称: 'name',
  产品数量: 'quantity',
  单位: 'unit',
  '产品占比(%)': 'proportion_org',
  备注: 'remark',
  描述: 'desc',
  // 图片: 'imgs',
  '长度(cm)': 'length',
  '宽度(cm)': 'width',
  '高度(cm)': 'height'
}

// 获取单元格信息

export function formatDate(headers, rowData) {
  return headers.value.reduce((acc, header, cloIndex) => {
    acc[header] = rowData.slice(1)[cloIndex]
    return acc
  }, {})
}

// 自定义排序函数，按照文件名中的数字部分进行数值排序
export function compareFilePaths(a, b) {
  const numA = parseInt(a.match(/\d+/)[0]) // 提取数字部分并转换为整数
  const numB = parseInt(b.match(/\d+/)[0]) // 提取数字部分并转换为整数
  return numA - numB
}
export const formartKey = (key) => {
  const newline = /\n/g
  const spaces = / /g
  let newKey = key

  if (newline.test(newKey)) {
    newKey = newKey.replace(newline, '')
  }
  if (spaces.test(newKey)) {
    newKey = newKey.replace(spaces, '')
  }
  return newKey
}

export const columns: BasicColumn[] = [
  {
    title: '主产品编码',
    dataIndex: 'puid',
    width: 100,
    resizable: true
  },
  {
    title: '产品中文名',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  {
    title: '产品数量',
    dataIndex: 'quantity',
    width: 100,
    resizable: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 100,
    resizable: true
  },
  {
    title: '产品占比',
    dataIndex: 'proportion_org',
    width: 100,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 100,
    resizable: true
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 100,
    resizable: true
  },
  {
    title: '长度(cm)',
    dataIndex: 'length',
    width: 100,
    resizable: true
  },
  {
    title: '宽度(cm)',
    dataIndex: 'width',
    width: 100,
    resizable: true
  },
  {
    title: '高度(cm)',
    dataIndex: 'height',
    width: 100,
    resizable: true
  }
  // {
  //   title: '图片',
  //   dataIndex: 'imgs',
  //   width: 100,
  //   resizable: true
  // }
]
