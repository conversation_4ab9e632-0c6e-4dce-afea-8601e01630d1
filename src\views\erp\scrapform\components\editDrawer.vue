<template>
  <BasicDrawer @register="registerDrawer" width="90%" @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #ProductSlot>
        <Alert
          message="未删除的包裹作为报废包裹"
          :description="
            pram_type === 'edit' && getDataSource().length == 1 ? '编辑时请至少保留一个报废包裹,如若不留那请作废该张报废订单' : ''
          "
          :type="pram_type === 'edit' && getDataSource().length == 1 ? 'error' : 'info'"
          show-icon
        />
        <BasicTable @register="registerTable">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action' && ['add', 'edit'].includes(pram_type)">
              <TableAction :actions="createActions(record)" />
            </template>
          </template>
          <template #expandedRowRender="{ record: packagerecord }">
            <BasicTable
              @register="registerChildrenTable"
              :api="getPackageDetail.bind(null, { ids: [pram_type == 'add' ? packagerecord.id : packagerecord.packing_package_id] })"
            />
          </template>
        </BasicTable>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { childrencolumns, inwharecolumns, schemas } from '../datas/edit.data'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table'
import { getPackageList } from '/@/api/erpFlow/packages'
import { ref } from 'vue'
import { Alert } from 'ant-design-vue'
import { cloneDeep } from 'lodash-es'
import { getPackageDetail } from '/@/api/erpFlow/packages'
import { scrapupdate } from '/@/api/erp/scrapform'

const pram_type = ref('')
const parm_id = ref('')
const emit = defineEmits(['success'])

const [registerForm, { validate, setFieldsValue, resetFields, updateSchema }] = useForm({
  schemas: schemas(handleOrderChange, pram_type.value),
  baseColProps: { span: 24 },
  colon: true,
  showActionButtonGroup: false,
  labelWidth: 120
})
const [registerTable, { getDataSource, setTableData, deleteTableDataRecord }] = useTable({
  columns: inwharecolumns,
  showIndexColumn: false,
  pagination: false,
  rowKey: 'id',
  actionColumn: {
    width: 170,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  }
})
const [registerChildrenTable] = useTable({
  showIndexColumn: false,
  pagination: false,
  maxHeight: 300,
  canResize: false,
  columns: childrencolumns,
  // resizeHeightOffset: 340,
  afterFetch: async (data) => {
    return data[0].items
  }
})

const [registerDrawer, { changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  console.log(data)
  updateSchema(schemas(handleOrderChange, data.type))
  await resetFields()
  setTableData([])
  pram_type.value = data.type
  if (data.type !== 'add') {
    parm_id.value = data.record.id
    data.record.package.forEach((item) => {
      item.strid = item.package_strid
    })
    setTableData([...data.record.package])
    setFieldsValue({ strid: data.record.strid, product_info: data.record.package })
  }
})
async function handleOrderChange(e) {
  if (e.length == 0) {
    setTableData([])
    return
  }
  const inWarehouseResult = await getPackageList({ sale_work_ids: [...e], pageSize: 99999 })
  const newtabledata = inWarehouseResult.items
    .filter(
      (item) =>
        item.is_out == 0 &&
        item.is_in == 2 &&
        item.is_cancel == 0 &&
        item.is_stock == 0 &&
        item.is_old == 0 &&
        item.is_retreat == 0 &&
        item.is_scrap == 0
    )
    .map((item) => ({ ...item }))
  console.log(newtabledata)

  await setFieldsValue({
    product_info: newtabledata
  })
  if (pram_type.value === 'add') {
    setTableData(newtabledata)
  } else if (pram_type.value === 'edit') {
    const dataSource = cloneDeep(getDataSource())
    newtabledata.forEach((newObj) => {
      const existingObjWithSameId = dataSource.find((obj) => obj.packing_package_id === newObj.id)
      if (!existingObjWithSameId) {
        dataSource.push(newObj)
      }
    })

    // 合并新数据到更新后的数组

    setTableData(dataSource)
    console.log(getDataSource())
  }
}

function createActions(record: Recordable): ActionItem[] {
  let buttonList: ActionItem[] = [
    {
      label: '删除',
      onClick: () => {
        deleteTableDataRecord(record.id)
      },
      disabled: getDataSource().length == 1
    }
  ]

  return buttonList
}

async function handleSubmit() {
  try {
    changeOkLoading(true)
    const packsgearr = ref<any>([])
    const formData = await validate()
    for (const item of getDataSource()) {
      packsgearr.value.push(item.packing_package_id ? item.packing_package_id : item.id)
    }
    // if (packsgearr.value.length == 0) return message.error('请选择退货包裹')
    const params = {
      id: pram_type.value !== 'add' ? parm_id.value : undefined,
      remark: formData.remark,
      packageList: packsgearr.value
    }
    const { msg } = await scrapupdate(params)
    if (msg === 'success') {
      emit('success')
      closeDrawer()
      changeOkLoading(false)
      return
    }
  } catch (e) {
    console.log(e)
    changeOkLoading(false)
  } finally {
    setTimeout(() => {
      changeOkLoading(false)
    }, 2000)
  }
}
</script>
