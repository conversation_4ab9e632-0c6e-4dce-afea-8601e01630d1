import { BasicColumn } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { FormSchema } from '/@/components/Form'
import { getStaffList } from '/@/api/baseData/staff'
import { DIVIDER_SCHEMA, GET_STATUS_SCHEMA } from '/@/const/status'
export const columns: BasicColumn[] = [
  {
    title: '报废单号',
    dataIndex: 'strid',
    resizable: true,
    width: 200
  },
  {
    title: '状态',
    dataIndex: 'status',
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        0: { label: '待确认', color: 'pink' },
        1: { label: '已确认 ', color: 'orange' },
        2: { label: '仓库已审核 ', color: 'skyblue' },
        3: { label: '仓库已驳回 ', color: 'red' },
        4: { label: '财务已驳回 ', color: 'red' },
        15: { label: '完成', color: 'blue' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否作废',
    dataIndex: 'is_cancel',
    width: 200,
    resizable: true,
    customRender({ text }) {
      const map = {
        0: { label: '否', color: 'green' },
        1: { label: '是 ', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '创建人名称',
    dataIndex: 'creator_name',
    resizable: true
  },
  {
    title: '确认人名称',
    dataIndex: 'auditor_name',
    resizable: true
  },
  {
    title: '仓库审核人',
    dataIndex: 'auditor_warehouse_name',
    resizable: true
  },
  {
    title: '财务审核人',
    dataIndex: 'auditor_finace_name',
    resizable: true
  },
  {
    title: '作废人',
    dataIndex: 'cancel_inCharge_name',
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    resizable: true
  },
  {
    title: '确认时间',
    dataIndex: 'status_at',
    resizable: true
  },
  {
    title: '仓库审核时间',
    dataIndex: 'auditor_warehouse_at',
    resizable: true
  },
  {
    title: '财务审核时间',
    dataIndex: 'auditor_finace_at',
    resizable: true
  },
  {
    title: '作废时间',
    dataIndex: 'cancel_at',
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    resizable: true
  },
  {
    title: '作废备注',
    dataIndex: 'cancel_remark',
    resizable: true
  },
  {
    title: '仓库驳回备注',
    dataIndex: 'warehouse_reject_remark',
    resizable: true
  },
  {
    title: '财务驳回备注',
    dataIndex: 'finace_reject_remark',
    resizable: true
  }
]

// gbuilder
export const gbuilderColumns: FormSchema[] = [
  {
    field: 'status',
    label: '状态',
    component: 'RadioButtonGroup',
    required: true,
    defaultValue: 1,
    componentProps: {
      options: [
        {
          label: '通过',
          value: 1
        },
        {
          label: '驳回',
          value: 2
        }
      ]
    }
  },
  {
    field: 'remark',
    label: '驳回备注',
    component: 'InputTextArea',
    show(renderCallbackParams) {
      return renderCallbackParams.values.status === 2
    },
    required(renderCallbackParams) {
      return renderCallbackParams.values.status === 2
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.values.status === 2
    },
    componentProps: {
      placeholder: '请输入驳回原因'
    }
  }
]

export const packercolumns: BasicColumn[] = [
  {
    title: '包裹号',
    dataIndex: 'package_strid',
    width: 250,
    resizable: true
  },
  {
    title: '是否已出库',
    dataIndex: 'is_out',
    customRender: ({ text }) => {
      const map = {
        2: { label: '已出库', color: 'green' },
        1: { label: '出库中', color: 'green' },
        0: { label: '未出库', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },

  {
    title: '是否已入库',
    dataIndex: 'is_in',
    customRender: ({ text }) => {
      const map = {
        2: { label: '已入库', color: 'green' },
        1: { label: '入库中', color: 'green' },
        0: { label: '未入库', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  // {
  //   title: '是否作废',
  //   dataIndex: 'is_cancel',
  //   customRender: ({ text }) => {
  //     const map = {
  //       1: { label: '已作废', color: 'red' },
  //       0: { label: '未作废', color: 'green' }
  //     }
  //     const curTag = map[text]
  //     if (!curTag) return text
  //     return useRender.renderTag(curTag.label, curTag.color)
  //   }
  // },
  {
    title: '是否已备货',
    dataIndex: 'is_stock',
    customRender: ({ text }) => {
      const map = {
        1: { label: '是', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否旧包裹',
    dataIndex: 'is_old',
    customRender: ({ text }) => {
      const map = {
        1: { label: '是', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否报废',
    dataIndex: 'is_scrap',
    customRender: ({ text }) => {
      const map = {
        1: { label: '是 ', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否拼货',
    dataIndex: 'is_join',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        1: { label: '是', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  }
]
const status_schema = GET_STATUS_SCHEMA([
  {
    label: '待确认',
    value: 0
  },
  {
    label: '已确认',
    value: 1
  },
  {
    label: '仓库已审核',
    value: 2
  },
  {
    label: '仓库已驳回',
    value: 3
  },
  {
    label: '财务已驳回',
    value: 4
  },
  {
    label: '完成',
    value: 15
  }
])
export const schemas: FormSchema[] = [
  status_schema,
  DIVIDER_SCHEMA,
  {
    field: 'strid',
    label: '单号',
    helpMessage: '可搜索装报废单号、销售单号、采购单号',
    component: 'Input'
  },
  // {
  //   field: 'status',
  //   label: '状态',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       {
  //         label: '待确认',
  //         value: 0
  //       },
  //       {
  //         label: '已确认',
  //         value: 1
  //       },
  //       {
  //         label: '仓库已审核',
  //         value: 2
  //       },
  //       {
  //         label: '仓库已驳回',
  //         value: 3
  //       },
  //       {
  //         label: '财务已驳回',
  //         value: 4
  //       },
  //       {
  //         label: '完成',
  //         value: 15
  //       }
  //     ]
  //   }
  // },
  {
    field: 'is_cancel',
    label: '是否作废',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '否',
          value: 0
        },
        {
          label: '是',
          value: 1
        }
      ]
    }
  },
  {
    field: 'creator',
    label: '创建人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'auditor',
    label: '审核人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'auditor_warehouse',
    label: '仓库审核人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'auditor_finace',
    label: '财务审核人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'cancel_inCharge',
    label: '作废人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'created_at',
    label: '创建日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  }
]
