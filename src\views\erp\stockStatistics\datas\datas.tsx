import { BasicColumn, FormSchema } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { useI18n } from '/@/hooks/web/useI18n'

const { tm } = useI18n()
export const columns: BasicColumn[] = [
  {
    title: '销售订单号',
    dataIndex: 'source_uniqid',
    width: 200,
    resizable: true
  },
  {
    title: '销售单状态',
    dataIndex: 'status',
    width: 100,
    resizable: true,
    customRender({ text }) {
      const statusOpts = tm('tag.salesStatus') || {}
      const curStatus = statusOpts[text]
      if (curStatus) return useRender.renderTag(curStatus.label, curStatus.color)
      return '-'
    }
  },
  {
    title: '是否有明细库存',
    dataIndex: 'is_have_stocking',
    width: 150,
    resizable: true,
    customRender({ text }) {
      const mapIsExists = tm('stockStatistics.mapIsExists') || {}
      const IsExists = mapIsExists[text]
      if (IsExists) return useRender.renderTag(IsExists.label, IsExists.color)
      return '-'
    }
  },
  {
    title: '存在入库未清点库存',
    dataIndex: 'is_have_no_Inventory',
    width: 150,
    resizable: true,
    customRender({ text }) {
      const mapIsExists = tm('stockStatistics.mapIsExists') || {}
      const IsExists = mapIsExists[text]
      if (IsExists) return useRender.renderTag(IsExists.label, IsExists.color)
      return '-'
    }
  },
  {
    title: '包裹总数',
    dataIndex: 'pkg_num'
  },
  {
    title: '实盘数量',
    dataIndex: 'true_quantity'
  },
  {
    title: '入库日期',
    dataIndex: 'in_warehouse_at'
  },
  {
    title: '出库日期',
    dataIndex: 'out_warehouse_at'
  },
  {
    title: '包裹异常备注',
    dataIndex: 'sale_remark'
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'source_uniqid',
    label: '销售单号',
    component: 'Input'
  },
  {
    field: 'status',
    label: '销售订单状态',
    component: 'Select',
    componentProps: {
      options: tm('stockStatistics.salesStatusOptions'),
      mode: 'multiple'
    }
  },
  {
    field: 'is_have_stocking',
    label: '已清点明细库存',
    component: 'Select',
    componentProps: {
      options: tm('stockStatistics.hasStockOptions')
    }
  },
  {
    field: 'is_have_no_Inventory',
    label: '入库未清点明细库存',
    component: 'Select',
    componentProps: {
      options: tm('stockStatistics.hasStockOptions')
    }
  }
]

export const childrenColumns: BasicColumn[] = [
  {
    title: '批号',
    dataIndex: 'FBatchNo',
    resizable: true,
    width: 250
  },
  {
    title: '包裹数量',
    dataIndex: 'FQty'
    // resizable: true
  },
  {
    title: '物料代码',
    dataIndex: 'FItemNum'
    // resizable: true
  },
  {
    title: '物料名称',
    dataIndex: 'FItemName'
    // resizable: true
  },

  {
    title: '仓库代码',
    dataIndex: 'FStockNum'
    // resizable: true
  },
  {
    title: '仓库名称',
    dataIndex: 'FStockName'
    // resizable: true
  },

  {
    title: '仓位代码',
    dataIndex: 'FSPNum'
    // resizable: true
  },
  {
    title: '仓位名称',
    dataIndex: 'FSPName'
    // resizable: true
  }
]
