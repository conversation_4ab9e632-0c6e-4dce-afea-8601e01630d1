<template>
  <BasicTable @register="registerTable">
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'action'">
        <TableAction stopButtonPropagation :actions="createActions(record)" />
      </template>
    </template>
    <template #expandedRowRender="{ expanded, record }">
      <div class="extend-table-container">
        <BasicTable
          v-if="expanded"
          :api="(params) => getRealTimeInventoryList({ ...params, source_uniqid: record.source_uniqid })"
          rowKey="id"
          size="small"
          :showIndexColumn="true"
          :pagination="true"
          :scroll="{ y: 400, x: 0 }"
          :columns="childrenColumns"
        />
      </div>
    </template>
  </BasicTable>
</template>

<script setup lang="tsx">
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import { childrenColumns, columns, schemas } from './datas/datas'
import { getSaleList, setSaleRemark } from '/@/api/erp/stockStatistics'
import { getRealTimeInventoryList } from '/@/api/wms/realTimeInventory'
import { InputNumber, Textarea, Form } from 'ant-design-vue'
import { useMessage } from '/@/hooks/web/useMessage'
const FormItem = Form.Item

const { createMessage } = useMessage()
const [registerTable, { reload }] = useTable({
  title: '仓库包裹库存统计',
  api: getSaleList,
  showIndexColumn: false,
  columns: columns,
  showTableSetting: true,
  useSearchForm: true,
  rowKey: 'id',
  isTreeTable: true,
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  formConfig: {
    labelWidth: 150,
    baseColProps: { span: 8 },
    fieldMapToTime: [['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]],
    schemas: schemas
  }
})

function createActions(record) {
  return [
    {
      icon: 'clarity:note-edit-line',
      label: '填写异常备注',
      popConfirm: {
        title: (
          <div class="w-100">
            <Form layout="vertical">
              <FormItem label="异常备注" required>
                <Textarea autoSize={false} v-model:value={record.remark} placeholder="请输入异常备注" allow-clear />
              </FormItem>
              <FormItem label="实盘数量" required>
                <InputNumber autoSize={false} v-model:value={record.true_quantity} placeholder="请输入实盘数量" allow-clear />
              </FormItem>
            </Form>
          </div>
        ),
        placement: 'left',
        confirm: handleEditRemark.bind(null, record)
      }
    }
  ]
}

async function handleEditRemark(record) {
  try {
    const { msg } = await setSaleRemark({ id: record.id, remark: record.remark, true_quantity: record.true_quantity })
    if (msg === 'success') {
      reload()
      return createMessage.success('填写备注成功')
    }
    createMessage.error('填写备注失败')
  } catch (err) {
    createMessage.error('填写备注失败')
    throw new Error(err)
  }
}
</script>
<style scoped lang="less">
.extend-table-container {
  :deep(.ant-table) {
    margin: 0 !important;
  }
}
</style>
