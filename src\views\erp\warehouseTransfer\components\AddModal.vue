<template>
  <BasicModal @register="register" title="转换商品添加" :showOkBtn="addsubmitshow" @ok="addsubmit" width="1000px">
    <Row :gutter="[8, 8]">
      <Col :span="12">
        <Card title="销售订单选取"> <BasicForm @register="registerSaleForm" /> </Card
      ></Col>
      <Col :span="12">
        <Card title="库存商品转换">
          <ScrollContainer style="width: 100%; height: 500px">
            <BasicForm @register="registerGoodsForm" :label-width="80">
              <template #Imgs>
                <Upload
                  v-model:file-list="fileList"
                  action="/api/oss/putImg"
                  list-type="picture-card"
                  :custom-request="handleRequest"
                  :multiple="true"
                >
                  <div>
                    <plus-outlined />
                    <div style="margin-top: 8px">Upload</div>
                  </div>
                </Upload>
              </template>
              <template #origin_id="{ model }">
                <PagingApiSelect
                  placeholder="请选择库存商品"
                  :pagingMode="true"
                  :api="(params) => getItemStocking({ ...params })"
                  :search-mode="true"
                  :always-load="false"
                  return-params-field="id"
                  :params="{ work_id: init_good_work_id, pageSize: 9999, is_residue: 1 }"
                  v-model:value="model.origin_id"
                  :select-props="{ fieldNames: { value: 'id', label: 'name' }, placeholder: '请选择' }"
                  resultField="items"
                  @change="Stockinggain"
                />
              </template>
            </BasicForm>
          </ScrollContainer>
        </Card>
      </Col>
    </Row>
  </BasicModal>
</template>
<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm, PagingApiSelect } from '/@/components/Form'
import { Upload, UploadProps, UploadFile, message, Card, Row, Col } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { ref, watch } from 'vue'

import { uploadaddaddInventoryImg } from '/@/api/erp/inventory'
import { schemasSale, schemasGoods } from '../datas/Modal'
import { getItemStocking } from '/@/api/commonUtils/index'
import { ScrollContainer } from '/@/components/Container'
// import { getSalesOrderListReq } from '/@/api/erp/sales'

//id
const dataid = ref()
//转换name
const dataName = ref()
//销售订单work_id
const init_work_id = ref()
//商品work_id
const init_good_work_id = ref()
//确认按钮
const addsubmitshow = ref(true)
//GoodsFrom
const [registerGoodsForm, { setFieldsValue, getFieldsValue, resetFields, validateFields }] = useForm({
  showActionButtonGroup: false,
  baseColProps: { span: 12 },
  schemas: schemasGoods
})
//SaleFrom
const [
  registerSaleForm,
  { resetSchema, getFieldsValue: getFieldsSaleValue, validateFields: SalevalidateFields, resetFields: SaleresetFields }
] = useForm({
  showActionButtonGroup: false,
  baseColProps: { span: 12 }
})
//modal
const [register, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  console.log(data.params)
  init_work_id.value = data.params.work_id
  init_good_work_id.value = data.params.good_work_id
  await resetFields()
  await SaleresetFields()
  fileList.value = []
  resetSchema(await schemasSale(getSalestock))
})
const emit = defineEmits(['success', 'register'])
const fromdata = ref()
const Salefromdata = ref()
async function addsubmit() {
  await changeOkLoading(true)
  await validateFields()
  await SalevalidateFields()
  fromdata.value = await getFieldsValue()
  Salefromdata.value = await getFieldsSaleValue()
  console.log(Salefromdata.value)
  if (!fromdata.value.hasOwnProperty('imgs')) {
    fromdata.value.imgs = []
  }
  // if (!Object.prototype.hasOwnProperty.call(fromdata.value, 'imgs')) {
  //   Reflect.set(fromdata.value, 'imgs', [])
  // }
  const params = {
    origin_stocking_id: dataid.value,
    origin_name: dataName.value,
    request_id: Salefromdata.value.request_id,
    work_id: Salefromdata.value.work_id,
    origin_work_id: good_work_id.value,
    quantity_need: Salefromdata.value.quantity_need,
    qty_request_left: Salefromdata.value.qty_request_left,
    desc: fromdata.value.desc,
    name: fromdata.value.name,
    origin_id: fromdata.value.origin_id,
    origin_qty_stocking: fromdata.value.origin_qty_stocking,
    quantity: fromdata.value.quantity,
    remark: fromdata.value.remark,
    unit: fromdata.value.unit,
    unit_price: fromdata.value.unit_price,
    warehouse_id: fromdata.value.warehouse_id,
    imgs: fromdata.value.imgs,
    source_uniqid: sale_source_uniqid.value,
    origin_source_uniqid: good_source_uniqid.value
  }
  console.log(params)

  if (fromdata.value.qty_stocking == 0) {
    resetFields()
    message.error('现库存量不足,无法转换')
    closeModal()
  } else {
    emit('success', params)
    resetFields()
    closeModal()
  }
  changeOkLoading(false)
}

//获取销售订单work_id
const sale_source_uniqid = ref()

async function getSalestock(strid: number, shall) {
  if (strid !== init_work_id.value && init_work_id.value) {
    message.error('请选择相同的销售订单')
    addsubmitshow.value = false
    return
  } else {
    sale_source_uniqid.value = shall.source_uniqid
    addsubmitshow.value = true
  }
}
//图片上传
const fileList = ref<UploadProps['fileList']>([])
async function handleRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await uploadaddaddInventoryImg({ file })
  onSuccess!(result.path)
  fileList.value = fileList.value!.map((item) => ({
    url: (item.url as string) || item.response,
    uid: item.uid
  })) as UploadProps['fileList'] & { url: string }
}
watch(
  () => fileList.value,
  async (val: any) => {
    if (!val) return
    await setFieldsValue({ imgs: val.map((item) => item.url) })
  }
)

//填充数据
const good_work_id = ref()
const good_source_uniqid = ref()
function Stockinggain(_, shall) {
  console.log(shall)
  good_source_uniqid.value = shall.source_uniqid
  good_work_id.value = shall.work_id
  dataid.value = shall.id
  dataName.value = shall.name
  setFieldsValue(shall)
  setFieldsValue({ origin_qty_stocking: shall.qty_stocking, quantity: shall.qty_reserve })
  const imgsdata = shall.imgs
  fileList.value = (
    typeof imgsdata === 'string'
      ? [{ url: imgsdata, uid: +new Date().toString(), name: imgsdata }]
      : (imgsdata as string[])?.map((item: string, idx: number) => ({ url: item, uid: idx.toString(), name: item }))
  ) as UploadFile[]
}
</script>
