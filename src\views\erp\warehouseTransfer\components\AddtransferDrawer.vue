<template>
  <div>
    <BasicDrawer @register="register" width="90%" :can-fullscreen="false">
      <div class="mt-2" v-if="types == 'detail'">
        <Card title="转换单表头" :bordered="false">
          <BasicForm @register="registerForm" />
        </Card>
        <Card title="转换商品" class="mt-2" :bordered="false">
          <BasicTable @register="registertable" :data-source="tabeldata">
            <template #bodyCell="{ text, column, record }">
              <template v-if="column.key === 'request_id'">
                <div v-for="(item, index) in unref(commoditydata)" :key="index">
                  <div v-if="record.type === 1 && item.id == record.request_id">
                    {{ item.name }}
                  </div>
                  <div v-else-if="record.type === 2 && item.id == record.request_sub_id">
                    {{ item.name }}
                  </div>
                </div>
              </template>
              <template v-if="column.key === 'imgs'">
                <TableImg :size="60" :simpleShow="true" :imgList="text" />
              </template>
              <template v-if="column.key === 'warehouse_id'">
                {{ mapStore[text] }}
              </template>
            </template>
          </BasicTable>
        </Card>
      </div>
      <div v-else>
        <BasicTable @register="registerTables" />
      </div>
    </BasicDrawer>
  </div>
</template>
<script lang="ts" setup>
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { schemas1, columns, allSalesWorkList } from '../datas/Modal'
import { Card } from 'ant-design-vue'
import { ref, unref } from 'vue'
import { BasicTable, useTable, TableImg } from '/@/components/Table'
import { getWorkList } from '/@/api/commonUtils'
import { cloneDeep } from 'lodash-es'
import { useMapStoreWithOut } from '/@/store/modules/commonMap'
import { formatItemRequest } from '/@/views/erp/warehouseTransfer/datas/packagesTransform.datas'
import { getSalesOrderAndDetail } from '/@/api/erp/sales'
import { useRender } from '/@/components/Table/src/hooks/useRender'

//分类
const tabeldata = ref([])
const type = ref()
const mapStore = ref({})
const types = ref()
//modal
const [register, { changeLoading }] = useDrawerInner(async (data) => {
  try {
    await changeLoading(true)
    types.value = data.type
    const { getMapStore, getAllStoreList } = useMapStoreWithOut()
    await getAllStoreList()
    mapStore.value = getMapStore
    resetFields()
    tabeldata.value = []
    type.value = data.type
    if (data.type == 'detail') {
      await setFieldsValue(data.record)
      data.record?.processor && setFieldsValue({ processor: Number(data.record.processor) })
      // const a = data.record?.id ? await getitemInventoryst({ doc_id: data.record.id }) : {}
      const workIds = [...new Set(data.record.info.map((item) => item.work_id))]
      const arr = await Promise.allSettled(
        workIds.map((id: number) => getWorkList(data.type === 'detail' ? { id } : { id, type: 3, status: [1, 2, 3, 4, 5], pageSize: 9999 }))
      )
      for (const work of arr) {
        if (work.status === 'fulfilled') {
          allSalesWorkList.value = [...allSalesWorkList.value, ...work.value.items]
        }
      }
      tabeldata.value = cloneDeep(data.record.info)
      commoditydatalist([...new Set(tabeldata.value.map((item) => item.work_id))])
    }
    const packarr = data.record.doc_stocking_convert_package.map((item: Recordable) => ({ ...item.packing_package }))
    setTableData(packarr)
  } catch (e) {
    console.log(e)

    throw new Error(`${e}`)
  } finally {
    changeLoading(false)
  }
})

// 商品编辑
//From
const [registerForm, { resetFields, setFieldsValue }] = useForm({
  schemas: schemas1,
  showActionButtonGroup: false
})
//Table
const [registertable] = useTable({
  showTableSetting: true,
  tableSetting: {
    form: false
  },
  columns,
  rowKey: 'id'
})

//关联订单商品
const commoditydata = ref()
async function commoditydatalist(work_id: number[]) {
  if (!work_id || work_id.length === 0) return

  let requestItems = []

  const { items } = await getSalesOrderAndDetail({
    work_ids: work_id
  })
  // let requestlist = await getItemRequest({ pageSize: 100, work_id: work_id })
  for (const saleOrder of items) {
    const itemRequest = formatItemRequest(saleOrder.items, saleOrder, { needFilter: false, setNullId: false })
    requestItems = requestItems.concat(itemRequest)
  }
  commoditydata.value = requestItems
  console.log(commoditydata.value)
}

const columnss = [
  {
    title: '装箱单号',
    dataIndex: 'packing_strid',
    resizable: true
  },
  {
    title: '包裹箱号',
    dataIndex: 'strid',
    resizable: true
  },
  {
    title: '是否已出库',
    dataIndex: 'is_out',
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        2: { label: '已出库', color: 'green' },
        1: { label: '出库中', color: 'green' },
        0: { label: '未出库', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },

  {
    title: '是否已入库',
    dataIndex: 'is_in',
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        2: { label: '已入库', color: 'green' },
        1: { label: '入库中', color: 'green' },
        0: { label: '未入库', color: 'red' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否作废',
    dataIndex: 'is_cancel',
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        1: { label: '已作废', color: 'red' },
        0: { label: '未作废', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否已备货',
    dataIndex: 'is_stock',
    resizable: true,
    customRender: ({ text }) => {
      const map = {
        1: { label: '是', color: 'red' },
        0: { label: '否', color: 'green' }
      }
      const curTag = map[text]
      if (!curTag) return text
      return useRender.renderTag(curTag.label, curTag.color)
    }
  },
  {
    title: '是否拼货',
    dataIndex: 'is_join',
    resizable: true,
    customRender: ({ text }) => {
      const mapStatus = {
        1: { label: '是', color: 'error' },
        0: { label: '否', color: 'success' }
      }
      return mapStatus[text] ? useRender.renderTag(mapStatus[text].label, mapStatus[text].color) : text
    }
  },
  {
    title: '是否报废',
    dataIndex: 'is_scrap',
    resizable: true,
    customRender: ({ text }) => {
      const mapStatus = {
        1: { label: '是', color: 'error' },
        0: { label: '否', color: 'success' }
      }
      return mapStatus[text] ? useRender.renderTag(mapStatus[text].label, mapStatus[text].color) : text
    }
  },
  {
    title: '仓库',
    dataIndex: 'warehouse_name',
    resizable: true
  },
  {
    title: '仓位',
    dataIndex: 'warehouse_item_name',
    resizable: true
  }
]
const [registerTables, { setTableData }] = useTable({
  columns: columnss,
  showIndexColumn: false,
  rowKey: 'id',
  immediate: false
})
</script>
