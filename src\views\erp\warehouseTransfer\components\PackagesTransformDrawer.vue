<template>
  <BasicDrawer @register="register" width="90%" show-footer destroyOnClose @close="handleInit" @ok="handleOk">
    <BasicForm :ref="(el) => (wrapFormRef = el)" v-model:model="formModel" @register="registerForm" />
    <div v-for="(item, idx) in packageList" :key="item.work_id">
      <Form
        ref="itemsRef"
        :model="{ ...item, idx }"
        @update:model="(val) => updatePackage(item.work_id, val)"
        :name="`package-${idx}`"
        :label-col="{ style: { width: `${packageFormProps.labelWidth}px` } }"
      >
        <Row>
          <Col
            v-for="pkgSchemas in packageFormProps.schemas"
            :key="pkgSchemas.field"
            :span="pkgSchemas.colProps?.span || packageFormProps.baseColProps.span"
          >
            <template v-if="pkgSchemas.field === 'info'">
              <Divider orientation="left">
                <span class="text-base">{{ item.source_uniqid }}转换明细</span>
              </Divider>
            </template>
            <template v-else-if="['width', 'height', 'length'].includes(pkgSchemas.field)">
              <FormItem
                :label="pkgSchemas.label"
                :name="pkgSchemas.field"
                :rules="[{ required: pkgSchemas.required, message: `请输入${pkgSchemas.label}` }]"
              >
                <component
                  :is="pkgSchemas.component"
                  v-model:value="packageList[idx][pkgSchemas.field]"
                  v-bind="pkgSchemas.componentProps"
                  @change="handleSetVolume(idx)"
                  :disabled="pkgSchemas.dynamicDisabled"
                  placeholder="请输入"
                />
              </FormItem>
            </template>
            <template v-else-if="pkgSchemas.field === 'items'">
              <FormItem
                :label="pkgSchemas.label"
                :name="pkgSchemas.field"
                :rules="[{ required: pkgSchemas.required, message: `请输入${pkgSchemas.label}` }]"
              >
                <component v-if="packageList[idx]" :is="pkgSchemas.render?.(Object.assign(packageList[idx], { idx }))" />
              </FormItem>
            </template>
            <template v-else-if="['warehouse_id', 'warehouse_item_id'].includes(pkgSchemas.field)">
              <FormItem
                :label="pkgSchemas.label"
                :name="pkgSchemas.field"
                :rules="[{ required: pkgSchemas.required, message: `请输入${pkgSchemas.label}` }]"
              >
                <component
                  :is="pkgSchemas.component"
                  v-model:value="packageList[idx][pkgSchemas.field]"
                  :api="
                    pkgSchemas.field === 'warehouse_id'
                      ? pkgSchemas.api
                      : (params) => item.warehouse_id && transformWarehouseOpt({ ...params, warehouse_id: item.warehouse_id }, getWMI)
                  "
                  v-bind="pkgSchemas.componentProps"
                  :disabled="pkgSchemas.dynamicDisabled"
                  placeholder="请输入"
                />
              </FormItem>
            </template>
            <template v-else>
              <FormItem
                :label="pkgSchemas.label"
                :name="pkgSchemas.field"
                :rules="[{ required: pkgSchemas.required, message: `请输入${pkgSchemas.label}` }]"
              >
                <component
                  :is="pkgSchemas.component"
                  v-model:value="packageList[idx][pkgSchemas.field]"
                  v-bind="pkgSchemas.componentProps"
                  :disabled="pkgSchemas.dynamicDisabled"
                  placeholder="请输入"
                />
              </FormItem>
            </template>
          </Col>
        </Row>
      </Form>
    </div>
  </BasicDrawer>
</template>

<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import {
  formModel,
  mapIn,
  schemas,
  VxeTableRef,
  mapOut,
  selectWorkId,
  formatItemRequest,
  wrapFormRef,
  packageList,
  packageSchemas
} from '/@/views/erp/warehouseTransfer/datas/packagesTransform.datas'
import { useMessage } from '/@/hooks/web/useMessage'
import { updateTransform } from '/@/api/erp/inventory'
import { ref } from 'vue'
import { getPackageDetail } from '/@/api/erpFlow/packages'
import { getSalesOrderAndDetail } from '/@/api/erp/sales'
import { cloneDeep } from 'lodash-es'
import { Divider, Form, Row, Col } from 'ant-design-vue'
import { div, mul } from '/@/utils/math'
import { transformWarehouseOpt } from '/@/views/erp/mainInWarehouse/datas/AddDrawer'
import { getWMI } from '/@/api/baseData/warehouse'

const itemsRef = ref()

const FormItem = Form.Item

const { createMessage } = useMessage()

const emits = defineEmits(['success'])

const propsData = ref({})

const [register, { closeDrawer, changeOkLoading, changeLoading }] = useDrawerInner(async (data) => {
  console.log(data)
  handleInit()
  propsData.value = data
  const mapOperateFn = {
    add: () => {},
    edit: handleEdit
  }

  mapOperateFn[data.type]?.(data)
})

async function handleEdit({ record }) {
  try {
    changeOkLoading(true)
    changeLoading(true)
    const { packageList: originPackageList, info, desc } = cloneDeep(record)

    // 获取当前的选中订单所有商品的最大转出数量
    const { items: salesItems } = await getSalesOrderAndDetail({
      work_ids: originPackageList.map((item) => item.work_id)
    })
    let salesArr = []
    for (const saleOrder of salesItems) {
      const itemRequest = formatItemRequest(saleOrder.items, saleOrder)
      salesArr = salesArr.concat(itemRequest)
    }

    // 最大转出数量写入映射对象
    for (const item of salesArr) {
      mapIn.value[item.rowKey] = item.maxInQuantity
    }

    // 获取当前包裹最大转出数量
    const packageIds = [...new Set(info.map((item) => item.packing_package_id))]
    const { items } = await getPackageDetail({ ids: packageIds })
    const packageItems = items.map((item) => item.items).flat()
    // 设置包裹商品的最大可转出数量
    for (const item of packageItems) {
      const outKey = item.id
      mapOut.value[outKey] = +item.quantity
    }
    await updateSchema({
      field: 'work_ids',
      componentProps: {
        defaultOptions: salesItems
      }
    })
    await setFieldsValue({ work_ids: originPackageList.map((item) => item.work_id), desc })
    packageList.value = originPackageList
  } catch (err) {
    createMessage.error('详情初始化失败！')
  } finally {
    changeOkLoading(false)
    changeLoading(false)
  }
}

const [registerForm, { validate, resetFields, setFieldsValue, updateSchema }] = useForm({
  schemas,
  baseColProps: {
    span: 8
  },
  labelWidth: 100,
  showActionButtonGroup: false
})

const packageFormProps = {
  baseColProps: {
    span: 6
  },
  labelWidth: 100,
  showActionButtonGroup: false,
  schemas: packageSchemas
}

function updatePackage(workId, packageItem) {
  const item = packageList.value.find((item) => item.work_id === workId)
  Object.assign(item, packageItem)
}

function getParams(data) {
  // const words = data.work_ids

  const { packageList } = data

  return {
    desc: data.desc,
    packageList: packageList.map((pack) => ({
      ...pack,
      // product: items.filter((item) => item.work_id === workId),
      items: pack.items.map((item) => ({
        ...item,
        origin_stocking_id: item.itemRequest.item_stocking_id,
        request_id_origin: item.itemRequest.request_id,
        request_sub_id_origin: item.itemRequest.request_sub_id,
        type_origin: item.itemRequest.type,
        code: `${item.code}`,
        quantity_origin: +item.quantity_origin,
        quantity_need: +item.quantity_need,
        quantity: +item.quantity_origin,
        warehouse_id: +pack.warehouse_id,
        warehouse_item_id: pack.warehouse_item_id
        // material: item.itemRequest.material
      }))
    }))
  }
}

async function handleOk() {
  try {
    // 验证包裹信息
    for (const item of itemsRef.value) {
      const res = await item.validate()
      console.log(res)
    }
    let itemsValid = true
    // 验证明细信息
    for (const workId of Object.keys(VxeTableRef.value)) {
      if (!VxeTableRef.value[workId]) continue
      const errMap = await VxeTableRef.value[workId].validate(true)
      if (errMap) {
        itemsValid = false
        break
      }
    }
    if (!itemsValid) return createMessage.error('存在未填写的转换明细信息！')

    // 验证描述和销售单信息
    const data = await validate()

    // 开始提交
    changeOkLoading(true)
    changeLoading(true)
    // 处理数据
    const params = getParams({ ...data, packageList: packageList.value })
    if (propsData.value.type === 'edit') params.id = propsData.value.record.id
    console.log(params)
    const { msg } = await updateTransform(params)
    if (msg === 'success') {
      closeDrawer()
      createMessage.success('提交成功')
      emits('success')
    }
    setTimeout(() => {
      changeOkLoading(false)
      changeLoading(false)
    }, 3000)
  } catch (err) {
    console.log(err)
    changeOkLoading(false)
    changeLoading(false)
    createMessage.error('提交失败')
  }
}

function handleSetVolume(idx) {
  const { length, width, height } = packageList.value[idx]
  packageList.value[idx].volume = div(mul(mul(length ?? 0, width ?? 0), height ?? 0), 1000000, 6)
}

function handleInit() {
  mapIn.value = {}
  mapOut.value = {}
  VxeTableRef.value = []
  resetFields()
  packageList.value = []
  selectWorkId.value = null
}
</script>
