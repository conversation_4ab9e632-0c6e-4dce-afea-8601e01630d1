<template>
  <BasicModal @register="register" title="商品修改" :showOkBtn="addsubmitshow" @ok="addsubmit" width="1000px">
    <Row :gutter="[8, 8]">
      <Col :span="12">
        <Card title="销售订单选取"> <BasicForm @register="registerSaleForm" /> </Card
      ></Col>
      <Col :span="12">
        <Card title="库存商品转换">
          <BasicForm @register="registerGoodsForm" :label-width="80">
            <template #Imgs>
              <Upload
                v-model:file-list="fileList"
                action="/api/oss/putImg"
                list-type="picture-card"
                :custom-request="handleRequest"
                :multiple="true"
              >
                <div>
                  <plus-outlined />
                  <div style="margin-top: 8px">Upload</div>
                </div>
              </Upload>
            </template>
            <template #origin_id="{ model }">
              <PagingApiSelect
                placeholder="请选择库存商品"
                :pagingMode="true"
                :api="(params) => getItemStocking({ ...params })"
                :search-mode="true"
                :always-load="false"
                return-params-field="id"
                :params="{ work_id: init_good_work_id, pageSize: 9999, is_residue: 1 }"
                v-model:value="model.origin_id"
                :select-props="{ fieldNames: { value: 'id', label: 'name' }, placeholder: '请选择' }"
                resultField="items"
                @change="Stockinggain"
              />
            </template>
          </BasicForm>
        </Card>
      </Col>
    </Row>
  </BasicModal>
</template>
<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm, PagingApiSelect } from '/@/components/Form'
import { Upload, UploadProps, UploadFile, message, Card, Col, Row } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { ref, watch } from 'vue'

import { uploadaddaddInventoryImg } from '/@/api/erp/inventory'
import { schemasSale, schemasGoods, qty_request_left, salesWorkList } from '../datas/Modal'
// import { getstockList } from '/@/api/erp/inventory'
import { getItemStocking, getWorkList } from '/@/api/commonUtils'
//库存转换的列表数组
const stockList = ref<any>([])
//id
const dataid = ref()
//转换name
const dataName = ref()
//转换单商品
const transferdata = ref<any>()
//销售订单work_id
const init_work_id = ref()
//商品work_id
const init_good_work_id = ref()
//确认按钮
const addsubmitshow = ref(true)
//From
const [registerGoodsForm, { setFieldsValue, getFieldsValue, resetFields, validateFields }] = useForm({
  showActionButtonGroup: false,
  baseColProps: { span: 12 },
  schemas: schemasGoods
})

const [
  registerSaleForm,
  { resetSchema, setFieldsValue: setFieldsSaleValue, getFieldsValue: getFieldsSaleValue, validateFields: SalevalidateFields }
] = useForm({
  showActionButtonGroup: false,
  baseColProps: { span: 12 }
})

//modal
const [register, { closeModal }] = useModalInner(async ({ record, params }) => {
  try {
    init_work_id.value = params.work_id
    init_good_work_id.value = params.good_work_id
    transferdata.value = record
    setFieldsValue({})
    resetSchema(await schemasSale(getSalestock))
    await setFieldsValue(record)
    await setFieldsValue({
      origin_id: record.origin_stocking_id,
      qty_stocking: record.qty_stocking
    })
    await setFieldsSaleValue({ ...record, quantity_need: record.quantity_need, qty_request_left: qty_request_left })
    if (record.origin_stocking_id) dataid.value = record.origin_stocking_id
    dataName.value = record.origin_name
    const imgsdata = typeof record.imgs === 'string' ? JSON.parse(record.imgs) : record.imgs
    fileList.value = (
      typeof imgsdata === 'string'
        ? [{ url: imgsdata, uid: +new Date().toString(), name: imgsdata }]
        : (imgsdata as string[])?.map((item: string, idx: number) => ({ url: item, uid: idx.toString(), name: item }))
    ) as UploadFile[]
    const { items } = await getWorkList({ id: record.work_id, type: 3, status: [1, 2, 3, 4, 5], pageSize: 9999 })
    salesWorkList.value = items
  } catch (e) {
    throw new Error(`${e}`)
  }
})
const emit = defineEmits(['success', 'register'])
const fromdata = ref()
const Salefromdata = ref()

async function addsubmit() {
  await validateFields()
  await SalevalidateFields()
  fromdata.value = await getFieldsValue()
  Salefromdata.value = await getFieldsSaleValue()
  // Reflect.set(fromdata.value, 'origin_stocking_id', dataid.value)
  // Reflect.set(fromdata.value, 'origin_name', dataName.value)
  // Reflect.set(fromdata.value, 'request_id', Salefromdata.value.request_id)
  // Reflect.set(fromdata.value, 'work_id', Salefromdata.value.work_id)
  if (!Object.prototype.hasOwnProperty.call(fromdata.value, 'imgs')) {
    Reflect.set(fromdata.value, 'imgs', [])
  }
  const params = {
    origin_stocking_id: dataid.value,
    origin_name: dataName.value,
    request_id: Salefromdata.value.request_id,
    work_id: Salefromdata.value.work_id,
    quantity_need: Salefromdata.value.quantity_need,
    qty_request_left: Salefromdata.value.qty_request_left,
    desc: fromdata.value.desc,
    name: fromdata.value.name,
    origin_id: fromdata.value.origin_id,
    origin_qty_stocking: fromdata.value.origin_qty_stocking,
    quantity: fromdata.value.quantity,
    remark: fromdata.value.remark,
    unit: fromdata.value.unit,
    unit_price: fromdata.value.unit_price,
    warehouse_id: fromdata.value.warehouse_id,
    imgs: fromdata.value.imgs
  }

  if (fromdata.value.qty_stocking == 0) {
    resetFields()
    message.error('现库存量不足,无法转换')
    closeModal()
  } else {
    emit('success', params)
    resetFields()
    closeModal()
  }
}

//图片上传
const fileList = ref<UploadProps['fileList']>([])
async function handleRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await uploadaddaddInventoryImg({ file })
  onSuccess!(result.path)
  fileList.value = fileList.value!.map((item) => ({
    url: (item.url as string) || item.response,
    uid: item.uid
  })) as UploadProps['fileList'] & { url: string }
}
watch(
  () => fileList.value,
  async (val: any) => {
    if (!val) return
    await setFieldsValue({ imgs: val.map((item) => item.url) })
  }
)

//获取销售订单work_id
async function getSalestock(strid: number) {
  if (strid !== init_work_id.value && init_work_id.value) {
    message.error('请选择相同的销售订单')
    addsubmitshow.value = false
    return
  } else {
    const { items } = await getItemStocking({ work_id: init_good_work_id.value, pageSize: 9999, is_residue: 1, is_reserve: 1 })
    items.forEach(async (item) => {
      if (transferdata.value.origin_stocking_id == item.id) {
        await setFieldsValue({
          qty_reserve: item.qty_reserve
        })
      }
    })
    addsubmitshow.value = true
    return (stockList.value = items.filter((item) => item.work_id !== strid))
  }
}
//填充数据
function Stockinggain(_, shall) {
  dataid.value = shall.id
  dataName.value = shall.name
  setFieldsValue(shall)
  const imgsdata = shall.imgs
  fileList.value = (
    typeof imgsdata === 'string'
      ? [{ url: imgsdata, uid: +new Date().toString(), name: imgsdata }]
      : (imgsdata as string[]).map((item: string, idx: number) => ({ url: item, uid: idx.toString(), name: item }))
  ) as UploadFile[]
}
</script>
