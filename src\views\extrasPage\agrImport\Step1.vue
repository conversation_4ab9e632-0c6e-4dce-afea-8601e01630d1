<template>
  <div class="step1">
    <BasicForm @register="registerForm">
      <template #formFooter>
        <div class="flex justify-center w-full">
          <a-button type="primary" @click="onPreview" :disabled="disabled" :loading="disabled">检查并下一步</a-button>
        </div>
      </template>
    </BasicForm>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { BasicForm, useForm } from '/@/components/Form'

import { schemasFn, segment, inAndOutWarehouse } from './datas/step1'
import { TRouteNameType, mapImpApi } from './datas/datas'

interface IProps {
  newRouteNameType: TRouteNameType
}
const props = withDefaults(defineProps<IProps>(), {
  newRouteNameType: inAndOutWarehouse[0]
})

const emit = defineEmits(['next'])

const disabled = ref(false)
const [registerForm, { validate }] = useForm({
  labelWidth: 150,
  colon: true,
  schemas: schemasFn(props.newRouteNameType),
  actionColOptions: {
    span: 14
  },
  baseColProps: { span: 12 },
  showActionButtonGroup: false
})

async function onPreview() {
  try {
    disabled.value = true
    const formData = await validate()
    const { newRouteNameType } = props
    let previewDataSource = segment(formData, newRouteNameType)
    if (previewDataSource.length === 0) return
    const params = {
      invoice_number: ['outwarehouse'].includes(newRouteNameType) ? formData.invoice_number : undefined,
      supplier_id: inAndOutWarehouse.slice(0, 1).includes(newRouteNameType) ? formData.supplier_id : undefined,
      warehouse_id: inAndOutWarehouse.includes(newRouteNameType) ? formData.warehouse_id : undefined,
      client_id: ['outwarehouse', 'packageCheck'].includes(newRouteNameType) ? formData.client_id : undefined,
      data: previewDataSource
    }

    const { items: ResultItems } = await mapImpApi[newRouteNameType].step1(params)

    emit('next', ResultItems)
    disabled.value = false
  } catch (error) {
    disabled.value = false
    console.error(error)
  }
}
</script>

<style lang="less" scoped>
.step1 {
  width: 900px;
  margin: 0 auto;
}
</style>
