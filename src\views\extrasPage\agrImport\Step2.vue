<template>
  <div class="w-900px m-auto">
    <Alert message="注意: 超额数大于0 的数据是不能继续提交给服务器的" type="warning" class="mb-4" />

    <BasicTable @register="registerTable">
      <template #toolbar>
        <div class="flex justify-center w-full">
          <a-button class="mr-4" @click="onPrev" :loading="disableding" :disabled="disableding">上一步</a-button>

          <a-button class="mr-4" type="primary" @click="onNextFunc" :loading="disableding" :disabled="disableding">提交</a-button>
          <a-button @click="exportData"><download-outlined />导出超额数大于0的数据</a-button>
        </div>
      </template>
      <template #expandedRowRender="{ record }" v-if="newRouteNameType && inAndOutWarehouse.includes(newRouteNameType)">
        <template v-if="record.result && record.result.length > 0">
          <BasicTable
            v-bind="{
              dataSource: record.result,
              columns: resultColumns(newRouteNameType),
              showIndexColumn: false,
              pagination: false,
              canResize: false
            }"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { Alert } from 'ant-design-vue'
import { DownloadOutlined } from '@ant-design/icons-vue'
import { BasicTable, useTable } from '/@/components/Table'

import { useMessage } from '/@/hooks/web/useMessage'
import { columnsFn, resultColumns, mapArrHeader } from './datas/step2'
import { aoaToSheetXlsx } from '/@/components/Excel'
import { mapImpApi, TRouteNameType } from './datas/datas'
import { inAndOutWarehouse, purchaseOrInWarehouseRetreat } from './datas/step1'
interface IProps {
  step1Values: Recordable[]
  newRouteNameType: TRouteNameType
}

const { notification } = useMessage()
const emit = defineEmits(['next', 'prev'])
const disableding = ref(false)

const props = withDefaults(defineProps<IProps>(), {
  newRouteNameType: inAndOutWarehouse[0]
})

//v-show再次为true,不会再执行onMounted
onMounted(() => {
  props.step1Values && setTableData(props.step1Values)
})

async function onNextFunc() {
  try {
    disableding.value = true
    let tableData = await getDataSource()
    if (tableData.some((item) => item.qty_left > 0)) return notification.warning({ message: '请注意超额数大于0的数据是不能提交的' })
    tableData = tableData.filter((item) => item.qty_left === 0)
    if (tableData.length === 0) return notification.warning({ message: '没有数据可以提交,请注意超额数大于0的数据是不能提交的' })
    const params = { data: tableData }
    const result = await mapImpApi[props.newRouteNameType].step2(params)
    emit('next', result)
  } catch (error) {
    console.error(error)
  } finally {
    disableding.value = false
  }
}
async function onPrev() {
  try {
    disableding.value = true
    await emit('prev')
    disableding.value = false
  } catch (err) {
    disableding.value = false
    console.error(err)
  }
}
function exportData() {
  try {
    disableding.value = true
    const tableData = getDataSource()
    const { newRouteNameType } = props

    //超额数大于0的数据是不能提交的,只能导出文件,让别人知道哪些超额了
    const data1 = tableData.filter((item) => item.qty_left > 0)

    const data2 = data1.map((item) => {
      const { qty_left } = item
      if (inAndOutWarehouse.includes(newRouteNameType)) {
        return { article: item.article, qty_left: qty_left }
      } else if (purchaseOrInWarehouseRetreat.includes(newRouteNameType)) {
        return { id: item.id, qty_left: qty_left }
      } else {
        return { un: item.un, qty_left: qty_left }
      }
    })

    const data3 = data2.map((item) => Object.keys(item).map((key) => item[key]))

    aoaToSheetXlsx({
      data: data3,
      header: mapArrHeader[newRouteNameType],
      filename: '导出超额数大于0的数据.xlsx'
    })
    disableding.value = false
  } catch (err) {
    disableding.value = false
    console.error(err)
  }
}

const [registerTable, { setTableData, getDataSource }] = useTable({
  dataSource: [],
  columns: columnsFn(props.newRouteNameType),
  showIndexColumn: false,
  maxHeight: 600,
  rowKey: 'key'
})

defineExpose({ setTableData })
</script>
