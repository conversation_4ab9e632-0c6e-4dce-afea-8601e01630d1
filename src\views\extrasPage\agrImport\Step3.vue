<template>
  <div class="w-150 m-auto">
    <Result status="success" title="操作成功">
      <template #extra>
        <a-button type="primary" @click="onRestart" :disabled="disabled" :loading="disabled"> 回到第一步继续导入 </a-button>
      </template>
    </Result></div
  >
</template>

<script lang="ts" setup>
import { Result } from 'ant-design-vue'
import { ref } from 'vue'
const emit = defineEmits(['redo'])
const disabled = ref(false)
const onRestart = () => {
  try {
    disabled.value = true
    emit('redo')
    disabled.value = false
  } catch (e) {
    console.error(e)
    disabled.value = false
  }
}
</script>
