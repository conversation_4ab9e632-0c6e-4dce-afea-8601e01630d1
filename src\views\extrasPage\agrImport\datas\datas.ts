import { ref } from 'vue'
import {
  importEwCheck,
  importOwCheck,
  importPurchaseReturnCheck,
  submitEw,
  submitOw,
  submitPurchaseReturn,
  importRequestReturnCheck,
  submitRequestReturn,
  importStockingReturnCheck,
  submitStockingReturn,
  importPackageCheck,
  submitPackage
} from '/@/api/extrasPage/agrImport'
export type TRouteNameType = 'inwarehouse' | 'outwarehouse' | 'purchaseRetreat' | 'saleOrderRetreat' | 'inWarehouseRetreat' | 'packageCheck'
export const mapImpApi = {
  inwarehouse: {
    step1: importEwCheck,
    step2: submitEw
  },
  outwarehouse: {
    step1: importOwCheck,
    step2: submitOw
  },
  purchaseRetreat: {
    step1: importPurchaseReturnCheck,
    step2: submitPurchaseReturn
  },
  saleOrderRetreat: {
    step1: importRequestReturnCheck,
    step2: submitRequestReturn
  },
  inWarehouseRetreat: {
    step1: importStockingReturnCheck,
    step2: submitStockingReturn
  },
  packageCheck: {
    step1: importPackageCheck,
    step2: submitPackage
  }
}

export const newRouteNameType = ref<TRouteNameType>('inwarehouse')
