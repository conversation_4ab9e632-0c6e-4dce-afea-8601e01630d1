import { TRouteNameType } from './datas'
import { inAndOutWarehouse, purchaseOrInWarehouseRetreat } from './step1'
import { BasicColumn } from '/@/components/Table'
// import { useRender } from '/@/components/Table/src/hooks/useRender'
import { isNullOrUnDef } from '/@/utils/is'

const QTYLEFTLABEL = '超额数'
export const columnsFn: (newRouteNameType: TRouteNameType) => BasicColumn[] = (newRouteNameType) => [
  {
    dataIndex: 'article',
    title: 'article',
    customRender: ({ value }) => {
      if (isNullOrUnDef(value)) return ''
    },
    ifShow: inAndOutWarehouse.includes(newRouteNameType)
  },
  {
    dataIndex: 'id',
    title: 'id',
    customRender: ({ value }) => {
      if (isNullOrUnDef(value)) return ''
    },
    ifShow: purchaseOrInWarehouseRetreat.includes(newRouteNameType)
  },
  {
    dataIndex: 'quantity',
    title: '数量'
  },
  {
    dataIndex: 'qty_left',
    title: QTYLEFTLABEL
  }
]

const packageCheckColumns: (newRouteNameType) => BasicColumn[] = (newRouteNameType) => [
  {
    dataIndex: 'remark',
    title: '备注',
    ifShow: ['packageCheck'].includes(newRouteNameType)
  },
  {
    dataIndex: 'request_id',
    title: 'request_id',
    ifShow: ['packageCheck'].includes(newRouteNameType)
  },
  {
    dataIndex: 'warehouse_id',
    title: '仓库id',
    ifShow: ['packageCheck'].includes(newRouteNameType)
  },
  {
    dataIndex: 'name',
    title: '商品名称',
    ifShow: ['packageCheck'].includes(newRouteNameType)
  },
  {
    dataIndex: 'qty_pk',
    title: '数量',
    ifShow: ['packageCheck'].includes(newRouteNameType)
  }
]

export const resultColumns: (newRouteNameType) => BasicColumn[] = (newRouteNameType) => [
  {
    dataIndex: 'puid',
    title: 'puid' //article
  },
  {
    dataIndex: 'uniqid',
    title: 'un',
    ifShow: !['packageCheck'].includes(newRouteNameType)
  },
  {
    dataIndex: 'remark',
    title: 'replacement',
    ifShow: !['packageCheck'].includes(newRouteNameType)
  },
  {
    dataIndex: 'strid',
    title: '采购单号',
    ifShow: !['packageCheck'].includes(newRouteNameType)
  },
  {
    dataIndex: 'unit_price',
    title: '单价',
    ifShow: !['packageCheck'].includes(newRouteNameType)
  },
  ...packageCheckColumns(newRouteNameType)
]

export const arrHeader = ['article', '替换号', QTYLEFTLABEL]
export const arrHeader2 = ['id', QTYLEFTLABEL]

export const mapArrHeader = {
  inwarehouse: arrHeader,
  outwarehouse: arrHeader,
  saleOrderRetreat: ['un', QTYLEFTLABEL],
  purchaseRetreat: arrHeader2,
  inWarehouseRetreat: arrHeader2
}
