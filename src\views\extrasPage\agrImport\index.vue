<template>
  <PageWrapper content-full-height>
    <div class="step-form-form pt-2">
      <Steps :current="current">
        <Step title="信息填写" />
        <Step title="提交" />
        <Step title="结果" />
      </Steps>
    </div>
    <div class="mt-10 flex justify-center">
      <Step1 @next="handleStep1Next" v-show="current === 0" :newRouteNameType="newRouteNameType" />
      <Step2
        @prev="handleStepPrev"
        @next="handleStep2Next"
        v-show="current === 1"
        v-if="state.initStep2"
        :step1Values="step1Values"
        :newRouteNameType="newRouteNameType"
        ref="Step2Ref"
      />
      <Step3 v-show="current === 2" @redo="handleRedo" v-if="state.initStep3" ref="Step3Ref" :step2Result="step2Result" />
    </div>
  </PageWrapper>
</template>
<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { Steps, Step } from 'ant-design-vue'

import { PageWrapper } from '/@/components/Page'

import Step1 from './Step1.vue'
import Step2 from './Step2.vue'
import Step3 from './Step3.vue'
import { newRouteNameType } from './datas/datas'

const { name: routeName } = useRoute()

watch(
  () => routeName,
  (newVal: string) => {
    if (newVal!.includes('importInwarehouse')) {
      newRouteNameType.value = 'inwarehouse'
    } else if (newVal!.includes('importOutwarehouse')) {
      newRouteNameType.value = 'outwarehouse'
    } else if (newVal!.includes('importSaleOrderRetreat')) {
      newRouteNameType.value = 'saleOrderRetreat'
    } else if (newVal!.includes('importPurchaseRetreat')) {
      newRouteNameType.value = 'purchaseRetreat'
    } else if (newVal!.includes('importInWarehouseRetreat')) {
      newRouteNameType.value = 'inWarehouseRetreat'
    } else if (newVal!.includes('importPackageCheck')) {
      newRouteNameType.value = 'packageCheck'
    }
  },
  { immediate: true }
)

const Step2Ref = ref()
const Step3Ref = ref()
const current = ref(0)
const state = reactive({
  initStep2: false,
  initStep3: false
})

const step1Values = ref()
const step2Result = ref()

async function handleStep1Next(ResultItems: any) {
  try {
    step1Values.value = ResultItems
    current.value++
    //如果已经挂载一次了
    if (state.initStep2) {
      Step2Ref.value.setTableData(ResultItems)
    }
    state.initStep2 = true
  } catch (e) {
    console.error(e)
  }
}

function handleStepPrev() {
  current.value--
}

function handleStep2Next(step2Values: any) {
  step2Result.value = step2Values
  current.value++
  if (state.initStep3) {
    Step3Ref.value.reload()
  }
  state.initStep3 = true
  // console.log(step2Values)
}

function handleRedo() {
  current.value = 0
  state.initStep2 = false
  state.initStep3 = false
}
</script>

<style scoped lang="less">
.step-form-form {
  width: 750px;
  margin: 0 auto;
}
</style>
