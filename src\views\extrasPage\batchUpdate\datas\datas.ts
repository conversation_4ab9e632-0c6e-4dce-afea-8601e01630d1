import { isNullOrUnDef } from '/@/utils/is'
// import { statusDataSource } from '/@/views/staff/batchUpdate/datas/datas'
import { FormSchema } from '/@/components/Form'
import { lineBreakOptions, splitterOptions } from '../../importPurchase/datas/step1'
import { usePermission } from '/@/hooks/web/usePermission'

export const paramsNameMap = {
  ['source_uniqid']: {
    typeValidFn: 'isString',
    transType: 'string',
    label: '销售订单号',
    permission: [429]
  },
  ['program_incharge']: {
    typeValidFn: 'isNumber',
    transType: 'number',
    label: '方案经理',
    permission: [430]
  },
  ['inCharge']: {
    typeValidFn: 'isNumber',
    transType: 'number',
    label: '项目经理',
    permission: [431]
  },
  ['delivery_incharge']: {
    typeValidFn: 'isNumber',
    transType: 'number',
    label: '交付经理',
    permission: [432]
  },
  ['operation']: {
    typeValidFn: 'isNumber',
    transType: 'number',
    label: '运营中心',
    permission: [433]
  },
  ['take_dept_id']: {
    typeValidFn: 'isNumber',
    transType: 'number',
    label: '带单部门',
    permission: [434]
  },
  ['phoenix_plan']: {
    typeValidFn: 'isString',
    transType: 'string',
    label: '凤凰计划',
    permission: [435]
  },
  ['design']: {
    typeValidFn: 'isString',
    transType: 'string',
    label: '设计师',
    permission: [436]
  },
  ['design2d']: {
    typeValidFn: 'isString',
    transType: 'string',
    label: '2D设计师',
    permission: [437]
  },
  ['design3d']: {
    typeValidFn: 'isString',
    transType: 'string',
    label: '3D设计师',
    permission: [438]
  },
  ['socialmedia']: {
    typeValidFn: 'isNumber',
    transType: 'number',
    label: '社媒运营',
    permission: [439]
  },
  ['guide']: {
    typeValidFn: 'isNumber',
    transType: 'number',
    label: '导航',
    permission: [440]
  },
  ['translate']: {
    typeValidFn: 'isString',
    transType: 'string',
    label: '翻译',
    permission: [441]
  },
  ['sale_excutive']: {
    typeValidFn: 'isString',
    transType: 'string',
    label: '销售跟单',
    permission: [442]
  },
  ['ach_app_at']: {
    typeValidFn: 'isString',
    transType: 'string',
    label: '业绩核定日期',
    permission: [443]
  },
  ['report_app_at']: {
    typeValidFn: 'isString',
    transType: 'string',
    label: '财务核定日期',
    permission: [657]
  }
}

const { hasPermission } = usePermission()

export const basicOptions = Object.entries(paramsNameMap)
  .filter(([_, label]) => hasPermission(label.permission))
  .map(([value, label]) => ({
    value,
    label: label.label,
    disabled: !hasPermission(label.permission) //这个其实意义不大,我只是怕到时候过滤不对,起码还可以禁用一下
  }))

// export const basicArr = basicOptions.map((item) => item.value)

export const batchUpdateFormSchema: FormSchema[] = [
  {
    field: 'splitter',
    label: '值与值之间的分隔符',
    defaultValue: '\t',
    // defaultValue: ' ',
    component: 'Select',
    required: true,
    componentProps: { options: splitterOptions },
    rules: [
      {
        required: true,
        message: '请选择值与值之间的分隔符'
      },
      {
        validator(_, value) {
          return new Promise((resolve, reject) => {
            if (isNullOrUnDef(value)) return reject('请选择分隔符')

            resolve()
          })
        },
        trigger: 'blur'
      }
    ]
  },
  {
    field: 'lineBreak',
    label: '选择换行符',
    component: 'Select',
    defaultValue: '\n',
    required: true,
    componentProps: {
      placeholder: '请选择',
      options: lineBreakOptions,
      style: { width: '100%' }
    },
    colProps: { span: 10 },
    rules: [
      {
        required: true,
        message: '请选择换行符'
      },
      {
        validator(_, value) {
          return new Promise((resolve, reject) => {
            if (isNullOrUnDef(value)) return reject('请选择换行符')

            resolve()
          })
        },
        trigger: 'blur'
      }
    ]
  },
  {
    field: 'preview',
    label: '',
    component: 'Input',
    slot: 'previewSlot',
    colProps: { span: 4 }
  },
  {
    field: 'paramNames',
    component: 'Select',
    label: '参数名',
    // slot: 'paramNames',
    required: true,
    defaultValue: [],
    componentProps: {
      options: basicOptions,
      mode: 'multiple'
    },
    colProps: {
      span: 24
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'parameter',
    label: '参数值',
    component: 'InputTextArea',
    colProps: { span: 24 },
    // slot: 'textArea',
    required: true,
    // rules: [{ required: true, validator: validatePass2, trigger: 'blur' }],
    helpMessage: '输入内容后请选择对应的分隔符！',
    componentProps: {
      rows: 12,
      style: 'white-space: nowrap; overflow: auto'
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  }
]

export const arrHeaderDept = ['部门', 'id']
export const staffHeaderDept = ['姓名', 'id']
