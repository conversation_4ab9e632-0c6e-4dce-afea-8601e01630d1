import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash-es'
import { isNullOrUnDef, isString } from '/@/utils/is'
import { paramsNameMap } from './datas'
import { ref } from 'vue'
// import dayjs from 'dayjs'
const a = ref([])

export const segment = (formData) => {
  try {
    const { splitter, lineBreak, params_value, params_name } = formData
    const formatter1 = params_value.split(splitter)
    const formatter2 = String(formatter1).split(lineBreak)

    console.log(splitter, lineBreak, params_value, params_name)

    let obj = {}
    let arr = [] as any
    let attributeName = [] as any
    if (!params_name || params_name.length === 0) {
      message.error('请选择参数名选项')
      return []
    }
    if (!params_name.includes('source_uniqid')) return message.error('销售订单号是必填的!!!')
    attributeName = params_name
    a.value = params_name
    for (const i in formatter2) {
      // const rowData = formatter2[i].split(',')
      const rowData = splitIgnoringBrackets(formatter2[i])

      if (formatter2[i].trim() == '') continue
      console.log(rowData)

      if (rowData.length !== attributeName.length) return message.error('请检查是否有多余空格或属性名数量不对')

      for (const valueIndex in rowData) {
        if (rowData[valueIndex] == '/') {
          if (attributeName[valueIndex] === 'source_uniqid') {
            message.error('销售单号不能为/或-')
            throw new Error('销售单号不能为/或-')
          }
          obj[attributeName[valueIndex]] = null
        } else if (rowData[valueIndex] == '-') {
          if (attributeName[valueIndex] === 'source_uniqid') {
            message.error('销售单号不能为/或-')
            throw new Error('销售单号不能为/或-')
          }
          obj[attributeName[valueIndex]] = undefined
        } else {
          obj = cloneDeep(obj)
          obj[attributeName[valueIndex]] = rowData[valueIndex]
        }
      }
      arr.push(obj)
    }

    //转类型
    arr = arr.map((item) => {
      // 定义一个通用的处理函数
      const translateValueType = (field, fieldName, transType) => {
        if (!isNullOrUnDef(item[field])) {
          if (transType === 'number') {
            item[field] = Number(item[field])
            if (isNaN(item[field])) {
              message.error(`${paramsNameMap[field].label} 需要是数字`)
              throw new Error(`${fieldName} 需要是数字`)
            }
          } else if (transType === 'string') {
            //一开始不都是string吗？
            item[field] = String(item[field])
            if (!isString(item[field])) {
              //这里的if永远不会为true
              message.error(`${paramsNameMap[field].label} 需要是字符串`)
              throw new Error(`${fieldName} 需要是字符串`)
            }
          }
        }
      }

      for (const key in item) {
        translateValueType(key, key, paramsNameMap[key].transType)
      }
      item = validateAndParse(item, ['design', 'design2d', 'design3d'])

      return item
    })

    return arr
  } catch (err) {
    console.error(err)
    return []
  }
}

// 转成[['部门', 'id'], ['敬城国际商城', 1], ['产品中心', 9]]的形式
export function transformArray(arr, arHeader) {
  // 定义一个空数组，用于存放转换后的结果
  const result = [arHeader]

  // 遍历输入的数组 arr
  arr.forEach((item) => {
    // 取出部门名称和部门 id
    const department = item.name
    const id = item.id

    // 将部门名称和部门 id 组成一个数组，并添加到结果数组中
    result.push([department, id])
  })

  // 返回转换后的结果数组，去除第一个重复项
  return result.slice(1)
}

function splitIgnoringBrackets(str) {
  const result = []
  let bracketLevel = 0
  let currentSegment = ''

  for (let i = 0; i < str.length; i++) {
    if (str[i] === '[') {
      bracketLevel++
      currentSegment += str[i]
    } else if (str[i] === ']') {
      bracketLevel--
      currentSegment += str[i]
      if (bracketLevel < 0) {
        message.error('单独右方括号,请检查数据完整度')
        throw new Error('Unmatched closing bracket at position ' + i)
      }
      if (bracketLevel === 0 && currentSegment.trim() === '[]') {
        message.error('方括号内为空值,请检查数据完整度')
        throw new Error('Empty bracket content at position ' + i)
      }
    } else if (str[i] === ',' && bracketLevel === 0) {
      // 只有当不在方括号内时才切割
      result.push(currentSegment.trim())
      currentSegment = ''
    } else {
      currentSegment += str[i]
    }
  }

  if (bracketLevel !== 0) {
    message.error('单独左方括号,请检查数据完整度')
    throw new Error('Unmatched opening bracket')
  }

  // 添加最后一段（如果有）
  if (currentSegment.trim() !== '') {
    result.push(currentSegment.trim())
  }

  return result
}

function validateAndParse(obj, specialKeys) {
  const result = {}

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key]

      if (specialKeys.includes(key)) {
        // 特定键允许方括号包裹的值
        if (typeof value === 'string' && /[\[\]]/.test(value)) {
          console.log(JSON.parse(value))
          // 去除方括号并选择性地转换为数组
          result[key] = JSON.parse(value)
        } else {
          // 如果值不是字符串或没有方括号包裹，直接赋值
          result[key] = [JSON.parse(value)]
        }
      } else {
        // 非特定键不允许方括号包裹的值
        if (typeof value === 'string' && /[\[\]]/.test(value)) {
          message.error(`${paramsNameMap[key].label}不能包含方括号包裹的值`)
          throw new Error(`Key '${key}' cannot contain bracket-wrapped values: ${value}`)
        } else {
          result[key] = value
        }
      }
    }
  }
  console.log(result)

  return result
}
