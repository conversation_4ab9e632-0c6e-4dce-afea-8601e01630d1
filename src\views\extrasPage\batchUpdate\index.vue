<template>
  <PageWrapper :contentFullHeight="true">
    <Row>
      <!-- 左边部分start -->
      <Col span="12">
        <Alert
          message="日期格式是'2024-01-01 23:15:13'.如果某个参数值为空白请填斜杠,即:'/',如果某个值保留原来值请填短横线,即:'-',设计师&2D设计师&3D设计师可多选,即: 账号ID 也可 [账号ID,账号ID]"
          type="info"
          show-icon
        />
        <div class="mt-4"></div>
        <BasicForm @register="registerForm">
          <template #previewSlot>
            <div class="flex justify-between items-center">
              <a-button type="primary" @click="onPreviewTable" class="mx-4" :loading="loading" :disabled="loading">预览</a-button>
              <Dropdown>
                <a class="ant-dropdown-link" @click.prevent>
                  <a-button :loading="loading" :disabled="loading"> 下载<DownOutlined /></a-button>
                </a>
                <template #overlay>
                  <Menu @click="handleMenuClick">
                    <MenuItem key="departExport"><UploadOutlined /> 部门信息</MenuItem>
                    <MenuItem key="staffExport"><DownloadOutlined /> 人员信息</MenuItem>
                  </Menu>
                </template>
              </Dropdown>
            </div>
          </template>
        </BasicForm>
      </Col>
      <!-- 左边部分end -->
      <!-- 右边部分start -->
      <Col :span="12">
        <BasicTable @register="registerTable" :dataSource="dataSource">
          <template #toolbar>
            <div>
              <Button type="primary" @click="onSubmit" :loading="loading" :disabled="loading">提交</Button>
            </div>
          </template>
        </BasicTable>
      </Col>
      <!-- 右边部分end -->
    </Row>
    <Loading :loading="loading" :absolute="false" />
  </PageWrapper>
</template>

<script setup lang="ts" name="clientBatchUpdate">
import { onMounted, ref, unref } from 'vue'
import { Row, Col, Button, message, Menu, MenuItem, Dropdown, Alert } from 'ant-design-vue'
import { UploadOutlined, DownloadOutlined, DownOutlined } from '@ant-design/icons-vue'
import { keys } from 'lodash-es'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicTable, useTable } from '/@/components/Table'
import type { BasicColumn } from '/@/components/Table'
import { PageWrapper } from '/@/components/Page'
import { Loading } from '/@/components/Loading'
import { arrHeaderDept, batchUpdateFormSchema, paramsNameMap, staffHeaderDept } from './datas/datas'
import { segment, transformArray } from './datas/fn'
import { getDept, getStaffList } from '/@/api/erp/systemInfo'
import { aoaToSheetXlsx } from '/@/components/Excel'
import { batchUpdateApi } from '/@/api/extrasPage/batchUpdate'
import { useRender } from '/@/components/Table/src/hooks/useRender'

const loading = ref<boolean>(false)

// 注册table
const columnsRef = ref<Recordable[]>([])
const dataSource = ref<any>([])
const [registerTable, {}] = useTable({
  title: '预览表格数据',
  columns: columnsRef,
  showTableSetting: false, // 是否显示表格设置
  bordered: true,
  showIndexColumn: true, // 显示序列号
  pagination: {
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30', '40']
  }
})

// 注册from
const [registerForm, { validate }] = useForm({
  schemas: batchUpdateFormSchema,
  showActionButtonGroup: false,
  labelWidth: 200
})

// 预览表格
const onPreviewTable = async () => {
  try {
    loading.value = true
    const { paramNames, lineBreak, splitter, parameter } = await validate()

    let previewDataSource = segment({
      splitter,
      lineBreak,
      params_value: parameter,
      params_name: paramNames
    })
    let allLabelArr = [] as any
    const columns: BasicColumn[] = []

    if (!paramNames || paramNames.length === 0) {
      message.error('请选择参数名选项')
      return
    }
    if (!splitter || !lineBreak) {
      message.error('请选择分割符后在预览！')
      return
    } else {
      console.log(previewDataSource)

      // 存储全部的label
      allLabelArr = keys(previewDataSource[0])
      for (let key of allLabelArr) {
        if (['design', 'design2d', 'design3d'].includes(key)) {
          columns.push({
            title: paramsNameMap[key].label,
            dataIndex: key,
            customRender: ({ text }) => {
              const nameArr = text.map((item) => item)
              return text ? useRender.renderTags(nameArr) : '-'
            }
          })
        } else {
          columns.push({
            title: paramsNameMap[key].label,
            dataIndex: key
          })
        }
      }
      columnsRef.value = columns
      dataSource.value = previewDataSource
      // console.log(dataSource.value, 'dataSource.value')
    }
    loading.value = false
  } catch (err) {
    console.log('预览表格时报错', err)
    loading.value = false
  }
}

async function onSubmit() {
  try {
    loading.value = true
    if (unref(dataSource).length < 1) throw new Error('暂无数据可提交')

    //发请求
    if (unref(dataSource).length > 0) await batchUpdateApi({ items: unref(dataSource) })

    message.success('OK')
    loading.value = false
  } catch (err) {
    message.error('error')
    console.log('提交按钮时报错', err)
    loading.value = false
  }
}

//获取用户和部门数据,用于给健哥下载查看id
const deptData = ref<any[]>([])
const staffData = ref<any[]>([])
onMounted(async () => {
  try {
    loading.value = true
    const { items } = await getDept()
    deptData.value = transformArray(items, arrHeaderDept)
    const { items: accountItems } = await getStaffList()

    staffData.value = transformArray(accountItems, staffHeaderDept)
  } catch (err) {
    console.error(err)
  } finally {
    loading.value = false
  }
})

function handleMenuClick({ key }) {
  if (key === 'departExport') {
    aoaToSheetXlsx({
      data: deptData.value,
      header: arrHeaderDept,
      filename: '部门信息.xlsx'
    })
  } else if (key === 'staffExport') {
    aoaToSheetXlsx({
      data: staffData.value,
      header: staffHeaderDept,
      filename: '员工信息.xlsx'
    })
  }
}
</script>
