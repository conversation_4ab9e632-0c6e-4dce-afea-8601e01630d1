<template>
  <BasicModal @register="registerModal" v-bind="$attrs" width="60%" @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #ImgsSlot>
        <Upload v-model:file-list="imgFileList" action="/api/oss/putImg2Stocking" list-type="picture-card" :custom-request="handleRequest">
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, unref } from 'vue'
import { Upload } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import type { UploadFile } from 'ant-design-vue'
import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { commonImgUpload } from '/@/api/commonUtils/upload'
import { schemas } from '../datas/modal'
import { multiply } from 'lodash-es'
import { expengetList } from '/@/api/baseData/expenseitem'

const emit = defineEmits(['add-success', 'update-success', 'register'])

const imgFileList = ref<UploadFile[]>([])

const propData = ref()

const [registerForm, { resetFields, setFieldsValue, validate, validateFields, resetSchema, updateSchema }] = useForm({
  baseColProps: { span: 12 },
  showActionButtonGroup: false,
  labelCol: { span: 6 },
  schemas: schemas(),
  colon: true
})

const [registerModal, { closeModal, changeOkLoading, changeLoading }] = useModalInner(async (data) => {
  try {
    changeLoading(true)
    await resetFields()
    console.log(data, 'data')

    resetSchema(schemas(data.type, Number(data.exchangeRate), data.currency))
    updateform(data?.type)
    propData.value = data
    imgFileList.value = []
    if (data?.isUpdate) {
      setFieldsValue(data.record)
      imgFileList.value = data.record.imgs.length > 0 ? data.record.imgs.map((item) => ({ url: item, name: item, uid: item })) : []
    }
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
})

watch(
  () => imgFileList.value,
  async (val) => {
    await setFieldsValue({ imgs: val })
  }
)

//图片上传
async function handleRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonImgUpload(file, 'saleOrder')

  onSuccess!(result.path)
  imgFileList.value = imgFileList.value!.map((item, idx) => {
    const url = item.url || item.response
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: url ? url.match(/[^\/]+$/)[0] : idx
    }
  })
  await setFieldsValue({ imgs: imgFileList.value.map((item) => item.url) })
  await nextTick(async () => {
    try {
      await validateFields(['imgs'])
    } catch (e) {
      throw new Error(`${e}`)
    }
  })
}

async function handleSubmit() {
  await changeOkLoading(true)
  try {
    const fromdata = await validate()
    // console.log(fromdata, 'formData')
    const params = {
      ...fromdata,
      foreign_currency_unit_pirce: fromdata.foreign_currency_unit_pirce || 0,
      unitPrice: fromdata.unitPrice == 0 ? 0 : fromdata.unitPrice,
      totalAmount: fromdata.unitPrice == 0 ? 0 : multiply(fromdata.quantity, fromdata.unitPrice),
      imgs: fromdata.imgs == null || fromdata.imgs.length === 0 ? [] : fromdata.imgs.map((item: UploadFile) => item!.url)
    }
    if (unref(propData).isUpdate) {
      emit('update-success', { ...params, key: unref(propData).record.key })
    } else {
      emit('add-success', { ...params, key: Date.now() })
    }
    await closeModal()
    changeOkLoading(false)
  } catch (err) {
    changeOkLoading(false)
    console.log(err)

    throw new Error(`${err}`)
  }
}

function updateform(type) {
  if (type === 27) {
    updateSchema({
      field: 'name',
      component: 'PagingApiSelect',
      componentProps: ({ formModel }) => {
        return {
          api: expengetList,
          params: { is_disabled: 0 },
          resultField: 'items',
          selectProps: {
            showSearch: true,
            allowClear: true,
            placeholder: '请选择产品',
            fieldNames: {
              value: 'name',
              label: 'name'
            },
            optionFilterProp: 'name',
            onChange(val) {
              if (!val) return
              if (['海运费', '货柜费'].includes(val)) {
                formModel.is_logistics_follow = 1
              }
            }
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    })
  }
}
</script>
