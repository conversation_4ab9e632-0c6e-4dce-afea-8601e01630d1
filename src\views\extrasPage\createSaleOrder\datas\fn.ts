export const fieldMap = {
  name: '产品名称',
  unit: '单位',
  foreign_currency_unit_pirce: '外汇单价',
  unitPrice: '单价(RMB)',
  quantity: '需求数量',
  puid: '产品编码',
  batch_code: '批号',
  uniqid: '产品唯一码',
  desc: '描述',
  remark: '备注'
}

import { message } from 'ant-design-vue'
import Decimal from 'decimal.js'

/**
 * 验证外币单价与人民币单价和汇率的关系
 * @param unitPrice 人民币单价
 * @param foreignCurrencyPrice 外币单价
 * @param exchangeRate 汇率
 * @returns 验证结果
 */
export function validateForeignCurrencyPrice(
  unitPrice: string | number,
  foreignCurrencyPrice: string | number,
  exchangeRate: string | number
) {
  // 使用 Decimal 进行精确计算
  const rmb = new Decimal(unitPrice || 0)
  const foreign = new Decimal(foreignCurrencyPrice || 0)
  const rate = new Decimal(exchangeRate || 1)

  // 计算期望的外币金额：人民币金额 / 汇率
  const expectedForeignValue = rate.isZero() ? new Decimal(0) : rmb.dividedBy(rate).toDecimalPlaces(6)

  // 计算期望的人民币金额：外币金额 * 汇率
  const expectedRMBValue = foreign.times(rate).toDecimalPlaces(2)

  // 检查外币金额是否与计算值一致（允许小误差）
  const foreignDiff = foreign.minus(expectedForeignValue).abs()
  const isForeignValid = foreignDiff.lessThanOrEqualTo(0.000001)

  // 检查人民币金额是否与计算值一致（允许小误差）
  const rmbDiff = rmb.minus(expectedRMBValue).abs()
  const isRMBValid = rmbDiff.lessThanOrEqualTo(0.01)

  return {
    isValid: isForeignValid || isRMBValid,
    calculatedValue: expectedForeignValue.toDecimalPlaces(6).toNumber(),
    calculatedRMBValue: expectedRMBValue.toDecimalPlaces(2).toNumber()
  }
}

/**
 * 检查当币种为人民币时是否存在外币金额
 * @param foreignCurrencyPrice 外币单价
 * @param currency 币种
 * @returns 验证结果
 */
export function validateCurrencyConsistency(foreignCurrencyPrice: string | number, currency: string) {
  // 如果币种为人民币(CNY/RMB)，但存在外币金额，则返回错误
  const foreign = new Decimal(foreignCurrencyPrice || 0)
  const isRMB = currency === '人民币' || currency === 'CNY'

  // 检查是否存在外币金额（大于0）
  const hasForeignAmount = !foreign.isZero() && !foreign.isNegative()

  return {
    isValid: !(isRMB && hasForeignAmount),
    currency,
    hasForeignAmount
  }
}

export function transformData2Import(data: Recordable[], exchangeRate: number, currency: string) {
  let hasValidationErrors = false
  let hasCurrencyConsistencyError = false

  const transformedData = data.map((item, index) => {
    const cookedData: Recordable = {
      name: '',
      unit: '',
      foreign_currency_unit_pirce: '',
      unitPrice: '',
      quantity: '',
      puid: '',
      batch_code: '',
      uniqid: '',
      desc: '',
      remark: ''
    }

    // 提取字段值
    for (const key in cookedData) {
      if (fieldMap[key]) {
        cookedData[key] = item[fieldMap[key]]
      }
    }

    // 验证币种一致性：当币种为人民币时不应该有外币金额
    const currencyConsistency = validateCurrencyConsistency(cookedData.foreign_currency_unit_pirce, currency)

    if (!currencyConsistency.isValid) {
      hasCurrencyConsistencyError = true
      message.error(
        `第${index + 1}行存在外币金额(${cookedData.foreign_currency_unit_pirce})，但当前币种为人民币。请更换币种或清空外币金额。`
      )
    }

    // 验证外币单价是否等于单价乘以汇率
    const validation = validateForeignCurrencyPrice(cookedData.unitPrice, cookedData.foreign_currency_unit_pirce, exchangeRate)

    if (!validation.isValid && !hasCurrencyConsistencyError) {
      hasValidationErrors = true
      message.warning(
        `第${index + 1}行的人民币单价(${cookedData.unitPrice})与外汇单价(${
          cookedData.foreign_currency_unit_pirce
        })和汇率(${exchangeRate})的计算结果(${validation.calculatedRMBValue})不一致`
      )

      // 自动修正人民币单价
      cookedData.unitPrice = validation.calculatedRMBValue.toString()
    }

    return cookedData
  })

  if (hasValidationErrors && !hasCurrencyConsistencyError) {
    message.info('已自动修正不一致的人民币单价')
  }

  // 如果存在币种一致性错误，则返回null表示验证失败
  if (hasCurrencyConsistencyError) {
    return null
  }

  return transformedData
}
