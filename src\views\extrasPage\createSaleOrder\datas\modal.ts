import Decimal from 'decimal.js'
import { FormSchema } from '/@/components/Form'

export const schemas = (type?: Number, exchangeRate?: Number, currency?: String): FormSchema[] => [
  {
    field: 'name',
    label: '产品名称',
    component: 'Input',
    required: true
  },
  {
    field: 'is_logistics_follow',
    label: '是否物流部跟进',
    defaultValue: 0,
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    },
    required(renderCallbackParams) {
      return ['物流费', '海运费', '货柜费'].includes(renderCallbackParams.values.name) ? true : false
    },
    show(renderCallbackParams) {
      return ['物流费', '海运费', '货柜费'].includes(renderCallbackParams.values.name) ? true : false
    }
    // ifShow(renderCallbackParams) {
    //   return ['物流费', '海运费', '货柜费'].includes(renderCallbackParams.values.name) ? true : false
    // }
  },
  {
    field: 'unit',
    label: '单位',
    component: 'Input',
    required: true
  },
  {
    field: 'unitPrice',
    label: '单价(RMB)',
    defaultValue: 0,
    component: 'InputNumber',
    componentProps: {
      min: type == 2 ? 0 : 0.01,
      max: type == 2 ? 0 : null,
      precision: type == 3 ? 6 : 2
    },
    dynamicDisabled: type == 2 ? true : currency !== '人民币' ? true : false,
    required: type !== 2 && currency == '人民币' ? true : false
  },
  {
    field: 'foreign_currency_unit_pirce',
    label: '外汇单价',
    component: 'InputNumber',
    defaultValue: 0,
    componentProps: ({ formModel }) => {
      return {
        min: type == 2 ? 0 : 0.01,
        max: type == 2 ? 0 : null,
        precision: type == 3 ? 6 : 2,
        onChange(val) {
          console.log(val)
          formModel.unitPrice = new Decimal(val).mul(exchangeRate).toDecimalPlaces(2).toNumber()
        }
      }
    },
    dynamicDisabled: type == 2 ? true : currency == '人民币' ? true : false,
    required: type !== 2 && currency !== '人民币' ? true : false
  },
  {
    field: 'quantity',
    label: '需求数量',
    component: 'InputNumber',
    componentProps: {
      min: 0.01,
      precision: 2
    },
    required: true
  },
  {
    field: 'puid',
    label: '产品编码',
    component: 'Input'
  },
  {
    field: 'batch_code',
    label: '批号',
    component: 'Input'
  },
  {
    field: 'uniqid',
    label: '产品唯一码',
    component: 'Input'
  },
  // {
  //   field: 'totalAmount',
  //   label: '总价',
  //   component: 'InputNumber'
  // },
  {
    field: 'desc',
    label: '描述',
    component: 'InputTextArea'
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea'
  },
  {
    field: 'imgs',
    label: '产品图片',
    component: 'Upload',
    slot: 'ImgsSlot'
  }
]
