<!-- eslint-disable max-len -->
<template>
  <PageWrapper title="创建销售/备货/费用订单">
    <template #headerContent>
      <a
        href="https://img.gbuilderchina.com/erp/%E8%B4%B9%E7%94%A8%E7%B1%BB%E8%AE%A2%E5%8D%95%E5%BD%95%E5%85%A5%E6%95%99%E7%A8%8B_20240817224702.pdf"
        style="color: red; font-size: 20px"
        target="_blank"
      >
        费用订单录入教程
      </a>
    </template>
    <BasicForm @register="registerForm" @submit="debounceSubmit" @reset="handleReset" @field-value-change="fieldValueChange">
      <template #FilesSlot>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg2Stocking"
          :custom-request="handleFileRequest"
          :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
        >
          <a-button type="primary">
            <upload-outlined />
            Upload
          </a-button>
        </Upload>
      </template>
      <template #saleGoodsSlot>
        <FormItemRest>
          <BasicTable @register="registerTable">
            <template #toolbar>
              <div class="btn-list">
                <Tooltip title="添加产品前,请确认汇率无错">
                  <a-button type="primary" @click="handleAdd" class="mr-8px"> <plus-outlined />添加产品 </a-button>
                </Tooltip>
                <Tooltip title="导入产品前,请确认汇率无错">
                  <Dropdown class="mr-8px">
                    <a class="ant-dropdown-link" @click.prevent>
                      <a-button> 文件导入 <CloudUploadOutlined /></a-button>
                    </a>
                    <template #overlay>
                      <Menu @click="handleMenuClick">
                        <MenuItem key="upload"><UploadOutlined /> 导入文件</MenuItem>
                        <MenuItem key="export"><DownloadOutlined /> 导出模板</MenuItem>
                      </Menu>
                    </template>
                  </Dropdown>
                </Tooltip>

                <Popover title="批量修改已选择产品的单价" trigger="click" v-model:visible="visibleUnitPrice">
                  <template #content>
                    <InputNumber
                      v-model:value="batchUnitPrice"
                      :precision="2"
                      :min="createtype == 2 ? 0 : 0.01"
                      :max="createtype == 2 ? 0 : 99999999"
                    />
                    <div class="flex mt-4px justify-end">
                      <a-button type="primary" class="mr-4px" @click="handleBatchUpdate('unitPrice')" size="small">确定 </a-button>
                      <a-button size="small" @click="visibleUnitPrice = false">取消 </a-button>
                    </div>
                  </template>
                  <a-button class="mr-8px"><BarsOutlined />批量修改单价</a-button>
                </Popover>
                <Popover title="批量修改已选择产品的需求数量" trigger="click" v-model:visible="visibleQuantity">
                  <template #content>
                    <InputNumber v-model:value="batchQuantity" :precision="2" :min="0.01" />
                    <div class="flex mt-4px justify-end">
                      <a-button type="primary" class="mr-4px" @click="handleBatchUpdate('quantity')" size="small">确定 </a-button>
                      <a-button size="small" @click="visibleQuantity = false">取消 </a-button>
                    </div>
                  </template>
                  <a-button><BarsOutlined />批量修改需求数量</a-button>
                </Popover>
              </div>
            </template>
            <template #bodyCell="{ text, column, record }">
              <template v-if="column && column.dataIndex === 'imgs'">
                <TableImg :size="60" :simpleShow="true" :imgList="text" v-if="text.length > 0" />
              </template>
              <template v-if="column && column.dataIndex === 'is_logistics_follow'">
                {{ record.is_logistics_follow == 1 ? '是' : '否' }}
              </template>
              <template v-if="column.key === 'action'">
                <TableAction :actions="createActions(record)" :stop-button-propagation="true" />
              </template>
            </template>
          </BasicTable>
        </FormItemRest>
      </template>
    </BasicForm>
    <ProductModal @register="registerModal" @add-success="handleAddSuccess" @update-success="handleUpdateProduct" />
    <ImpExcelModal @register="registerUploadModal" :dataCallBackFn="handleUploadData" ref="ImpExcelModalRef" />
  </PageWrapper>
</template>
<script lang="ts" setup name="CreateSaleOrder">
import { ref, onUnmounted, onMounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { Form, Popover, InputNumber, Upload, Dropdown, Menu, MenuItem, message, Tooltip } from 'ant-design-vue'
import type { UploadFile } from 'ant-design-vue'
import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { PlusOutlined, BarsOutlined, UploadOutlined, CloudUploadOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import { throttle } from 'lodash-es'
import { PageWrapper } from '/@/components/Page'
import { BasicForm, useForm } from '/@/components/Form'
import { useModal } from '/@/components/Modal'
import { BasicTable, useTable, TableImg, TableAction, EditRecordRow } from '/@/components/Table'
import type { ActionItem } from '/@/components/Table'
import { updateOrCreateSalesOrder } from '/@/api/extrasPage/createSaleOrder'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import mitt from '/@/utils/mitt'
import { add, mul } from '/@/utils/math'
import { useUserStore } from '/@/store/modules/user'
import ProductModal from './components/ProductModal.vue'
import { schemas, columns } from './datas/datas'
import { router } from '/@/router'
import { useGo } from '/@/hooks/web/usePage'
import { ImpExcelModal } from '/@/components/Excel'
import { onExpExcelTemplate } from '/@/utils/exportTemplate'
import { fieldMap, transformData2Import } from './datas/fn'
import Decimal from 'decimal.js'
const pathname = window.location.pathname
//创建类型便于商品金额判断
const createtype = ref(pathname == '/s/' ? 2 : 3)

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const FormItemRest = Form.ItemRest

const submitLoading = ref(false)
const [registerModal, { openModal, setModalProps }] = useModal()
const [registerUploadModal, { openModal: openUploadModal }] = useModal()

const ImpExcelModalRef = ref<InstanceType<typeof ImpExcelModal>>()
const rootMenuEmitter = mitt()
const filesList = ref<UploadFile[]>([])

onMounted(async () => {
  await updateSchema([
    {
      field: 'inCharge',
      componentProps: {
        defaultOptions: [
          {
            wxworkId: userInfo.value!.wxworkId,
            name: userInfo.value!.realName
          }
        ]
      }
    },
    {
      field: 'creator',
      componentProps: {
        defaultOptions: [
          {
            id: userInfo.value!.userId,
            name: userInfo.value!.realName
          }
        ]
      }
    }
  ])

  await setFieldsValue({ inCharge: userInfo.value!.wxworkId, wxworkId: userInfo.value!.wxworkId, creator: userInfo.value!.userId })
})

const [registerForm, { setFieldsValue, validate, getFieldsValue, updateSchema, resetFields }] = useForm({
  schemas: schemas(handleShowDeposit, handleproject),
  compact: false,
  actionColOptions: { span: 24 },
  labelWidth: 130,
  colon: true,
  baseColProps: { span: 8 },
  submitButtonOptions: { text: '提交', loading: submitLoading },
  resetFunc: async () => {
    setTableData([])
  }
})

const [
  registerTable,
  { getDataSource, setTableData, getSelectRowKeys, updateTableDataRecord, insertTableDataRecord, deleteTableDataRecord, setColumns }
] = useTable({
  showIndexColumn: true,
  columns,
  dataSource: [],
  pagination: false,
  striped: true,
  canResize: false,
  rowKey: 'key',
  actionColumn: {
    width: 230,
    title: '操作',
    dataIndex: 'action'
  },
  rowSelection: {
    type: 'checkbox'
  }
})

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'saleOrder')
  onSuccess!(result.path)
  filesList.value = filesList.value!.map((item, idx) => {
    const url = item.url || item.response
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: url ? url.match(/[^\/]+$/)[0] : idx
    }
  })
  await setFieldsValue({ files: filesList.value.map((item) => item.url) })
}

async function handleAdd() {
  const { exchangeRate, currency } = getFieldsValue()
  setModalProps({ title: '添加产品' })
  openModal(true, { isUpdate: false, type: createtype.value, exchangeRate, currency })
}

function handleAddSuccess(params) {
  insertTableDataRecord(params)
  const dataSource = getDataSource()
  const total = dataSource.reduce((pre, cur) => add(pre, mul(cur.foreign_currency_unit_pirce, cur.quantity, 2)), 0)
  setFieldsValue({ foreign_currency_amount: total, sales_goods: dataSource })

  // setFieldsValue({ sales_goods: getDataSource() })
}

function handleUpdateProduct(params) {
  updateTableDataRecord(params.key, params)
  const dataSource = getDataSource()
  const total = dataSource.reduce((pre, cur) => add(pre, mul(cur.foreign_currency_unit_pirce, cur.quantity, 2)), 0)
  setFieldsValue({ foreign_currency_amount: total, sales_goods: dataSource })
}

const handleMenuClick = ({ key }) => {
  if (key === 'export') {
    onExpExcelTemplate(Object.values(fieldMap), false)
  } else if (key === 'upload') {
    openUploadModal(true, {
      sheetName: 'Sheet1',
      headerRow: 1,
      startCell: 'A2',
      endCell: `I200000`
    })
  }
}

//上传
async function handleUploadData(data) {
  try {
    ImpExcelModalRef.value?.changeLoading(true)
    const { exchangeRate, currency } = getFieldsValue()
    const step1Data = transformData2Import(data, Number(exchangeRate), currency)
    //计算总价和key
    const tableData = await getDataSource()

    const lastKey = tableData.length > 0 ? tableData[tableData.length - 1].key : 0

    //插入表格
    const sales_goods = [
      ...step1Data.map((item, index) => ({
        ...item,
        key: lastKey + index + 1,
        imgs: [],
        totalAmount: mul(item.unitPrice, item.quantity, 2)
      })),
      ...tableData
    ]
    setTableData(sales_goods)

    await setFieldsValue({ sales_goods })
    //关闭弹窗

    return Promise.resolve('OK')
  } catch (err) {
    console.log(err)
    return Promise.reject('Fail')
  } finally {
    ImpExcelModalRef.value?.changeLoading(false)
  }
}

function createActions(record: EditRecordRow): ActionItem[] {
  return [
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record)
    },
    {
      icon: 'ant-design:delete-outlined',
      color: 'error',
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        placement: 'left',
        confirm: handleDelete.bind(null, record)
      }
    }
  ]
}

async function handleEdit(record: EditRecordRow) {
  const { exchangeRate } = getFieldsValue()
  setModalProps({ title: '编辑产品' })
  openModal(true, { isUpdate: true, record, type: createtype.value, exchangeRate })
}

async function handleDelete(record: EditRecordRow) {
  deleteTableDataRecord(record.key)
  await setFieldsValue({ sales_goods: getDataSource() })
}

//批量修改
const visibleUnitPrice = ref<boolean>(false)
const visibleQuantity = ref<boolean>(false)
const batchUnitPrice = ref(0.01)
const batchQuantity = ref(0.01)

async function handleBatchUpdate(type = 'unitPrice') {
  const selectKeys = getSelectRowKeys()
  if (selectKeys.length === 0) return
  const { sales_goods, exchangeRate, currency } = getFieldsValue()
  for (const item of sales_goods) {
    if (selectKeys.includes(item.key)) {
      if (type === 'unitPrice' && (currency == '人民币' || currency == 'CNY')) {
        item.unitPrice = batchUnitPrice.value
      } else if (type === 'unitPrice' && currency !== 'rmb') {
        item.foreign_currency_unit_pirce = batchUnitPrice.value
        item.unitPrice = new Decimal(item.foreign_currency_unit_pirce).mul(exchangeRate).toDecimalPlaces(2).toNumber()
      } else {
        item.quantity = batchQuantity.value
      }
      item.totalAmount = mul(item.unitPrice, item.quantity, 2)
      updateTableDataRecord(item.key, item)
    }
  }
  await setFieldsValue({ sales_goods })
  if (type === 'unitPrice') {
    visibleUnitPrice.value = false
    batchUnitPrice.value = 0
  } else {
    visibleQuantity.value = false
    batchQuantity.value = 0
  }
}

function reloadTable(reload) {
  reload()
}

async function handleShowDeposit(isShow: number) {
  await updateSchema([
    {
      field: 'deposit_name',
      ifShow: isShow == 1
    },
    {
      field: 'deposit_contact',
      ifShow: isShow == 1
    },
    {
      field: 'commission',
      ifShow: isShow == 1
    },
    {
      field: 'deposit_account',
      ifShow: isShow == 1
    }
  ])
}

async function handleSubmit() {
  submitLoading.value = true
  try {
    validate()
    const formData = await getFieldsValue()
    const {
      client_name,
      client_contact,
      client_location,
      show_deposit,
      deposit_name,
      deposit_contact,
      deposit_account,
      sales_goods,
      type
    } = formData
    const params = {
      ...formData,
      files: formData?.files?.map((item: UploadFile) => item.url) ?? [],
      sales_goods: undefined,
      type,
      receivable: sales_goods.reduce((accumulator, currentValue) => add(accumulator, currentValue.totalAmount), 0), //所有总价之和
      client: {
        name: client_name,
        contact: client_contact ?? null,
        location: client_location
      },
      deposit: show_deposit
        ? {
            name: deposit_name,
            contact: deposit_contact,
            account: deposit_account
          }
        : undefined,
      items: sales_goods.map((item) => ({
        ...item,
        puid: item.puid ?? '',
        totalAmount: item.totalAmount,
        desc: item.desc ?? '',
        remark: item.remark ?? ''
      }))
    }

    // 当类型为销售订单(type==3)时，验证每个商品的单价不能为0
    if (type == 3) {
      // 查找单价为0的商品
      const zeroUnitPriceItems = sales_goods.filter((item) => {
        const unitPrice = new Decimal(item.unitPrice || 0)
        return unitPrice.isZero() || unitPrice.isNegative()
      })

      // 如果存在单价为0的商品，显示错误消息并中断提交
      if (zeroUnitPriceItems.length > 0) {
        const itemNames = zeroUnitPriceItems.map((item) => item.name || '未命名商品').join('、')
        message.error(`销售订单中的商品单价不能为0，请检查以下商品：${itemNames}`)
        submitLoading.value = false
        return
      }
    }
    //备货与消费订单不填销售单号,单需要给个固定值,后端替换生成新单号
    if (!params.tpuid) {
      params.tpuid = 'YTTY-888886666677777'
    }
    //消费订单销售渠道不填默认erp
    if (params.type == 27 && !params.src) {
      params.src = 'erp'
    }
    console.log(params)
    delete params.exchange_rate

    await updateOrCreateSalesOrder(params)
    setTimeout(() => {
      submitLoading.value = false
      const go = useGo(router)
      switch (params.type) {
        case 3:
          go({ path: '/erp/saleOrder' })
          break
        case 2:
          go({ path: '/erp/backorder' })
          break
        case 27:
          go({ path: '/erp/consumption' })
          break
      }
    }, 1000)
  } catch (err) {
    submitLoading.value = false
    throw new Error(`${err}`)
  }
}

const debounceSubmit = throttle(handleSubmit, 500)

watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val })
  }
)
function handleReset() {
  resetFields()
  filesList.value = []
  setTableData([])
}

onUnmounted(() => rootMenuEmitter.off('sale-order-reload', reloadTable))

//记录订单类型便于商品金额更改
async function fieldValueChange(key, value) {
  console.log(key, value)
  if (key == 'type') {
    createtype.value = value
    const tabledata = getDataSource()
    for (const item of tabledata) {
      item.totalAmount = 0
      item.unitPrice = 0
    }
    setTableData(tabledata)
    setColumns(
      columns.map((column) => {
        if (column.dataIndex === 'is_logistics_follow') {
          return { ...column, defaultHidden: value == 27 ? false : true }
        }
        return column
      })
    )
    if (value == 27) {
      await updateSchema([
        {
          field: 'creator',
          componentProps: {
            defaultOptions: [
              {
                id: userInfo.value!.userId,
                name: userInfo.value!.realName
              }
            ]
          }
        }
      ])
      await setFieldsValue({ creator: userInfo.value!.userId })
    }
  }

  if (key === 'exchangeRate') {
    processTableData('updateRate', value)
  } else if (key === 'exchange_rate') {
    message.success('汇率币种更改,金额清空,请注意重新输入')
    processTableData('changeCurrency')
  }
}

function processTableData(operationType, value?) {
  const tabledata = getDataSource()

  for (const item of tabledata) {
    if (operationType === 'updateRate') {
      // 更新汇率计算单价和总价
      item.unitPrice = new Decimal(item.foreign_currency_unit_pirce).mul(value).toDecimalPlaces(2).toNumber()
      item.totalAmount = mul(item.unitPrice, item.quantity, 2)
    } else if (operationType === 'changeCurrency') {
      // 更改币种，清空金额
      item.foreign_currency_unit_pirce = 0
      item.unitPrice = 0
      item.totalAmount = mul(item.unitPrice, item.quantity, 2)
    }
  }

  setTableData(tabledata)
}
async function handleproject(formModel, shall) {
  if (formModel.type == 27) {
    await updateSchema([
      {
        field: 'program_incharge',
        componentProps: {
          defaultOptions: [
            {
              wxworkId: userInfo.value!.wxworkId,
              name: userInfo.value!.realName
            }
          ]
        }
      },
      {
        field: 'creator',
        componentProps: {
          defaultOptions: [
            {
              id: userInfo.value!.userId,
              name: userInfo.value!.realName
            }
          ]
        }
      },
      {
        field: 'inCharge',
        componentProps: {
          defaultOptions: [
            {
              wxworkId: userInfo.value!.wxworkId,
              name: userInfo.value!.realName
            }
          ]
        }
      }
    ])

    await setFieldsValue({
      inCharge: shall.user_wxworkId,
      program_incharge: userInfo.value!.wxworkId,
      creator: userInfo.value!.userId
    })
  }
}
</script>

<style scoped lang="less">
.btn-list {
  display: flex;
  justify-content: flex-start;
  width: 100%;
}
</style>
