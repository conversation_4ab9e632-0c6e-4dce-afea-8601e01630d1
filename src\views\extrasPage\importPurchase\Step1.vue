<template>
  <div class="step1">
    <Alert message="如果某个参数值为空请填斜杠,即:'/',如果某个值保留原来值或者不传值请填短横线,即:'-'" type="info" show-icon />
    <BasicForm @register="registerForm"
      ><template #formFooter>
        <div class="flex justify-center w-full">
          <a-button type="primary" @click="onPreview" :disabled="disabled" :loading="disabled">检查并下一步</a-button>
        </div>
      </template>
    </BasicForm>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { Alert } from 'ant-design-vue'
import { schemas, segment } from './datas/step1'
import { BasicForm, useForm } from '/@/components/Form'

import { batchImpPurchaseCheck } from '/@/api/extrasPage/importPurchase'

const emit = defineEmits(['next'])
const disabled = ref(false)

const [registerForm, { validate }] = useForm({
  labelWidth: 150,
  colon: true,
  schemas,
  actionColOptions: {
    span: 14
  },
  baseColProps: { span: 12 },
  submitButtonOptions: {
    text: '检查并下一步'
  },
  showActionButtonGroup: false
})

async function onPreview() {
  try {
    disabled.value = true
    const formData = await validate()
    let previewDataSource = segment(formData)
    if (previewDataSource.length === 0) return
    const { items: ResultItems } = await batchImpPurchaseCheck({
      brand: formData.brand,
      supplier_id: formData.supplier_id,
      client_id: formData.client_id,
      data: previewDataSource
    })

    emit('next', { formData, ResultItems })
    disabled.value = false
  } catch (error) {
    disabled.value = false
    console.error(error)
  }
}
</script>

<style lang="less" scoped>
.step1 {
  width: 900px;
  margin: 0 auto;
}
</style>
