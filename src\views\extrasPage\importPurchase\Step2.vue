<template>
  <div class="w-1600px m-auto">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <div class="flex justify-center w-full">
          <a-button class="mr-4" @click="onPrevFunc" :loading="disableding" :disabled="disableding">上一步</a-button>
          <a-button class="mr-4" @click="onRedo" :loading="disableding" :disabled="disableding"><redo-outlined />重新检查</a-button>
          <a-button type="primary" @click="onNextFunc" :loading="disableding" :disabled="disableding">检查后提交到草稿</a-button>
        </div>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { RedoOutlined } from '@ant-design/icons-vue'
import { BasicTable, useTable } from '/@/components/Table'
import { batchImpPurchaseCheck, submitToDraft } from '/@/api/extrasPage/importPurchase'
import { calcParams, columns } from './datas/step2'
import { useMessage } from '/@/hooks/web/useMessage'

const disableding = ref(false)

interface IProps {
  step1Values: {
    formData: Recordable
    ResultItems: Recordable[]
  }
}

const { notification, createConfirm } = useMessage()
const emit = defineEmits(['next', 'prev'])

const prop = withDefaults(defineProps<IProps>(), {})

//v-show再次为true,不会再执行onMounted
onMounted(() => {
  prop.step1Values && setTableData(prop.step1Values.ResultItems)
})

async function onPrevFunc() {
  try {
    disableding.value = true
    await emit('prev')
    disableding.value = false
  } catch (err) {
    disableding.value = false
    console.error(err)
  }
}

async function onRedo() {
  try {
    disableding.value = true
    const tableData = await getDataSource()
    console.log(tableData, 'tableData')
    const {
      step1Values: { formData }
    } = prop
    const { items } = await batchImpPurchaseCheck(calcParams(tableData, formData))
    setTableData(items.map((item, index) => ({ ...item, key: index })))
  } catch (error) {
    console.error(error)
  } finally {
    disableding.value = false
  }
}
async function onNextFunc() {
  try {
    const {
      step1Values: { formData }
    } = prop
    const tableData = await getDataSource()
    if (tableData.every((item) => item.pass === true && item.qty_left !== false && item.qty_left >= 0)) {
      const params = calcParams(tableData, formData)
      const newParams = { ...params, truncate: true }
      if (tableData.some((item) => item.qty_left === 0)) {
        createConfirm({
          title: '提示',
          iconType: 'warning',
          content: '存在商品可采购数量为0,是否继续提交?',
          onOk: async () => {
            try {
              disableding.value = true
              await submitToDraft(newParams)
            } catch (error) {
              console.error(error)
            } finally {
              disableding.value = false
            }
          }
        })
      } else {
        try {
          disableding.value = true
          await submitToDraft(newParams)
        } catch (error) {
          console.error(error)
        } finally {
          disableding.value = false
        }
      }
      emit('next', tableData)
    } else {
      const errMessage = '请点击重新检查,检查数据是否正确,article全不为红才可提交'
      notification.error({
        message: errMessage
      })
      throw new Error(errMessage)
    }
  } catch (error) {
    console.error(error)
  }
}

const [registerTable, { setTableData, getDataSource }] = useTable({
  dataSource: [],
  columns: columns,
  showIndexColumn: false,
  maxHeight: 600,
  rowKey: 'key'
})

defineExpose({ setTableData })
</script>
