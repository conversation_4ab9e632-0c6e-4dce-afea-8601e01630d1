<template>
  <BasicTable @register="registerTable">
    <template #toolbar>
      <div class="flex justify-center w-full">
        <a-button class="mr-4" @click="onPrev" :loading="disableding" :disabled="disableding">上一步</a-button>
        <a-button type="primary" @click="onSubmit" :loading="disableding" :disabled="disableding"> 提交草稿生成订单采购单 </a-button>
      </div>
    </template>
  </BasicTable>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { BasicTable, useTable } from '/@/components/Table'
import { getDraftList, submitToPurchaseOrder } from '/@/api/extrasPage/importPurchase'
import { columns, schemas } from './datas/step3'

const emit = defineEmits(['next', 'prev'])
const disableding = ref(false)

const [registerTable, { reload }] = useTable({
  dataSource: [],
  columns: columns,
  showIndexColumn: false,
  maxHeight: 600,
  rowKey: 'id',
  api: getDraftList,
  useSearchForm: true,
  formConfig: {
    labelWidth: 100,
    baseColProps: { span: 8 },
    autoSubmitOnEnter: true,
    schemas
  }
})
async function onPrev() {
  try {
    disableding.value = true
    await emit('prev')
    disableding.value = false
  } catch (err) {
    disableding.value = false
    console.error(err)
  }
}

async function onSubmit() {
  try {
    disableding.value = true
    await submitToPurchaseOrder()
    emit('next')
  } catch (e) {
    console.error(e)
  } finally {
    disableding.value = false
  }
}

defineExpose({ reload })
</script>
