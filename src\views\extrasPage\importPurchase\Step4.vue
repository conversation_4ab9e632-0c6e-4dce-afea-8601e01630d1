<template>
  <div class="w-150 m-auto">
    <Result status="success" title="操作成功">
      <template #extra>
        <a-button type="primary" @click="onRedo" :loading="disableding" :disabled="disableding"> 回到第一步继续导入 </a-button>
      </template>
    </Result></div
  >
</template>

<script lang="ts" setup>
import { Result } from 'ant-design-vue'
import { ref } from 'vue'
const emit = defineEmits(['redo'])
const disableding = ref(false)
async function onRedo() {
  try {
    disableding.value = true
    await emit('redo')
    disableding.value = false
  } catch (err) {
    disableding.value = false
    console.error(err)
  }
}
</script>
