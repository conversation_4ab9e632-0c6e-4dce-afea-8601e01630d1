import { getClientList, getErpSupplier } from '/@/api/commonUtils'
import { FormSchema } from '/@/components/Form'
import { isNullOrUnDef } from '/@/utils/is'
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash-es'
import { ref } from 'vue'

const pathname = window.location.pathname

export const submitBtnDisabled = ref(true)
export const brandList = ['FAW', 'SAI', 'SKO', 'AUD', 'SIN', 'ORI']
const paramsOptions = [
  {
    value: 'article',
    label: 'article'
  },
  {
    value: 'quantity',
    label: '数量'
  },
  {
    value: 'price',
    label: '价格'
  },
  {
    value: 'source_uniqid',
    label: '单号'
  },
  {
    value: 'replacement',
    label: '替换号'
  },
  {
    value: 'currency',
    label: '币别'
  },
  {
    value: 'exchange_rate',
    label: '汇率'
  }
]

export const splitterOptions = [
  {
    value: '\t',
    label: 'excel空格'
  },
  {
    label: '普通空格',
    value: ' '
  },
  {
    label: '等于号(=)',
    value: '='
  },
  {
    label: '双斜杆(//)',
    value: '//'
  },
  {
    label: '中文逗号(，)',
    value: '，'
  },
  {
    label: '英文逗号(,)',
    value: ','
  },
  {
    label: '中文分号(；)',
    value: '；'
  },
  {
    label: '英文分号(;)',
    value: ';'
  },
  {
    label: '中文冒号(：)',
    value: '：'
  },
  {
    label: '英文冒号(:)',
    value: ':'
  }
]

export const lineBreakOptions = [
  {
    value: '\n',
    label: '换行(Enter)'
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'supplier_id',
    label: '供应商',
    component: 'PagingApiSelect',
    required: true,
    componentProps: () => {
      return {
        api: getErpSupplier,
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        },
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        resultField: 'items'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'brand',
    label: '品牌',
    required: true,
    component: 'Select',
    componentProps: {
      options: brandList.map((item) => ({ label: item, value: item })),
      mode: 'multiple'
    }
  },
  {
    field: 'client_id',
    label: '客户',
    component: 'ApiSelect',
    required: true,
    componentProps: ({}) => ({
      api: getClientList,
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      },

      resultField: 'items'
    }),
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'splitter',
    label: '值与值之间的分隔符',
    defaultValue: '\t',
    // defaultValue: ' ',
    component: 'Select',
    required: true,
    componentProps: { options: splitterOptions },
    rules: [
      {
        required: true,
        message: '请选择值与值之间的分隔符'
      },
      {
        validator(_, value) {
          return new Promise((resolve, reject) => {
            if (isNullOrUnDef(value)) return reject('请选择分隔符')

            resolve()
          })
        },
        trigger: 'blur'
      }
    ]
  },
  {
    field: 'lineBreak',
    label: '选择换行符',
    defaultValue: '\n',
    component: 'Select',
    required: true,
    componentProps: { options: lineBreakOptions },
    rules: [
      {
        required: true,
        message: '请选择换行符'
      },
      {
        validator(_, value) {
          return new Promise((resolve, reject) => {
            if (isNullOrUnDef(value)) return reject('请选择换行符')

            resolve()
          })
        },
        trigger: 'blur'
      }
    ]
  },
  {
    field: 'params_name',
    component: 'Select',
    label: '参数名',
    dynamicDisabled: true,
    defaultValue: !['/sp/', '/sptests/'].includes(pathname)
      ? paramsOptions.filter((item) => !['currency', 'exchange_rate'].includes(item.value)).map((item) => item.value)
      : paramsOptions.map((item) => item.value),
    componentProps: {
      options: paramsOptions,
      mode: 'multiple'
    },
    colProps: {
      span: 24
    }
  },
  {
    field: 'params_value',
    label: '参数值',
    component: 'InputTextArea',
    helpMessage: '输入内容后请选择对应的分隔符！',
    required: true,
    colProps: {
      span: 24
    },
    componentProps: {
      rows: 9,
      onChange: () => {
        submitBtnDisabled.value = true
      }
    }
  }
]

export const segment = (formData) => {
  const { splitter, lineBreak, params_value, params_name } = formData
  const formatter1 = params_value.split(splitter)
  const formatter2 = String(formatter1).split(lineBreak)

  let obj = {}
  let arr = [] as any
  let attributeName = [] as any
  if (!params_name || params_name.length === 0) {
    message.error('请选择参数名选项')
    return []
  }
  attributeName = params_name
  for (const i in formatter2) {
    const rowData = formatter2[i].split(',')

    if (formatter2[i].trim() == '') {
      continue
    }

    if (rowData.length !== attributeName.length) return message.error('请检查是否有多余空格或属性名数量不对')

    for (const valueIndex in rowData) {
      const errorMessage = 'article,价格,数量不能为/或-，请检查'
      if (rowData[valueIndex] == '/') {
        if (['article', 'price', 'quantity'].includes(attributeName[valueIndex])) {
          message.error(errorMessage)
          throw new Error(errorMessage)
        }
        obj[attributeName[valueIndex]] = null
      } else if (rowData[valueIndex] == '-') {
        if (['article', 'price', 'quantity'].includes(attributeName[valueIndex])) {
          message.error(errorMessage)
          throw new Error(errorMessage)
        }
        obj[attributeName[valueIndex]] = undefined
      } else {
        obj = cloneDeep(obj)
        obj[attributeName[valueIndex]] = rowData[valueIndex]
      }
    }
    arr.push(obj)
  }

  //先将price和quantity都转为数字类型
  arr = arr.map((item, index) => {
    item.quantity = Number(item.quantity)
    item.price = Number(item.price)
    item.exchange_rate = Number(item.exchange_rate)
    item.key = Date.now() + index
    return item
  })

  //校验价格和数量是否都是数字(我没校验是否大于等于0)
  const isAllNumber = arr.every((item) => {
    return !isNaN(item.quantity) && !isNaN(item.price)
  })
  if (!isAllNumber) {
    message.error('价格和数量必须为数字')
    return []
  }

  return arr
}
