import { h } from 'vue'
import { BasicColumn } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { isNullOrUnDef } from '/@/utils/is'

const pathname = window.location.pathname

const mapArticleRender = (qty_left) => {
  if (qty_left === false) {
    return 'red'
  } else if (qty_left === 0) {
    return 'yellow'
  } else if (qty_left > 0) {
    return 'green'
  }
}

export const columns: BasicColumn[] = [
  {
    dataIndex: 'article',
    title: 'article',
    width: 120,
    resizable: true,
    customRender: ({ value, record }) => {
      if (isNullOrUnDef(value)) return ''
      const { pass } = record
      return useRender.renderBadge(value, pass ? mapArticleRender(record.qty_left) : 'red')
    }
  },
  {
    dataIndex: 'quantity',
    title: '数量',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'price',
    title: '价格',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'qty_left',
    title: '可采购数',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      if (isNullOrUnDef(value)) return '-'
      return value
    }
  },
  {
    dataIndex: 'source_uniqid',
    title: '单号',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'replacement',
    title: '替换号',
    width: 120,
    resizable: true,
    edit: true,
    editDynamicDisabled({ record }) {
      return record.pass
    }
  },
  {
    dataIndex: 'comment',
    title: '备注',
    width: 150,
    resizable: true,
    customRender: ({ value }) => h('div', null, value)
  },
  {
    dataIndex: 'currency',
    title: '币别',
    width: 100,
    resizable: true,
    defaultHidden: !['/sp/', '/sptests/'].includes(pathname)
  },
  {
    dataIndex: 'exchange_rate',
    title: '汇率',
    width: 100,
    resizable: true,
    defaultHidden: !['/sp/', '/sptests/'].includes(pathname)
  }
]

export const needParams = columns.map((item) => item.dataIndex)

export function filterTableData(tableData) {
  return tableData.map((item) => {
    const newItem: Recordable = {}
    needParams.forEach((param) => {
      if (item.hasOwnProperty(param)) {
        newItem[param] = item[param]
      }
    })
    newItem.replacement === '' && (newItem.replacement = undefined)
    newItem.qty_left = undefined
    return newItem
  })
}

export function calcParams(tableData, formData) {
  return {
    brand: formData.brand,
    supplier_id: formData.supplier_id,
    client_id: formData.client_id,
    data: filterTableData(tableData)
  }
}
