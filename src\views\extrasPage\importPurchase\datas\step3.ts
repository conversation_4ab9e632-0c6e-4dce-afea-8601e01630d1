import { brandList } from './step1'
import { BasicColumn, FormSchema } from '/@/components/Table'
export const columns: BasicColumn[] = [
  {
    dataIndex: 'link',
    title: 'link'
  },
  {
    dataIndex: 'article',
    title: 'article'
  },
  {
    dataIndex: 'quantity',
    title: '数量'
  },
  {
    dataIndex: 'request_qty',
    title: '需求量'
  },
  {
    dataIndex: 'ex_qty',
    title: '超购数'
  },
  {
    dataIndex: 'stock_price',
    title: '价格' //采购价
  },
  {
    dataIndex: 'retail_price',
    title: '售价'
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'article',
    label: 'article',
    component: 'Input'
  },
  {
    field: 'link',
    label: 'link',
    component: 'Input'
  },
  {
    field: 'brand',
    label: 'brand',
    component: 'Select',
    componentProps: {
      options: brandList.map((item) => ({ label: item, value: item }))
    }
  }
]
