<template>
  <PageWrapper content-full-height>
    <div class="step-form-form">
      <Steps :current="current">
        <Step title="信息填写" />
        <Step title="检查后提交到草稿" />
        <Step title="提交草稿生成订单采购单" />
        <Step title="结果" />
      </Steps>
    </div>
    <div class="mt-10 flex justify-center">
      <Step1 @next="handleStep1Next" v-show="current === 0" />
      <Step2
        @prev="handleStepPrev"
        @next="handleStep2Next"
        v-show="current === 1"
        v-if="state.initStep2"
        :step1Values="step1Values"
        ref="Step2Ref"
      />
      <Step3 v-show="current === 2" @next="handleStep3Next" @prev="handleStepPrev" v-if="state.initStep3" ref="Step3Ref" />
      <Step4 v-show="current === 3" @redo="handleRedo" v-if="state.initStep4" ref="Step3Ref" />
    </div>
  </PageWrapper>
</template>
<script lang="ts" setup name="">
import { reactive, ref } from 'vue'
import { Steps, Step } from 'ant-design-vue'
import { PageWrapper } from '/@/components/Page'

import Step1 from './Step1.vue'
import Step2 from './Step2.vue'
import Step3 from './Step3.vue'
import Step4 from './Step4.vue'

const Step2Ref = ref()
const Step3Ref = ref()
const current = ref(0)
const state = reactive({
  initStep2: false,
  initStep3: false,
  initStep4: false
})

const step1Values = ref()
async function handleStep1Next(values: any) {
  try {
    step1Values.value = values
    current.value++
    //如果已经挂载一次了
    if (state.initStep2) {
      Step2Ref.value.setTableData(values.ResultItems)
    }
    state.initStep2 = true
  } catch (e) {
    console.error(e)
  }
}

function handleStepPrev() {
  current.value--
}

function handleStep2Next(step2Values: any) {
  current.value++
  if (state.initStep3) {
    Step3Ref.value.reload()
  }
  state.initStep3 = true
  console.log(step2Values)
}

function handleStep3Next() {
  current.value++
  state.initStep4 = true
}

function handleRedo() {
  current.value = 0
  state.initStep2 = false
  state.initStep3 = false
  state.initStep4 = false
}
</script>

<style scoped lang="less">
.step-form-form {
  width: 1000px;
  margin: 0 auto;
}
</style>
