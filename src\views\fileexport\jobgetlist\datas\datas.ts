import { h } from 'vue'
import { BasicColumn } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'

const statusmap = {
  Finish: { text: '已完成', color: 'green' },
  Runing: { text: '执行中', color: 'blue' },
  Failed: { text: '失败', color: 'red' },
  Pending: { text: '待执行', color: 'orange' }
}
const types = {
  1: { label: '采购待质检查询', color: 'blue' },
  2: { label: '库存待质检查询', color: '#1234' },
  3: { label: '付款单', color: '#125456' },
  4: { label: '包裹 (导入/新增/转换)', color: '#334523' }
}
const dealtype = {
  1: { label: '导出', color: 'blue' },
  2: { label: '导入', color: '#1234' }
}
export const columns: BasicColumn[] = [
  {
    title: '操作人名称',
    dataIndex: 'user_name',
    width: 150,
    resizable: true
  },
  {
    title: '操作类型',
    dataIndex: 'deal_type',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) || text === '' ? '-' : h(Tag, { color: dealtype[text].color }, dealtype[text].label)
    }
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) || text === '' ? '-' : h(Tag, { color: types[text].color }, types[text].label)
    }
  },
  {
    title: '文件目录',
    dataIndex: 'result',
    width: 350,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) || text === '' ? '-' : text
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: statusmap[text].color }, statusmap[text].text)
    }
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 150,
    resizable: true
  }
]
