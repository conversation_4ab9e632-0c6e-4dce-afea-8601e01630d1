<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script setup lang="ts">
import { message } from 'ant-design-vue'
import { columns } from './datas/datas'
import { jobgetList } from '/@/api/fileexport/jobgetlist'
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import { onMounted, onUnmounted, onBeforeUnmount, watch } from 'vue'

const props = defineProps({
  reloads: {
    type: Boolean,
    default: false
  }
})

watch(
  () => props.reloads,
  () => {
    reload()
  }
)

const [registerTable, { reload }] = useTable({
  api: jobgetList,
  showIndexColumn: false,
  showTableSetting: true,
  useSearchForm: false,
  title: '任务队列管理',
  bordered: true,
  columns,
  actionColumn: {
    width: 150,
    title: '下载',
    dataIndex: 'action',
    fixed: 'right'
  }
})

function createActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = [
    {
      icon: 'ant-design:download-outlined',
      // label: '',
      onClick: handleexport.bind(null, record),
      disabled: record.deal_type == 1 ? record.status !== 'Finish' : true
    }
  ]

  return editButtonList
}

async function handleexport(record: any) {
  try {
    // 从环境变量中获取API前缀
    const { VITE_GLOB_API_URL_PREFIX } = import.meta.env

    // 创建下载链接
    const downloadLink = document.createElement('a')

    // 使用环境变量中的API前缀与record.result拼接
    const fileUrl = `${VITE_GLOB_API_URL_PREFIX}${record.result}`

    // 设置下载链接
    downloadLink.href = fileUrl
    // downloadLink.download = `${record.source_uniqid || '导出'}产品明细.xlsx`
    downloadLink.target = '_blank' // 在新窗口打开

    // 模拟点击下载链接
    downloadLink.click()

    message.success('文件下载已开始')
  } catch (e) {
    console.error('下载失败:', e)
    message.error('下载失败')
  }
}

// 定义轮询定时器和间隔时间
let pollTimer: ReturnType<typeof setInterval> | null = null
const POLL_INTERVAL = 10000 // 10秒轮询一次

// 启动轮询函数
function startPolling() {
  // 清除可能存在的旧定时器
  stopPolling()

  // 设置新的定时器
  pollTimer = setInterval(() => {
    reload()
  }, POLL_INTERVAL)
}

// 停止轮询函数
function stopPolling() {
  if (pollTimer) {
    clearInterval(pollTimer)
    pollTimer = null
  }
}

// 组件挂载时启动轮询
onMounted(() => {
  startPolling()
})

// 组件卸载前进行清理工作
onBeforeUnmount(() => {
  // 停止轮询
  stopPolling()
  // 清理其他资源或引用
  console.log('组件即将销毁，清理资源')
})

// 组件卸载时停止轮询
onUnmounted(() => {
  stopPolling()
  console.log('组件已卸载')
})
</script>
