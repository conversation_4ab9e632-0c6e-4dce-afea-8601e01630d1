<template>
  <BasicDrawer @register="registerDrawer" width="90%" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleAdd">新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { invoicedetail, invoiceupdate } from '/@/api/financialDocuments/InvoiceManagement'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import { columns, schemas } from '../datas/edit.data'
import { cloneDeep } from 'lodash-es'
import { message, Button } from 'ant-design-vue'

const param_type = ref()
const invoiceData = ref<any>([])
//保存点击,其他禁用
const currentEditKeyRef = ref('')
const emit = defineEmits(['success', 'register'])

const [registerDrawer, { changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  console.log(data)
  param_type.value = data.type
  resetFields()
  setTableData([])
  if (data.type !== 'add') {
    const { items } = await invoicedetail({ id: data.record.id })
    invoiceData.value = items
    setFieldsValue(items)
    setTableData(items.items)
  }
})
const [registerForm, { setFieldsValue, validate, resetFields }] = useForm({
  baseColProps: { span: 8 },
  showActionButtonGroup: false,
  labelCol: { span: 6 },
  schemas
})
const [registerTable, { setTableData, deleteTableDataRecord, getDataSource, getColumns, updateTableDataRecord }] = useTable({
  showIndexColumn: false,
  title: '明细',
  columns,
  actionColumn: {
    width: 250,
    title: '操作',
    dataIndex: 'action'
  }
})

function createActions(record: EditRecordRow): ActionItem[] {
  if (!record.editable) {
    return [
      {
        label: '编辑',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        ifShow: record.is_check2 !== 2 && !record.is_cancel,
        onClick: handleEdit.bind(null, record)
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          placement: 'right',
          confirm: handleDelete.bind(null, record)
        },
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        // ifShow: record.is_check2 !== 2 && !record.is_cancel
        ifShow: record.is_check2 !== 2 && !record.is_cancel
      },
      {
        label: '复制',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        ifShow: record.is_check2 !== 2 && !record.is_cancel && record.is_check !== 2,
        // ifShow: !(record.is_check == 2) && record.is_check2 !== 2,
        onClick: handlecopylink.bind(null, record)
      }
    ]
  }
  return [
    {
      label: '保存',
      onClick: handleSave.bind(null, record)
    },
    {
      label: '取消',
      popConfirm: {
        title: '是否取消编辑',
        placement: 'right',
        confirm: handleCancel.bind(null, record)
      }
    }
  ]
}
// 存储编辑前的record
const beforeRecord = ref()
function handleEdit(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  beforeRecord.value = cloneDeep(record)
  record.onEdit?.(true, false)
}

//删除tabel
function handleDelete(record) {
  deleteTableDataRecord(record.key)
}

function handlecopylink(record) {
  const newrecord = formatObject(record) as any
  delete newrecord.id
  const newRowDataItem = {
    ...newrecord
  }
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
}
//格式化某一行的数据
function formatObject(record) {
  // getDataSource()有我们不需要的属性,所以需要清除
  let temporary = {}
  for (let colName of getColumns()) {
    if (colName.key !== 'action') {
      temporary[`${colName.dataIndex}`] = record[`${colName.dataIndex}`]
    }
  }
  return temporary
}
// 格式化提交的数据
function formatSubmit() {
  // getDataSource()有我们不需要的属性,所以需要清除
  const dataSource = getDataSource().map((item) => {
    let temporary = {}
    for (let colName of getColumns()) {
      if (colName.key !== 'action') {
        temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
      }
    }
    return temporary
  })
  return dataSource
}

//保存
async function handleSave(record: EditRecordRow) {
  // 校验
  const valid = await record.onValid?.()
  if (valid) {
    try {
      //TODO 此处将数据提交给服务器保存
      // ...
      // 保存之后提交编辑状态c
      // 检查除了business_type外的其他属性
      const Columnskey = formatObject(record)
      for (const key in Columnskey) {
        if (Columnskey.hasOwnProperty(key)) {
          // 如果不是business_type且值为空（或undefined）
          if (key !== 'business_type' && !Columnskey[key]) {
            message.error('除去特定业务类型 其他为必填,请检查是否完成填写')
            return
          }
        }
      }
      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
      }
      message.success({ content: '数据已保存', key: 'saving' })
      // meesg.value = false
    } catch (error) {
      message.error({ content: '保存失败', key: 'saving' })
      throw new Error(`${error}`)
    }
  } else {
    message.error({ content: '请填写正确的数据', key: 'saving' })
  }
}
//取消
function handleCancel(record: EditRecordRow) {
  currentEditKeyRef.value = ''
  updateTableDataRecord(record.key, {
    ...beforeRecord.value
  })
  record.onEdit?.(false, false)
}

//添加明细
async function handleAdd() {
  const newRowDataItem = {
    parent_strid: null,
    source_uniqid: null,
    account_name: null
  }
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
}

//提交
async function handleSubmit() {
  try {
    changeOkLoading(true)
    const formdata = await validate()
    const Tabledata: any = await formatSubmit()
    if (!formdata.id) {
      delete formdata.id
    }
    if (Tabledata.length === 0) {
      message.error('明细为空，请添加明细')
      changeOkLoading(false)
      return
    }
    if (currentEditKeyRef.value) {
      message.error('请先保存数据')
      changeOkLoading(false)
      return
    }
    for (let item of Tabledata) {
      if (!item.amount) {
        message.error('明细不能为空')
        throw new Error('请填写完整信息,不能提交空明细')
      }
    }
    invoiceupdate({ invoiceList: [{ doc: { ...formdata }, items: Tabledata }] })
    setTimeout(() => {
      changeOkLoading(false)
      closeDrawer()
      emit('success')
    }, 1000)
  } catch (error) {
    console.log(error)
    changeOkLoading(false)
  }
}
</script>
