<template>
  <BasicDrawer @register="registerDrawer" width="90%" @ok="handleSubmit" show-footer>
    <BasicForm @register="registerForm" @field-value-change="fieldvaluechange" />
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
        <template v-if="column.key === 'fdoc_name'">
          <div @click="handleskip(record)" style="color: blue; cursor: pointer; text-decoration: underline">{{ record.fdoc_name }}</div>
        </template>
      </template>
      <template #headerCell="{ column }">
        <template v-if="column.dataIndex === 'detail'">
          <Popover trigger="click" title="批量选择已勾选产品的所在仓库">
            <template #content>
              <Select
                style="width: 188px"
                :options="option"
                v-model:value="detailid"
                optionFilterProp="label"
                show-search
                allow-clear
                placeholder="请选择已勾选产品的所在仓库"
                @change="
                  (values, shall) => {
                    detailidobj = shall
                  }
                "
              />
              <Button style="margin-left: 5px" type="primary" @click="handleBatchEdit">确定</Button>
            </template>
            <span style="margin-right: 10px">
              {{ column.customTitle }}
              <EditOutlined />
            </span>
          </Popover>
        </template>
        <template v-else>{{ column.customTitle }}</template>
      </template>
    </BasicTable>
  </BasicDrawer>
  <DetailsDrawer @register="registerDetailsDrawer" />
</template>
<script setup lang="ts">
import { BasicDrawer, useDrawer, useDrawerInner } from '/@/components/Drawer'
import { ActionItem, BasicTable, EditRecordRow, useTable, TableAction } from '/@/components/Table'
import { BasicForm, useForm } from '/@/components/Form'
import { columns, schemas } from '../datas/relevanceedit'
import { stockgetList } from '/@/api/erp/inventory'
import { ref } from 'vue'
import { cloneDeep } from 'lodash-es'
import { message, Popover, Button, Select } from 'ant-design-vue'
import { invoicebind, invoicebindDetail, invoicedetail } from '/@/api/financialDocuments/InvoiceManagement'
import { EditOutlined } from '@ant-design/icons-vue'
import DetailsDrawer from '../../paymentOrder/components/DetailsDrawer.vue'

//保存点击,其他禁用
const currentEditKeyRef = ref('')
const option = ref([])
const detailid = ref()
const detailidobj = ref()
const invoice_id = ref()
const fdoc_ids = ref<any>([])
const bind_detail = ref([])
const emit = defineEmits(['success', 'register'])
const [registerDetailsDrawer, { openDrawer: openDetailsDrawer, setDrawerProps: setDetailsDrawerProps }] = useDrawer()

const [registerDrawer, { changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  console.log(data)
  resetFields()
  setTableData([])
  fdoc_ids.value = []
  bind_detail.value = []
  option.value = []
  invoice_id.value = data.record.id
  if (data.record.is_bind == 1) {
    const { items } = await invoicebindDetail({ id: data.record.id })
    bind_detail.value = items
    const arrays = ref<any>([])
    const arraysid = ref<any>([])
    arrays.value = []
    for (const item of items.doc) {
      arrays.value.push(item.doc_fund)
      arraysid.value.push(item.doc_fund.strid)
      fdoc_ids.value.push(item.doc_fund.id)
    }
    const arraystable = items.item.map((item) => {
      return {
        fdoc_name: item.doc_fund.strid,
        header_strid: item.header.strid,
        name: item.item_stocking.name,
        quantity: item.item_stocking.qty_received - item.item_stocking.qty_return,
        qty_received: item.item_stocking.qty_received,
        qty_return: item.item_stocking.qty_return,
        qc_status: item.item_stocking.qc_status,
        detail: item.item_invoice.tax_code,
        detil_bind_quantity: item.item_invoice.quantity,
        bind_quantity: item.quantity,
        item_stocking_id: item.item_stocking_id,
        doc_in_warehouse_header_id: item.doc_in_warehouse_header_id,
        fdoc_id: item.doc_fund.id,
        item_invoice_id: item.item_invoice_id
      }
    })
    updateSchema({
      field: 'id',
      componentProps: {
        defineOptions: arrays.value
      }
    })
    setFieldsValue({
      id: arraysid.value
    })
    setTableData(arraystable)
  } else {
  }
  const { items } = await invoicedetail({ id: data.record.id })
  option.value = items.items.map((item) => {
    return {
      label: item.tax_code,
      value: item.id,
      quantity: item.quantity
    }
  })
  setColumns(columns(option.value))
})

const [registerForm, { resetFields, setFieldsValue, updateSchema }] = useForm({
  baseColProps: { span: 8 },
  showActionButtonGroup: false,
  labelCol: { span: 6 },
  schemas
})
const [
  registerTable,
  { getSelectRows, setLoading, getDataSource, setTableData, deleteTableDataRecord, getColumns, updateTableDataRecord, setColumns }
] = useTable({
  showIndexColumn: false,
  title: '明细',
  columns: columns(),
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action'
  },
  rowSelection: {}
})

async function fieldvaluechange(key, value) {
  if (value.length == 0) {
    setTableData([])
    return
  }
  fdoc_ids.value = value
  setLoading(true)
  const { items } = await stockgetList({ fdoc_ids: value, pageSize: 999 })
  const newdata = items.map((item) => {
    return {
      fdoc_name: item.doc_fund_strid[0].strid,
      fdoc_id: item.doc_fund_strid[0].id,
      header_strid: item.header_strid,
      doc_in_warehouse_header_id: item.doc_in_warehouse_header_id,
      name: item.name,
      quantity: item.qty_received - item.qty_return,
      qc_status: item.qc_status,
      item_stocking_id: item.id,
      qty_received: item.qty_received,
      qty_return: item.qty_return
    }
  })
  setLoading(false)
  setTableData(newdata)
}

function createActions(record: EditRecordRow): ActionItem[] {
  if (!record.editable) {
    return [
      {
        label: '编辑',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        // ifShow: record.is_check2 !== 2 && !record.is_cancel,
        onClick: handleEdit.bind(null, record)
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          placement: 'right',
          confirm: handleDelete.bind(null, record)
        },
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false
        // ifShow: record.is_check2 !== 2 && !record.is_cancel
        // ifShow: record.is_check2 !== 2 && !record.is_cancel
      }
    ]
  }
  return [
    {
      label: '保存',
      onClick: handleSave.bind(null, record)
    },
    {
      label: '取消',
      popConfirm: {
        title: '是否取消编辑',
        placement: 'right',
        confirm: handleCancel.bind(null, record)
      }
    }
  ]
}

// 存储编辑前的record
const beforeRecord = ref()
function handleEdit(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  beforeRecord.value = cloneDeep(record)
  record.onEdit?.(true, false)
}

//删除tabel
function handleDelete(record) {
  deleteTableDataRecord(record.key)
}

//格式化某一行的数据
function formatObject(record) {
  // getDataSource()有我们不需要的属性,所以需要清除
  let temporary = {}
  for (let colName of getColumns()) {
    if (colName.key !== 'action') {
      temporary[`${colName.dataIndex}`] = record[`${colName.dataIndex}`]
    }
  }
  return temporary
}
// 格式化提交的数据
function formatSubmit() {
  // getDataSource()有我们不需要的属性,所以需要清除
  const dataSource = getDataSource().map((item) => {
    let temporary = {}
    for (let colName of getColumns()) {
      if (colName.key !== 'action') {
        temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
      }
    }
    return temporary
  })
  return dataSource
}
//保存
async function handleSave(record: EditRecordRow) {
  // 校验
  const valid = await record.onValid?.()
  if (valid) {
    try {
      //TODO 此处将数据提交给服务器保存
      // ...
      // 保存之后提交编辑状态c
      // 检查除了business_type外的其他属性
      const Columnskey: any = formatObject(record)
      if (!Columnskey.item_invoice_id) {
        message.error('请选择发票明细')
        return
      }
      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
      }
      message.success({ content: '数据已保存', key: 'saving' })
      // meesg.value = false
    } catch (error) {
      message.error({ content: '保存失败', key: 'saving' })
      throw new Error(`${error}`)
    }
  } else {
    message.error({ content: '请填写正确的数据', key: 'saving' })
  }
}

//计算
function checkQuantities(items) {
  const quantitiesSum = {}
  const bindQuantitiesSum = {}

  items.forEach((item) => {
    if (quantitiesSum[item.item_invoice_id]) {
      quantitiesSum[item.item_invoice_id] += Number(item.quantity)
      bindQuantitiesSum[item.item_invoice_id] += Number(item.bind_quantity)
    } else {
      quantitiesSum[item.item_invoice_id] = Number(item.quantity)
      bindQuantitiesSum[item.item_invoice_id] = Number(item.bind_quantity)
    }
  })
  console.log(quantitiesSum, bindQuantitiesSum)

  return Object.keys(quantitiesSum).every((key) => quantitiesSum[key] === bindQuantitiesSum[key])
}
//取消
function handleCancel(record: EditRecordRow) {
  currentEditKeyRef.value = ''
  updateTableDataRecord(record.key, {
    ...beforeRecord.value
  })
  record.onEdit?.(false, false)
}

async function handleSubmit() {
  const dataSource: any = formatSubmit()
  for (const item of dataSource) {
    if (!item.item_invoice_id) return message.error('不能保留未选择发票明细的库存商品')
    if (Number(item.quantity) !== Number(item.bind_quantity)) return message.error('库存商品数量与发票明细数量不匹配')
  }
  const quantityshow = checkQuantities(dataSource)
  if (quantityshow == false) return message.error('库存商品数量与发票明细数量不匹配')
  const params = {
    id: invoice_id.value,
    fdoc_ids: fdoc_ids.value,
    bindList: dataSource.map((item) => {
      return {
        item_stocking_id: item.item_stocking_id,
        item_invoice_id: item.item_invoice_id,
        // bind_quantity: item.bind_quantity,
        doc_in_warehouse_header_id: item.doc_in_warehouse_header_id,
        fdoc_id: item.fdoc_id,
        quantity: Number(item.bind_quantity)
      }
    })
  }
  try {
    changeOkLoading(true)
    const { msg } = await invoicebind(params)
    if (msg === 'success') {
      setTimeout(() => {
        changeOkLoading(false)
        closeDrawer()
        emit('success')
      }, 1000)
    } else {
      changeOkLoading(false)
    }
  } catch (e) {
    changeOkLoading(false)
    console.log(e)
  }
}

//批量
function handleBatchEdit() {
  const keys = getSelectRows()
  const tabledata = getDataSource()
  if (keys.length == 0) return message.error('请选择库存商品')
  for (const item of keys) {
    item.detail = detailidobj.value.label
    item.item_invoice_id = detailidobj.value.value
    item.bind_quantity = detailidobj.value.quantity
    item.detil_bind_quantity = detailidobj.value.quantity
  }
  const result = mergeArraysByUniqueID(tabledata, keys)

  setTableData(result)
}

// 合并函数
function mergeArraysByUniqueID(oldArray, newArray) {
  const mergedArray: any = []

  // 创建一个映射来保存新数组中的元素
  const newMap = new Map(newArray.map((item) => [item.item_stocking_id, item]))

  // 遍历旧数组
  oldArray.forEach((oldItem) => {
    // 检查新数组中是否有对应 ID 的元素
    if (newMap.has(oldItem.item_stocking_id)) {
      // 替换旧元素
      mergedArray.push(newMap.get(oldItem.item_stocking_id))
    } else {
      // 如果新数组中没有，则保留旧元素
      mergedArray.push(oldItem)
    }
  })

  return mergedArray
}

function handleskip(record) {
  setDetailsDrawerProps({ title: '付款单详情' })
  openDetailsDrawer(true, {
    id: record.fdoc_id,
    showProjectList: true
  })
}
</script>
