export function transformData2Import(data: Recordable[]): any[] {
  const fieldMap = {
    pur_strid: '采购单号 ',
    invoice_code: '发票代码',
    invoice_number: '发票号码',
    digital_ticket_number: '数电票号码',
    seller_number: '销方识别号',
    seller_name: '销方名称',
    buy_number: '购方识别号',
    buy_name: '购买方名称',
    invoice_at: '开票日期',
    invoice_source: '发票来源',
    invoice_type: '发票票种',
    invoice_status: '发票状态',
    is_positive: '是否正数发票',
    invoice_level: '发票风险等级',
    invoice_inCharge: '开票人',
    remark: '备注',
    tax_code: '税收分类编码',
    business_type: '特定业务类型',
    tax_service_name: '货物或应税劳务名称',
    size: '规格型号',
    unit: '单位',
    quantity: '数量',
    unit_price: '单价',
    amount: '金额',
    tax_rate: '税率',
    tax_amount: '税额',
    tax_total_amount: '价税合计'
  }

  const tabledata = data.map((obj) => {
    const cookedData: any = {
      pur_strid: '',
      invoice_code: '',
      invoice_number: '',
      digital_ticket_number: '',
      seller_number: '',
      seller_name: '',
      invoice_inCharge: '',
      buy_number: '',
      buy_name: '',
      invoice_at: '',
      invoice_source: '',
      invoice_type: '',
      invoice_status: '',
      is_positive: '',
      invoice_level: '',
      remark: '',
      tax_code: '',
      business_type: '',
      tax_service_name: '',
      size: '',
      unit: '',
      quantity: undefined,
      unit_price: undefined,
      amount: undefined,
      tax_rate: undefined,
      tax_amount: undefined,
      tax_total_amount: ''
    }
    // ['occurrence_at', 'to_plaform', 'from_plaform'].includes(key)
    //       ? obj[fieldMap[key]].toString().trim()
    //       :
    for (const key in cookedData) {
      if (fieldMap[key]) {
        cookedData[key] = obj[fieldMap[key]]
      }
    }
    cookedData.invoice_status = cookedData.invoice_status === '正常' ? 0 : 1
    cookedData.is_positive = cookedData.is_positive === '否' ? 0 : 1
    const invoiceLevelMap = {
      正常: 1,
      危险: 2,
      默认: 3
    }
    cookedData.invoice_level = invoiceLevelMap[cookedData.invoice_level]

    return cookedData
  })

  // 创建一个对象来存储合并后的发票数据
  const mergedInvoices = {}

  // 遍历每个发票数据对象
  tabledata.forEach((invoice) => {
    // 提取发票的主要信息
    const doc = {
      invoice_code: invoice.invoice_code.toString().trim(),
      invoice_number: invoice.invoice_number.toString().trim(),
      digital_ticket_number: invoice.digital_ticket_number.toString().trim(),
      seller_number: invoice.seller_number.toString().trim(),
      seller_name: invoice.seller_name.toString().trim(),
      invoice_inCharge: invoice.invoice_inCharge.toString().trim(),
      buy_number: invoice.buy_number.toString().trim(),
      buy_name: invoice.buy_name.toString().trim(),
      invoice_at: invoice.invoice_at.toString().trim(),
      invoice_source: invoice.invoice_source.toString().trim(),
      invoice_type: invoice.invoice_type.toString().trim(),
      invoice_status: invoice.invoice_status.toString().trim(),
      is_positive: invoice.is_positive.toString().trim(),
      invoice_level: invoice.invoice_level.toString().trim(),
      remark: invoice.remark.toString().trim(),
      pur_strid: invoice.pur_strid.toString().trim()
    }

    // 提取发票的项目信息
    const items = [
      {
        tax_code: invoice.tax_code.toString().trim(),
        business_type: invoice.business_type.toString().trim(),
        tax_service_name: invoice.tax_service_name.toString().trim(),
        size: invoice.size.toString().trim(),
        unit: invoice.unit.toString().trim(),
        quantity: Number(invoice.quantity),
        unit_price: Number(invoice.unit_price),
        amount: Number(invoice.amount),
        tax_rate: Number(invoice.tax_rate),
        tax_amount: Number(invoice.tax_amount),
        tax_total_amount: invoice.tax_total_amount
      }
    ]

    // 使用 digital_ticket_number 或 invoice_number 作为唯一标识符
    const key = invoice.digital_ticket_number || invoice.invoice_number

    // 如果已有该发票，则合并项目信息
    if (mergedInvoices[key]) {
      mergedInvoices[key].items.push(...items)
    } else {
      mergedInvoices[key] = {
        doc: doc,
        items: items
      }
    }
  })

  // 将合并后的发票数据转换成数组形式
  const result = Object.values(mergedInvoices)
  console.log(result)

  return result
}
