import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { Tag } from 'ant-design-vue'
import { getStaffList } from '/@/api/erp/systemInfo'
import { GET_STATUS_SCHEMA } from '/@/const/status'

export const columns: BasicColumn[] = [
  {
    title: '发票代码',
    dataIndex: 'invoice_code',
    width: 150,
    resizable: true
  },
  {
    title: '发票号码',
    dataIndex: 'invoice_number',
    width: 150,
    resizable: true
  },
  {
    title: '采购单号',
    dataIndex: 'pur_strid',
    width: 250,
    resizable: true
  },
  {
    title: '数电票号码',
    dataIndex: 'digital_ticket_number',
    width: 150,
    resizable: true
  },
  {
    title: '销方识别号',
    dataIndex: 'seller_number',
    width: 150,
    resizable: true
  },
  {
    title: '销方名称',
    dataIndex: 'seller_name',
    width: 150,
    resizable: true
  },
  {
    title: '购方识别号',
    dataIndex: 'buy_number',
    width: 150,
    resizable: true
  },
  {
    title: '购买方名称',
    dataIndex: 'buy_name',
    width: 150,
    resizable: true
  },
  {
    title: '开票时间',
    dataIndex: 'invoice_at',
    width: 150,
    resizable: true
  },
  {
    title: '发票来源',
    dataIndex: 'invoice_source',
    width: 150,
    resizable: true
  },
  {
    title: '发票票种',
    dataIndex: 'invoice_type',
    width: 150,
    resizable: true
  },
  {
    title: '审核状态',
    dataIndex: 'status',
    width: 150,
    resizable: true,
    customRender({ value }) {
      const map = {
        0: { text: '未审核', color: '' },
        15: { text: '已完成', color: 'green' }
      }
      return h(Tag, { color: map[value].color }, map[value].text)
    }
  },
  {
    title: '是否绑定',
    dataIndex: 'is_bind',
    width: 150,
    resizable: true,
    customRender({ value }) {
      const map = {
        0: { text: '否', color: '' },
        1: { text: '是', color: 'green' }
      }
      return h(Tag, { color: map[value].color }, map[value].text)
    }
  },
  {
    title: '财务审核状态',
    dataIndex: 'check_status',
    width: 150,
    resizable: true,
    customRender({ value }) {
      const map = {
        0: { text: '未审核', color: '' },
        1: { text: '已审核', color: 'green' }
      }
      return h(Tag, { color: map[value].color }, map[value].text)
    }
  },
  {
    title: '是否作废',
    dataIndex: 'is_cancel',
    width: 150,
    resizable: true,
    customRender({ value }) {
      const map = {
        0: { text: '否', color: '' },
        1: { text: '是', color: 'green' }
      }
      return h(Tag, { color: map[value].color }, map[value].text)
    }
  },
  {
    title: '发票状态',
    dataIndex: 'invoice_status',
    width: 150,
    resizable: true,
    customRender({ value }) {
      const map = {
        0: { text: '正常', color: 'green' },
        1: { text: '已红冲-全额', color: 'red' }
      }
      return h(Tag, { color: map[value].color }, map[value].text)
    }
  },
  {
    title: '发票风险等级',
    dataIndex: 'invoice_level',
    width: 150,
    resizable: true,
    customRender({ value }) {
      const map = {
        1: { text: '正常', color: 'green' },
        2: { text: '危险', color: 'orange' },
        3: { text: '非常危险', color: 'red' }
      }
      return h(Tag, { color: map[value].color }, map[value].text)
    }
  },
  {
    title: '是否正数发票',
    dataIndex: 'is_positive',
    width: 150,
    resizable: true,
    customRender({ value }) {
      const map = {
        0: { text: '否', color: '#ccc' },
        1: { text: '是', color: 'green' }
      }
      return h(Tag, { color: map[value].color }, map[value].text)
    }
  },
  {
    title: '开票人',
    dataIndex: 'invoice_inCharge',
    width: 150,
    resizable: true
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 150,
    resizable: true
  },
  {
    title: '作废人',
    dataIndex: 'cancel_inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '作废时间',
    dataIndex: 'cancel_at',
    width: 150,
    resizable: true
  },
  {
    title: '审核时间',
    dataIndex: 'status_at',
    width: 150,
    resizable: true
  },
  {
    title: '财务审核时间',
    dataIndex: 'check_at',
    width: 150,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 150,
    resizable: true
  },
  {
    title: '作废备注',
    dataIndex: 'cancel_remark',
    width: 150,
    resizable: true
  }
]

const status_schema = GET_STATUS_SCHEMA(
  [
    { label: '正常', value: 0 },
    { label: '已红冲-全额', value: 1 }
  ],
  {
    field: 'invoice_status'
  }
)

export const feachSchemas: FormSchema[] = [
  status_schema,
  {
    field: 'invoice_code',
    label: '发票代码',
    component: 'Input'
  },
  {
    field: 'invoice_number',
    label: '发票号码',
    component: 'Input'
  },
  {
    field: 'digital_ticket_number',
    label: '数电票号码',
    component: 'Input'
  },
  {
    field: 'seller_number',
    label: '销方识别号',
    component: 'Input'
  },
  {
    field: 'seller_name',
    label: '销方名称',
    component: 'Input'
  },
  {
    field: 'buy_number',
    label: '购方识别号',
    component: 'Input'
  },
  {
    field: 'buy_name',
    label: '购买方名称',
    component: 'Input'
  },
  {
    field: 'invoice_at',
    label: '开票时间',
    component: 'SingleRangeDate',
    componentProps: {
      allowEmpty: [true, true],
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  },
  {
    field: 'invoice_source',
    label: '发票来源',
    component: 'Input'
  },
  {
    field: 'invoice_type',
    label: '发票票种',
    component: 'Input'
  },
  {
    field: 'status',
    label: '审核状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未审核', value: 0 },
        { label: '已完成', value: 15 }
      ]
    }
  },
  {
    field: 'is_cancel',
    label: '是否作废',
    component: 'Select',
    componentProps: {
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ]
    }
  },
  // {
  //   field: 'invoice_status',
  //   label: '发票状态',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: '正常', value: 0 },
  //       { label: '已红冲-全额', value: 1 }
  //     ]
  //   }
  // },
  {
    field: 'invoice_level',
    label: '发票风险等级',
    component: 'Select',
    componentProps: {
      options: [
        { label: '正常', value: 1 },
        { label: '危险', value: 2 },
        { label: '非常危险', value: 3 }
      ]
    }
  },
  {
    field: 'is_positive',
    label: '是否正数发票',
    component: 'Select',
    componentProps: {
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ]
    }
  },
  {
    field: 'invoice_inCharge',
    label: '开票人',
    component: 'Input'
  },
  {
    field: 'creator',
    label: '创建人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'remark',
    label: '备注',
    component: 'Input'
  },
  {
    field: 'cancel_remark',
    label: '作废备注',
    component: 'Input'
  }
]

export const excelHeader = [
  '采购单号 ',
  '发票代码',
  '发票号码',
  '数电票号码',
  '销方识别号',
  '销方名称',
  '购方识别号',
  '购买方名称',
  '开票日期',
  '发票来源',
  '发票票种',
  '发票状态',
  '是否正数发票',
  '发票风险等级',
  '开票人',
  '备注',
  '税收分类编码',
  '特定业务类型',
  '货物或应税劳务名称',
  '规格型号',
  '单位',
  '数量',
  '单价',
  '金额',
  '税率',
  '税额',
  '价税合计'
]
