import { h, ref, withDirectives } from 'vue'
import { getRelatePurchaseList } from '/@/api/erp/inWarehouse'
import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import loadingDirective from '/@/directives/loading'
export const loading = ref(false)

export const schemas: FormSchema[] = [
  {
    field: 'invoice_code',
    label: '发票代码',
    component: 'Input'
  },
  {
    field: 'invoice_number',
    label: '发票号码',
    component: 'Input'
  },
  {
    field: 'pur_strid',
    label: '关联采购订单',
    component: 'PagingApiSelect',
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: ({ formModel }) => ({
      api: getRelatePurchaseList,
      params: {
        is_auth_status: 1
      },
      searchParamField: 'strid',
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { key: 'id', value: 'strid', label: 'strid' },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        dropdownRender: ({ menuNode }) => {
          const vNode = h('div', {}, menuNode)
          return withDirectives(vNode, [[loadingDirective, loading.value]])
        },
        onChange: (_, shall) => {
          formModel.pur_id = shall ? shall.id : undefined
        }
      },
      pagingMode: true,
      searchMode: true,
      resultField: 'items'
    })
  },
  {
    field: 'digital_ticket_number',
    label: '数电票号码',
    component: 'Input',
    required: true
  },
  {
    field: 'seller_number',
    label: '销方识别号',
    component: 'Input',
    required: true
  },
  {
    field: 'seller_name',
    label: '销方名称',
    component: 'Input',
    required: true
  },
  {
    field: 'buy_number',
    label: '购方识别号',
    component: 'Input',
    required: true
  },
  {
    field: 'buy_name',
    label: '购买方名称',
    component: 'Input',
    required: true
  },
  {
    field: 'invoice_at',
    label: '开票时间',
    component: 'DatePicker',
    required: true,
    componentProps: {
      style: {
        width: '100%'
      },
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'invoice_source',
    label: '发票来源',
    component: 'Input'
  },
  {
    field: 'invoice_type',
    label: '发票票种',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '专用发票',
          value: '专用发票'
        },
        {
          label: '普通发票',
          value: '普通发票'
        }
      ]
    },
    required: true
  },
  {
    field: 'invoice_status',
    label: '发票状态',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '正常',
          value: 0
        },
        {
          label: '已红冲-全额',
          value: 1
        }
      ]
    },
    required: true
  },
  {
    field: 'invoice_level',
    label: '发票风险等级',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '正常',
          value: 1
        },
        {
          label: '危险',
          value: 2
        },
        {
          label: '非常危险',
          value: 3
        }
      ]
    },
    required: true
  },
  {
    field: 'is_positive',
    label: '是否正数发票',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '否',
          value: 0
        },
        {
          label: '是',
          value: 1
        }
      ]
    },
    required: true
  },
  {
    field: 'invoice_inCharge',
    label: '开票人',
    component: 'Input',
    required: true
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea'
  },

  {
    field: 'id',
    label: 'id',
    component: 'InputTextArea',
    show: false
  },
  {
    field: 'pur_id',
    label: 'pur_id',
    component: 'InputTextArea',
    show: false
  }
]

export const columns: BasicColumn[] = [
  {
    title: '税收分类编码',
    dataIndex: 'tax_code',
    width: 100,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },
  {
    title: '特定业务类型',
    dataIndex: 'business_type',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },
  {
    title: '货物或应税劳务名称',
    dataIndex: 'tax_service_name',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },
  {
    title: '规格型号',
    dataIndex: 'size',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      min: 0,
      precision: 2
    }
  },
  {
    title: '单价',
    dataIndex: 'unit_price',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      min: 0,
      precision: 2
    }
  },
  {
    title: '金额',
    dataIndex: 'amount',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      min: 0,
      precision: 2
    }
  },
  {
    title: '税率',
    dataIndex: 'tax_rate',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      min: 0,
      precision: 2
    }
  },
  {
    title: '税额',
    dataIndex: 'tax_amount',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      min: 0,
      precision: 2
    }
  },
  {
    title: '价税合计',
    dataIndex: 'tax_total_amount',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      min: 0,
      precision: 2
    }
  }
]
