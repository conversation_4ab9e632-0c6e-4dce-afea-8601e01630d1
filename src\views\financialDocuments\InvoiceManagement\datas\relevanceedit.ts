import { isNull } from 'lodash-es'
import { getPaymentOrderList } from '/@/api/financialDocuments/paymentOrder'
import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { useI18n } from '/@/hooks/web/useI18n'

const { t } = useI18n()

export const schemas: FormSchema[] = [
  {
    field: 'id',
    label: '关联付款单',
    component: 'PagingApiSelect',
    componentProps: {
      api: getPaymentOrderList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      searchParamField: 'strid',
      params: {
        is_have_doc_in_warehouse_header_id: 1,
        status: 2,
        pageSize: 999
      },
      selectProps: {
        mode: 'multiple',
        allowClear: true,
        fieldNames: { value: 'id', label: 'strid' },
        showSearch: true,
        placeholder: '请选择采购订单',
        optionFilterProp: 'strid',
        onChange: (val: any) => {
          if (val.length == 0) return
        }
      }
    }
  }
]

export const columns = (options?): BasicColumn[] => [
  {
    title: '关联付款单',
    dataIndex: 'fdoc_name',
    width: 150,
    resizable: true
  },
  {
    title: '主入库单',
    dataIndex: 'header_strid',
    width: 150,
    resizable: true
  },
  {
    title: '库存商品名',
    dataIndex: 'name',
    width: 150,
    resizable: true
  },
  {
    title: '可绑定库存数量',
    dataIndex: 'quantity',
    width: 150,
    resizable: true
  },
  {
    title: '实际入库数量',
    dataIndex: 'qty_received',
    width: 150,
    resizable: true
  },
  {
    title: '退货数量',
    dataIndex: 'qty_return',
    width: 150,
    resizable: true
  },
  {
    title: '质检状态',
    dataIndex: 'qc_status',
    width: 100,
    customRender: ({ text }) =>
      !isNull(text) ? useRender.renderTag(t(`tag.qcStockTag.${text}.label`), t(`tag.qcStockTag.${text}.color`)) : '-'
  },
  {
    title: '发票明细',
    dataIndex: 'detail',
    editRow: true,
    width: 150,
    editComponent: 'Select',
    editComponentProps: ({ record }) => {
      return {
        options: options,
        showSearch: true,
        allowClear: true,
        optionFilterProp: 'label',
        onChange: (value, shall) => {
          record.item_invoice_id = value
          record.bind_quantity = shall.quantity
          record.detil_bind_quantity = shall.quantity
        }
      }
    }
  },
  {
    title: '明细可绑定数量',
    dataIndex: 'detil_bind_quantity',
    width: 150
  },
  {
    title: '绑定数量',
    dataIndex: 'bind_quantity',
    editRow: true,
    width: 150,
    editComponent: 'InputNumber',
    editComponentProps: ({ record }) => {
      return {
        min: 0,
        max: record.detil_bind_quantity,
        precision: 2
      }
    }
  },
  {
    title: 'item_stocking_id',
    dataIndex: 'item_stocking_id',
    ifShow: false
  },
  {
    title: 'doc_in_warehouse_header_id',
    dataIndex: 'doc_in_warehouse_header_id',
    ifShow: false
  },
  {
    title: 'fdoc_id',
    dataIndex: 'fdoc_id',
    ifShow: false
  },
  {
    title: 'item_invoice_id',
    dataIndex: 'item_invoice_id',
    ifShow: false
  }
]
