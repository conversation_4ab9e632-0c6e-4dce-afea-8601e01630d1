<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" v-if="hasPermission([531])" @click="handleexamines">批量审核</Button>
        <Button type="primary" v-if="hasPermission([540])" @click="handlefian">批量财务审核</Button>
        <!-- <Button type="primary" v-if="hasPermission([532])" @click="handleOpenImpExcelModal">发票导入</Button> -->
        <Dropdown>
          <a class="ant-dropdown-link" @click.prevent>
            <a-button type="primary">
              <template #icon><download-outlined /> </template>发票导入 <download-outlined />
            </a-button>
          </a>
          <template #overlay>
            <Menu @click="handleMenuClick">
              <MenuItem key="upload"><upload-outlined /> 发票导入</MenuItem>
              <MenuItem key="export"><download-outlined />模板</MenuItem>
            </Menu>
          </template>
        </Dropdown>
        <Button type="primary" v-if="hasPermission([533])" @click="handleAdd">发票新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" :stopButtonPropagation="true" />
        </template>
      </template>
    </BasicTable>
    <ImpExcelModal @register="registerUploadModal" :dataCallBackFn="handleUploadData" ref="ImpExcelModalRef" />
    <editDrawer @register="registerDrawer" @success="reload" />
    <relevanceedit @register="registerrelevance" @success="reload" />
  </div>
</template>
<script setup lang="tsx">
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import { Button, Form, message, Textarea, Dropdown, Menu, MenuItem } from 'ant-design-vue'
import { ImpExcelModal } from '/@/components/Excel'
import { useModal } from '/@/components/Modal'
import { ref } from 'vue'
import { transformData2Import } from './datas/Upexcel'
import {
  invoicegetList,
  invoicesetCheckStatus,
  invoicesetIsCancel,
  invoicesetStatus,
  invoiceupdate
} from '/@/api/financialDocuments/InvoiceManagement'
import { columns, feachSchemas, excelHeader } from './datas/data'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { usePermission } from '/@/hooks/web/usePermission'
import relevanceedit from './components/relevanceedit.vue'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'
import { onExpExcelTemplate } from '/@/utils/exportTemplate'
import { UploadOutlined, DownloadOutlined } from '@ant-design/icons-vue'

const FormItem = Form.Item
const cancel_remark = ref('')
const { hasPermission } = usePermission()

const [registerTable, { reload, clearSelectedRowKeys, getSelectRows }] = useTable({
  title: '发票管理',
  api: invoicegetList,
  columns,
  showTableSetting: true,
  rowKey: 'id',
  showIndexColumn: true,
  useSearchForm: true,
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    //自动展开行
    fieldMapToTime: [['invoice_at', ['invoice_at_start', 'invoice_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]],
    schemas: feachSchemas
  },
  rowSelection: {}
})

const [registerUploadModal, { openModal: openImpExcelModal }] = useModal()

function handleMenuClick({ key }) {
  if (key === 'export') {
    onExpExcelTemplate(excelHeader, true, '发票导入模板')
  } else if (key === 'upload') {
    openImpExcelModal(true, {
      sheetName: 'Sheet1',
      headerRow: 1,
      startCell: 'A2',
      endCell: `AA20000`
    })
  }
}

function createActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = [
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleUpdate.bind(null, record),
      disabled: record.is_cancel == 1 || record.status === 15,
      ifShow: hasPermission([534])
    },
    {
      label: '发票绑定',
      onClick: handlerelevance.bind(null, record, 'add'),
      disabled: record.is_cancel == 1 || record.status === 1 || record.check_status === 1,
      // disabled: record.status !== 0,
      ifShow: hasPermission([541])
    }
  ]
  return editButtonList
}
function createDropDownActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record)
      // ifShow: hasPermission([113])
    },
    {
      icon: 'ant-design:delete-outlined',
      color: 'error',
      label: '作废',
      popConfirm: {
        title: (
          <div>
            <span>是否确认作废</span>
            <Form>
              <FormItem required label={'作废备注'}>
                <Textarea v-model:value={cancel_remark.value} style="width: 200px" />
              </FormItem>
            </Form>
          </div>
        ),
        placement: 'left',
        confirm: handleDelete.bind(null, record),
        disabled: record.is_cancel == 1
      },
      // disabled: record.status !== 0 && record.is_check !== 2,
      ifShow: hasPermission([535])
    },
    {
      label: '发票绑定列表',
      onClick: handlerelevance.bind(null, record, 'detail'),
      disabled: !(record.check_status === 1 && record.is_bind === 1),
      // disabled: record.status !== 0,
      ifShow: hasPermission([541])
    }
  ]

  return editButtonList
}

//审核
async function handleexamines() {
  const rowKey = getSelectRows()
  const ids = ref<any>([])
  for (const item of rowKey) {
    if (item.status == 15) return message.error('请选择未审核的发票')
    if (item.is_cancel == 1) return message.error('请选择未作废的发票')
    ids.value.push(item.id)
  }
  await invoicesetStatus({ ids: ids.value })
  await reload()
  clearSelectedRowKeys()
}

//编辑
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerrelevance, { openDrawer: openrelevance, setDrawerProps: setrelevanceProps }] = useDrawer()
function handleUpdate(record) {
  openDrawer(true, { record: record, type: 'edit' })
  setDrawerProps({ title: '编辑', showFooter: true })
}

//绑定
function handlerelevance(record, type) {
  openrelevance(true, { record: record })
  setrelevanceProps({ title: '发票绑定', showFooter: type === 'add' })
}

//新增
function handleAdd(record) {
  openDrawer(true, { record: record, type: 'add' })
  setDrawerProps({ title: '新增', showFooter: true })
}

//详情
function handleDetail(record) {
  openDrawer(true, { record: record, type: 'detail' })
  setDrawerProps({ title: '详情', showFooter: false })
}

//删除
async function handleDelete(record) {
  invoicesetIsCancel({ id: record.id, cancel_remark: cancel_remark.value, is_cancel: 1 })
  cancel_remark.value = ''
  await reload()
}

//execl上传
const ImpExcelModalRef = ref<InstanceType<typeof ImpExcelModal>>()
//上传
async function handleUploadData(data) {
  ImpExcelModalRef.value?.changeLoading(true)
  try {
    const cookedData = transformData2Import(data)
    console.log(cookedData)

    // //每次最多5000条数据
    // const chunkSize = 5000
    // const totalChunks = Math.ceil(cookedData.length / chunkSize)

    // for (let i = 0; i < totalChunks; i++) {
    //   const start = i * chunkSize
    //   const end = Math.min(start + chunkSize, cookedData.length)
    //   const chunk = cookedData.slice(start, end)
    //发请求
    await invoiceupdate({ invoiceList: cookedData })
    // }

    await reload()
    ImpExcelModalRef.value?.changeLoading(false)
    return Promise.resolve('OK')
  } catch (err) {
    console.log(err)
    ImpExcelModalRef.value?.changeLoading(false)
    return Promise.reject('Fail')
  }
}

//财务审核
async function handlefian() {
  const rowKey = getSelectRows()
  const ids = ref<any>([])
  for (const item of rowKey) {
    if (item.status == 1) return message.error('请选择已审核的发票')
    if (item.check_status == 1) return message.error('请选择未财务审核的发票')
    if (item.is_cancel == 1) return message.error('请选择未作废的发票')
    ids.value.push(item.id)
  }
  await invoicesetCheckStatus({ ids: rowKey })
  await reload()
  clearSelectedRowKeys()
}
</script>
