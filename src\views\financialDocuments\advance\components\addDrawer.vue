<template>
  <BasicDrawer @register="registerDrawer" width="90%" showFooter @ok="handleOk">
    <BasicForm @register="registerForm">
      <template #files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg2Stocking"
          :custom-request="handleFileRequest"
          :multiple="true"
          :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
        >
          <a-button type="primary" :disabled="edittype == 'cashier'">
            <upload-outlined />
            Upload
          </a-button>
        </Upload>
      </template>
    </BasicForm>
    <Card title="供应商信息">
      <template #extra>
        <span v-if="updateStatus">
          <Button type="primary" @click="handleSave" size="small" class="mr-2">保存</Button>
          <Button @click="handleCancel" size="small">取消</Button>
        </span>
        <span v-else>
          <Button type="primary" @click="handleUpdateSupplier" size="small">编辑</Button>
        </span>
      </template>
      <BasicForm @register="registerSupplierFrom">
        <template #businessicense>
          <Upload
            v-model:file-list="businessicense"
            action="/api/oss/putImg"
            list-type="picture-card"
            :custom-request="handlebusinessicenseFileRequest"
            :multiple="true"
            :disabled="!updateStatus"
          >
            <div>
              <plus-outlined />
              <div style="margin-top: 8px">Upload</div>
            </div>
          </Upload>
        </template>
        <template #contract>
          <Upload
            v-model:file-list="contracts"
            action="/api/oss/putImg"
            list-type="picture-card"
            :custom-request="handlecontractsFileRequest"
            :multiple="true"
            :disabled="!updateStatus"
          >
            <div>
              <plus-outlined />
              <div style="margin-top: 8px">Upload</div>
            </div>
          </Upload>
        </template>
        <template #names="{ model }">
          <Input v-if="!model.is_company" v-model:value="model.name" :disabled="!updateStatus" />
          <div v-else>
            <InputSearch
              v-model:value="model.name"
              enter-button="搜索"
              :loading="searchlodaing"
              @search="search"
              :disabled="!updateStatus"
            />
            <div class="serchdiv" v-if="qccarray.length > 0">
              <ul ref="list" class="serchul">
                <li class="serchli" v-for="item in qccarray" :key="item.name" @click="select(item)">{{ item.Name }}</li>
              </ul>
            </div>
          </div>
        </template>
      </BasicForm>
      <BasicTable @register="registersuppliertable">
        <template #toolbar>
          <Button type="primary" @click="handleAdd" :disabled="!updateStatus">新增</Button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction :actions="createActions(record)" />
          </template>
        </template>
      </BasicTable>
    </Card>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { addschemasFn } from '../datas/add.data'
import { postupdate } from '/@/api/financialDocuments/abvance'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { Button, Card, message, Upload, UploadFile, InputSearch, Input } from 'ant-design-vue'
import { schemasFn } from '/@/views/baseData/supplier/datas/SupplierModal'
import { getSupplier, smqccsearchKey, updateSupplier } from '/@/api/baseData/supplier'
import { ref, watch } from 'vue'
import { UploadOutlined } from '@ant-design/icons-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { cloneDeep } from 'lodash-es'
import { ActionItem, EditRecordRow, useTable, TableAction, BasicTable } from '/@/components/Table'
import { suppliercolumns } from '/@/views/baseData/supplier/datas/SupplierModal'

const emit = defineEmits(['reload', 'register'])
//供应商
const updateStatus = ref<boolean>(false)
//供应商
const supplierData = ref()
//供应商id
const supplierId = ref<number>()
//附件
const filesList = ref<UploadFile[]>([])
//编辑
const edittype = ref()
const init_id = ref()

//营业执照
const businessicense = ref<UploadFile[]>([])
//合同
const contracts = ref<UploadFile[]>([])

const searchlodaing = ref(false)
const qccarray = ref<any>([])
const currentEditKeyRef = ref('')

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val.map((item) => item.url) })
  }
)
watch(
  () => businessicense.value,
  async (val) => {
    await setFieldsValue({ business_license: val?.map((item) => item.url) })
  }
)
watch(
  () => contracts.value,
  async (val) => {
    await setFieldsValue({ contract: val?.map((item) => item.url) })
  }
)

const [registerDrawer, { changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  console.log(data)

  await resetSchema(addschemasFn(fieldValueChange, data.Permission))
  edittype.value = data.Permission
  resetFields()
  supplierresetFields()
  setTableData([])
  filesList.value = []
  businessicense.value = []
  contracts.value = []
  updateStatus.value = false
  if (data.type !== 'add') {
    init_id.value = data.record.id
    filesList.value = data.record?.files?.map((file) => ({ name: file, url: file, uid: Math.random() * 100000 })) ?? []
    setFieldsValue({
      ...data.record
    })
    filesList.value = data.record?.files?.map((file) => ({ name: file, url: file, uid: Math.random() * 100000 })) ?? []
    const { items } = await getSupplier({ id: data.record.supplier_id })
    supplierId.value = items[0].id
    suppliersetFieldsValue(items[0])
    getSupplierAttachment(items[0])
  }
})

/** 注册供应商表单 */
const [
  registerSupplierFrom,
  {
    validate: supplierFromValidate,
    setFieldsValue: suppliersetFieldsValue,
    updateSchema: supplierupdateSchema,
    getFieldsValue,
    clearValidate,
    resetFields: supplierresetFields
  }
] = useForm({
  labelWidth: 120,
  schemas: schemasFn(true, 'purchase'),
  showSubmitButton: false,
  showResetButton: false,
  baseColProps: { span: 24 }
})

const [registerForm, { validate, resetFields, setFieldsValue, resetSchema }] = useForm({
  showActionButtonGroup: false,
  baseColProps: { span: 12 },
  labelWidth: 100
})

async function handleOk() {
  try {
    changeOkLoading(true)
    const formdata = await validate()
    postupdate({ ...formdata, id: init_id.value })
    setTimeout(() => {
      closeDrawer()
      emit('reload')
      changeOkLoading(false)
    }, 1000)
  } catch (e) {
    console.log(e)
    changeOkLoading(false)
  }
}

/** 获取供应商 */
function fieldValueChange(value) {
  console.log(value)

  suppliersetFieldsValue(value)
  supplierData.value = value
  supplierId.value = value.id
  supplierData.value = value
  getSupplierAttachment(value)
}

/** 编辑供应商 */
async function handleUpdateSupplier() {
  updateStatus.value = true
  setDisable(false)
}
/** 供应商附件更改 */
async function handlebusinessicenseFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  businessicense.value = businessicense.value!.map((item) => {
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: item.name
    }
  })
  await setFieldsValue({
    files: businessicense.value.map((item) => item.url)
  })
}

//附件上传
async function handlecontractsFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  contracts.value = contracts.value!.map((item) => {
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: item.name
    }
  })
  await setFieldsValue({
    files: contracts.value.map((item) => item.url)
  })
}

/** 保存 */
async function handleSave() {
  try {
    // 供应商信息校验
    let supplier = await supplierFromValidate()
    const tabledata = formatSubmit()
    supplier.inCharge = supplier.inCharge ? supplier.inCharge : null
    await updateSupplier({
      id: supplierId.value,
      ...supplier,
      Invoicing_is_self: supplier.Invoicing_is_self ?? undefined,
      tax_point: supplier.tax_point ?? undefined,
      ticket_point: supplier.ticket_point ?? undefined,
      items: tabledata
    })
    setDisable(true)
    updateStatus.value = false
    message.success('保存成功')
  } catch (error) {
    message.error('保存失败')
    throw new Error(`${error}`)
  }
}

/** 取消 */
async function handleCancel() {
  updateStatus.value = false
  // 重置供应商信息
  await suppliersetFieldsValue(supplierData.value)
  setDisable(true)
}

/** 设置disable */
function setDisable(status: boolean) {
  supplierupdateSchema(schemasFn(status, 'purchase', { supplierupdateSchema, getFieldsValue, clearValidate }))
}

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    console.log(file)
    changeOkLoading(true)
    const curFile = filesList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({
      files: filesList.value.map((item) => item.url)
    })
    // 判断是否所有的文件都已经上传成功，如果有uploading状态的就不修改按钮状态
    const isAllDone = filesList.value.every((item) => item.url)
    if (isAllDone) {
      changeOkLoading(false)
    }
  } catch (err) {
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}

function search(e) {
  qccarray.value = []
  searchlodaing.value = true
  setTimeout(async () => {
    const { Result } = await smqccsearchKey({ searchKey: e })
    qccarray.value = Result
    searchlodaing.value = false
  }, 2000)
}

/** 注册供应商卡号明细表格 */
const [registersuppliertable, { updateTableDataRecord, getColumns, getDataSource, setTableData, deleteTableDataRecord }] = useTable({
  columns: suppliercolumns,
  maxHeight: 400,
  title: '供应商分属账户',
  showIndexColumn: false,
  actionColumn: {
    width: 100,
    title: '操作',
    dataIndex: 'action'
  }
})

//添加明细
async function handleAdd() {
  const newRowDataItem = {}
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
}

function createActions(record: EditRecordRow): ActionItem[] {
  if (!record.editable) {
    return [
      {
        label: '编辑',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false || !updateStatus.value,
        // ifShow: record.is_check2 !== 2 && !record.is_cancel,
        onClick: handleEdit.bind(null, record)
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          placement: 'left',
          confirm: handleDelete.bind(null, record)
        },
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false || !updateStatus.value
      },
      {
        label: '复制',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false || !updateStatus.value,
        onClick: handlecopylink.bind(null, record)
      }
    ]
  }
  return [
    {
      label: '保存',
      onClick: handlesupllierSave.bind(null, record)
    },
    {
      label: '取消',
      popConfirm: {
        title: '是否取消编辑',
        placement: 'right',
        confirm: handlesuplierCancel.bind(null, record)
      }
    }
  ]
}

// 存储编辑前的record
const beforeRecord = ref()
function handleEdit(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  beforeRecord.value = cloneDeep(record)
  record.onEdit?.(true, false)
}
function handleDelete(record) {
  deleteTableDataRecord(record.key)
}

function handlecopylink(record) {
  const newrecord = formatObject(record) as any
  delete newrecord.id
  const newRowDataItem = {
    ...newrecord
  }
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
}

//保存
async function handlesupllierSave(record: EditRecordRow) {
  // 校验
  const valid = await record.onValid?.()
  if (valid) {
    try {
      //TODO 此处将数据提交给服务器保存
      // ...
      // 保存之后提交编辑状态c
      // 检查除了business_type外的其他属性
      const Columnskey = formatObject(record)
      for (const key in Columnskey) {
        // 如果不是business_type且值为空（或undefined）
        if (Columnskey[key] === '' || Columnskey[key] === undefined) {
          message.error('全为必填,请检查是否完成填写')
          return
        }
      }
      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
      }
      message.success({ content: '数据已保存', key: 'saving' })
      // meesg.value = false
    } catch (error) {
      message.error({ content: '保存失败', key: 'saving' })
      throw new Error(`${error}`)
    }
  } else {
    message.error({ content: '请填写正确的数据', key: 'saving' })
  }
}

//取消
function handlesuplierCancel(record: EditRecordRow) {
  currentEditKeyRef.value = ''
  updateTableDataRecord(record.key, {
    ...beforeRecord.value
  })
  record.onEdit?.(false, false)
}

//格式化某一行的数据
function formatObject(record) {
  // getDataSource()有我们不需要的属性,所以需要清除
  let temporary = {}
  for (let colName of getColumns()) {
    if (colName.key !== 'action') {
      temporary[`${colName.dataIndex}`] = record[`${colName.dataIndex}`]
    }
  }
  return temporary
}
// 格式化提交的数据
function formatSubmit() {
  // getDataSource()有我们不需要的属性,所以需要清除
  const dataSource = getDataSource().map((item) => {
    let temporary = {}
    for (let colName of getColumns()) {
      if (colName.key !== 'action') {
        temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
      }
    }
    return temporary
  })
  return dataSource
}

function select(item) {
  setFieldsValue({
    name: item.Name,
    credit_code: item.CreditCode,
    oper_name: item.OperName,
    company_status: item.Status,
    address: item.Address
  })
}

//供应商获取附件赋值
function getSupplierAttachment(value) {
  console.log(value)

  const imgsdata = typeof value.business_license === 'string' ? JSON.parse(value.business_license) : value.business_license
  const imgsdata2 = typeof value.contract === 'string' ? JSON.parse(value.contract) : value.contract
  businessicense.value = (
    typeof imgsdata === 'string'
      ? [{ url: imgsdata, uid: +new Date().toString(), name: imgsdata }]
      : (imgsdata as string[])?.map((item: string, idx: number) => ({ url: item, uid: idx.toString(), name: item }))
  ) as UploadFile[]
  contracts.value = (
    typeof imgsdata2 === 'string'
      ? [{ url: imgsdata2, uid: +new Date().toString(), name: imgsdata2 }]
      : (imgsdata2 as string[])?.map((item: string, idx: number) => ({ url: item, uid: idx.toString(), name: item }))
  ) as UploadFile[]
  setTableData(value.item)
}
</script>
