<template>
  <BasicDrawer @register="registerDrawer" width="50%">
    <Descriptions title="其他支出单详情" :column="2">
      <DescriptionsItem label="预付款单号">{{ records.strid }}</DescriptionsItem>
      <DescriptionsItem label="预付款金额">{{ records.amount }} ¥</DescriptionsItem>
      <DescriptionsItem label="状态">
        <Tag :color="status[records.status]?.color"> {{ status[records.status]?.label }}</Tag>
      </DescriptionsItem>
      <DescriptionsItem label="部门">{{ records.department }}</DescriptionsItem>
      <DescriptionsItem label="创建人">{{ records.creator_name }}</DescriptionsItem>
      <DescriptionsItem label="负责人">{{ records.inCharge_name }}</DescriptionsItem>
      <DescriptionsItem label="供应商">{{ records.supplier_name }}</DescriptionsItem>
      <DescriptionsItem label="账号名">{{ records.supplier_account_name }}</DescriptionsItem>
      <DescriptionsItem label="付款账号">{{ records.supplier_account }}</DescriptionsItem>
      <DescriptionsItem label="驳回日期">{{ records.reject_at || '-' }}</DescriptionsItem>
      <DescriptionsItem label="驳回备注">{{ records.reject_remark || '-' }}</DescriptionsItem>
      <DescriptionsItem label="申请时间">{{ records.created_at }}</DescriptionsItem>
      <DescriptionsItem label="完成日期">{{ records.finish_at || '-' }} </DescriptionsItem>
      <DescriptionsItem label="备注">
        {{ records.remark || '-' }}
      </DescriptionsItem>
      <DescriptionsItem label="附件">
        <ul>
          <li v-for="(item, index) in records.files" :key="item">
            <a :href="item" target="_blank" @click="handlePreview(item, $event)">{{ `附件${index + 1}` }}</a>
          </li>
        </ul>
      </DescriptionsItem>
    </Descriptions>
    <PreviewFile @register="registerModal" />
  </BasicDrawer>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { Descriptions, DescriptionsItem, Tag } from 'ant-design-vue'
import { status } from '../datas/data'
import { createImgPreview } from '/@/components/Preview'
import { useMessage } from '/@/hooks/web/useMessage'
import { useModal } from '/@/components/Modal'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'

const { createMessage } = useMessage()
const records = ref<any>({})
const [registerDrawer] = useDrawerInner((data) => {
  records.value = data
  console.log(data)
})
//预览
const [registerModal, { openModal }] = useModal()
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  console.log(prefix)
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>
