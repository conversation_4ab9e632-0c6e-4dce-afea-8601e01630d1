<template>
  <BasicModal @register="register" @ok="handleOk" :minHeight="400">
    <BasicForm @register="registerform" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { schemas } from '../datas/generalModal'
import { ref } from 'vue'
import { setStatus } from '/@/api/financialDocuments/abvance'

const emit = defineEmits(['reload', 'register'])

const propsData = ref({})

const [register, { closeModal }] = useModalInner(async (data) => {
  resetFields()
  resetSchema(schemas(data.type))
  setFieldsValue({
    amount: data.amount
  })
  propsData.value = data
})
const [registerform, { resetSchema, resetFields, validate, setFieldsValue }] = useForm({
  // schemas,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelCol: { span: 5 }
})

// 提交
async function handleOk() {
  try {
    const formdata = await validate()
    if (propsData.value.type == 'finance') {
      if (formdata.type == 1) {
        commonStatusUpdate(4)
      } else if (formdata.type == 2) {
        commonStatusUpdate(16, { reject_remark: formdata.reject_remark })
      }
    } else if (propsData.value.type == 'cashier') {
      if (formdata.type == 1) {
        commonStatusUpdate(15, formdata)
      } else if (formdata.type == 2) {
        commonStatusUpdate(16, { reject_remark: formdata.reject_remark })
      }
    }
    console.log(formdata)
    emit('reload')
  } catch (e) {
    console.log(e)
  }
}
const commonStatusUpdate = (status, additionalData = {}) => {
  setStatus({
    id: propsData.value.id,
    status,
    ...additionalData
  })
  closeModal()
}
</script>
