import { getDeptTree } from '/@/api/admin/dept'
import { getSupplier } from '/@/api/baseData/supplier'
import { getAccountList } from '/@/api/commonUtils'
import { FormSchema } from '/@/components/Form'

export function addschemasFn(hand?: Function, Permission?: String): FormSchema[] {
  return [
    {
      field: 'supplier_id',
      label: '供应商',
      component: 'PagingApiSelect',
      required: true,
      componentProps: {
        api: getSupplier,
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          onChange(_, shall) {
            if (!shall) return
            hand && hand(shall)
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      },
      dynamicDisabled: Permission == 'cashier'
    },
    {
      field: 'dept_id',
      label: '部门',
      component: 'ApiTreeSelect',
      required: true,
      componentProps: {
        api: getDeptTree,
        immediate: false,
        lazyLoad: true,
        treeSelectProps: {
          fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          filterTreeNode: (search, item) => {
            if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
            return false
          }
        }
      },
      dynamicDisabled: Permission == 'cashier'
    },
    {
      field: 'amount',
      label: '预付款金额',
      required: true,
      component: 'InputNumber',
      componentProps: {
        precision: 2,
        min: 0
      }
    },
    {
      field: 'inCharge',
      label: '负责人',
      component: 'ApiSelect',
      componentProps: {
        api: getAccountList,
        resultField: 'items',
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          disabled: Permission == 'cashier',
          optionFilterProp: 'name'
        }
      },
      required: true,
      itemProps: {
        validateTrigger: 'blur'
      },
      dynamicDisabled: Permission == 'cashier'
    },
    {
      field: 'remark',
      label: '备注',
      component: 'InputTextArea',
      dynamicDisabled: Permission == 'cashier'
    },
    {
      field: 'files',
      label: '附件',
      component: 'Upload',
      slot: 'files',
      required: true
    }
  ]
}
