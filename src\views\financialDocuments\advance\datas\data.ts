import { h } from 'vue'
import type { BasicColumn, FormSchema } from '/@/components/Table'
import { isNull } from 'lodash-es'
import { Tag } from 'ant-design-vue'
import { getErpSupplier } from '/@/api/commonUtils'
import { GET_STATUS_SCHEMA } from '/@/const/status'

export const status = {
  0: { label: '未执行', color: '' },
  1: { label: '确认', color: 'skyblue' },
  2: { label: '主管审核', color: 'orange' },
  3: { label: '财务审核', color: 'blue' },
  4: { label: '出纳支付', color: 'pink' },
  15: { label: '已完成', color: 'green' },
  16: { label: '驳回', color: 'red' }
}

export const columns: BasicColumn[] = [
  {
    title: '预付款单号',
    dataIndex: 'strid',
    width: 150,
    resizable: true
  },
  {
    title: '预付款金额',
    dataIndex: 'amount',
    width: 150,
    resizable: true
  },

  {
    title: '状态',
    dataIndex: 'status',
    width: 150,
    resizable: true,
    customRender({ value }) {
      return isNull(value) ? '-' : h(Tag, { color: status[value].color }, status[value].label)
    }
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 150,
    resizable: true
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 150,
    resizable: true
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    width: 150,
    resizable: true
  },
  {
    title: '驳回日期',
    dataIndex: 'reject_at',
    width: 150,
    resizable: true
  },
  {
    title: '驳回备注',
    dataIndex: 'reject_remark',
    width: 150,
    resizable: true
  },
  {
    title: '申请时间',
    dataIndex: 'created_at',
    width: 150,
    resizable: true
  },
  {
    title: '完成日期',
    dataIndex: 'finish_at',
    width: 150,
    resizable: true
  }
]

const status_schema = GET_STATUS_SCHEMA(
  Object.keys(status).map((key) => {
    return {
      label: status[key].label,
      value: key
    }
  })
)

export const schemas: FormSchema[] = [
  status_schema,
  {
    field: 'strid',
    label: '预付款单号',
    component: 'Input'
  },
  {
    field: 'supplier_id',
    label: '供应商',
    component: 'PagingApiSelect',
    componentProps: () => {
      return {
        api: getErpSupplier,
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        },
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        resultField: 'items'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  }
  // {
  //   field: 'status',
  //   label: '状态',
  //   component: 'Select',
  //   componentProps: {
  //     options: Object.keys(status).map((key) => {
  //       return {
  //         label: status[key].label,
  //         value: key
  //       }
  //     })
  //   }
  // }
]
