import { getRmbquot } from '/@/api/erp/sales'
import { getFinancialInformation } from '/@/api/financialDocuments/capitalFlow'
import { FormSchema } from '/@/components/Form'

export const schemas = (type: string): FormSchema[] => [
  {
    field: 'type',
    label: '类型',
    component: 'RadioButtonGroup',
    defaultValue: 1,
    componentProps: {
      options: [
        { label: '通过', value: 1 },
        { label: '驳回', value: 2 }
      ]
    },
    required: true
  },
  {
    field: 'reject_remark',
    label: '驳回备注',
    component: 'InputTextArea',
    required: true,
    ifShow(renderCallbackParams) {
      return renderCallbackParams.values.type === 2
    }
  },
  {
    field: 'occurrence_at',
    label: '付款日期',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD'
    },
    required: true,
    ifShow(renderCallbackParams) {
      return renderCallbackParams.values.type === 1 && type === 'cashier'
    }
    // colProps: { span: 12, style: 'margin-right:1px' }
  },
  {
    field: 'rate',
    label: '汇率',
    component: 'PagingApiSelect',
    componentProps: ({ formModel }) => {
      return {
        searchMode: true,
        api: getRmbquot,
        resultField: 'items',
        selectProps: {
          fieldNames: { value: 'fBuyPri', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true
        },
        onChange(val, shall) {
          console.log(shall)

          if (val == '1.000000') {
            formModel.fg_amount = 0
            formModel.fgfee_amount = 0
            formModel.fgins_amount = 0
          }
          formModel.currency = shall?.name.split('-')[1]
          formModel.fg_amount = (Number(formModel.amount) / Number(val ? val : 0)).toFixed(2)
          //   formModel.amount = (Number(val ? val : 0) * Number(formModel.fg_amount)).toFixed(2)
          formModel.fee = (Number(val ? val : 0) * Number(formModel.fgfee_amount)).toFixed(2)
          formModel.insurance_amount = (Number(val ? val : 0) * Number(formModel.fgins_amount)).toFixed(2)
          formModel.currency = shall?.name.split('-')[0]
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    required: true,
    ifShow(renderCallbackParams) {
      return renderCallbackParams.values.type === 1 && type === 'cashier'
    }
  },
  {
    field: 'from_plaform',
    label: '付款资金资料',
    component: 'ApiSelect',
    componentProps: {
      api: getFinancialInformation,
      selectProps: {
        fieldNames: { value: 'name', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    required: true,
    ifShow(renderCallbackParams) {
      return renderCallbackParams.values.type === 1 && type === 'cashier'
    }
  },
  {
    field: 'to_plaform',
    label: '收款资金资料',
    component: 'Input',
    ifShow(renderCallbackParams) {
      return renderCallbackParams.values.type === 1 && type === 'cashier'
    }
  },
  {
    field: 'fgfee_amount',
    label: '外汇手续费',
    component: 'InputNumber',
    defaultValue: 0,
    componentProps: ({ formModel }) => {
      return {
        min: 0,
        precision: 4,
        onChange(val) {
          formModel.fee = (Number(val ? val : 0) * Number(formModel.rate)).toFixed(2)
        }
      }
    },
    dynamicDisabled(renderCallbackParams) {
      return renderCallbackParams.values.rate == '1.000000'
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.values.type === 1 && type === 'cashier'
    }
  },
  {
    field: 'fee',
    label: '手续费',
    component: 'InputNumber',
    defaultValue: 0,
    componentProps: {
      min: 0,
      precision: 4
    },
    dynamicDisabled(renderCallbackParams) {
      return renderCallbackParams.values.rate !== '1.000000'
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.values.type === 1 && type === 'cashier'
    }
  },
  {
    field: 'fg_amount',
    label: '外汇金额',
    component: 'InputNumber',
    componentProps: ({ formModel }) => {
      return {
        prefix: '￥',
        suffix: 'RMB',
        min: 0,
        precision: 4,
        onChange(val) {
          formModel.amount = (Number(val ? val : 0) * Number(formModel.rate)).toFixed(2)
        }
      }
    },
    rules: [
      {
        required: true,
        message: '金额不能为0！',
        validator(_rule: any, value: any, callback) {
          if (value == 0) {
            return Promise.reject()
          }
          callback()
        }
      }
    ],
    defaultValue: 0,
    dynamicDisabled: true,
    ifShow(renderCallbackParams) {
      return renderCallbackParams.values.type === 1 && type === 'cashier'
    }
  },
  {
    field: 'amount',
    label: '金额',
    component: 'InputNumber',
    componentProps: {
      prefix: '￥',
      suffix: 'RMB',
      min: 0,
      precision: 4
    },
    rules: [
      {
        required: true,
        message: '金额不能为0！',
        validator(_rule: any, value: any, callback) {
          if (value == 0) {
            return Promise.reject()
          }
          callback()
        }
      }
    ],
    defaultValue: 0,
    required: true,
    dynamicDisabled: true,
    ifShow(renderCallbackParams) {
      return renderCallbackParams.values.type === 1 && type === 'cashier'
    }
  },
  {
    field: 'insurance_strid',
    label: '信保单号',
    component: 'Input',
    ifShow(renderCallbackParams) {
      return renderCallbackParams.values.type === 1 && type === 'cashier'
    }
  },
  {
    field: 'fgins_amount',
    label: '信保外汇金额',
    component: 'InputNumber',
    componentProps: ({ formModel }) => {
      return {
        // prefix: '￥',
        // suffix: 'RMB',
        min: 0,
        precision: 2,
        disabled: formModel.rate === '1.000000',
        onChange(val) {
          formModel.insurance_amount = (Number(val ? val : 0) * Number(formModel.rate)).toFixed(2)
        }
      }
    },
    defaultValue: 0,
    ifShow(renderCallbackParams) {
      return renderCallbackParams.values.type === 1 && type === 'cashier'
    }
  },
  {
    field: 'insurance_amount',
    label: '信保金额',
    component: 'InputNumber',
    itemHelpMessage: '当汇率为外币时,请填写对应外汇金额,金额会自动根据汇率进行换算填充',
    componentProps: ({ formModel }) => {
      return {
        // prefix: '￥',
        // suffix: 'RMB',
        min: 0,
        precision: 2,
        disabled: formModel.rate !== '1.000000'
      }
    },
    defaultValue: 0,
    ifShow(renderCallbackParams) {
      return renderCallbackParams.values.type === 1 && type === 'cashier'
    }
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    ifShow(renderCallbackParams) {
      return renderCallbackParams.values.type === 1 && type === 'cashier'
    }
  },
  {
    field: 'currency',
    label: '币种',
    show: false,
    // ifShow: false,
    component: 'InputTextArea'
  }
]
