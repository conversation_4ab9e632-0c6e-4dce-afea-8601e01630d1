<template>
  <div>
    <BasicTable :data-cachekey="routePath" @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleAdd" v-if="hasPermission([463])">新增</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
      </template>
    </BasicTable>
    <AddDrawer @register="registeradd" @reload="reload" />
    <GeneralModal @register="registergeneral" @reload="reload" />
    <DetailDrawer @register="registerdetail" />
  </div>
</template>

<script setup lang="ts">
// import { ImpExcelModal } from '/@/components/Excel'
import { ActionItem, BasicTable, EditRecordRow, useTable, TableAction } from '/@/components/Table'
import AddDrawer from './components/addDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { columns, schemas } from './datas/data'
import { getList, setStatus } from '/@/api/financialDocuments/abvance'
import GeneralModal from './components/generalModal.vue'
import { ref } from 'vue'
import { useModal } from '/@/components/Modal'
import DetailDrawer from './components/detailDrawer.vue'
import { usePermission } from '/@/hooks/web/usePermission'
import { useRoute } from 'vue-router'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'

const route = useRoute()
const { path: routePath } = route
const { hasPermission } = usePermission()
/** 注册表格 */
const [registerTable, { reload }] = useTable({
  title: '预付款列表',
  api: getList,
  showIndexColumn: false,
  actionColumn: {
    width: 400,
    title: '操作',
    dataIndex: 'action'
  },
  columns,
  showTableSetting: true,
  useSearchForm: true,
  rowKey: 'id',
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas,
    fieldMapToTime: []
  }
})

const [registeradd, { openDrawer, setDrawerProps }] = useDrawer()
const [registergeneral, { openModal, setModalProps }] = useModal()
const [registerdetail, { openDrawer: opendetailDrawer, setDrawerProps: setDrawerdetailProps }] = useDrawer()

function createActions(record: EditRecordRow): Recordable[] {
  let buttonList: ActionItem[] = [
    {
      label: '采购确认',
      onClick: handleUpdate.bind(null, record, 'affirm'),
      disabled: record.status !== 0,
      ifShow: hasPermission([464])
    },
    {
      label: '主管审核',
      onClick: handleUpdate.bind(null, record, 'management'),
      disabled: record.status !== 2,
      ifShow: hasPermission([465])
    },
    {
      label: '财务审核',
      onClick: handleUpdate.bind(null, record, 'finance'),
      disabled: record.status !== 3,
      ifShow: hasPermission([466])
    },
    {
      label: '出纳支付',
      onClick: handleUpdate.bind(null, record, 'cashier'),
      disabled: record.status !== 4,
      ifShow: hasPermission([467])
    }
  ]
  return buttonList
}
function createDropDownActions(record: EditRecordRow): ActionItem[] {
  let buttonList: ActionItem[] = [
    {
      icon: 'clarity:note-edit-line',
      label: '详情',
      onClick: handledetail.bind(null, record)
    },
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleedit.bind(null, record, 'common'),
      disabled: record.status !== 0,
      ifShow: hasPermission([468])
    },
    {
      icon: 'clarity:note-edit-line',
      label: '删除',
      onClick: handledeled.bind(null, record),
      disabled: record.status !== 0,
      ifShow: hasPermission([513])
    },
    {
      icon: 'clarity:note-edit-line',
      label: '出纳编辑',
      onClick: handleedit.bind(null, record, 'cashier'),
      disabled: record.status !== 4,
      ifShow: hasPermission([507])
    },
    {
      label: '主管审批取消',
      onClick: handleUpdate.bind(null, record, 'affirm'),
      disabled: record.status !== 3,
      ifShow: hasPermission([469])
    },
    {
      label: '取消采购确认',
      onClick: handleUpdate.bind(null, record, 'unaffirm'),
      disabled: record.status !== 2,
      ifShow: hasPermission([470])
    }
  ]

  return buttonList
}

function handleAdd() {
  openDrawer(true, { type: 'add' })
  setDrawerProps({ title: '新增预付款' })
}
function handleedit(record, type) {
  openDrawer(true, { type: 'edit', record, Permission: type })
  setDrawerProps({ title: '编辑预付款' })
}
function handledetail(record) {
  opendetailDrawer(true, record)
  setDrawerdetailProps({ title: '预付款详情' })
}

async function handleUpdate(record, type: string) {
  const statusnumber = ref()
  switch (type) {
    case 'unaffirm':
      statusnumber.value = 0
      break
    case 'affirm':
      statusnumber.value = 2
      break
    case 'management':
      statusnumber.value = 3
      break
    case 'finance':
      statusnumber.value = 4
      break
    case 'cashier':
      statusnumber.value = 15
      break
  }
  if (['unaffirm', 'affirm', 'management'].includes(type)) {
    await setStatus({ id: record.id, status: statusnumber.value })
    reload()
  } else {
    openModal(true, {
      id: record.id,
      amount: record.amount,
      type: type
    })
    setModalProps({ title: `${type == 'finance' ? '财务审核' : '出纳支付'}` })
  }
}

function handledeled(record) {
  setStatus({ id: record.id, status: 9 })
  reload()
}
</script>
