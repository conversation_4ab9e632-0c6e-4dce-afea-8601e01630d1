<template>
  <BasicModal @register="register" title="核对备注" @ok="handleOk" width="800px">
    <BasicForm @register="registerform" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { message } from 'ant-design-vue'
import { ref } from 'vue'
import { schemas } from '../datas/Modal'
import { setCheckRemark } from '/@/api/financialDocuments/capitalFlow'
import { debounce } from 'lodash-es'

const init_id = ref()
const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
  console.log(data)
  init_id.value = data.id
})
const [registerform, { getFieldsValue }] = useForm({
  schemas,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelCol: { span: 5 }
})

const emit = defineEmits(['success', 'register'])

/** 核对备注 */
async function _handleOk() {
  try {
    await changeOkLoading(true)
    const formdata = await getFieldsValue()
    const params = {
      id: init_id.value,
      check_remark: formdata.check_remark
    }
    const res: any = await setCheckRemark(params)
    console.log(res)

    if (res.news == 'success') {
      emit('success')
      await closeModal()
      message.success('核对备注上传成功')
    }
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  } finally {
    changeOkLoading(false)
  }
}
const handleOk = debounce(_handleOk, 200)
</script>
