<template>
  <BasicModal @register="register" @ok="handleOk" defaultFullscreen>
    <BasicForm @register="registerform" />
    <BasicTable @register="registerTable" @edit-end="editend" @edit-cancel="editcancal" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { colandwith, columns } from '../datas/Modal'
import { BasicTable, useTable } from '/@/components/Table'
import { createrFinCostShare, getFundDepositInfo } from '/@/api/financialDocuments/capitalFlow'
import { ref } from 'vue'
import { add } from '/@/utils/math'
import { message } from 'ant-design-vue'

const fund_ids = ref([])
const [register, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  resetFields()
  fund_ids.value = data.fund_ids
  const res = await getFundDepositInfo({ fund_ids: data.fund_ids })
  setTableData(res.items)
  await resetSchema(colandwith(settabrate, res.fgtotal_amount))
  await setFieldsValue({ fg_amount: res.fgtotal_amount, from_plaform: data.to_plaform })
})
const [registerform, { setFieldsValue, resetSchema, getFieldsValue, validate, resetFields }] = useForm({
  showActionButtonGroup: false,
  baseColProps: { span: 12 },
  labelCol: { span: 5 }
})

const [registerTable, { setTableData, getDataSource, updateTableDataRecord }] = useTable({
  title: '收款提现明细表',
  columns: columns,
  showTableSetting: false,
  useSearchForm: false,
  maxHeight: 300,
  rowKey: 'fund_id'
})

async function settabrate(record) {
  const data = await getDataSource()
  const fromdata = await getFieldsValue()
  data.forEach((item) => {
    item.c_rate = record.rate
    if (item.fgc_amount !== 0) {
      item.rmb_amount = (Number(item.fgc_amount) * Number(item.rate)).toFixed(2)
      //手续费
      item.c_fee = ((item.fgc_amount / record.max) * record.fee).toFixed(2)
      //提现人民币
      item.rmbc_amount = (item.fgc_amount * record.rates).toFixed(2)
      //汇兑
      item.pl_amount = (Number(item.rmbc_amount) - Number((Number(item.rate) * Number(item.fgc_amount)).toFixed(2))).toFixed(2)
    }
  })
  console.log(record)

  const sumOfCfee = data.slice(0, -1).reduce((acc, item) => acc + Number(item.c_fee), 0)

  data[data.length - 1].c_fee = (Number(fromdata.fee) - Number(sumOfCfee)).toFixed(2)
  await setTableData(data)
}

async function editend({ record, value }) {
  const fromdata = await getFieldsValue()
  record.rmb_amount = (Number(record.fgc_amount) * Number(record.rate)).toFixed(2)

  //提现人民币
  record.rmbc_amount = (value * (Number(fromdata.amount) / (Number(fromdata.fg_amount) - Number(fromdata.fgfee_amount)))).toFixed(2)
  //手续费
  record.c_fee = ((value / fromdata.fg_amount) * fromdata.fee).toFixed(2)
  //汇兑
  record.pl_amount = (Number(record.rmbc_amount) - Number((Number(record.rate) * Number(record.fgc_amount)).toFixed(2))).toFixed(2)
  updateTableDataRecord(record.fund_id, { record })
  const dataTable = await getDataSource()
  const fgc_amount = dataTable.reduce((arr, cur) => {
    return add(arr, cur.fgc_amount, 4)
  }, 0)
  const sumOfCfee = dataTable.slice(0, -1).reduce((acc, item) => acc + Number(item.c_fee), 0)
  fgc_amount[dataTable.length - 1].c_fee = (Number(fromdata.fee) - Number(sumOfCfee)).toFixed(2)

  setFieldsValue({ fg_amount: fgc_amount })
}
async function editcancal({ index, value }) {
  const data = await getDataSource()
  data[index].fgc_amount = value
  setTableData(data)
}

const emit = defineEmits(['success', 'register'])
// 提交
async function handleOk() {
  try {
    changeOkLoading(true)
    const formdata = await validate()
    const tandata = await getDataSource()
    const totalamount = tandata.reduce((a, b) => add(a, b.fgc_amount, 4), 0)
    if (totalamount > formdata.fg_amount) {
      message.error('提现金额合计不得大于外汇金额')
      return
    }
    for (let item of tandata) {
      if (item.fgc_amount == 0) {
        return message.error('外汇提现金额不能为0')
      }
    }
    const params = {
      ...formdata,
      funds: tandata.map((item) => {
        return {
          id: item.fund_id,
          fgc_amount: item.fgc_amount,
          pl_amount: item.pl_amount,
          c_fee: item.c_fee,
          rmb_amount: item.rmb_amount
        }
      }),
      from_currency: tandata[0].currency
    }

    const res = await createrFinCostShare(params)
    if (res.news == 'success') {
      closeModal()
      changeOkLoading(false)
      emit('success')
    }
  } catch (e) {
    changeOkLoading(false)
    console.log(e)
  }
}
</script>
