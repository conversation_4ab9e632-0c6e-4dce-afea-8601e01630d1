import dayjs from 'dayjs'
import { getFinancialInformation } from '/@/api/financialDocuments/capitalFlow'
import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'

export const schemas: FormSchema[] = [
  {
    field: 'check_remark',
    component: 'InputTextArea',
    label: '核对备注',
    required: true,
    colProps: {
      span: 20
    },
    componentProps: {
      autosize: { minRows: 3, maxRows: 6 }
    }
  }
]

export const colandwith = (fn?: Function, fgtotal_amount?: Number): FormSchema[] => [
  {
    field: 'occurrence_at',
    label: '收款日期',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD'
    },
    required: true
  },
  {
    field: 'from_plaform',
    label: '付款资金资料',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: getFinancialInformation,
        selectProps: {
          fieldNames: { value: 'name', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          disabled: true,
          onChange: (_, shall) => {
            formModel.from_plaform_id = shall?.a_id
          }
        }
      }
    },
    required: true
  },
  {
    field: 'currency',
    label: '币种',
    component: 'Input',
    show: false
  },
  {
    field: 'from_plaform_id',
    label: 'from_plaform_id',
    component: 'Input',
    show: false
  },
  {
    field: 'to_plaform_id',
    label: 'to_plaform_id',
    component: 'Input',
    show: false
  },
  {
    field: 'to_plaform',
    label: '收款资金资料',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: getFinancialInformation,
        selectProps: {
          fieldNames: { value: 'name', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          onChange: async (_, shall) => {
            const res = await getFinancialInformation()
            res.forEach((item) => {
              if (item.name == formModel.from_plaform) {
                formModel.from_plaform_id = item.a_id
              }
            })
            formModel.to_plaform_id = shall?.a_id
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    required: true
  },
  {
    field: 'rate',
    label: '汇率',
    component: 'Input',
    itemProps: {
      validateTrigger: 'blur'
    },
    dynamicDisabled: true,
    required: true
  },
  {
    field: 'fg_amount',
    label: '外汇金额',
    component: 'InputNumber',
    componentProps: ({ formModel }) => {
      return {
        min: 0,
        precision: 4,
        max: fgtotal_amount,
        onChange: (val) => {
          if (formModel.amount == 0 || val == 0) {
            formModel.rate = 0
            return
          }

          formModel.rate = (Number(formModel.amount) / (Number(val) - Number(formModel.fgfee_amount))).toFixed(6)
          formModel.fee = (
            Number(formModel.fgfee_amount) *
            (Number(formModel.amount) / (Number(val) - Number(formModel.fgfee_amount)))
          ).toFixed(2)
          fn &&
            fn({
              rate: formModel.rate,
              max: val || 0,
              fee: formModel.fee,
              rates: Number(formModel.amount) / (Number(val) - Number(formModel.fgfee_amount))
            })
        }
      }
    },
    defaultValue: 0
  },
  {
    field: 'amount',
    label: '金额',
    component: 'InputNumber',
    componentProps: ({ formModel }) => {
      return {
        min: 0,
        precision: 4,
        onChange: (val) => {
          if (formModel.fg_amount == 0) {
            formModel.rate = 0
            return
          }
          formModel.rate = (Number(val) / (Number(formModel.fg_amount) - Number(formModel.fgfee_amount))).toFixed(6)
          formModel.fee = (
            Number(formModel.fgfee_amount) *
            (Number(val) / (Number(formModel.fg_amount) - Number(formModel.fgfee_amount)))
          ).toFixed(2)
          fn &&
            fn({
              rate: formModel.rate,
              max: formModel.fg_amount || 0,
              fee: formModel.fee,
              rates: Number(val) / (Number(formModel.fg_amount) - Number(formModel.fgfee_amount))
            })
        }
      }
    },
    rules: [
      {
        required: true,
        message: '金额不能为0！',
        validator(_rule: any, value: any, callback) {
          if (value == 0) {
            return Promise.reject()
          }
          callback()
        }
      }
    ],
    defaultValue: 0,
    required: true
  },
  {
    field: 'fgfee_amount',
    label: '手续费外汇金额',
    component: 'InputNumber',
    componentProps: ({ formModel }) => {
      return {
        min: 0,
        precision: 2,
        onChange: (val) => {
          formModel.fee = (Number(val) * (Number(formModel.amount) / (Number(formModel.fg_amount) - Number(val)))).toFixed(2)
          formModel.rate = (Number(formModel.amount) / (Number(formModel.fg_amount) - Number(val))).toFixed(6)
          fn &&
            fn({
              rate: formModel.rate,
              max: formModel.fg_amount || 0,
              fee: formModel.fee,
              rates: Number(formModel.amount) / (Number(formModel.fg_amount) - Number(val))
            })
        }
      }
    },
    defaultValue: 0
  },
  {
    field: 'fee',
    label: '手续费',
    component: 'InputNumber',
    defaultValue: 0,
    componentProps: {
      min: 0,
      precision: 2,
      disabled: true
    }
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea'
  },
  {
    field: 'currency',
    label: '币种',
    show: false,
    component: 'InputTextArea'
  }
]

export const columns: BasicColumn[] = [
  {
    title: '单号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '收款日期',
    dataIndex: 'occurrence_at',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? dayjs(text).format('YYYY-MM-DD') : '-'
    }
  },
  {
    title: '币种',
    dataIndex: 'currency',
    width: 200,
    resizable: true
  },

  {
    title: '外汇收款金额',
    dataIndex: 'fgr_amount',
    width: 200,
    resizable: true
  },
  {
    title: '外汇提现金额',
    dataIndex: 'fgc_amount',
    edit: true,
    editComponent: 'InputNumber',
    helpMessage: '外汇提现金额大于外汇关联金额',
    editComponentProps: ({ record }) => {
      return {
        min: record.fgrel_amount,
        precision: 2,
        editText: true,
        max: record.fgr_amount
      }
    },
    width: 200,
    resizable: true
  },
  {
    title: '人民币收款金额',
    dataIndex: 'rmb_amount',
    width: 200,
    resizable: true
  },
  {
    title: '人民币提现金额',
    dataIndex: 'rmbc_amount',
    width: 200,
    resizable: true
  },
  {
    title: '汇兑损益',
    dataIndex: 'pl_amount',
    width: 200,
    resizable: true
  },
  {
    title: '提现手续费',
    dataIndex: 'c_fee',
    width: 200,
    resizable: true
  },
  {
    title: '收款汇率',
    dataIndex: 'rate',
    width: 200,
    resizable: true
  },
  {
    title: '提现汇率',
    dataIndex: 'c_rate',
    width: 200,
    resizable: true
  },
  {
    title: '外币累计提现金额',
    dataIndex: 'fgcsum_amount',
    width: 200,
    resizable: true
  },
  {
    title: '外币关联金额',
    dataIndex: 'fgrel_amount',
    width: 200,
    resizable: true
  }
]
