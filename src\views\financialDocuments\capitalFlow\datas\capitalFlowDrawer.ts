import { DefaultOptionType } from 'ant-design-vue/lib/select'
import { getType, handlePlaformChange } from './fn'
import { getClientList, getErpSupplier } from '/@/api/commonUtils'
import { getFinancialInformation } from '/@/api/financialDocuments/capitalFlow'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { FormSchema } from '/@/components/Form'
import { getDeptSelectTree } from '/@/api/admin/dept'
import { getRmbquot } from '/@/api/erp/sales'
import Decimal from 'decimal.js'
export const createFormSchema: (Fn?) => FormSchema[] = (Fn) => [
  {
    field: 'occurrence_at',
    label: '收款日期',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD'
    },
    required: true
    // colProps: { span: 12, style: 'margin-right:1px' }
  },
  {
    field: 'type',
    label: '类型',
    component: 'Select',
    componentProps: {
      options: getType(),
      onChange: (type: number) => {
        console.log(type, 'type')
        // 1：收入,2：支出,3:资金调拨 下面那个函数根本没必要,直接用ifshow不是更快,更极简单?写下面那种会整个表单都重新渲染,用ifshow只需要重新渲染两个form-item组件
        Fn.changeFromComponent(type)
      }
    },
    defaultValue: 1,
    required: true
  },
  {
    field: 'from_plaform',
    label: '付款资金资料',
    component: 'Input',
    required: true
  },
  {
    field: 'to_plaform',
    label: '收款资金资料',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: getFinancialInformation,
        selectProps: {
          fieldNames: { value: 'name', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        },
        onChange: (_, shall) => {
          formModel.to_plaform_id = shall?.a_id
          handlePlaformChange(shall, Fn)
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    required: true
  },
  {
    field: 'currency',
    label: '币种',
    component: 'Input',
    defaultValue: '人民币',
    show: false
  },
  {
    field: 'rate',
    label: '汇率',
    component: 'PagingApiSelect',
    componentProps: ({ formModel }) => {
      return {
        searchMode: true,
        api: getRmbquot,
        resultField: 'items',
        pagingMode: true,
        returnParamsField: 'fBuyPri',
        params: {
          occurrence_at: formModel.occurrence_at
        },
        selectProps: {
          fieldNames: { value: 'fBuyPri', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true
        },
        onChange(val, shall) {
          console.log(shall)
          if (!shall) return
          if (val == '1.000000') {
            formModel.fg_amount = 0
            formModel.fgfee_amount = 0
            formModel.fgins_amount = 0
          }
          formModel.amount = new Decimal(formModel.fg_amount ?? 0).mul(val ?? 0).toDecimalPlaces(2)
          formModel.fee = new Decimal(formModel.fgfee_amount ?? 0).mul(val ?? 0).toDecimalPlaces(2)
          formModel.insurance_amount = new Decimal(formModel.fgins_amount ?? 0).mul(val ?? 0).toDecimalPlaces(2)
          formModel.currency = shall?.name.split('-')[0]
          console.log(shall?.name.split('-')[0])
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    required: true
  },
  {
    field: 'supplier_id',
    label: '供应商',
    component: 'PagingApiSelect',
    componentProps: () => {
      return {
        api: getErpSupplier,
        searchMode: true,
        pagingMode: true,
        returnParamsField: 'id',
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        },
        resultField: 'items'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'client_id',
    label: '客户',
    component: 'ApiSelect',
    componentProps: {
      api: getClientList,
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      },
      resultField: 'items'
    },
    itemProps: {
      validateTrigger: 'blur'
    }
    // required: true
  },
  {
    field: 'dept_id',
    label: '部门',
    required: true,
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptSelectTree,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'id', value: 'id', label: 'name' },
        placeholder: '请选择',
        showSearch: true,
        filterTreeNode: (search: string, item: DefaultOptionType) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getCreatorList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
        // disabled: true
      }
    }
    // defaultValue: userStore?.userInfo?.userId
    // required: true
  },

  {
    field: 'fg_amount',
    label: '外汇金额',
    component: 'InputNumber',
    componentProps: ({ formModel }) => {
      return {
        // prefix: '￥',
        // suffix: 'RMB',
        min: 0,
        precision: 2,
        disabled: formModel.type === 2 ? false : ['人民币', 'CNY'].includes(formModel.currency) ? true : false,
        onChange(val) {
          if (formModel.type !== 2) {
            formModel.amount = new Decimal(formModel.rate ?? 0).mul(val ?? 0).toDecimalPlaces(2)
          } else if (formModel.type == 2 && !['人民币', 'CNY'].includes(formModel.currency)) {
            formModel.rate = new Decimal(formModel.amount || 0).div(val || 0).toDecimalPlaces(6)
          }
        }
      }
    },
    dynamicDisabled({ model }) {
      return ['人民币', 'CNY'].includes(model.currency)
    },
    required: ({ model }) => {
      return ['人民币', 'CNY'].includes(model.currency) ? false : true
    },
    dynamicRules({ model }) {
      return ['人民币', 'CNY'].includes(model.currency)
        ? []
        : [
            {
              required: true,
              message: '金额不能为0！',
              validator(_rule: any, value: any, callback) {
                if (value == 0) {
                  return Promise.reject()
                }
                callback()
              }
            }
          ]
    },

    defaultValue: 0
  },
  {
    field: 'amount',
    label: '金额',
    component: 'InputNumber',
    itemHelpMessage: '当汇率为外币时,请填写对应外汇金额,金额会自动根据汇率进行换算填充',
    componentProps: ({ formModel }) => {
      return {
        prefix: '￥',
        suffix: 'RMB',
        min: 0,
        precision: 2,
        disabled: formModel.type === 2 ? false : !['人民币', 'CNY'].includes(formModel.currency) ? true : false,
        onChange(e) {
          if (formModel.type == 2 && !['人民币', 'CNY'].includes(formModel.currency)) {
            formModel.rate = new Decimal(e || 0).div(formModel.fg_amount || 0).toDecimalPlaces(6)
          }
        }
      }
    },
    rules: [
      {
        required: true,
        message: '金额不能为0！',
        validator(_rule: any, value: any, callback) {
          if (value == 0) {
            return Promise.reject()
          }
          callback()
        }
      }
    ],
    defaultValue: 0,
    required: true
  },

  {
    field: 'fgfee_amount',
    label: '手续费外汇金额',
    component: 'InputNumber',
    componentProps: ({ formModel }) => {
      return {
        // prefix: '￥',
        // suffix: 'RMB',
        min: 0,
        precision: 2,
        disabled: formModel.type === 2 ? false : ['人民币', 'CNY'].includes(formModel.currency) ? true : false,
        onChange(val) {
          formModel.fee = new Decimal(formModel.rate).mul(val || 0).toDecimalPlaces(2)
        }
      }
    },
    defaultValue: 0,
    dynamicDisabled({ model }) {
      return ['人民币', 'CNY'].includes(model.currency)
    }
  },
  {
    field: 'fee',
    label: '手续费',
    component: 'InputNumber',
    itemHelpMessage: '当汇率为外币时,请填写对应外汇金额,金额会自动根据汇率进行换算填充',
    // rules: [{ trigger: 'change', validator }],
    defaultValue: 0,
    componentProps: ({ formModel }) => {
      return {
        min: 0,
        precision: 2,
        disabled: formModel.type === 2 ? false : !['人民币', 'CNY'].includes(formModel.currency) ? true : false
      }
    }
  },
  {
    field: 'insurance_strid',
    label: '信保单号',
    component: 'Input'
  },
  {
    field: 'insurance_amount',
    label: '信保金额',
    component: 'InputNumber',
    itemHelpMessage: '当汇率为外币时,请填写对应外汇金额,金额会自动根据汇率进行换算填充',
    componentProps: ({ formModel }) => {
      return {
        // prefix: '￥',
        // suffix: 'RMB',
        min: 0,
        precision: 2,
        disabled: formModel.type === 2 ? false : !['人民币', 'CNY'].includes(formModel.currency) ? true : false
      }
    },
    defaultValue: 0
  },
  {
    field: 'fgins_amount',
    label: '信保外汇金额',
    component: 'InputNumber',
    componentProps: ({ formModel }) => {
      return {
        // prefix: '￥',
        // suffix: 'RMB',
        min: 0,
        precision: 2,
        disabled: formModel.type === 2 ? false : ['人民币', 'CNY'].includes(formModel.currency) ? true : false,
        onChange(val) {
          formModel.insurance_amount = new Decimal(formModel.rate).mul(val || 0).toDecimalPlaces(2)
        }
      }
    },
    defaultValue: 0,
    dynamicDisabled({ model }) {
      return ['人民币', 'CNY'].includes(model.currency)
    }
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea'
  },
  // {
  //   field: 'is_allot',
  //   label: '资金调拨',
  //   component: 'RadioButtonGroup',
  //   defaultValue: 0,
  //   componentProps: {
  //     options: [
  //       { label: '否', value: 0 },
  //       { label: '是', value: 1 }
  //     ]
  //   }
  // colProps: { span: 24 }
  // }
  {
    field: 'files',
    label: '附件',
    component: 'Upload',
    slot: 'Files'
  },
  {
    field: 'from_plaform_id',
    label: 'from_plaform_id',
    component: 'Input',
    show: false,
    ifShow: true
  },
  {
    field: 'to_plaform_id',
    label: 'to_plaform_id',
    component: 'Input',
    show: false,
    ifShow: true
  }
]

/** 更新 */
export const updateFormSchema = (Fn) =>
  createFormSchema(Fn).map((item) => {
    if (item.field === 'type') {
      item.componentProps = {
        ...item.componentProps,
        options: getType(true)
      }
    }
    return item
  })
