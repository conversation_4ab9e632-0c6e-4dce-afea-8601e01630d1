import { render } from 'vue'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { isNullOrUnDef } from '/@/utils/is'

/** 流水类型 */
export function getFundType(record) {
  const { type } = record
  const { is_allot: isAllot } = record
  const mapValue = {
    1: { text: '收入', color: 'green' },
    2: { text: '支出', color: 'red' },
    3: { text: '收入资金调拨', color: 'green' },
    4: { text: '支出资金调拨', color: 'red' }
  }
  if (record.type == 1 && isAllot == 1) {
    return useRender.renderTag(mapValue[3].text, mapValue[3].color)
  } else if (record.type == 2 && isAllot == 1) {
    return useRender.renderTag(mapValue[4].text, mapValue[4].color)
  } else {
    return useRender.renderTag(mapValue[type].text, mapValue[type].color)
  }
}

/** 流水状态 */
export const getStatus = (status) => {
  const mapValue = {
    0: { text: '未审批', color: 'red' },
    1: { text: '已审批', color: 'green' }
  }
  return useRender.renderTag(mapValue[status].text, mapValue[status].color)
}
/** 流水是否作废 */
export const getcancel = (cancel) => {
  const mapValue = {
    0: { text: '正常', color: 'green' },
    1: { text: '已作废', color: 'red' }
  }
  return useRender.renderTag(mapValue[cancel].text, mapValue[cancel].color)
}

/** 流水详情关联单据类型 */
export function getRelationType(type) {
  const mapValue = {
    1: { text: '收款单' },
    2: { text: '付款单' }
  }
  return useRender.renderTag(mapValue[type].text)
}

export const vCancel = {
  beforeUpdate(el, binding) {
    const vnode = getcancel(binding.value)
    render(vnode, el)
  }
}

/** 流水类型自定义指令 */
export const vType = {
  beforeUpdate(el, binding) {
    const vnode = getFundType(binding.value)
    render(vnode, el)
  }
}

/** 流水状态自定义指令 */
export const vStatus = {
  beforeUpdate(el, binding) {
    const vnode = getStatus(binding.value)
    render(vnode, el)
  }
}

/** 获取审核类型 */
export function getStatusOptions() {
  return [
    { label: '未审批', value: 0 },
    { label: '已审批', value: 1 }
  ]
}

/** 获取创建、编辑时的类型 */
// 默认返回全部，编辑传入参数去除资金调拨选项
export function getType(isUpdate = false) {
  return [
    { label: '收入', value: 1 },
    { label: '支出', value: 2 },
    { label: '资金调拨', value: 3, disabled: isUpdate }
  ]
}

/** 获取筛选时的类型 */
export function getSearchType() {
  return [
    { label: '收入', value: 1 },
    { label: '支出', value: 2 },
    { label: '收入资金调拨', value: 3 },
    { label: '支出资金调拨', value: 4 }
    // { label: '资金调拨', value: 3 }
  ]
}
/** 获取资金货币 */
export function getCurrency() {
  return [
    { label: '人民币', value: '人民币' },
    { label: '美元', value: '美元' }
  ]
}

/** 查询条件 */
export function getCondition() {
  return [
    { label: '剩余金额＞0.01，且部门为空', value: '0' },
    { label: '剩余金额＞0.01，且部门不为空', value: '1' },
    { label: '流水金额=剩余金额', value: '2' }
  ]
}

export function transformData2Import(data: Recordable[]): any[] {
  const fieldMap = {
    // strid: '流水单号',
    occurrence_at: '收款日期',
    type: '类型',
    amount: '金额',
    to_plaform: '收款方资金资料',
    from_plaform: '付款方资金资料',
    rate: '汇率',
    currency: '币种',
    fg_amount: '外汇金额',
    fee: '手续费',
    fgfee_amount: '手续费外汇金额',
    insurance_strid: '信保单号',
    remark: '备注',
    is_allot: '是否资金调拨',
    dept_id: '部门ID'
  }

  return data.map((obj) => {
    const cookedData: any = {
      occurrence_at: '',
      type: undefined,
      amount: undefined,
      to_plaform: '',
      from_plaform: '',
      rate: '',
      currency: '',
      fg_amount: undefined,
      fee: undefined,
      fgfee_amount: undefined,
      insurance_strid: '',
      remark: '',
      is_allot: undefined,
      dept_id: undefined
    }

    for (const key in cookedData) {
      if (fieldMap[key]) {
        cookedData[key] = ['occurrence_at', 'to_plaform', 'from_plaform'].includes(key)
          ? obj[fieldMap[key]].toString().trim()
          : obj[fieldMap[key]]
      }
    }
    console.log(cookedData)

    if (!cookedData.is_allot) {
      cookedData.is_allot = 0
    }
    if (!cookedData.dept_id) {
      cookedData.dept_id = 1
    }

    return cookedData
  })
}

export function handlePlaformChange(shall, fn) {
  {
    //下面的判断是否后续会有问题呢
    if (isNullOrUnDef(shall)) return
    // const formData = fn?.getFieldsValue()
    // if (formData && formData.dept_id) return

    return fn?.setFieldsValue({ dept_id: shall.dept_id })
  }
}
