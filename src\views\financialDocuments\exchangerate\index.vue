<template>
  <div>
    <BasicTable @register="registerTable" />
  </div>
</template>
<script setup lang="ts" name="/credential/log">
import { useTable, BasicTable } from '/@/components/Table'
import { columns } from './datas/datas'
import { getrateList } from '/@/api/financialDocuments/exchangerate'

const [registerTable] = useTable({
  title: '汇率列表',
  api: getrateList,
  columns,
  showTableSetting: false,
  tableSetting: { redo: false },
  rowKey: 'id',
  showIndexColumn: false
})
</script>
