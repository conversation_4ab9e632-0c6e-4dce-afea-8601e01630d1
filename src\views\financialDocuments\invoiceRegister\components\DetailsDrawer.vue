<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="详情" width="90%">
    <ScrollContainer v-if="details && details.items">
      <Descriptions title="" :column="2">
        <DescriptionsItem label="登记日期">{{ details.items.doc.date }}</DescriptionsItem>
        <DescriptionsItem label="登记人">{{ details.items.doc.register_name }}</DescriptionsItem>
        <DescriptionsItem label="登记单号">{{ details.items.doc.strid }}</DescriptionsItem>
      </Descriptions>
      <Descriptions title="采购明细">
        <DescriptionsItem> <BasicTable @register="registerTable" /></DescriptionsItem>
      </Descriptions>
    </ScrollContainer>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { detailsColumns } from '../datas/drawer.data'
import { detailsInvoice } from '/@/api/financialDocuments/invoiceRegister'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable } from '/@/components/Table'
import { Descriptions, DescriptionsItem } from 'ant-design-vue'
import { ScrollContainer } from '/@/components/Container'
import { nextTick, ref } from 'vue'

const details = ref<any>()
const [registerTable, { setTableData }] = useTable({
  title: '',
  dataSource: [],
  columns: detailsColumns,
  showIndexColumn: false,
  useSearchForm: false,
  rowKey: 'id',
  canResize: false
})

const [registerDrawer, {}] = useDrawerInner(async (data) => {
  const result = await detailsInvoice({ doc_id: data.record.id })
  details.value = result
  console.log(result.items.items)

  nextTick(() => {
    setTableData(result.items.items ?? [])
  })
})
</script>
