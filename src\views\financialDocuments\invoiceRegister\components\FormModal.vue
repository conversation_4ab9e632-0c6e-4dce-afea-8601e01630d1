<template>
  <BasicModal @register="registerModal" v-bind="$attrs" width="60%" @ok="handleSubmit" :minHeight="540" destroyOnClose>
    <BasicForm @register="registerForm" />
    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button @click="handleSubmit('sole')" type="primary">确认</a-button>
      <a-button @click="handleSubmit('next')" type="primary" v-if="!propsData.isUpdate">确认并填写下一条</a-button>
    </template>
  </BasicModal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'

import { schemas } from '../datas/modal.data'

const emit = defineEmits(['add-success', 'update-success', 'register'])

const propsData = ref<{ isUpdate: boolean; record?: Recordable }>({ isUpdate: false })
const [registerForm, { resetFields, setFieldsValue, validate, getFieldsValue }] = useForm({
  baseColProps: { span: 12 },
  showActionButtonGroup: false,
  labelCol: { span: 7 },
  schemas: schemas(),
  colon: true
})

const [registerModal, { closeModal, changeOkLoading, changeLoading }] = useModalInner(async (data) => {
  try {
    changeLoading(true)
    await resetFields()
    propsData.value = data

    if (propsData.value?.isUpdate) {
      setFieldsValue(propsData.value.record)
    }
  } catch (err) {
    console.log(err)
  } finally {
    changeLoading(false)
  }
})

async function handleSubmit(type: 'sole' | 'next') {
  changeOkLoading(true)
  try {
    await validate()
    const formData = await getFieldsValue()

    const params = {
      ...formData,
      key: propsData.value.isUpdate ? propsData.value.record?.key : Date.now()
    }

    if (propsData.value.isUpdate) {
      emit('update-success', params)
    } else {
      emit('add-success', params)
    }
    if (type === 'sole') return closeModal()

    resetFields()
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeOkLoading(false)
  }
}
</script>
