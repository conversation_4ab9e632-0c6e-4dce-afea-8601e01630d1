<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" show-footer @ok="handleSubmit" width="100%" destroyOnClose>
    <BasicForm @register="registerForm" />
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button @click="handleAddDetail" type="primary">新增</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :stop-button-propagation="true" />
        </template>
      </template>
    </BasicTable>
    <template #footer>
      <a-button @click="handleCancel" :disabled="isSubmiting">取消</a-button>
      <a-button type="primary" @click="handleSubmit('sole')" :disabled="isSubmiting">确认</a-button>
      <a-button type="primary" @click="handleSubmit('batch')" :disabled="isSubmiting">确认并录入下一条</a-button>
    </template>
    <FormModal @register="registerModal" @add-success="handleAdd" @update-success="handleUpdate" />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash-es'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import type { ActionItem } from '/@/components/Table'
import type { EditRecordRow } from '/@/components/Table'
import { useModal } from '/@/components/Modal'
import FormModal from './FormModal.vue'
import { getSchemas, currentEditKeyRef, getDetailColumns } from '../datas/drawer.data'
import { createInvoice, updateInvoice } from '/@/api/financialDocuments/invoiceRegister'

const isSubmiting = ref(false)
const beforeRecord: any = ref()
const emits = defineEmits(['register', 'success'])
const props = ref()
const [registerModal, { openModal }] = useModal()

const [registerDrawer, { changeLoading, closeDrawer }] = useDrawerInner(async (data) => {
  try {
    await changeLoading(true)
    isSubmiting.value = false
    props.value = data
    resetFields()
    setTableData([])
    if (data.type === 'edit') {
      setFieldsValue({ date: data.record.date })
      const baseTimestamp = new Date(0).getTime() // 1970年1月1日 00:00:00 UTC 的时间戳
      setTableData(
        data.record.items.map((item, index) => ({
          id: item.id,
          number: Number(item.number),
          pur_id: item.pur_id,
          amount: Number(item.amount),
          strid: item.pur_strid,
          key: baseTimestamp + index,
          // tax_amount: item.tax_amount,
          in_amount: item.in_amount,
          invoice_type: item.invoice_type,
          contracting_party: item.contracting_party,
          enterprise_name: item.enterprise_name,
          department_name: item.department
        }))
      )
    }
  } catch (err) {
    console.error(err)
  } finally {
    changeLoading(false)
  }
})

const [registerTable, { getDataSource, setTableData, insertTableDataRecord, deleteTableDataRecord, updateTableDataRecord }] = useTable({
  showIndexColumn: false,
  columns: getDetailColumns(),
  dataSource: [],
  pagination: false,
  rowKey: 'key',
  bordered: true,
  title: '采购明细',
  canResize: false,
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action'
  }
})

const [registerForm, { resetFields, validate, setFieldsValue }] = useForm({
  schemas: getSchemas(),
  baseColProps: { span: 6 },
  showActionButtonGroup: false,
  colon: true,
  labelCol: { style: { width: '80px' } }
})

function handleAddDetail() {
  openModal(true, { isUpdate: false })
}

//写入表格
async function handleAdd(params) {
  await insertTableDataRecord(params)
  isSubmiting.value = false
}

function createActions(record: EditRecordRow): ActionItem[] {
  if (!record.editable) {
    return [
      {
        // label: '编辑',
        icon: 'clarity:note-edit-line',
        tooltip: '编辑',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        onClick: handleEdit.bind(null, record)
      },
      {
        // label: '删除',
        icon: 'ant-design:delete-outlined',
        color: 'error',
        tooltip: '删除',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        popConfirm: {
          title: '是否确认删除',
          placement: 'left',
          confirm: handleDelete.bind(null, record)
        }
      }
    ]
  }
  return [
    {
      // label: '保存',
      icon: 'ant-design:check-outlined',
      tooltip: '保存',
      onClick: handleSave.bind(null, record)
    },
    {
      // label: '取消',
      icon: 'ant-design:close-outlined',
      color: 'error',
      tooltip: '取消',
      popConfirm: {
        title: '是否取消编辑',
        confirm: handleCancelClick.bind(null, record)
      }
    }
  ]
}

function handleEdit(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  beforeRecord.value = cloneDeep(record)

  record.onEdit?.(true)
}

async function handleDelete(record: EditRecordRow) {
  await deleteTableDataRecord(record.key)
}

async function handleSave(record: EditRecordRow) {
  // 校验
  message.loading({ content: '正在保存...', duration: 0, key: 'saving' })
  const valid = await record.onValid?.()
  if (valid) {
    try {
      const data = cloneDeep(record.editValueRefs)
      console.log(data)
      //TODO 此处将数据提交给服务器保存
      // ...
      // 保存之后提交编辑状态
      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
      }

      message.success({ content: '数据已保存', key: 'saving' })
    } catch (error) {
      message.error({ content: '保存失败', key: 'saving' })
    }
  } else {
    message.error({ content: '请填写正确的数据', key: 'saving' })
  }
}

function handleCancelClick(record: EditRecordRow) {
  currentEditKeyRef.value = ''
  record.onEdit?.(false, false)
  updateTableDataRecord(record.key, beforeRecord.value)
}

async function handleUpdate(params) {
  await updateTableDataRecord(params.key, params)
  isSubmiting.value = false
}

function handleCancel() {
  //还要清空表格
  setTableData([])
  resetFields()
  closeDrawer()
  currentEditKeyRef.value = ''
}

async function handleSubmit(type: 'sole' | 'batch') {
  try {
    isSubmiting.value = true
    const { date } = await validate()
    const details = getDataSource()

    if (details.length === 0) return message.error('请添加明细')
    const doc = props.value.type === 'create' ? { date } : { date, id: props.value.record.id }
    const params = {
      doc,
      items: details.map((item) => ({
        id: item.id || undefined,
        pur_id: item.pur_id.toString(),
        amount: item.amount,
        number: item.number,
        // tax_amount: item.tax_amount,
        in_amount: item.in_amount,
        invoice_type: item.invoice_type,
        contracting_party: item.contracting_party,
        enterprise_name: item.enterprise_name,
        dept_id: item.dept_id
      }))
    }
    props.value.type === 'create' ? await createInvoice(params) : await updateInvoice(params)
    switch (type) {
      case 'sole':
        await closeDrawer()
        break
    }
    emits('success')
    resetFields()
    currentEditKeyRef.value = ''
    setTableData([])
    isSubmiting.value = false
  } catch (err) {
    isSubmiting.value = false
    console.error('提交日志出错', err)
    throw new Error(`${err}`)
  }
}
</script>

<style scoped lang="less">
:deep(.vben-basic-table) {
  font-size: 12px;
}
</style>
