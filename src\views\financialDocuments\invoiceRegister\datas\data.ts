import { BasicColumn, FormSchema } from '/@/components/Table'

export const mapType = {
  1: '发票登记'
}

export const columns: BasicColumn[] = [
  {
    title: '登记日期',
    dataIndex: 'date',
    width: 150,
    resizable: true
  },
  {
    title: '登记人',
    dataIndex: 'register_name',
    width: 150,
    resizable: true
  },
  {
    title: '登记单号',
    dataIndex: 'strid',
    width: 150,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'date',
    label: '登记时间',
    component: 'SingleRangeDate',
    componentProps: {
      allowEmpty: [true, true],
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    },
    colProps: {
      span: 8
    }
  }
]
