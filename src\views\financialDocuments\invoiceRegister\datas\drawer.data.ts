import { h, ref, withDirectives } from 'vue'
import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import loadingDirective from '/@/directives/loading'
import { getRelatePurchaseList } from '/@/api/erp/inWarehouse'
import { mapType } from './data'
import { cpgetList } from '/@/api/erp/purchaseOrder'

export const currentEditKeyRef = ref('')

export const getSchemas: () => FormSchema[] = () => [
  {
    field: 'date',
    label: '登记日期',
    required: true,
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  }
]

export const propsToKeep = {
  amount: true,
  number: true,
  tax_amount: true,
  in_amount: true,
  pur_id: true
}

export const loading = ref(false)

export const getDetailColumns: () => BasicColumn[] = () => [
  {
    dataIndex: 'id',
    title: 'ID',
    width: 100,
    defaultHidden: true
  },
  {
    dataIndex: 'invoice_type',
    title: '发票类型',
    width: 150,
    resizable: true,
    editRow: true,
    editComponent: 'Select',
    editComponentProps: {
      options: [
        {
          label: '增值税专用发票',
          value: 1
        },
        {
          label: '普通发票',
          value: 2
        },
        {
          label: '不开票',
          value: 3
        }
      ]
    }
  },
  {
    dataIndex: 'number',
    title: '发票号码',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber'
  },
  {
    dataIndex: 'amount',
    title: '发票金额',
    width: 200,
    resizable: true,
    editRow: true,
    // 默认必填校验
    editRule: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      precision: 2,
      min: 0.01
    }
  },
  // {
  //   dataIndex: 'tax_amount',
  //   title: '采购订单税金额',
  //   width: 200,
  //   resizable: true,
  //   editRow: true,
  //   // 默认必填校验
  //   editRule: true,
  //   editComponent: 'InputNumber',
  //   editComponentProps: {
  //     precision: 2,
  //     min: 0.01
  //   }
  // },
  {
    dataIndex: 'in_amount',
    title: '采购订单含税金额',
    width: 200,
    resizable: true,
    editRow: true,
    // 默认必填校验
    editRule: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      precision: 2,
      min: 0.01
    }
  },
  {
    dataIndex: 'strid',
    title: '采购订单',
    width: 200,
    resizable: true,
    editRow: true,
    // 默认必填校验
    editRule: true,
    editComponent: 'PagingApiSelect',
    editComponentProps: ({ record }) => ({
      api: getRelatePurchaseList,
      params: {
        is_auth_status: 1
      },
      searchParamField: 'strid',
      selectProps: {
        fieldNames: { key: 'id', value: 'strid', label: 'strid' },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        dropdownRender: ({ menuNode }) => {
          const vNode = h('div', {}, menuNode)

          return withDirectives(vNode, [[loadingDirective, loading.value]])
        },
        onChange: (_, shall) => {
          record.pur_id = shall ? shall.id : undefined
          record.department_name = shall ? shall.department_name : undefined
          record.dept_id = shall ? shall.dept_id : undefined
        }
      },
      pagingMode: true,
      searchMode: true,
      resultField: 'items'
    })
  },
  {
    title: '采购订单部门',
    dataIndex: 'department_name',
    width: 150,
    resizable: true
  },
  {
    title: 'dept_id',
    dataIndex: 'dept_id',
    width: 150,
    resizable: true,
    defaultHidden: true
  },
  {
    dataIndex: 'contracting_party',
    title: '我司签约主体',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'PagingApiSelect',
    editComponentProps: {
      api: cpgetList,
      selectProps: {
        fieldNames: { key: 'key', value: 'name', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        optionFilterProp: 'name'
      },
      resultField: 'items'
    }
  },
  {
    dataIndex: 'enterprise_name',
    title: '供应商开票企业名称',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  }
]

export const detailsColumns: BasicColumn[] = [
  {
    title: '采购单号',
    dataIndex: 'pur_strid',
    width: 150,
    resizable: true
  },
  {
    title: '采购单日期',
    dataIndex: 'pur_date',
    width: 150,
    resizable: true
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    width: 150,
    resizable: true
  },
  {
    title: '采购单号',
    dataIndex: 'pur_strid',
    width: 150,
    resizable: true
  },
  {
    title: '发票金额',
    dataIndex: 'amount',
    width: 150,
    resizable: true
  },
  {
    title: '发票号码',
    dataIndex: 'number',
    width: 150,
    resizable: true
  },
  {
    title: '采购订单税金',
    dataIndex: 'tax_amount',
    width: 150,
    resizable: true
  },
  {
    title: '采购订单含金额',
    dataIndex: 'in_amount',
    width: 150,
    resizable: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 150,
    resizable: true,
    customRender: ({ value }) => (value ? mapType[value] : '')
  }
]
