import { h, ref, withDirectives } from 'vue'
import { getRelatePurchaseList } from '/@/api/erp/inWarehouse'
import { FormSchema } from '/@/components/Form'
import loadingDirective from '/@/directives/loading'
import { cpgetList } from '/@/api/erp/purchaseOrder'
export const loading = ref(false)

export const schemas: () => FormSchema[] = () => [
  {
    field: 'invoice_type',
    label: '开票类型',
    component: 'Select',
    required: true,
    componentProps: {
      options: [
        {
          label: '增值税专用发票',
          value: 1
        },
        {
          label: '普通发票',
          value: 2
        },
        {
          label: '不开票',
          value: 3
        }
      ]
    }
  },
  { required: true, field: 'number', label: '发票号码', component: 'InputNumber' },
  {
    required: true,
    field: 'amount',
    label: '发票金额',
    component: 'InputNumber',
    componentProps: {
      precision: 2,
      min: 0.01
    }
  },
  // {
  //   field: 'tax_amount',
  //   label: '采购订单税金额',
  //   required: true,
  //   component: 'InputNumber',
  //   componentProps: {
  //     precision: 2,
  //     min: 0.01
  //   }
  // },
  {
    field: 'in_amount',
    label: '采购订单含税金额',
    component: 'InputNumber',
    required: true,
    componentProps: {
      precision: 2,
      min: 0.01
    }
  },
  {
    field: 'strid',
    label: '关联采购订单',
    component: 'PagingApiSelect',
    required: true,
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: ({ formModel }) => ({
      api: getRelatePurchaseList,
      params: {
        is_auth_status: 1
      },
      searchParamField: 'strid',
      selectProps: {
        fieldNames: { key: 'id', value: 'strid', label: 'strid' },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        dropdownRender: ({ menuNode }) => {
          const vNode = h('div', {}, menuNode)
          return withDirectives(vNode, [[loadingDirective, loading.value]])
        },
        onChange: (_, shall) => {
          formModel.pur_id = shall ? shall.id : undefined
          formModel.department_name = shall ? shall.department_name : undefined
          formModel.dept_id = shall ? shall.dept_id : undefined
        }
      },
      pagingMode: true,
      searchMode: true,
      resultField: 'items'
    })
  },
  {
    field: 'department_name',
    label: '采购订单部门',
    component: 'Input',
    componentProps: {
      disabled: true
    }
  },
  {
    field: 'dept_id',
    label: 'dept_id',
    component: 'Input',
    show: false
  },
  {
    field: 'contracting_party',
    label: '我司签约主体',
    component: 'PagingApiSelect',
    required: true,
    componentProps: {
      api: cpgetList,
      selectProps: {
        fieldNames: { key: 'key', value: 'name', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        optionFilterProp: 'name'
      },
      resultField: 'items'
    }
  },
  {
    field: 'enterprise_name',
    label: '供应商开票企业名称',
    component: 'Input',
    required: true
  }
]
