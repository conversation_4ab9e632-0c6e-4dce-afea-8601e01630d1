<template>
  <div
    ><BasicTable @register="registerTable">
      <template #form-advanceBefore>
        <a-button type="primary" @click="handleExport" :loading="buttonloading" :disabled="buttonloading" class="mr-8px">
          <cloud-download-outlined /> 导出搜索结果
        </a-button>
      </template>
      <template #toolbar>
        <Dropdown>
          <template #overlay>
            <Menu @click="handleMenuClick">
              <MenuItem key="confirm" v-if="hasPermission([624])">
                <CheckOutlined />
                信息确认
              </MenuItem>
              <MenuItem key="delete" v-if="hasPermission([623])">
                <DeleteOutlined />
                删除
              </MenuItem>
            </Menu>
          </template>
          <a-button>
            批量操作
            <DownOutlined />
          </a-button>
        </Dropdown>

        <a-button type="primary" @click="handleEdit('create')" v-if="hasPermission([621])" :loading="buttonloading">创建</a-button>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <UpdateDrawer @register="registerUpdateDrawer" @success="reload" />
    <DetailsDrawer @register="registerDetailsDrawer" />
  </div>
</template>

<script setup lang="ts">
import { Dropdown, Menu, MenuItem, message } from 'ant-design-vue'
import { CheckOutlined, DeleteOutlined, DownOutlined } from '@ant-design/icons-vue'
import { isNullOrUnDef } from '/@/utils/is'
import { getInvoiceList, deleteInvoice, setStatusInvoice, Invoiceexport } from '/@/api/financialDocuments/invoiceRegister'
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import { usePermission } from '/@/hooks/web/usePermission'
import { columns, schemas } from './datas/data'
import { useDrawer } from '/@/components/Drawer'
import UpdateDrawer from './components/UpdateDrawer.vue'
import DetailsDrawer from './components/DetailsDrawer.vue'
import { TType } from './datas/type'
import { downloadByData } from '/@/utils/file/download'
import { ref } from 'vue'
const buttonloading = ref(false)

const { hasPermission } = usePermission()
const [registerUpdateDrawer, { openDrawer: openUpdateDrawer, setDrawerProps: setUpdateDrawerProps }] = useDrawer()
const [registerDetailsDrawer, { openDrawer: openDetailsDrawer }] = useDrawer()

const [registerTable, { reload, setLoading, getSelectRowKeys, getForm }] = useTable({
  title: '发票登记',
  api: getInvoiceList,
  columns,
  showTableSetting: true,
  rowKey: 'id',
  showIndexColumn: false,
  useSearchForm: true,
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  formConfig: {
    labelAlign: 'left',
    colon: true,
    labelWidth: 110,
    baseColProps: {
      span: 6
    },
    autoSubmitOnEnter: true,
    actionColOptions: {
      span: 24
    },
    schemas: schemas,
    fieldMapToTime: [['date', ['date1', 'date2'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  },
  // beforeFetch: (params) => ({ ...params, date: dayjs(params.date).format('YYYY-MM-DD') }),
  // 下面的权限值是根据上面的批量操作按钮权限
  rowSelection: hasPermission([624, 623])
    ? {
        type: 'checkbox',
        getCheckboxProps: (record) => {
          return {
            disabled: record.status === 1 || isNullOrUnDef(record.status)
          }
        }
      }
    : void 0
})

function createActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = [
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, 'edit', record),
      disabled: record.status === 1,
      ifShow: hasPermission([622])
    },

    {
      label: '详情',
      onClick: ((record) => {
        openDetailsDrawer(true, { record })
      }).bind(null, record),
      ifShow: hasPermission([634])
    }
  ]
  return editButtonList
}

async function handleMenuClick({ key }) {
  const selectRowKeys = await getSelectRowKeys()
  if (selectRowKeys.length === 0) {
    return message.warn('请选择需要操作的数据!')
  }
  switch (key) {
    case 'confirm':
      await handleComfirm(selectRowKeys)
      break
    case 'delete':
      await handleDelete(selectRowKeys)
      break
    default:
      break
  }
}

async function handleComfirm(selectRowKeys) {
  try {
    setLoading(true)
    await setStatusInvoice({ doc_ids: selectRowKeys })
    reload()
    setLoading(false)
  } catch (e) {
    console.error(e)
    setLoading(false)
  }
}

function handleEdit(type: TType, record?: Recordable) {
  setUpdateDrawerProps({ title: type === 'create' ? '创建' : '编辑' })
  openUpdateDrawer(true, { type, record })
}

async function handleDelete(selectRowKeys) {
  try {
    setLoading(true)
    await deleteInvoice({ doc_ids: selectRowKeys })
    reload()
    setLoading(false)
  } catch (e) {
    console.error(e)
    setLoading(false)
  }
}

async function handleExport() {
  try {
    buttonloading.value = true

    const params = await getForm()?.getFieldsValue()
    console.log(params)

    const response = await Invoiceexport({
      ...params
    })

    //将二进制流转xlsx文件并下载
    downloadByData(response as any, `采购发票-${+new Date()}.xlsx`)
    message.success('导出成功')
  } catch (err) {
    message.error('导出失败')
    throw new Error(err)
  } finally {
    buttonloading.value = false
  }
}
</script>

<style scoped lang="less"></style>
