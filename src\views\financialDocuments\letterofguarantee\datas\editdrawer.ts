import { cpgetList } from '/@/api/erp/purchaseOrder'
import { getProjectList } from '/@/api/revisit'
import { FormSchema } from '/@/components/Form'

export const schemas: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false
  },
  {
    field: 'project_number',
    label: '项目ID',
    component: 'PagingApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: getProjectList,
        resultField: 'items',
        labelField: 'title',
        valueField: 'id',
        params: {
          is_backletter: 1
        },
        searchMode: true,
        pagingMode: true,
        searchParamField: 'project_number',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'project_number',
            label: 'project_number'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'id',
          allowClear: true,
          onChange(_, shall) {
            console.log(shall)
            if (!shall) {
              formModel.project_name = undefined
              formModel.pm_name = undefined
              formModel.bk_packing_strids = undefined
              formModel.bk_source_uniqids = undefined
              formModel.pm_id = undefined
              formModel.is_receip = undefined
            }
            formModel.project_name = shall.project_name
            formModel.pm_name = shall.project_incharge_name
            formModel.bk_packing_strids = shall.bk_packing_strids
            formModel.bk_source_uniqids = shall.bk_source_uniqids
            formModel.pm_id = shall.project_incharge
            formModel.is_receip = shall.bk_source_uniqids.length > 0 ? 1 : 2
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    required: true
  },
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input',
    dynamicDisabled: true
  },
  {
    field: 'pm_name',
    label: '项目经理',
    component: 'Input',
    dynamicDisabled: true
  },
  {
    field: 'bk_packing_strids',
    label: '装箱单号',
    component: 'Input',
    slot: 'bkPackingStrids',
    dynamicDisabled: true,
    show: ({ model }) => {
      return model.bk_packing_strids?.length > 0
    }
  },
  {
    field: 'bk_source_uniqids',
    label: '未收款销售单号 ',
    component: 'Input',
    slot: 'bkSourceUniqids',
    dynamicDisabled: true,
    show: ({ model }) => {
      return model.bk_source_uniqids?.length > 0
    }
  },
  {
    field: 'pm_id',
    label: '项目经理',
    component: 'Input',
    show: false
  },

  {
    field: 'is_receip',
    label: '是否收齐款',
    component: 'Select',
    dynamicDisabled: true,
    required: true,
    componentProps: {
      options: [
        {
          label: '是',
          value: 2
        },
        {
          label: '否',
          value: 1
        }
      ]
    }
  },
  {
    field: 'contracting_party',
    label: '文件盖章主体',
    component: 'PagingApiSelect',
    required: true,
    componentProps: {
      api: cpgetList,
      selectProps: {
        fieldNames: { key: 'key', value: 'name', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        optionFilterProp: 'name'
      },
      resultField: 'items'
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'file',
    label: '需盖章文件',
    component: 'Upload',
    slot: 'File',
    required: true
  },
  {
    field: 'other_file',
    label: '其他附件',
    component: 'Upload',
    slot: 'otherfile'
  }
]
