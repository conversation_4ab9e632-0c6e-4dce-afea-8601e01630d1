<template>
  <BasicModal @register="register" title="分摊模式" @ok="handleOk" width="800px" @close="handleClose">
    <BasicForm @register="registerform" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { UploadFile, message } from 'ant-design-vue'
import { ref } from 'vue'
import { schemasFn } from '../datas/Modal'
import { getOtherExpendupdate } from '/@/api/financialDocuments/otherExpendApportion'
//record
const init_ids = ref([])
const [register, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  console.log(data)
  resetFields()
  init_ids.value = data.record
  resetSchema(await schemasFn(assignment))
})
const [registerform, { setFieldsValue, resetSchema, validate, resetFields }] = useForm({
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelCol: { span: 5 }
})

//附件
const filesList = ref<UploadFile[]>([])

async function assignment(shall: any) {
  if (!shall) return
  setFieldsValue({
    share_account_code: shall.account_code
  })
}

const emit = defineEmits(['relaod', 'register'])
// 提交
async function handleOk() {
  try {
    await changeOkLoading(true)
    const values = await validate()
    if (values.share_status == '启用') {
      values.share_status = 1
    }
    console.log(init_ids.value)
    const params = {
      ...values,
      ids: init_ids.value
    }
    const res = await getOtherExpendupdate(params)
    if (res.news == 'success') {
      emit('relaod')
      await closeModal()
      message.success('分摊模式设置成功')
      filesList.value = []
    }
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  }
}
function handleClose() {
  closeModal()
  emit('relaod')
}
</script>
