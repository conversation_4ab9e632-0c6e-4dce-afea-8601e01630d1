<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" @close="close" title="详情" width="90%">
    <ScrollContainer>
      <Descriptions title="其他支出单详情" :column="2">
        <DescriptionsItem label="日期">{{ records.created_at || '-' }}</DescriptionsItem>
        <DescriptionsItem label="支出科目">{{ records.account_name || '-' }} </DescriptionsItem>
        <DescriptionsItem label="支出代码">{{ records.account_code || '-' }}</DescriptionsItem>
        <DescriptionsItem label="分摊科目">{{ records.share_account_name || '-' }} </DescriptionsItem>
        <DescriptionsItem label="分摊科目代码">{{ records.share_account_code || '-' }}</DescriptionsItem>
        <DescriptionsItem label="分摊模式">{{ records.share_setting_name || '-' }}</DescriptionsItem>
        <DescriptionsItem label="分摊状态">
          <Tag :color="sharestatus[records.share_status]?.color"> {{ sharestatus[records.share_status]?.text || '-' }}</Tag>
        </DescriptionsItem>
        <DescriptionsItem label="支出部门">{{ records.department || '-' }}</DescriptionsItem>
        <DescriptionsItem label="支出摘要">{{ records.desc || '-' }}</DescriptionsItem>
        <DescriptionsItem label="支出金额">{{ records.amount || '-' }} ￥</DescriptionsItem>
        <DescriptionsItem label="往来单位类型">
          <Tag :color="correstype[records.corres_type]?.color"> {{ correstype[records.corres_type]?.text || '-' }}</Tag>
        </DescriptionsItem>
        <DescriptionsItem label="往来单位">{{ records.corres_pondent || '-' }} </DescriptionsItem>
        <DescriptionsItem label="附件">
          <ul>
            <li v-for="item in records.files" :key="item">
              <a :href="item" target="_blank">{{ item || '-' }}</a>
            </li>
          </ul>
        </DescriptionsItem>
      </Descriptions>
    </ScrollContainer>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { Descriptions, DescriptionsItem, Tag } from 'ant-design-vue'
import { ScrollContainer } from '/@/components/Container'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { getOtherExpenddetail } from '/@/api/financialDocuments/otherExpendApportion'
import { correstype, sharestatus } from '../datas/datas'
const emit = defineEmits(['register', 'relaod'])
const records: Recordable = ref({})
const [registerDrawer, { closeDrawer }] = useDrawerInner(async (data) => {
  const detaildata: any = await getOtherExpenddetail({ id: data.record.id })
  console.log(detaildata)

  records.value = detaildata.items
})

//部门

function close() {
  closeDrawer()
  emit('relaod')
}
</script>
