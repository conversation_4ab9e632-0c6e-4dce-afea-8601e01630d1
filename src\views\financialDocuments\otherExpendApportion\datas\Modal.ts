import { getShareManageList } from '/@/api/baseData/shareManage'
import { getCategory } from '/@/api/financialDocuments/otherIncome'
import { FormSchema } from '/@/components/Form'

export async function schemasFn(hand: Function): Promise<FormSchema[]> {
  const schemas: FormSchema[] = [
    {
      field: 'share_account_name',
      component: 'ApiSelect',
      label: '分摊科目名称',
      colProps: {
        span: 18
      },
      componentProps: () => {
        return {
          api: getCategory,
          resultField: 'items',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'account_name',
              label: 'account_name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'account_name',
            allowClear: true,
            onChange: async (_, shall) => {
              hand && hand(shall)
            }
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'share_account_code',
      component: 'Input',
      label: '分摊科目代码',
      colProps: {
        span: 18
      },
      componentProps: {
        disabled: true,
        placeholder: '请输入'
      }
    },
    {
      field: 'share_setting_id',
      required: true,
      component: 'ApiSelect',
      label: '分摊模式',
      colProps: {
        span: 18
      },
      componentProps: () => {
        return {
          api: getShareManageList,
          resultField: 'items',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'id',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'account_name',
            // itEmpty: true,
            allowClear: true
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'share_status',
      required: true,
      component: 'Select',
      label: '分摊状态',
      colProps: {
        span: 18
      },
      defaultValue: '启用',
      componentProps: {
        defaultValue: 1,
        options: [
          {
            label: '禁用',
            value: 0
          },
          {
            label: '启用',
            value: 1
          }
        ],
        placeholder: '请选择',
        allowClear: true,
        disabled: true
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    }
  ]
  return schemas
}
