import type { BasicColumn } from '/@/components/Table'
import type { FormSchema } from '/@/components/Form'
import { formatter } from '/@/utils/erp/formatterPrice'
import { getDeptSelectTree } from '/@/api/admin/dept'
import { getCategory } from '/@/api/financialDocuments/otherIncome'

export //status
const statusMap = {
  0: { color: '', text: '待执行' },
  1: { color: 'green', text: '生效' },
  3: { color: 'cyan', text: '待执行' },
  4: { color: 'blue', text: '执行中' }
}
export const checkMap = {
  0: { color: '', text: '未审核' },
  1: { color: 'green', text: '通过' },
  2: { color: 'red', text: '驳回' }
}
export const typeMap = {
  1: { text: '个人报销', color: 'default' },
  2: { text: '款项支出', color: 'blue' },
  3: { text: '财务费用', color: 'green' }
}
export const correstype = {
  1: { text: '员工', color: 'skyblue' },
  2: { text: '部门', color: 'blue' },
  3: { text: '客户', color: 'green' },
  4: { text: '供应商', color: 'green' },
  5: { text: '其他', color: 'pink' }
}
export const sharestatus = {
  0: { text: '未启用', color: 'red' },
  1: { text: '启用', color: 'green' }
}
export const cancel = {
  0: { text: '', color: '' },
  1: { text: '取消', color: 'red' }
}
export const isshare = {
  0: { text: '不分摊', color: 'red' },
  1: { text: '分摊', color: 'green' }
}
export const bindfund = {
  0: { color: 'red', text: '未关联 ' },
  1: { color: 'green', text: '已关联' }
}
export const columns: BasicColumn[] = [
  {
    title: '创建日期',
    dataIndex: 'created_at',
    width: 200,
    resizable: true
  },
  {
    title: '支出单明细号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '其他支出单类型',
    dataIndex: 'type',
    width: 200,
    resizable: true
  },
  // {
  //   title: '是否关联流水',
  //   dataIndex: 'is_bind_fund',
  //   width: 100,
  //   resizable: true
  // },
  {
    title: '收/付款日期',
    dataIndex: 'collection_at',
    width: 100,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'department_name',
    width: 200,
    resizable: true
  },
  {
    title: '支出科目',
    dataIndex: 'account_name',
    width: 200,
    resizable: true
  },
  {
    title: '支出科目代码',
    dataIndex: 'account_code',
    width: 200,
    resizable: true
  },
  {
    title: '摘要',
    dataIndex: 'desc',
    width: 200
  },
  {
    title: '是否分摊',
    dataIndex: 'is_share',
    width: 200
  },
  {
    title: '分摊状态',
    dataIndex: 'share_status',
    width: 200
  },
  // {
  //   title: '财务审核',
  //   dataIndex: 'is_check',
  //   width: 100,
  //   resizable: true
  // },
  // {
  //   title: '取消状态',
  //   dataIndex: 'is_cancel',
  //   width: 200
  // },

  {
    title: '分摊日期',
    dataIndex: 'share_at',
    width: 200,
    resizable: true
  },
  {
    title: '分摊科目',
    dataIndex: 'share_account_name',
    width: 200,
    resizable: true
  },
  {
    title: '分摊科目代码',
    dataIndex: 'share_account_code',
    width: 200,
    resizable: true
  },
  {
    title: '分摊模式名称',
    dataIndex: 'share_setting_name',
    width: 200,
    resizable: true
  },
  {
    title: '往来单位分类',
    dataIndex: 'corres_type',
    width: 200,
    resizable: true
  },
  {
    title: '往来单位',
    dataIndex: 'corres_pondent',
    width: 200,
    resizable: true
  },
  {
    title: '外汇金额',
    dataIndex: 'foreign_currency_amount',
    width: 120,
    customRender: ({ value }) => {
      return formatter.format(value)
    },
    resizable: true
  },
  {
    title: '支出金额',
    dataIndex: 'amount',
    width: 120,
    customRender: ({ value }) => {
      return formatter.format(value)
    },
    resizable: true
  },

  {
    title: '附件',
    dataIndex: 'files',
    width: 200
  }
]

// const statusOptions = [
//   { label: '个人报销', value: 1 },
//   { label: '款项支出', value: 2 }
// ]
// const statusOption = [{ label: '财务费用', value: 3 }]

export function formConfigFn() {
  const schemas: FormSchema[] = [
    {
      field: 'is_bind_fund',
      label: '是否关联流水',
      component: 'Select',
      defaultValue: 1,
      componentProps: {
        options: [
          { label: '未关联', value: 0 },
          { label: '已关联', value: 1 }
        ]
      }
    },
    {
      field: 'is_share',
      label: '是否分摊',
      component: 'Select',
      defaultValue: 1,
      componentProps: {
        options: [
          { label: '不分摊', value: 0 },
          { label: '分摊', value: 1 }
        ]
      }
    },
    {
      field: 'is_production',
      label: '是否产品部',
      component: 'Select',
      defaultValue: 0,
      componentProps: {
        options: [
          { label: '不是', value: 0 },
          { label: '是', value: 1 }
        ]
      }
    },
    {
      field: 'share_type',
      label: '分摊模式设置',
      component: 'Select',
      defaultValue: 0,
      componentProps: {
        options: [
          { label: '未设置', value: 0 },
          { label: '已设置', value: 1 }
        ]
      }
    },
    {
      field: 'share_status',
      label: '分摊状态',
      component: 'Select',
      defaultValue: 0,
      componentProps: {
        options: [
          { label: '未启用', value: 0 },
          { label: '启用', value: 1 }
        ]
      }
    },

    {
      field: 'strid',
      label: '其他支出单号-明细',
      component: 'Input'
    },
    {
      field: 'doc_strid',
      label: '其他支出单号',
      component: 'Input'
    },
    {
      field: 'desc',
      label: '摘要',
      component: 'Input'
    },
    // {
    //   field: 'is_check',
    //   label: '财务审核',
    //   component: 'Select',
    //   defaultValue: 1,
    //   componentProps: {
    //     options: [
    //       { label: '否', value: 0 },
    //       { label: '通过', value: 1 },
    //       { label: '驳回', value: 2 }
    //     ]
    //   },
    //
    // },

    // {
    //   field: 'desc',
    //   label: '摘要',
    //   component: 'Input',
    //
    // },
    //字段待定
    {
      field: 'created_at',
      label: '创建日期',
      component: 'SingleRangeDate',

      componentProps: {
        allowEmpty: [true, true],
        showTime: false,
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        style: {
          width: '100%'
        }
      }
    },
    {
      field: 'dept_id',
      label: '部门',
      component: 'ApiTreeSelect',
      componentProps: {
        api: getDeptSelectTree,
        immediate: false,
        lazyLoad: true,
        treeSelectProps: {
          fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
          placeholder: '请选择',
          showSearch: true,
          optionFilterProp: 'name',
          treeDefaultExpandAll: true,
          filterTreeNode: (search, item) => {
            if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
            return false
          }
        }
      }
    },
    {
      field: 'share_account_code',
      label: '分摊科目',
      component: 'PagingApiSelect',
      componentProps: {
        api: getCategory,
        resultField: 'items',
        labelField: 'account_name',
        valueField: 'account_name',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'account_code',
            label: 'account_name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'account_name',
          allowClear: true
        },
        params: {
          status: 1
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'account_code',
      label: '支出科目',
      component: 'PagingApiSelect',
      componentProps: {
        api: getCategory,
        resultField: 'items',
        labelField: 'account_name',
        valueField: 'account_name',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'account_code',
            label: 'account_name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'account_name',
          allowClear: true
        },

        params: {
          status: 1
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'share_setting_name',
      label: '分摊模式名称',
      component: 'Input'
    },
    {
      field: 'collection_at',
      label: '收付款日期',
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD'
      }
    },
    {
      field: 'is_operate',
      label: '支出部门是否为运营中心',
      component: 'Select',
      defaultValue: 0,
      componentProps: {
        options: [
          { label: '否', value: 0 },
          { label: '是', value: 1 }
        ]
      }
    }
  ]
  return schemas
}
