<template>
  <div>
    <BasicTable
      @register="registerTable"
      :search-info="{ is_finance: routeName == '/financialDocuments/otherExpendApportionFinance' ? 1 : 0 }"
      :data-cachekey="routePath"
    >
      <template #toolbar>
        <Button :disabled="generateBtnStatus" v-if="hasPermission([241])" type="primary" @click="handleCreate"> 设置分摊 </Button>
        <Button v-if="hasPermission([243])" type="primary" @click="onOpenCreate"> 新增分摊模式 </Button>
      </template>
      <template #bodyCell="{ text, column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :stopButtonPropagation="true" />
        </template>
        <template v-if="column.key === 'is_check'">
          <Tag :color="checkMap[record.is_check]?.color"> {{ checkMap[record.is_check]?.text }}</Tag>
        </template>
        <template v-if="column.key === 'corres_type'">
          <Tag :color="correstype[record.corres_type]?.color"> {{ correstype[record.corres_type]?.text || '-' }}</Tag>
        </template>
        <template v-if="column.key === 'is_cancel'">
          <Tag :color="cancel[record.is_cancel]?.color"> {{ cancel[record.is_cancel]?.text }}</Tag>
        </template>
        <template v-if="column.key === 'is_share'">
          <Tag :color="isshare[record.is_share]?.color"> {{ isshare[record.is_share]?.text }}</Tag>
        </template>
        <template v-if="column.key === 'type'">
          <Tag :color="typeMap[record.type]?.color"> {{ typeMap[record.type]?.text }}</Tag>
        </template>
        <template v-if="column.key === 'is_bind_fund'">
          <Tag :color="bindfund[record.is_bind_fund]?.color"> {{ bindfund[record.is_bind_fund]?.text }}</Tag>
        </template>
        <template v-if="column.key === 'share_status'">
          <Tag :color="sharestatus[record.share_status]?.color"> {{ sharestatus[record.share_status]?.text }}</Tag>
        </template>
        <template v-if="column.dataIndex === 'files'">
          <TableImg :size="60" :simpleShow="true" :imgList="text" />
        </template>
      </template>
      <template #footer="data">
        <div class="footer">支出金额合计(当前页)：{{ statisticsAmount(data) }}</div>
      </template>
    </BasicTable>
    <ApportionModal @register="registerModal" @relaod="handleSuccess" />
    <DetailsDrawer @register="registerOtherExpendDrawer" @relaod="handleSuccess" />
    <ShareDrawer @register="registerDrawer" @relaod="handleSuccess" />
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import { Tag, Button } from 'ant-design-vue'
import { usePermission } from '/@/hooks/web/usePermission'
import { getOtherExpendList } from '/@/api/financialDocuments/otherExpendApportion'
import { columns, formConfigFn, checkMap, correstype, cancel, sharestatus, isshare, typeMap, bindfund } from './datas/datas'
import { BasicTable, useTable, TableAction, TableImg } from '/@/components/Table'
import type { ActionItem, EditRecordRow } from '/@/components/Table'
import { useDrawer } from '/@/components/Drawer'
import DetailsDrawer from './components/DetailsDrawer.vue'
import { ref } from 'vue'
import ApportionModal from './components/ApportionModal.vue'
import { useModal } from '/@/components/Modal'
import ShareDrawer from '../../baseData/shareManage/components/ShareDrawer.vue'
import { add } from '/@/utils/math'

const route = useRoute()
const { name: routeName, path: routePath } = route
const { hasPermission } = usePermission()
//初始化
const [registerOtherExpendDrawer, { openDrawer: openDetailsDrawer, setDrawerProps: setDetailsDrawerProps }] = useDrawer()
const [registerTable, { reload, clearSelectedRowKeys, getSelectRowKeys }] = useTable({
  title: '其他支出单分摊列表',
  api: getOtherExpendList,
  columns,
  titleHelpMessage: '分摊模式规则：填了分摊科目就按照分摊科目进行分摊，如果没有就按照明细的科目进行分摊',
  showTableSetting: true,
  afterFetch: () => {
    clearSelectedRowKeys()
  },
  useSearchForm: true,
  showIndexColumn: false,
  formConfig: {
    labelWidth: 170,
    actionColOptions: {
      span: 24
    },
    //自动展开行
    fieldMapToTime: [['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]],
    showAdvancedButton: false,
    baseColProps: {
      span: 8
    },
    schemas: formConfigFn() //这里还不如直接定义any类型
  },
  rowSelection: {
    type: 'checkbox',
    onChange: handleChange,
    getCheckboxProps: (record) => {
      if (record.is_bind_fund == 1 && record.share_status == 0 && record.is_share == 1) {
        return { disabled: false }
      } else {
        return { disabled: true }
      }
    }
  },
  rowKey: 'id',
  pagination: {
    pageSize: 10,
    pageSizeOptions: ['10', '20', '100', '500', '1000']
  },
  actionColumn: hasPermission([242])
    ? {
        width: 290,
        title: '操作',
        dataIndex: 'action',
        fixed: 'right'
      }
    : void 0
})
function createActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission([242])
    }
  ]

  if (record.has_fund == 1) {
    editButtonList.forEach((item) => {
      item.disabled = true
    })
  }

  return editButtonList
}

// 底部统计函数
function statisticsAmount(data) {
  return data.reduce((total, item) => add(total, item.amount), 0)
}

//详情
function handleDetail(record) {
  setDetailsDrawerProps({ title: '其他支出单分摊详情' })
  openDetailsDrawer(true, {
    record
  })
}
//新增
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
function onOpenCreate() {
  setDrawerProps({ title: '新增分摊管理' })
  openDrawer(true, {
    type: 'create'
  })
}
//勾选
const generateBtnStatus = ref(true)
const allSelectdata = ref<any>([])
function handleChange() {
  if (getSelectRowKeys().length == 0) {
    generateBtnStatus.value = true
  } else {
    generateBtnStatus.value = false
  }
}
//分摊
const [registerModal, { openModal }] = useModal()
function handleCreate() {
  const allSelectRows = getSelectRowKeys()
  allSelectRows.forEach((item) => {
    allSelectdata.value.push(item)
  })
  openModal(true, {
    record: allSelectdata.value
  })
  allSelectdata.value = []
}
function handleSuccess() {
  clearSelectedRowKeys()
  reload()
}
</script>

<style scoped lang="less">
.footer {
  font-size: 16px;
  font-weight: bold;
}
</style>
