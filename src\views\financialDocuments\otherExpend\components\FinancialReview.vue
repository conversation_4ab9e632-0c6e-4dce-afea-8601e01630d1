<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" @close="close" title="财务审核" showFooter width="90%">
    <template #footer>
      <Button @click="close">取消</Button>
      <!-- <Button type="primary" @click="handleapportion" :disabled="generateBtnStatus">明细分摊</Button> -->
      <Button type="success" @click="handlePass" :disabled="!generateBtnStatus">通过</Button>
      <Button type="danger" @click="debounceHandleReject" :disabled="generateBtnStatus">驳回</Button>
    </template>
    <ScrollContainer>
      <Descriptions title="其他支出单详情" :column="2">
        <DescriptionsItem label="单据类型">
          {{ orderMap[records.order]?.text }}
        </DescriptionsItem>
        <DescriptionsItem label="日期">{{ records.created_at }}</DescriptionsItem>
        <DescriptionsItem label="我司签约主体">{{ records.contracting_party }}</DescriptionsItem>
        <!-- <DescriptionsItem label="单号">{{ record.strid }}</DescriptionsItem> -->
        <!-- <DescriptionsItem label="客户">{{ records.client_name }}</DescriptionsItem> -->
        <DescriptionsItem label="申请人">{{ records.applicant_name }}</DescriptionsItem>
        <DescriptionsItem label="创建人">{{ records.creator_name }}</DescriptionsItem>
        <DescriptionsItem label="负责人">{{ records.inCharge_name }}</DescriptionsItem>
        <DescriptionsItem label="账号">{{ records.account }}</DescriptionsItem>
        <DescriptionsItem label="账号名称">{{ records.account_name }}</DescriptionsItem>
        <DescriptionsItem label="开户行">{{ records.bank }}</DescriptionsItem>
        <DescriptionsItem label="部门">{{ records.department }}</DescriptionsItem>
        <DescriptionsItem label="支出摘要">{{ records.desc }}</DescriptionsItem>
        <DescriptionsItem label="支出备注">{{ records.remark }}</DescriptionsItem>
        <DescriptionsItem label="支出金额">{{ records.amount }} ￥</DescriptionsItem>
        <DescriptionsItem label="币种">{{ records.currency }} </DescriptionsItem>
        <DescriptionsItem label="汇率">{{ records.exchange_rate }} </DescriptionsItem>
        <DescriptionsItem label="外汇金额">{{ records.foreign_currency_amount }} </DescriptionsItem>
        <DescriptionsItem label="附件" :span="2">
          <ul>
            <li v-for="(item, index) in records.files" :key="item">
              <a :href="item" target="_blank" @click="handlePreview(item, $event)">{{ `附件${index + 1}` }}</a>
            </li>
          </ul>
        </DescriptionsItem>
      </Descriptions>
      <Alert message="仅提供关联销售单号，以供查询" type="info" show-icon />
      <!-- 收入明细 -->
      <Descriptions title="支出明细">
        <DescriptionsItem>
          <BasicTable
            style="width: 1660px"
            @register="register"
            :canResize="false"
            @edit-end="editend"
            @edit-cancel="editcancal"
            @edit-start="handleEditStart"
          >
            <template #toolbar>
              <div style="position: absolute; left: 0; width: 200px; display: flex; justify-content: space-around">
                <Tooltip title="将勾选明细的支出部门更改为新部门以进行公司分摊">
                  <Button type="primary" @click="handleApportion('company')">公司分摊</Button>
                </Tooltip>
                <Tooltip title="将勾选明细的支出部门更改为新部门以进行部门分摊">
                  <Button type="primary" @click="handleApportion('department')">部门分摊</Button>
                </Tooltip>
              </div>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'amount'"> {{ record.amount }} {{ '￥' }}</template>
              <template v-if="column.key === 'is_check'">
                <Tag :color="checkMap[record.is_check]?.color"> {{ checkMap[record.is_check]?.text }}</Tag>
              </template>
              <template v-if="column.key === 'is_check2'">
                <Tag :color="checkMap[record.is_check2]?.color"> {{ checkMap[record.is_check2]?.text }}</Tag>
              </template>
              <template v-if="column.key === 'files'">
                <div v-for="(newVal, index) in record.files" :key="index">
                  <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
                >
              </template>
            </template>
          </BasicTable>
        </DescriptionsItem>
      </Descriptions>
      <!-- <Modal title="关联销售单"/> -->
    </ScrollContainer>
  </BasicDrawer>
  <GenerateModal @register="registerModal" @success="closedrawer" />
  <PreviewFile @register="registerPreModal" />
  <!-- <ApportionModal @register="registerapporModal" @relaod="clearSelectedRowKeys" /> -->
</template>

<script lang="ts" setup>
import { updataexpenseDetails, updataexpense, childrenColumns } from '../datas/Financial'
import { getDept } from '/@/api/erp/systemInfo'
import { ref, unref } from 'vue'
import { Descriptions, DescriptionsItem, Alert, Button, Tag, message, Tooltip } from 'ant-design-vue'
import { ScrollContainer } from '/@/components/Container'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable } from '/@/components/Table'
import { detailsList, getsetReject, postupdateExpendList } from '/@/api/financialDocuments/otherExpend'
import { checkMap, orderMap } from '../datas/datas'
import GenerateModal from './GenerateModal.vue'
import { useModal } from '/@/components/Modal'
import { cloneDeep, debounce } from 'lodash-es'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { createImgPreview } from '/@/components/Preview'
import { useMessage } from '/@/hooks/web/useMessage'
// import ApportionModal from '../../otherExpendApportion/components/ApportionModal.vue'

const { createMessage } = useMessage()
const generateBtnStatus = ref(true)
const init_id = ref(0)
const emit = defineEmits(['register', 'relaod', 'registerDrawer'])
const records: Recordable = ref({})
const datasource = ref()
const newdatasource = ref()
const newDetalisColumns: any = ref([])
// const newColumns: any = ref([])
const props_data = ref()

const [registerDrawer, { closeDrawer }] = useDrawerInner(async (data) => {
  init_id.value = data.record.id
  generateBtnStatus.value = true
  props_data.value = data
  deptlist()

  setColumns(await updataexpenseDetails(getfundDetails, data.type, data.record?.order))
  newDetalisColumns.value = cloneDeep(await updataexpense(getfundDetails, data.type, data.record?.order))

  const detaildata = await detailsList({ id: data.record.id })
  records.value = detaildata.items
  datasource.value = records.value.sales
  newdatasource.value = cloneDeep(records.value.sales)
  statusdata.value.forEach((item: any) => {
    datasource.value.forEach((val) => {
      if (val.dept_id == item.id) {
        Reflect.set(val, 'department', item.name)
      }
      if (val.clear_dept_id == item.id) {
        Reflect.set(val, 'clear_department', item.name)
      }
    })
  })
  setTableData(datasource.value)
})

const [register, { setTableData, setColumns, getSelectRows, clearSelectedRowKeys, getDataSource, getColumns, updateTableDataRecord }] =
  useTable({
    showIndexColumn: false,
    // dataSource: datasource,
    rowKey: 'work_id',
    rowSelection: {
      // type: 'checkbox',
      onChange: handleChange
    }
  })
//部门
const statusdata = ref([])
async function deptlist() {
  const deptdata = await getDept()
  statusdata.value = unref(deptdata.items)
}
function close() {
  setTableData([])
  closeDrawer()
  clearSelectedRowKeys()
}
//审核
function handlePass() {
  handleUppload()
}
//修改数据
//index
const init_val = ref<any>({})
async function getfundDetails(val: any) {
  init_val.value = val
  // if (val.value && val.value === 5) {
  //   await setColumns(childrenColumns(getfundDetails, props_data.value.type, props_data.value.record?.order))
  // } else if (val.value && val.value !== 5) {
  //   await setColumns(updataexpenseDetails(getfundDetails, props_data.value.type, props_data.value.record?.order))
  // }
}
const [registerModal, { openModal }] = useModal()
function handleUppload() {
  openModal(true, { id: records.value.id, sales: datasource, record: records.value })
}
function closedrawer() {
  closeDrawer()
  emit('relaod')
}

/** 驳回 */
async function handleChange(data, record) {
  console.log(data)

  let columns = cloneDeep(newDetalisColumns.value)
  console.log(columns)

  columns.splice(2, 0, {
    title: '驳回原因',
    dataIndex: 'reject_remark',
    editComponent: 'Textarea',
    editRow: true,
    width: 250,
    resizable: true,
    editRender: (data) => {
      if (!data.text) {
        return '-'
      } else {
        return data.text
      }
    }
  })

  if (data.length !== 0) {
    setColumns(columns)
    // 解决第一次选择驳回原因不展开的问题
    await record[record.length - 1].onEdit?.(true, false)
    records.value.sales.map(async (item) => {
      if (data.includes(item.work_id)) {
        await item.onEdit?.(true, false)
      } else {
        await item.onEdit?.(false, true)
        item.reject_remark = ''
      }

      return item
    })
  } else {
    records.value.sales.map(async (item) => {
      await item.onEdit?.(false, true)
      return item
    })
    setColumns(await updataexpenseDetails(getfundDetails, data.type, data.record?.order))
  }

  // setColumns()
  if (getSelectRows().length == 0) {
    generateBtnStatus.value = true
  } else {
    generateBtnStatus.value = false
  }
}

async function handleReject() {
  try {
    console.log(getSelectRows())

    const selectRowsData = getSelectRows().map((item) => {
      if (!item.reject_remark) {
        throw new Error('请填写驳回原因！')
      }
      // return item.work_id
      return { reject_remark: item.reject_remark, id: item.id }
    })
    await getsetReject({ sales: selectRowsData, id: init_id.value, is_check: 2 })
    emit('relaod')
    message.success('驳回成功！')
    clearSelectedRowKeys()
    closeDrawer()
  } catch (error: any) {
    message.error(error.message)
    // throw new Error(`${error}`)
  }
}

const debounceHandleReject = debounce(handleReject, 500)

// 格式化提交的数据
function formatSubmit() {
  // getDataSource()有我们不需要的属性,所以需要清除
  const dataSource = getDataSource().map((item) => {
    let temporary = {}
    for (let colName of getColumns()) {
      if (colName.key !== 'action') {
        temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
      }
    }
    return temporary
  })
  return dataSource
}

async function editend({ record, key, value }) {
  console.log(record, key, value)

  // 更新数据源中的记录
  if (record && record.id) {
    const targetItem = datasource.value.find((item) => item.id === record.id)
    if (targetItem) {
      if (!value) {
        // 如果值为空，设置为 null
        targetItem[key] = null
      } else {
        // 根据不同的字段类型更新不同的属性
        switch (key) {
          case 'department':
            targetItem.department = init_val.value.name
            targetItem.dept_id = init_val.value.id
            break
          case 'corres_pondent':
            targetItem.corres_pondent = value
            break
          case 'account_name':
            targetItem.account_name = init_val.value.account_name
            targetItem.account_code = init_val.value.account_code
            break
          case 'corres_type':
            targetItem.corres_type = init_val.value.value
            targetItem.corres_pondent = null
            break
          default:
            // 对于其他字段，直接更新值
            targetItem[key] = value
        }
      }
    }
  }

  // 填充缺失的数据
  for (const item of datasource.value) {
    // 查找对应的原始数据
    const originalData = newdatasource.value.find((val) => val.id === item.id) || {}

    // 填充缺失的字段
    if (!item.department) {
      item.department = originalData.department
      item.dept_id = originalData.dept_id
    }
    if (!item.desc) {
      item.desc = originalData.desc
    }
    if (!item.account_name) {
      item.account_name = originalData.account_name
      item.account_code = originalData.account_code
    }
  }

  // 格式化提交数据
  const dateform = await formatSubmit()

  // 构建文档对象
  const doc = {
    id: records.value.id,
    type: records.value.type,
    status: records.value.status,
    currency: records.value.currency,
    urgent_level: records.value.urgent_level,
    cost: records.value.amount,
    dept_id: records.value.dept_id,
    applicant: records.value.applicant,
    account: records.value.account,
    account_name: records.value.account_name,
    inCharge: records.value.inCharge,
    bank: records.value.bank,
    remark: records.value.remark,
    desc: records.value.desc,
    files: records.value.files,
    is_edit: 1
  }

  // 处理每个数据项
  dateform.forEach((item: any) => {
    // 设置类型和父工作ID
    item.type = 2
    item.par_work_id = item.parent_id

    // 删除不需要的字段
    const fieldsToDelete = ['department', 'clear_department', 'strid', 'parent_strid', 'parent_id', 'source_uniqid', 'work_id']

    fieldsToDelete.forEach((field) => {
      delete item[field]
    })
  })

  // 提交数据
  try {
    const params = {
      doc,
      sales: dateform
    }

    await postupdateExpendList(params)
    setTableData(datasource.value)
  } catch (error) {
    console.error('更新数据失败:', error)
  }
}

function editcancal({ record, index, key, value }) {
  if (key == 'account_name') {
    datasource.value[index].account_name = value
    datasource.value[index].account_code = record.account_code
  } else if (key == 'corres_type' && value !== null) {
    datasource.value[index].corres_type = record.corres_type
  } else if (key == 'corres_pondent' && value !== null) {
    datasource.value[index].corres_pondent = record.corres_pondent
  } else if (key == 'department' && value == null) {
    datasource.value[index].department = record.department
    datasource.value[index].dept_id = record.parent_id
  }
  setTableData(datasource.value)
}

const [registerPreModal, { openModal: openpreModal }] = useModal()
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  console.log(prefix)
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openpreModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}

//设置分摊
// const [registerapporModal, { openModal: openapporModal }] = useModal()
// function handleapportion() {
//   const allSelectRows = getSelectRows()

//   openapporModal(true, {
//     record: allSelectRows.map((item) => item.id)
//   })
// }

//更改分摊部门
async function handleApportion(type) {
  const allSelectRows = getSelectRows()
  console.log(allSelectRows)
  if (allSelectRows.length == 0) {
    createMessage.error('请选择数据')
    return
  }
  for (const item of allSelectRows) {
    if (item.parent_strid) {
      createMessage.error('请选择没有销售单号的明细进行分摊更改')
      return
    }
  }
  if (allSelectRows.length == 1) {
    await updateTableDataRecord(allSelectRows[0].work_id, {
      department: type === 'company' ? '公司分摊（录支出用）' : '产品部分摊（录支出用）',
      dept_id: type === 'company' ? 119 : 120
    })
  } else if (allSelectRows.length > 1) {
    allSelectRows.forEach(async (item) => {
      await updateTableDataRecord(item.work_id, {
        department: type === 'company' ? '公司分摊（录支出用）' : '产品部分摊（录支出用）',
        dept_id: type === 'company' ? 119 : 120
      })
    })
  }
}

// 处理 edit-start 事件
async function handleEditStart(params) {
  console.log('edit-start', params)

  const { record } = params

  try {
    // 根据条件设置不同的列配置
    let columns
    columns =
      record.corres_type === 5
        ? childrenColumns(getfundDetails, props_data.value.type, props_data.value.record?.order)
        : updataexpenseDetails(getfundDetails, props_data.value.type, props_data.value.record?.order)

    // 设置列配置
    await setColumns(columns)
  } catch (error) {
    console.error('handleEditStart 错误:', error)
  }
}
</script>
