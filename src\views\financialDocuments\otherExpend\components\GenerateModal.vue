<template>
  <BasicModal @register="registerModal" title="生成付款单" @ok="handok" @cancel="colse">
    <Alert message="可不进行备注填写 " type="info" />
    <BasicForm @register="registerform" class="mt-2" />
  </BasicModal>
</template>
<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { schemas } from '../datas/model'
import { Alert, message, Modal } from 'ant-design-vue'
import { getsetSetCheck } from '/@/api/financialDocuments/otherExpend'
import { ref } from 'vue'
import { RouteLocationNormalizedLoaded, useRoute } from 'vue-router'
import { unref } from 'vue'

//路由
const route = useRoute()
const routeName = unref<RouteLocationNormalizedLoaded>(route).name

const init_id = ref(null)
const init_sales = ref([])
const is_cashier = ref()
const init_boolen = ref(false)

const [registerModal, { closeModal, changeOkLoading }] = useModalInner((data) => {
  try {
    init_id.value = data.id
    init_sales.value = data.sales
    if (data.record.is_check == 1 && data.record.is_check2 == 0) {
      is_cashier.value = true
    } else if (data.record.is_check == 0 && data.record.is_check2 == 0) {
      is_cashier.value = false
    }

    init_boolen.value = false
  } catch (err) {
    throw new Error(`${err}`)
  }
})

const [registerform, { getFieldsValue, resetFields }] = useForm({
  schemas,
  layout: 'vertical',
  showActionButtonGroup: false,
  baseColProps: { span: 24 }
})
//子传父
const emit = defineEmits(['success', 'register'])
async function handok() {
  await changeOkLoading(true)
  const init_remark = await getFieldsValue()
  console.log(init_sales)
  const selesdata = init_sales.value.map((item: any) => ({
    id: item.id,
    account_code: item.account_code,
    account_name: item.account_name,
    corres_type: item.corres_type,
    corres_pondent: item.corres_pondent,
    dept_id: item.dept_id
  }))

  for (let item of selesdata) {
    if (item.corres_type && !item.corres_pondent) {
      message.error({ content: '选取往来单位' })
      init_boolen.value = true
      changeOkLoading(false)
      return
    }
    if (!item.corres_type && item.corres_pondent) {
      message.error({ content: '选取往来单位类型' })
      init_boolen.value = true
      changeOkLoading(false)
      return
    }
  }
  const stridkey = checkAndFindDuplicates(init_sales.value)
  console.log(stridkey)

  try {
    // if (!init_boolen.value) {
    //   if (stridkey.length > 0) {
    //     const stridar = ref<any>([])
    //     stridkey.forEach((item) => {
    //       stridar.value.push(item.strid)
    //     })

    //     Modal.confirm({
    //       title: `有疑似重复单据,${stridar.value}是否继续执行支付？`,
    //       okText: '确定',
    //       okType: 'danger',
    //       cancelText: '取消',
    //       async onOk() {
    //         const res: any = await getsetSetCheck({
    //           id: init_id.value,
    //           is_cashier: is_cashier.value ? 1 : 0,
    //           sales: selesdata,
    //           g_remark: init_remark.g_reamrk,
    //           payment_type: init_remark.payment_type,
    //           is_finance: routeName == '/financialDocuments/otherExpendFinance' ? 1 : 0
    //         })
    //         if (res.news) {
    //           message.success('生成付款单成功')
    //           message.success('审核成功')
    //           emit('success')
    //           resetFields()
    //           await closeModal()
    //         }
    //       }
    //     })
    //   } else {
    //     const res: any = await getsetSetCheck({
    //       id: init_id.value,
    //       is_cashier: is_cashier.value ? 1 : 0,
    //       sales: selesdata,
    //       g_remark: init_remark.g_reamrk,
    //       payment_type: init_remark.payment_type,
    //       is_finance: routeName == '/financialDocuments/otherExpendFinance' ? 1 : 0
    //     })
    //     if (res.news) {
    //       message.success('生成付款单成功')
    //       message.success('审核成功')
    //       emit('success')
    //       resetFields()
    //       await closeModal()
    //     }
    //   }
    // }
    if (!init_boolen.value) {
      let confirmationMessage = ''
      let stridar = []

      if (stridkey.length > 0) {
        stridkey.forEach((item) => stridar.push(item.strid))
        confirmationMessage = `有疑似重复单据, ${stridar.join(', ')} 是否继续执行支付？`

        // Show the confirmation dialog only if there are duplicate items
        const shouldProceed = await showConfirmationDialog(confirmationMessage)
        if (!shouldProceed) return // If user cancels, exit early
      }

      const paymentData = {
        id: init_id.value,
        is_cashier: is_cashier.value ? 1 : 0,
        sales: selesdata,
        g_remark: init_remark.g_reamrk,
        payment_type: init_remark.payment_type,
        is_finance: routeName == '/financialDocuments/otherExpendFinance' ? 1 : 0 ? 1 : 0
      }

      const paymentResponse = await createPaymentDocument(paymentData)

      if (paymentResponse.news) {
        showSuccessMessages()
        resetFormAndCloseModal()
      }
    }
    await changeOkLoading(false)
  } catch (error) {
    await changeOkLoading(false)
    throw new Error(`${error}`)
  } finally {
    await changeOkLoading(false)
  }
}

async function showConfirmationDialog(message) {
  return new Promise((resolve) => {
    Modal.confirm({
      title: message,
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => resolve(true),
      onCancel: () => resolve(false)
    })
  })
}

async function createPaymentDocument(data) {
  const response = await getsetSetCheck(data)
  return response
}

function showSuccessMessages() {
  message.success('生成付款单成功')
  message.success('审核成功')
  emit('success')
}

function resetFormAndCloseModal() {
  resetFields()
  closeModal()
}

function checkAndFindDuplicates(data) {
  // 过滤数组以只保留满足条件的对象
  const filteredData = data.filter((item) => {
    return item.is_check !== 2 && item.is_check2 !== 2 && item.is_cancel !== 1 && item.parent_id
  })

  // 如果过滤后的数组为空，则直接返回空数组
  if (filteredData.length === 0) {
    return []
  }

  const seen = new Map()

  // 遍历数据
  filteredData.forEach((item) => {
    const key = `${item.amount}-${item.parent_id}-${item.account_code}`

    if (seen.has(key)) {
      seen.get(key).push(item.strid)
    } else {
      seen.set(key, [item.strid])
    }
  })

  // 找出所有包含多个名称的键
  const duplicates = []
  seen.forEach((strid, key) => {
    if (strid.length > 1) {
      duplicates.push({ key, strid })
    }
  })

  return duplicates
}
//关闭
function colse() {
  resetFields()
}
</script>
