<template>
  <BasicModal @register="register" title="附件上传" @ok="handleOk" width="800px">
    <BasicForm @register="registerform">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          :multiple="true"
          @remove="handleRemove"
        >
          <template #itemRender="{ file, originNode }">
            <div style="width: 104px; height: 104px; position: relative">
              <!-- 保留原有的 antd 展示（包括预览和删除） -->
              <component :is="originNode" />
              <div v-if="file.status === 'error'" style="color: red; text-align: center; font-size: 12px; margin-top: 4px">
                上传失败，请删除
              </div>
            </div>
          </template>
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { UploadSchemas } from '../datas/model'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { UploadFile, Upload, message } from 'ant-design-vue'
import { ref, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { getupdateFiles } from '/@/api/financialDocuments/otherExpend'
//id
const init_id = ref()
const propsFiles = ref()
const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
  console.log(data)
  propsFiles.value = data?.files
  filesList.value = data?.files?.map((file) => ({ name: file, url: file, uid: Math.random() * 100000 })) ?? []
  init_id.value = data.id
})
const [registerform, { setFieldsValue, getFieldsValue }] = useForm({
  schemas: UploadSchemas,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelCol: { span: 5 }
})

//附件
const filesList = ref<UploadFile[]>([])
const uploadQueue = ref<UploadFile[]>([])
const isUploading = ref(false)

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val.map((item) => item.url) })
  }
)

// 处理文件上传队列
async function processUploadQueue() {
  if (!uploadQueue.value || uploadQueue.value.length === 0) {
    isUploading.value = false
    changeOkLoading(false)
    return
  }

  isUploading.value = true
  const file = uploadQueue.value[0]

  try {
    if (!file) {
      uploadQueue.value.shift()
      processUploadQueue()
      return
    }

    // 只依赖后端/axios超时
    const result = await commonFileUpload(file as any, 'purchase', file)

    if (result && result.path) {
      // 上传成功
      const index = filesList.value.findIndex((item) => item?.uid === file?.uid)
      if (index !== -1) {
        filesList.value[index] = {
          ...filesList.value[index],
          url: result.path,
          status: 'done'
        }
      } else {
        filesList.value.push({
          ...file,
          url: result.path,
          status: 'done'
        })
      } // 更新表单值
      try {
        await setFieldsValue({
          files: filesList.value.filter((item) => item?.url).map((item) => item.url)
        })
      } catch (error) {
        console.error('更新表单值失败:', error)
      }
    } else {
      // 上传失败
      const index = filesList.value.findIndex((item) => item?.uid === file?.uid)
      if (index !== -1) {
        filesList.value[index] = {
          ...filesList.value[index],
          status: 'error'
        }
      } else if (typeof file === 'object') {
        filesList.value.push({
          name: file.name,
          uid: file.uid,
          url: file.url || file.thumbUrl || (file.originFileObj && URL.createObjectURL(file.originFileObj)) || '',
          status: 'error'
        } as UploadFile)
      }
      message.error(`${file?.name || '文件'} 上传失败，请删除后重新上传`)
    }
  } catch (err: any) {
    // 失败（包括后端超时）
    const index = filesList.value.findIndex((item) => item?.uid === file?.uid)
    if (index !== -1) {
      filesList.value[index] = {
        ...filesList.value[index],
        status: 'error'
      }
    } else if (typeof file === 'object') {
      filesList.value.push({
        name: file.name,
        uid: file.uid,
        url: file.url || file.thumbUrl || (file.originFileObj && URL.createObjectURL(file.originFileObj)) || '',
        status: 'error'
      } as UploadFile)
    }
    message.error(`${file?.name || '文件'} 上传失败，请删除后重新上传: ${err?.message || '未知错误'}`)
  }

  // 无论成功失败都 shift 并继续下一个
  uploadQueue.value.shift()
  isUploading.value = uploadQueue.value.length > 0
  changeOkLoading(isUploading.value)
  if (isUploading.value) {
    processUploadQueue()
  }
}

async function handleFileRequest({ file, onSuccess, onError }: UploadRequestOption) {
  try {
    if (!file) {
      message.error('无效的文件')
      return
    }

    // 确保 uploadQueue 存在
    if (!uploadQueue.value) {
      uploadQueue.value = []
    }

    // 确保 filesList 存在
    if (!filesList.value) {
      filesList.value = []
    }

    // 将文件添加到上传队列
    uploadQueue.value.push(file as UploadFile)

    // 如果当前没有正在上传的文件，开始处理队列
    if (!isUploading.value) {
      changeOkLoading(true)
      processUploadQueue()
    }

    // 立即返回成功，实际上传在队列中处理
    onSuccess!('pending')
  } catch (err: any) {
    message.error(`处理文件失败，请删除失败文件后重新上传: ${err?.message || '未知错误'}`)
    if (onError) onError(err)
  }
}

async function handleRemove(file: UploadFile) {
  if (propsFiles.value.length > 0) {
    for (const item of propsFiles.value) {
      if (item == file.url) {
        message.error('不能删除已存在的文件')
        return false
      }
    }
  }
}

const emit = defineEmits(['relaod', 'register'])
// 提交
async function handleOk() {
  try {
    await changeOkLoading(true)
    const formdata = await getFieldsValue()
    for (const items of formdata.files) {
      if (items == '' || items == undefined || items == null) {
        return message.error('文件上传失败,请检查文件上次后,再提交')
      }
    }
    const params = {
      id: init_id.value,
      files: formdata.files
    }
    await getupdateFiles(params)
    emit('relaod')
    await closeModal()
    message.success('附件上传成功')
    filesList.value = []
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  } finally {
    changeOkLoading(false)
  }
}
</script>
<style scoped>
::v-deep(.ant-upload-list-picture-card-container) {
  margin-top: 20px !important;
}
::v-deep(.ant-upload.ant-upload-select-picture-card) {
  margin-top: 20px !important;
}
</style>
