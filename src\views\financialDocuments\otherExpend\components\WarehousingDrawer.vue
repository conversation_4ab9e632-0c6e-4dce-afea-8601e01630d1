<template>
  <BasicDrawer @register="registerDrawer" :visible="false" v-bind="$attrs" showFooter width="90%" @ok="debouncehandleOk">
    <Descriptions title="支出头部" />
    <BasicForm @register="registerForm">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg2Stocking"
          :custom-request="handleFileRequest"
          :multiple="true"
          :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
        >
          <a-button type="primary">
            <upload-outlined />
            Upload
          </a-button>
        </Upload>
      </template>
    </BasicForm>

    <Descriptions title="支出明细" />
    <DescriptionsItem>
      <BasicTable @register="register" :canResize="false">
        <template #headerCell="{ column }">
          <template v-if="column.dataIndex === 'amount'">
            <div class="text-center">
              <Popover trigger="click" @visible-change="handleHoverChange" :visible="visible" title="">
                <template #content>
                  <div class="mb-5">
                    明细支出金额总额:
                    <InputNumber v-model:value="newtotal_price" :min="0" :precision="2" />
                  </div>
                  <Button style="margin-right: 5px; width: 100%" type="primary" @click="handleprice(newtotal_price)">比例分配</Button>
                </template>
                <span style="margin-right: 10px">
                  {{ column.customTitle }}
                  <EditOutlined />
                </span>
              </Popover>
            </div>
          </template>
          <template v-else>{{ column.customTitle }}</template>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
          </template>
          <template v-if="column.key === 'is_check'">
            <Tag :color="checkMap[record.is_check]?.color"> {{ checkMap[record.is_check]?.text }}</Tag>
          </template>
          <template v-if="column.key === 'is_check2'">
            <Tag :color="checkMap[record.is_check2]?.color"> {{ checkMap[record.is_check2]?.text }}</Tag>
          </template>
          <template v-if="column.key === 'files'">
            <div v-for="(newVal, index) in record.files" :key="index">
              <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
            >
          </template>
        </template>
      </BasicTable>
    </DescriptionsItem>
    <UploadModal @register="registerUploadFilsModal" @success="handleSuccess" />
    <PreviewFile @register="registerModal" />
  </BasicDrawer>
</template>
<script lang="tsx" setup>
import { ref, reactive } from 'vue'
import { cloneDeep, debounce } from 'lodash-es'
import { Button, Descriptions, DescriptionsItem, Upload, UploadFile, message, Tag, Popover, InputNumber } from 'ant-design-vue'
import { BasicForm, useForm, PagingApiSelect } from '/@/components/Form'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table'
import type { EditRecordRow } from '/@/components/Table'
import { postaddExpendList } from '/@/api/financialDocuments/otherExpend'
import { useMessage } from '/@/hooks/web/useMessage'
import { getDept } from '/@/api/erp/systemInfo'
import { updateFormSchemaFn, updataexpenseDetails, inChargeList, childrenColumns } from '../datas/drawer'
import { checkMap } from '../datas/datas'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { UploadOutlined, EditOutlined } from '@ant-design/icons-vue'
import { watch } from 'vue'
import UploadModal from './DrawerUploadModal.vue'
import { useModal } from '/@/components/Modal'
import defaultUser from '/@/utils/erp/defaultUser'
import { add, div, sub } from '/@/utils/math'
import { createImgPreview } from '/@/components/Preview/index'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { verifyOutOrder } from '/@/api/erp/outWarehouse'

//附件
const filesList = ref<UploadFile[]>([])
//子传父
const emit = defineEmits(['success', 'register'])
//保存点击,其他禁用
const currentEditKeyRef = ref('')
//form初始化
const [registerForm, { resetFields, setFieldsValue, validate, resetSchema }] = useForm({
  baseColProps: { span: 12 },
  showActionButtonGroup: false,
  labelCol: { span: 4 }
})

//类别
const istype = ref()
//传数据
let recordData = reactive<any>({})
const { createMessage: msg } = useMessage()
//保存
const save = ref(false)
//work列表

//分摊部门
const departmentArray = ref([])
//分摊人员
const personnelArray = ref([])
//总金额显示
const visible = ref(false)
//新总金额
const newtotal_price = ref(0)
//普通record
const props_record = ref()
//获取drawer数据
const [registerDrawer, { closeDrawer, changeLoading, changeOkLoading }] = useDrawerInner(async (data) => {
  try {
    save.value = false
    show.value = false
    resetFields()
    changeLoading(true)
    currentEditKeyRef.value = ''
    filesList.value = []
    istype.value = data.type
    setColumns(await updataexpenseDetails([], getfundDetails, data.type, data.type == 'inwarehouse' ? 2 : 1, false))
    resetSchema(await updateFormSchemaFn(0, true, 'add'))
    setFieldsValue({
      applicant: defaultUser!.userId
    })
    let tabeldata
    if (data.type == 'inwarehouse') {
      tabeldata = data.record.purchaseList.map((item) => {
        return {
          parent_strid: item.purchase_strid,
          par_work_id: item.purchase_work_id,
          basic_work_id: item.sale_work_id,
          source_uniqid: item.source_uniqid,
          department: item.purchase_department,
          dept_id: item.dept_id,
          amount: item.total_price
        }
      })
    } else {
      const itemsArray = ref<any>([])
      console.log(itemsArray.value)
      for (const item of data.id) {
        const { items } = await verifyOutOrder(item)
        itemsArray.value.push(...items)
      }
      console.log(itemsArray.value[0])
      // const { items } = await verifyOutOrder(data.id)
      tabeldata = itemsArray.value.map((item) => {
        return {
          dept_id: item.dept_id,
          par_work_id: item.work_id,
          parent_strid: item.source_uniqid,
          department: item.department,
          amount: item.total_price,
          clear_dept_id: item.operation,
          clear_department: item.operation_naem
        }
      })
      console.log(tabeldata)
    }
    setTableData(tabeldata)

    console.log(data)
  } catch (err) {
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
})
//初始化tabel
const [register, { setTableData, deleteTableDataRecord, getDataSource, updateTableDataRecord, getColumns, setColumns }] = useTable({
  title: '',
  showIndexColumn: false,
  actionColumn: {
    width: 250,
    title: '操作',
    dataIndex: 'action'
  }
})
//action
function createActions(record: EditRecordRow): ActionItem[] {
  if (!record.editable) {
    return [
      {
        label: '编辑',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        ifShow: record.is_check2 !== 2 && !record.is_cancel,
        onClick: handleEdit.bind(null, record)
      },
      {
        label: '附件',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        // ifShow: record.is_check2 !== 2 && !record.is_cancel,
        ifShow: record.is_check2 !== 2 && !record.is_cancel,
        onClick: handleAddFile.bind(null, record)
      }
    ]
  }
  return [
    {
      label: '保存',
      onClick: handleSave.bind(null, record)
    },
    {
      label: '取消',
      popConfirm: {
        title: '是否取消编辑',
        placement: 'right',
        confirm: handleCancel.bind(null, record)
      }
    }
  ]
}
function createDropDownActions(record) {
  return [
    {
      label: '部门分摊',
      // onClick: handlefundDetails.bind(null, record),
      popConfirm: {
        title: (
          <div class="w-100">
            <div style={{ fontSize: '18px', color: 'red' }}>请选择分摊部门,进行金额的平均分摊,可多选部门</div>
            <PagingApiSelect
              placeholder="请选择分摊部门"
              api={(params) => getDept({ ...params, status: 1, is_show: 1, is_audit: 1 })}
              pagingMode={true}
              search-mode={true}
              always-load={false}
              v-model:value={departmentArray.value}
              return-params-field="id"
              mode="multiple"
              select-props={{ fieldNames: { value: 'name', label: 'name' }, placeholder: '请选择', style: { width: '100%' } }}
              resultField="items"
            />
          </div>
        ),
        confirm: handleapportion.bind(null, record, 'department'),
        placement: 'left',
        disabled: recordData.order == 2 || !record.amount || record.parent_strid || record.is_check2 == 2 || record.is_check == 2
      }

      // disabled: recordData.order == 2
    },
    {
      label: '人员分摊',
      popConfirm: {
        title: (
          <div class="w-100">
            <div style={{ fontSize: '18px', color: 'red' }}>请选择分摊人员,进行金额的平均分摊,可多选人员</div>
            <PagingApiSelect
              placeholder="请选择分摊人员"
              api={(params) => getCreatorList({ ...params, status: 1 })}
              pagingMode={true}
              search-mode={true}
              always-load={false}
              v-model:value={personnelArray.value}
              return-params-field="id"
              mode="multiple"
              select-props={{ fieldNames: { value: 'name', label: 'name' }, placeholder: '请选择', style: { width: '100%' } }}
              resultField="items"
              onChange={(_, shall) => {
                if (!shall) return
                shall.forEach((val) => {
                  if (!inChargeList.value.some((item) => item.id === val.id)) {
                    inChargeList.value.push(val)
                  }
                })
              }}
            />
          </div>
        ),
        confirm: handleapportion.bind(null, record, 'share_inCharge'),
        placement: 'left',
        disabled: recordData.order == 2 || !record.amount || record.parent_strid || record.is_check2 == 2 || record.is_check == 2
      }
    }
  ]
}
//
//修改数据
async function getfundDetails(val: any) {
  if (!val) return
  if (val.account_name) {
    updateTableDataRecord(currentEditKeyRef.value, { account_code: val.account_code })
    return
  }
  if (val.value === 5) {
    await setColumns(
      childrenColumns(
        [],
        getfundDetails,
        istype.value,
        props_record.value?.order,
        props_record.value?.is_check == 2 || props_record.value?.is_check2 == 2
      )
    )
  } else {
    await setColumns(
      updataexpenseDetails(
        [],
        getfundDetails,
        istype.value,
        props_record.value?.order,
        props_record.value?.is_check == 2 || props_record.value?.is_check2 == 2
      )
    )
  }
}

// 存储编辑前的record
const beforeRecord = ref()
const show = ref(false)
async function handleEdit(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  beforeRecord.value = cloneDeep(record)
  record.onEdit?.(true, false)
  save.value = true
  show.value = true
  if (record.corres_type === 5) {
    await setColumns(
      childrenColumns(
        [],
        getfundDetails,
        istype.value,
        props_record.value?.order,
        props_record.value?.is_check == 2 || props_record.value?.is_check2 == 2
      )
    )
  } else {
    await setColumns(
      updataexpenseDetails(
        [],
        getfundDetails,
        istype.value,
        props_record.value?.order,
        props_record.value?.is_check == 2 || props_record.value?.is_check2 == 2
      )
    )
  }
}

function handleCancel(record: EditRecordRow) {
  currentEditKeyRef.value = ''
  updateTableDataRecord(record.key, {
    amount: beforeRecord.value.amount,
    source_uniqid: beforeRecord.value.source_uniqid,
    account_name: beforeRecord.value.account_name,
    account_code: beforeRecord.value.account_code,
    department: beforeRecord.value.department,
    clear_department: beforeRecord.value.clear_department,
    desc: beforeRecord.value.desc,
    parent_strid: beforeRecord.value.parent_strid
  })

  record.onEdit?.(false, false)
  save.value = false
  show.value = false
}

async function handleSave(record: EditRecordRow) {
  // 校验
  const valid = await record.onValid?.()
  if (valid) {
    try {
      const fields = [
        { field: 'account_code', message: '明细当中支出科目为必填项,请完成填写' },
        { field: 'account_name', message: '明细当中支出科目为必填项,请完成填写' },
        { field: 'desc', message: '明细当中摘要为必填项,请完成填写' },
        { field: 'amount', message: '明细当中支出金额为必填项,请完成填写' },
        { field: 'department', message: '明细当中支出部门为必填项,请完成填写' }
      ]

      for (const field of fields) {
        if (!record[field.field]) {
          msg.error({ content: field.message })
          save.value = true
          return
        }
      }

      if (record.corres_type && !record.corres_pondent) {
        msg.error({ content: '选取往来单位' })
        save.value = true
        return
      }

      if (!record.corres_type && record.corres_pondent) {
        msg.error({ content: '选取往来单位类型' })
        save.value = true
        return
      }
      costprice()
      save.value = false
      show.value = false
      //TODO 此处将数据提交给服务器保存
      // ...
      // 保存之后提交编辑状态

      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
      }
      msg.success({ content: '数据已保存', key: 'saving' })
      // meesg.value = false
    } catch (error) {
      msg.error({ content: '保存失败', key: 'saving' })
      throw new Error(`${error}`)
    }
  } else {
    msg.error({ content: '请填写正确的数据', key: 'saving' })
  }
}

//添加附件
const [registerUploadFilsModal, { openModal: openModalUplad }] = useModal()
function handleAddFile(record) {
  openModalUplad(true, record)
}

// 格式化提交的数据
function formatSubmit() {
  // getDataSource()有我们不需要的属性,所以需要清除
  const dataSource = getDataSource().map((item) => {
    let temporary = {}
    for (let colName of getColumns()) {
      if (colName.key !== 'action') {
        temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
      }
    }
    return temporary
  })
  return dataSource
}

//格式化某一行的数据
function formatObject(record) {
  // getDataSource()有我们不需要的属性,所以需要清除
  let temporary = {}
  for (let colName of getColumns()) {
    if (colName.key !== 'action') {
      temporary[`${colName.dataIndex}`] = record[`${colName.dataIndex}`]
    }
  }
  return temporary
}
//计算cost价格
function costprice() {
  const da = getDataSource()
  const costdata = da
    .filter((item) => {
      return !item.is_cancel && item.amount
    })
    .reduce((pre, item) => {
      return add(pre, item.amount, 2)
    }, 0)
  setFieldsValue({ cost: costdata })
}

//提交
async function handleOk() {
  try {
    changeOkLoading(true)
    const values = await validate()
    values['is_finance'] = 0
    values['id'] = recordData.id
    values['order'] = istype.value == 'inwarehouse' ? 2 : 1
    const isChancel: any = ref([])
    if (show.value) {
      throw new Error('请先保存')
    }
    //明细填写
    const mesg = ref()
    //获取保存的数据
    const tabledatas: any = await formatSubmit()
    const showdata = ref<any>([])
    const filesboolean = tabledatas.find((obj: any) => obj.files && obj.files.length > 0)
    if (!filesboolean && (!values.files || values.files.length == 0)) {
      throw new Error('票据附件与明细附件至少上传一个')
    }

    for (let item of tabledatas) {
      if (!item.account_code || !item.account_name || !item.department) {
        save.value = true
        throw new Error('请填写完整信息,不能提交空明细')
      } else {
        save.value = false
      }
      if (item.is_check2 == 2 && item.is_cancel !== 1) {
        throw new Error('请先取消明细在提交')
      }

      if (item.is_cancel == 1) {
        isChancel.value.push(item.is_cancel)
      }

      if (item.share_inCharge_name) {
        inChargeList.value.forEach((items) => {
          if (items.name == item.share_inCharge_name) {
            item.share_inCharge = items.id
          }
        })
      }
      const { corres_type, share_source, share_inCharge_name, is_last_disburse } = item
      item.corres_type = corres_type || null
      item.share_source = share_source || null
      item.share_inCharge = share_inCharge_name ? item.share_inCharge : null
      item.is_last_disburse = is_last_disburse || null
      showdata.value.push(item.is_check2)
    }

    tabledatas.forEach((item: any) => {
      delete item.strid
      delete item.department
      delete item.clear_department
      delete item.source_uniqid
      delete item.parent_strid
      delete item.parent_id
      delete item.reject_remark1
      delete item.reject_remark2
      delete item.share_inCharge_name
    })
    const params = {
      sales: tabledatas || [],
      doc: values
    }
    if (isChancel.value.length > 0 && isChancel.value.length == tabledatas.length) {
      if (new Set(isChancel.value).size == 1) {
        params.doc['status'] = 16
      }
    } else {
      if (values.cost <= 0) {
        throw new Error('本单的应付金额应大于0')
      }
    }
    if (params.sales.every((item: any) => item.type == 3) || params.sales.length == 0) {
      throw new Error('请完整的填入支出明细并保存')
    }
    console.log(params)

    //创建
    if (save.value == false) {
      mesg.value = await postaddExpendList({ ...params })
      if (mesg.value.type == 'success' || mesg.value.news == 'success') {
        msg.success({ content: '提交成功' })
        closeDrawer()
        emit('success')
        setTimeout(() => {
          changeOkLoading(false)
        }, 1000)
      } else {
        throw new Error(mesg.value.message)
      }
    } else {
      save.value = false
      throw new Error('请先检查数据是否完整')
    }
  } catch (err: any) {
    changeOkLoading(false)
    if (!err.message) {
      message.error(`请先填写信息,在提交`)
    } else {
      message.error(`${err.message}`)
    }
    throw new Error(`${err}`)
  }
}
const debouncehandleOk = debounce(handleOk, 200)

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val?.map((item) => item.url) ?? [] })
  }
)
//附件上传
async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  filesList.value = filesList.value!.map((item) => {
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: item.name
    }
  })
  await setFieldsValue({
    files: filesList.value.map((item) => item.url)
  })
}

//明细附件上传成功
function handleSuccess(record) {
  handleSave(record)
}

//附件预览

// 预览
const { createMessage } = useMessage()
const [registerModal, { openModal: openMODAL }] = useModal()
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openMODAL(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}

//分摊部门
function handleapportion(record, type: string) {
  const Arraylist = type == 'department' ? departmentArray.value : personnelArray.value
  const totalamount = Number(record.amount)
  //评价值
  const average = Math.round(totalamount / Arraylist.length)
  //最后一个值
  const lastamount = sub(totalamount, average * (Arraylist.length - 1), 2)

  const newrecord = formatObject(record)
  const newRowDataItem = {
    ...newrecord,
    amount: average
  }
  const lastNewRowDataItem = {
    ...newRowDataItem,
    amount: lastamount
  }
  let dataSource = cloneDeep(getDataSource())
  // 添加所有的部门，除了最后一个
  Arraylist.slice(0, -1).forEach((item) => {
    const row = type == 'department' ? { ...newRowDataItem, department: item } : { ...newRowDataItem, share_inCharge_name: item }
    dataSource = [...dataSource, row]
  })

  // 添加最后一个部门
  const lastRow =
    type == 'department'
      ? { ...lastNewRowDataItem, department: Arraylist[Arraylist.length - 1] }
      : { ...lastNewRowDataItem, share_inCharge_name: Arraylist[Arraylist.length - 1] }
  dataSource = [...dataSource, lastRow]
  setTableData(dataSource)
  deleteTableDataRecord(record.key)
  costprice()
}

//总金额事件
function handleHoverChange(e) {
  if (e == false) {
    visible.value = false
  } else {
    visible.value = true
  }
}
//总金额确认事件
function handleprice(price) {
  let proportions = []
  const tabledata = getDataSource()
  const oldtotal = tabledata.reduce((acc, cur) => acc + Number(cur.amount), 0)

  tabledata.forEach((item) => {
    const proportion = item.amount / oldtotal
    proportions.push(proportion)
  })
  tabledata.forEach((item, index) => {
    item.amount = (price * proportions[index]).toFixed(2)
  })
  setTableData(tabledata)
  visible.value = false
  costprice()
}
</script>

<style lang="less" scoped>
:deep .ant-picker {
  width: 100%;
}
</style>
