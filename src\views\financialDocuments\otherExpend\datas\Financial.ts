import { h, ref } from 'vue'
import { getErpSupplier, getWorkList } from '/@/api/commonUtils'
import { BasicColumn } from '/@/components/Table'
import { getCategory, getcustomerList } from '/@/api/financialDocuments/otherIncome'

import { message, Tag } from 'ant-design-vue'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { getSourceSelect } from '/@/api/baseData/warehouse'
import { getDept } from '/@/api/erp/systemInfo'
import { FormSchema } from '/@/components/Form'
import { getShareManageList } from '/@/api/baseData/shareManage'
import { isNullAndUnDef } from '/@/utils/is'
import { cloneDeep } from 'lodash-es'
import { toolsgetPaOrderInfo } from '/@/api/financialDocuments/otherExpend'

const canceltypeOptions = {
  0: { label: '未取消', color: '' },
  1: { label: '取消', color: 'red' }
}
const hasBeenSet = ref(false)
const num = ref()
const pathname = window.location.pathname
function getWorkListFn(type) {
  if (!type) {
    return getCreatorList
  }
  if (type == 1) {
    return getCreatorList
  }
  if (type == 2) {
    return getDept
  }
  if (type == 3) {
    return getcustomerList
  }
  if (type == 4) {
    return getErpSupplier
  }
}
export function updataexpenseDetails(hand?: Function, type?: any, order?: any): any {
  const expenseDetailsColumns: BasicColumn[] = [
    {
      title: '关联销售单号',
      dataIndex: 'source_uniqid',
      editComponent: 'PagingApiSelect',
      editComponentProps: () => {
        return {
          api: getWorkList,
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          params: {
            type: 3,
            status: [1, 2, 3, 4, 5, 15]
          },
          // pagingSize: 20,
          searchParamField: 'source_uniqid',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'source_uniqid',
              label: 'source_uniqid'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'source_uniqid',
            allowClear: true,
            style: {
              width: '100%'
            }
          }
        }
      },
      ifShow: order == 2,
      width: 200,
      resizable: true,
      editRow: true
    },
    {
      title: order == 2 ? '关联采购单号' : '关联销售单号',
      dataIndex: 'parent_strid',
      editComponent: 'PagingApiSelect',
      editComponentProps: () => {
        return {
          api: getWorkList,
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          pagingSize: 20,
          params: {
            type: order == 2 ? 4 : 3,
            status: [1, 2, 3, 4, 5, 15]
          },
          selectProps: {
            fieldNames: {
              key: 'key',
              value: order == 2 ? 'purchase_strid' : 'source_uniqid',
              label: order == 2 ? 'purchase_strid' : 'source_uniqid'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: order == 2 ? 'purchase_strid' : 'source_uniqid',
            allowClear: true,
            style: {
              width: '100%'
            }
          }
        }
      },
      // ifShow: order == 2,
      width: 200,
      editRow: true,
      resizable: true
    },
    {
      title: '摘要',
      dataIndex: 'desc',
      editComponent: 'Textarea',
      resizable: true,
      width: 250,
      edit: true,
      editComponentProps: {
        autosize: { minRows: 3, maxRows: 6 },
        editText: true,
        style: {
          minWidth: 'calc(100% - 100px)'
        }
      }
    },
    {
      title: '支出科目名称',
      dataIndex: 'account_name',
      editComponent: 'ApiSelect',
      width: 250,
      edit: true,
      resizable: true,
      editComponentProps: {
        api: getCategory,
        resultField: 'items',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'account_name',
            label: 'account_name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'account_name',
          allowClear: true,
          onChange: async (_, shall) => {
            hand && hand(shall)
          },
          style: {
            minWidth: 'calc(100% - 100px)'
          }
        },
        editText: true,
        params: {
          status: 1
        }
      },
      helpMessage: '明细具有销售订单时支出科目不能选择营业费及其下属科目用等支出科目,反之不具有销售订单时不可选择其他业务支出及其下属科目',
      editRule(text, record) {
        if (!text) return false
        if (!['/car', '/sp', '/sptests'].includes(pathname)) {
          if (record.parent_strid) {
            const dashIndex = text.indexOf('-')
            const beforeDash = text.substring(0, dashIndex)
            if (beforeDash === '营业费用' || beforeDash === '营业费用（旧）') {
              message.error('不能选择营业费用')
              return true
            } else {
              return false
            }
          } else {
            const dashIndex = text.indexOf('-')
            const beforeDash = text.substring(0, dashIndex)
            if (beforeDash === '其他业务支出' || beforeDash === '其他业务支出（ERP）') {
              message.error('不能选择其他业务支出')
              return true
            } else {
              return false
            }
          }
        }
      }
    },
    {
      title: 'OA工单号',
      dataIndex: 'order_no',
      helpMessage: 'OA工单号选择之后,将会覆盖选择的销售订单或者采购订单的支出部门,请谨慎选择',
      editComponent: 'PagingApiSelect',
      editComponentProps: ({ record }) => {
        return {
          api: toolsgetPaOrderInfo,
          params: { type: 2 },
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          pagingSize: 20,
          labelField: 'order_no',
          valueField: 'order_no',
          searchParamField: 'order_no',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'order_no',
              label: 'order_no'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'account_name',
            allowClear: true,
            onChange: async (_, shall) => {
              record.dept_id = shall.f_dept_id
              record.department = shall.f_department
            },
            style: {
              width: '100%'
            }
          }
        }
      },
      editDynamicDisabled: !['edit', 'detail', 'add'].includes(type),
      width: 250,
      editRow: true,
      resizable: true
    },
    {
      title: '外币金额',
      dataIndex: 'foreign_currency_amount',
      editComponent: 'InputNumber',
      editComponentProps: ({ record }) => {
        return {
          // max: num.value,
          precision: 2,
          step: 0.01,
          style: {
            width: '100%'
          },
          onChange: (val) => {
            record.amount = new Decimal(val).times(record.exchange_rate).toDecimalPlaces(2).toNumber()
          }
        }
      },
      width: 150,
      editRow: true,
      resizable: true,
      editDynamicDisabled({ record }) {
        return record.exchange_rate !== '1.000000' ? false : true
      }
    },
    {
      title: '支出金额',
      dataIndex: 'amount',
      editComponent: 'InputNumber',
      editComponentProps: ({ record }) => {
        if (hasBeenSet.value == false && record.amount) {
          console.log(record.amount)
          hasBeenSet.value = true
          num.value = record.amount
        }
        return {
          // min: 0,
          // max: num.value,
          precision: 4,
          step: 0.0001
        }
      },
      width: 100,
      editDynamicDisabled({ record }) {
        return record.exchange_rate !== '1.000000' ? true : false
      }
      // editRow: true
    },
    {
      title: '支出部门',
      edit: true,
      resizable: true,
      dataIndex: 'department',
      editComponent: 'PagingApiSelect',
      editComponentProps: {
        api: getDept,
        params: { status: 1, is_show: 1, is_audit: 1 },
        resultField: 'items',
        labelField: 'name',
        valueField: 'id',
        searchMode: true,
        pagingMode: true,
        editText: true,
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'name',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          onChange: async (_, shall) => {
            hand && hand(shall)
          },
          style: {
            minWidth: 'calc(100% - 100px)'
          }
        }
      },
      width: 200,
      editDynamicDisabled(record) {
        return record.record.parent_strid ? true : false
      }
      // editRow: true
    },
    {
      title: '往来单位类型',
      dataIndex: 'corres_type',
      editComponent: 'Select',
      resizable: true,
      editComponentProps: ({ record }) => {
        return {
          allowClear: true,
          options: [
            { label: '员工', value: 1 },
            { label: '部门', value: 2 },
            { label: '客户', value: 3 },
            { label: '供应商', value: 4 },
            { label: '其他', value: 5 }
          ],
          editText: true,
          onChange: async (_, shall) => {
            hand && hand(shall)
            record.corres_pondent = null
          },
          style: {
            minWidth: 'calc(100% - 100px)'
          }
        }
      },
      width: 150,
      edit: true
    },
    {
      title: '往来单位',
      dataIndex: 'corres_pondent',
      editComponent: 'PagingApiSelect',
      editComponentProps: (fromat) => {
        return {
          api: getWorkListFn(fromat.record.corres_type),
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          params: {
            type: 3,
            status: [1, 3, 4, 5, 15]
          },
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'name',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            onChange: async (_, shall) => {
              hand && hand(shall)
            },
            style: {
              minWidth: 'calc(100% - 100px)'
            }
          },
          editText: true
        }
      },
      width: 200,
      resizable: true,
      edit: true
    },
    {
      title: '附件',
      dataIndex: 'files',
      width: 100
    },
    {
      title: '结算部门',
      dataIndex: 'clear_department',
      editComponent: 'PagingApiSelect',
      editComponentProps: (from) => {
        return {
          api: getDept,
          params: { is_production: 1, is_audit: 1 },
          resultField: 'items',
          labelField: 'name',
          valueField: 'id',
          searchMode: true,
          pagingMode: true,
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'name',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            style: {
              width: '100%'
            },
            disabled: from.record.parent_strid
          }
        }
      },
      width: 200,
      editRow: true,
      resizable: true
    },
    {
      title: '是否订单最后一笔支出',
      dataIndex: 'is_last_disburse',
      editComponent: 'Select',
      width: 200,
      customRender({ text }) {
        return isNullAndUnDef(text) ? '-' : text === 1 ? '是' : '否'
      }
    },
    {
      title: '明细单号',
      dataIndex: 'strid',
      width: 200,
      // ifShow: ['edit', 'detail'].includes(type),
      resizable: true
    },
    {
      title: '汇率',
      dataIndex: 'exchange_rate',
      width: 100,
      resizable: true
    },
    {
      title: '货币',
      dataIndex: 'currency',
      width: 100,
      // editRule: RouleFn,
      resizable: true
    },
    {
      title: '支出科目代码',
      dataIndex: 'account_code',
      width: 100,
      // editRow: true,
      editComponent: 'Input',
      editDynamicDisabled: true,
      resizable: true
    },
    {
      title: '是否取消',
      dataIndex: 'is_cancel',
      width: 100,
      customRender: ({ value }) => {
        return value ? h(Tag, { color: canceltypeOptions[value].color }, canceltypeOptions[value].label) : '-'
      }
    },
    {
      title: '取消时间',
      dataIndex: 'cancel_at',
      width: 150,
      customRender: ({ value }) => {
        // return value ? value.split(' ')[0].slice(0, 10) : '-'
        return value ? value : '-'
      }
    },
    {
      title: '分摊人员',
      dataIndex: 'share_inCharge',
      editComponent: 'PagingApiSelect',
      editComponentProps: () => {
        return {
          api: getCreatorList,
          params: { status: 1 },
          resultField: 'items',
          labelField: 'name',
          valueField: 'id',
          searchMode: true,
          pagingMode: true,
          returnParamsField: 'id',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'name',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            style: {
              width: '100%'
            }
          }
        }
      },
      width: 200,
      // editRow: true,
      resizable: true
    },
    {
      title: '分摊渠道',
      dataIndex: 'share_source',
      editComponent: 'PagingApiSelect',
      editComponentProps: () => {
        return {
          api: getSourceSelect,
          params: { status: 0 },
          resultField: 'items',
          labelField: 'name',
          valueField: 'id',
          searchMode: true,
          pagingMode: true,
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'name',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            style: {
              width: '100%'
            }
          }
        }
      },
      width: 200,
      // editRow: true,
      resizable: true
    },
    {
      title: '财务审核',
      dataIndex: 'is_check',
      width: 100,
      ifShow: ['detail', 'edit'].includes(type)
    },
    {
      title: '出纳审核',
      dataIndex: 'is_check2',
      width: 100,
      ifShow: ['detail', 'edit'].includes(type)
    },

    {
      title: 'dept_id',
      dataIndex: 'dept_id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'clear_dept_id',
      dataIndex: 'clear_dept_id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'id',
      dataIndex: 'id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'parent_id',
      dataIndex: 'parent_id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'work_id',
      dataIndex: 'work_id',
      width: 100,
      defaultHidden: true
    }
  ]
  return expenseDetailsColumns
}
export async function updataexpense(hand?: Function, type?: any, order?: any): Promise<BasicColumn[]> {
  const expenseDetailsColumns: BasicColumn[] = [
    {
      title: '关联销售单号',
      dataIndex: 'source_uniqid',
      editComponent: 'PagingApiSelect',
      editComponentProps: {
        api: getWorkList,
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        params: {
          type: 3,
          status: [1, 3, 4, 5, 15]
        },
        // pagingSize: 20,
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'source_uniqid',
            label: 'source_uniqid'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          onChange: (_, shall) => {
            hand && hand(shall)
            hasBeenSet.value = false
          }
        }
      },
      width: 150,
      resizable: true
      // editRow: true
    },
    {
      title: '关联采购单号',
      dataIndex: 'parent_strid',
      editComponent: 'PagingApiSelect',
      editComponentProps: {
        api: getWorkList,
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        params: {
          type: 4,
          status: [1, 3, 4, 5, 15]
        },
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'strid',
            label: 'strid'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          onChange: (_, shall) => {
            hand && hand(shall)
            hasBeenSet.value = false
          }
        }
      },
      ifShow: order == 2,
      width: 150
      // editRow: true
    },
    {
      title: '摘要',
      dataIndex: 'desc',
      editComponent: 'Textarea',
      width: 150,
      // editRow: true,
      editComponentProps: {
        autosize: { minRows: 3, maxRows: 6 }
      }
    },
    {
      title: '支出科目名称',
      dataIndex: 'account_name',
      editComponent: 'ApiSelect',
      width: 350,
      // edit: true,
      resizable: true,
      editComponentProps: () => {
        return {
          api: getCategory,
          resultField: 'items',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'account_name',
              label: 'account_name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'account_name',
            allowClear: true,
            onChange: async (_, shall) => {
              hand && hand(shall)
            },
            style: {
              minWidth: 'calc(100% - 100px)'
            }
          },
          editText: true,
          params: {
            status: 1
          }
        }
      }
    },
    {
      title: '往来单位类型',
      dataIndex: 'corres_type',
      editComponent: 'Select',
      editComponentProps: ({ record }) => {
        return {
          allowClear: true,
          options: [
            { label: '员工', value: 1 },
            { label: '部门', value: 2 },
            { label: '客户', value: 3 },
            { label: '供应商', value: 4 },
            { label: '其他', value: 5 }
          ],
          editText: true,
          onChange: async (_, shall) => {
            hand && hand(shall)
            record.corres_pondent = null
          },
          style: {
            minWidth: 'calc(100% - 100px)'
          }
        }
      },
      width: 150
      // edit: true
    },
    {
      title: '往来单位',
      dataIndex: 'corres_pondent',
      editComponent: 'PagingApiSelect',
      editComponentProps: (fromat) => {
        return {
          api: getWorkListFn(fromat.record.corres_type),
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          params: {
            type: 3,
            status: [1, 3, 4, 5, 15]
          },
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'name',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            onChange: async (_, shall) => {
              hand && hand(shall)
            },
            style: {
              minWidth: 'calc(100% - 100px)'
            }
          },
          editText: true
        }
      },
      width: 200,
      resizable: true
      // edit: true
    },
    {
      title: '明细单号',
      dataIndex: 'strid',
      width: 200,
      // ifShow: ['edit', 'detail'].includes(type),
      resizable: true
    },
    {
      title: '支出科目代码',
      dataIndex: 'account_code',
      width: 100,
      // editRow: true,
      editComponent: 'Input',
      editDynamicDisabled: true,
      resizable: true
    },
    {
      title: '支出金额',
      dataIndex: 'amount',
      editComponent: 'InputNumber',
      editComponentProps: ({ record }) => {
        if (hasBeenSet.value == false && record.amount) {
          console.log(record.amount)
          hasBeenSet.value = true
          num.value = record.amount
        }
        return {
          // min: 0,
          // max: num.value,
          precision: 4,
          step: 0.0001
        }
      },
      width: 100
      // editRow: true
    },
    {
      title: '支出部门',
      dataIndex: 'department',
      editComponent: 'PagingApiSelect',
      editComponentProps: {
        api: getDept,
        params: { status: 1, is_show: 1 },
        resultField: 'items',
        labelField: 'name',
        valueField: 'id',
        searchMode: true,
        pagingMode: true,
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'name',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true
        }
      },
      width: 120
      // editRow: true
    },
    {
      title: '财务审核',
      dataIndex: 'is_check',
      width: 100,
      ifShow: ['detail', 'edit'].includes(type)
    },
    {
      title: '出纳审核',
      dataIndex: 'is_check2',
      width: 100,
      ifShow: ['detail', 'edit'].includes(type)
    },
    {
      title: '是否取消',
      dataIndex: 'is_cancel',
      width: 100,
      customRender: ({ value }) => {
        return value ? h(Tag, { color: canceltypeOptions[value].color }, canceltypeOptions[value].label) : '-'
      }
    },
    {
      title: '取消时间',
      dataIndex: 'cancel_at',
      width: 150,
      customRender: ({ value }) => {
        // return value ? value.split(' ')[0].slice(0, 10) : '-'
        return value ? value : '-'
      }
    },

    {
      title: '附件',
      dataIndex: 'files',
      width: 100
    }
  ]
  return expenseDetailsColumns
}

export const schemas: FormSchema[] = [
  {
    field: 'share_account_name',
    component: 'ApiSelect',
    label: '分摊科目名称',
    componentProps: ({ formModel }) => {
      return {
        api: getCategory,
        resultField: 'items',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'account_name',
            label: 'account_name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'account_name',
          allowClear: true,
          onChange: async (_, shall) => {
            if (shall) {
              formModel.share_account_code = shall.account_code
            }
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'share_setting_id',
    required: true,
    component: 'ApiSelect',
    label: '分摊模式',
    componentProps: () => {
      return {
        api: getShareManageList,
        resultField: 'items',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'id',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'account_name',
          // itEmpty: true,
          allowClear: true
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'share_account_code',
    component: 'Input',
    label: '分摊科目代码',
    componentProps: {
      disabled: true,
      placeholder: '请输入'
    }
  },
  {
    field: 'share_status',
    required: true,
    component: 'Select',
    label: '分摊状态',
    defaultValue: '启用',
    componentProps: {
      defaultValue: 1,
      options: [
        {
          label: '禁用',
          value: 0
        },
        {
          label: '启用',
          value: 1
        }
      ],
      placeholder: '请选择',
      allowClear: true,
      disabled: true
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  }
]
export const childrenColumns = (hand?: Function, type?: any, order?: any): BasicColumn[] => {
  const newDetailsColumns: Array<any> = []
  const datas = cloneDeep(updataexpenseDetails(hand, type, order))
  for (const item of datas) {
    if (item.dataIndex == 'corres_pondent') {
      item.editComponent = 'Input'
      item.editComponentProps = {
        editText: true
      }
    }
    newDetailsColumns.push(item)
  }
  return newDetailsColumns
}
