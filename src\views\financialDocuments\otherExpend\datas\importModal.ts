import { message } from 'ant-design-vue'
import { getDept } from '/@/api/erp/systemInfo'
import { ref } from 'vue'

export const IMP_EXCEL_END = 20000

export async function transformData2Import(data: Recordable[], form: any): Promise<any[]> {
  // 判断原始数据是否为空
  if (!data || data.length === 0) {
    message.error('导入的文件内容为空，请检查后重新上传')
    throw new Error('导入数据为空')
  }

  const fieldMap = {
    amount: '支出金额',
    desc: '支出摘要',
    account_name: '支出科目名称',
    account_code: '支出科目代码',
    department: '支出部门名称'
  }

  // 存放错误信息

  const result = data
    .map((obj) => {
      const cookedData: any = {
        amount: '',
        desc: '',
        account_name: '',
        account_code: '',
        department: ''
      }

      for (const key in cookedData) {
        if (fieldMap[key]) {
          cookedData[key] = obj[fieldMap[key]]
        }
      }

      // 去除空格
      cookedData.amount = String(cookedData.amount || '').replace(/\s/g, '')
      cookedData.desc = String(cookedData.desc || '').replace(/\s/g, '')
      cookedData.account_name = String(cookedData.account_name || '').replace(/\s/g, '')
      cookedData.account_code = String(cookedData.account_code || '').replace(/\s/g, '')
      cookedData.department = String(cookedData.department || '').replace(/\s/g, '')

      // 校验必填字段
      const requiredFields = ['amount', 'desc', 'account_name', 'account_code', 'department']

      const hasEmpty = requiredFields.some((key) => !cookedData[key])

      // 如果有空字段，跳过该行
      if (hasEmpty) {
        return null
      }

      return cookedData
    })
    .filter((item) => item !== null) // 过滤掉无效行

  // 判断处理后是否有有效数据
  if (result.length === 0) {
    message.error('导入的数据中没有有效的明细记录，请检查文件内容')
    throw new Error('导入数据无效')
  }

  // 👇 提取所有部门名称去请求 dept_id
  const departmentNames = [...new Set(result.map((item) => item.department))]
  const deptNameToIdMap = ref()
  try {
    const { items } = await getDept({ status: 1, is_show: 1, is_audit: 1, names: departmentNames })
    console.log(deptNameToIdMap)
    deptNameToIdMap.value = items.reduce((acc, dept) => {
      acc[dept.name] = dept.id
      return acc
    }, {})
  } catch (e) {
    message.error('获取部门ID失败，请检查部门名称是否正确')
    throw e
  }
  const finalResult = result.map((item) => {
    const deptId = deptNameToIdMap.value[item.department]
    if (!deptId) {
      message.error(`部门名称 "${item.department}" 不存在，请检查后重新上传`)
      throw new Error(`部门名称 "${item.department}" 不存在，请检查后重新上传`)
    }
    return {
      ...item,
      dept_id: deptId,
      exchange_rate: form.exchange_rate,
      currency: form.currency
    }
  })

  return finalResult
}
