import { FormSchema } from '/@/components/Form'
import { Rule } from 'ant-design-vue/lib/form'
// const category_id = [
//   { label: '1', value: '1' },
//   { label: '2', value: '2' },
//   { label: '3', value: '3' },
//   { label: '4', value: '4' },
//   { label: '5', value: '5' },
//   { label: '6', value: '6' }
// ]
// const category = [
//   { value: '搬运', label: '搬运' },
//   { value: '奖金', label: '奖金' },
//   { value: '报销', label: '报销' },
//   { value: '教书', label: '教书' },
//   { value: '学习', label: '学习' },
//   { value: '跑路', label: '跑路' }
// ]
export const schemas: FormSchema[] = [
  {
    field: 'payment_type',
    label: '款项类型',
    component: 'RadioGroup',
    defaultValue: 3,
    componentProps: {
      options: [
        {
          label: '定金',
          value: 1
        },
        {
          label: '最后一笔款',
          value: 2
        },
        {
          label: '全款',
          value: 3
        }
      ],
      disabled: true
    },
    colProps: {
      span: 11
    },
    required: true
  },
  {
    field: 'g_reamrk',
    component: 'InputTextArea',
    label: '生成付款单携带备注',
    colProps: {
      span: 24
    },
    componentProps: {
      autosize: { minRows: 3, maxRows: 6 }
    }
  }
]
export const UploadSchemas: FormSchema[] = [
  {
    field: 'files',
    label: '附件上传',
    component: 'Upload',
    slot: 'Files',
    required: true,
    rules: [
      {
        required: true,
        validator: async (_rule: Rule, value: string) => {
          if (!value || value.length === 0) return Promise.reject('请上传附件')
          return Promise.resolve()
        }
        // trigger: 'change'
      }
    ],
    colProps: {
      span: 24
    }
  }
]
