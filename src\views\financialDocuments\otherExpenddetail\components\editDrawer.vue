<template>
  <BasicDrawer @register="registerDrawer" width="30%" @ok="handleSubmit"> <BasicForm @register="registerForm" /></BasicDrawer
></template>
<script setup lang="ts">
import { ref } from 'vue'
import { schemas } from '../datas/edit.data'
import { stockodsetCorresPondent } from '/@/api/financialDocuments/otherExpendApportion'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
const in_id = ref()
const emit = defineEmits(['success'])
const [registerDrawer, { changeOkLoading, closeDrawer }] = useDrawerInner((data) => {
  setFieldsValue(data.record)
  in_id.value = data.record.id
})
const [registerForm, { setFieldsValue, validate }] = useForm({
  schemas,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelCol: { span: 5 }
})

async function handleSubmit() {
  const formdata = await validate()
  console.log(formdata)
  try {
    changeOkLoading(true)
    stockodsetCorresPondent({ id: in_id.value, ...formdata })
    setTimeout(() => {
      changeOkLoading(false)
      closeDrawer()
      emit('success')
    }, 1000)
  } catch (e) {
    changeOkLoading(false)

    console.log(e)
  }
}
</script>
