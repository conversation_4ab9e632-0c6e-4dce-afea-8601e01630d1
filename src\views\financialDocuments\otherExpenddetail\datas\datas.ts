import type { BasicColumn } from '/@/components/Table'
import type { FormSchema } from '/@/components/Form'
import { formatter } from '/@/utils/erp/formatterPrice'
import { getDeptSelectTree } from '/@/api/admin/dept'
import { getCategory } from '/@/api/financialDocuments/otherIncome'
import { getStaffList } from '/@/api/baseData/staff'

export const statusMap = {
  0: { color: '', text: '待执行' },
  1: { color: 'green', text: '生效' },
  3: { color: 'cyan', text: '待执行' },
  4: { color: 'blue', text: '执行中' }
}
export const checkMap = {
  0: { color: '', text: '未审核' },
  1: { color: 'green', text: '通过' },
  2: { color: 'red', text: '驳回' }
}
// export const typeMap = {
//   1: { text: '个人报销', color: 'default' },
//   2: { text: '款项支出', color: 'blue' },
//   3: { text: '财务费用', color: 'green' }
// }
export const correstype = {
  1: { text: '员工', color: 'skyblue' },
  2: { text: '部门', color: 'blue' },
  3: { text: '客户', color: 'green' },
  4: { text: '供应商', color: 'green' },
  5: { text: '其他', color: 'green' }
}
// export const sharestatus = {
//   0: { text: '禁用', color: 'red' },
//   1: { text: '启用', color: 'green' }
// }
export const cancel = {
  0: { text: '', color: '' },
  1: { text: '取消', color: 'red' }
}
// export const isshare = {
//   0: { text: '不分摊', color: 'red' },
//   1: { text: '分摊', color: 'green' }
// }
// export const bindfund = {
//   0: { color: 'red', text: '未关联 ' },
//   1: { color: 'green', text: '已关联' }
// }
export const columns: BasicColumn[] = [
  {
    title: '创建日期',
    dataIndex: 'created_at',
    width: 200,
    resizable: true
  },
  {
    title: '支出单号',
    dataIndex: 'doc_strid',
    width: 200,
    resizable: true
  },
  {
    title: '支出单明细号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '关联销售订单号',
    dataIndex: 'parent_strid',
    width: 200,
    resizable: true
  },
  // {
  //   title: '其他支出单类型',
  //   dataIndex: 'type',
  //   width: 200,
  //   resizable: true
  // },
  // {
  //   title: '是否关联流水',
  //   dataIndex: 'is_bind_fund',
  //   width: 100,
  //   resizable: true
  // },
  // {
  //   title: '收/付款日期',
  //   dataIndex: 'collection_at',
  //   width: 100,
  //   resizable: true
  // },
  {
    title: '部门',
    dataIndex: 'department_name',
    width: 200,
    resizable: true
  },
  {
    title: '支出科目',
    dataIndex: 'account_name',
    width: 200,
    resizable: true
  },
  {
    title: '摘要',
    dataIndex: 'desc',
    width: 200
  },
  {
    title: '外汇金额',
    dataIndex: 'foreign_currency_amount',
    width: 120,
    customRender: ({ value }) => {
      return formatter.format(value)
    },
    resizable: true
  },
  {
    title: '支出金额',
    dataIndex: 'amount',
    width: 120,
    customRender: ({ value }) => {
      return formatter.format(value)
    },
    resizable: true
  },
  {
    title: '支出科目代码',
    dataIndex: 'account_code',
    width: 200,
    resizable: true
  },
  {
    title: '出纳审核',
    dataIndex: 'is_check',
    width: 100,
    resizable: true
  },
  {
    title: '财务审核',
    dataIndex: 'is_check2',
    width: 100,
    resizable: true
  },
  {
    title: '取消状态',
    dataIndex: 'is_cancel',
    width: 200
  },
  {
    title: '取消时间',
    dataIndex: 'cancel_at ',
    width: 200
  },
  {
    title: '分摊人员',
    dataIndex: 'share_inCharge_name',
    width: 200,
    resizable: true
  },
  {
    title: '分摊渠道',
    dataIndex: 'share_source',
    width: 200,
    resizable: true
  },
  {
    title: '往来单位分类',
    dataIndex: 'corres_type',
    width: 200,
    resizable: true
  },
  {
    title: '往来单位',
    dataIndex: 'corres_pondent',
    width: 200,
    resizable: true
  },

  {
    title: '附件',
    dataIndex: 'files',
    width: 200
  }
]

export function formConfigFn() {
  const schemas: FormSchema[] = [
    // {
    //   field: 'is_share',
    //   label: '是否分摊',
    //   component: 'Select',
    //   componentProps: {
    //     options: [
    //       { label: '不分摊', value: 0 },
    //       { label: '分摊', value: 1 }
    //     ]
    //   }
    // },
    {
      field: 'is_production',
      label: '是否产品部',
      component: 'Select',
      componentProps: {
        options: [
          { label: '不是', value: 0 },
          { label: '是', value: 1 }
        ]
      }
    },
    {
      field: 'strid',
      label: '其他支出单号-明细',
      component: 'Input'
    },
    {
      field: 'doc_strid',
      label: '其他支出单号',
      component: 'Input'
    },
    {
      field: 'desc',
      label: '摘要',
      component: 'Input'
    },
    {
      field: 'is_check',
      label: '财务审核',
      component: 'Select',
      componentProps: {
        options: [
          { label: '否', value: 0 },
          { label: '通过', value: 1 },
          { label: '驳回', value: 2 }
        ]
      }
    },
    //字段待定
    {
      field: 'created_at',
      label: '创建日期',
      component: 'SingleRangeDate',
      componentProps: {
        allowEmpty: [true, true],
        showTime: false,
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        style: {
          width: '100%'
        }
      }
    },
    {
      field: 'dept_id',
      label: '部门',
      component: 'ApiTreeSelect',
      componentProps: {
        api: getDeptSelectTree,
        immediate: false,
        lazyLoad: true,
        treeSelectProps: {
          fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
          placeholder: '请选择',
          showSearch: true,
          optionFilterProp: 'name',
          treeDefaultExpandAll: true,
          filterTreeNode: (search, item) => {
            if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
            return false
          }
        }
      }
    },
    // {
    //   field: 'share_account_code',
    //   label: '分摊科目',
    //   component: 'PagingApiSelect',
    //   componentProps: {
    //     api: getCategory,
    //     resultField: 'items',
    //     labelField: 'account_name',
    //     valueField: 'account_name',
    //     selectProps: {
    //       fieldNames: {
    //         key: 'key',
    //         value: 'account_code',
    //         label: 'account_name'
    //       },
    //       showSearch: true,
    //       placeholder: '请选择',
    //       optionFilterProp: 'account_name',
    //       allowClear: true
    //     },
    //     params: {
    //       status: 1
    //     }
    //   },
    //   itemProps: {
    //     validateTrigger: 'blur'
    //   }
    // },
    {
      field: 'account_code',
      label: '支出科目',
      component: 'PagingApiSelect',
      componentProps: {
        api: getCategory,
        resultField: 'items',
        labelField: 'account_name',
        valueField: 'account_name',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'account_code',
            label: 'account_name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'account_name',
          allowClear: true
        },

        params: {
          status: 1
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'share_inCharge_id',
      label: '分摊人员',
      component: 'PagingApiSelect',
      componentProps: {
        api: getStaffList,
        resultField: 'items',
        immediate: true,
        lazyLoad: true,
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    // {
    //   field: 'share_setting_name',
    //   label: '分摊模式名称',
    //   component: 'Input'
    // },
    {
      field: 'collection_at',
      label: '收付款日期',
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD'
      }
    },
    {
      field: 'source_uniqid',
      label: '销售单号',
      component: 'Input'
    }
  ]
  return schemas
}
