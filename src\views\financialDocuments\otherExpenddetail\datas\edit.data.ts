import { getErpSupplier } from '/@/api/commonUtils'
import { getDept } from '/@/api/erp/systemInfo'
import { getcustomerList } from '/@/api/financialDocuments/otherIncome'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { FormSchema } from '/@/components/Form'

export const corresType = [
  { label: '员工', value: 1 },
  { label: '部门', value: 2 },
  { label: '客户', value: 3 },
  { label: '供应商', value: 4 },
  { label: '其他', value: 5 }
]

function getWorkListFn(type) {
  if (!type) {
    return getCreatorList
  }
  if (type == 1) {
    return getCreatorList
  }
  if (type == 2) {
    return getDept
  }
  if (type == 3) {
    return getcustomerList
  }
  if (type == 4) {
    return getErpSupplier
  }
}

export const schemas: FormSchema[] = [
  {
    label: '往来单位类型',
    field: 'corres_type',
    component: 'Select',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        allowClear: true,
        options: corresType,
        style: {
          width: '100%'
        },
        onChange: (val) => {
          console.log(val)
          if (val == undefined) return (formModel.corres_pondent = undefined)
        }
      }
    }
  },
  {
    label: '往来单位',
    field: 'corres_pondent',
    component: 'PagingApiSelect',
    componentProps: (fromat) => {
      return {
        api: getWorkListFn(fromat.formModel.corres_type),
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        params: {
          type: 3,
          status: [1, 3, 4, 5, 15]
        },
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'name',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          style: {
            width: '100%'
          }
        }
      }
    },
    required(renderCallbackParams) {
      return renderCallbackParams.model.corres_type !== 5
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.corres_type !== 5
    },
    show(renderCallbackParams) {
      return renderCallbackParams.model.corres_type !== 5
    }
  },
  {
    label: '往来单位',
    field: 'corres_pondent',
    component: 'Input',
    required(renderCallbackParams) {
      return renderCallbackParams.model.corres_type == 5
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.corres_type == 5
    },
    show(renderCallbackParams) {
      return renderCallbackParams.model.corres_type == 5
    }
  }
]
