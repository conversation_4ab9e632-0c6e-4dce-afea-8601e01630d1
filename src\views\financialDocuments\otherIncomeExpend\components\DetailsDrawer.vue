<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="详情" width="90%" @close="close">
    <ScrollContainer>
      <Descriptions title="收入单详情" :column="2">
        <DescriptionsItem label="申请人">{{ record.applicant_name }}</DescriptionsItem>
        <DescriptionsItem label="负责人">{{ record.inCharge_name }}</DescriptionsItem>
        <DescriptionsItem label="创建人">{{ record.creator_name }}</DescriptionsItem>
        <DescriptionsItem label="日期">{{ record.created_at }}</DescriptionsItem>
        <DescriptionsItem label="收入单号">{{ record.strid }}</DescriptionsItem>
        <DescriptionsItem label="部门">{{ record.department }}</DescriptionsItem>
        <!-- <DescriptionsItem label="关联流水单号">{{ record.fund_strid }}</DescriptionsItem> -->
        <DescriptionsItem label="总金额">{{ record.amount }}</DescriptionsItem>
        <DescriptionsItem label="外币总金额">{{ record.foreign_currency_amount }}</DescriptionsItem>
        <DescriptionsItem label="币种">{{ record.currency }}</DescriptionsItem>
        <!-- <DescriptionsItem label="订单号">{{ record.df.strid }}</DescriptionsItem> -->
        <DescriptionsItem label="状态">
          <Tag :color="statusMap[record.status]?.color"> {{ statusMap[record.status]?.text }}</Tag>
        </DescriptionsItem>
        <DescriptionsItem label="审核">
          <Tag :color="ischecks[record.is_check2]?.color"> {{ ischecks[record.is_check2]?.text }}</Tag>
        </DescriptionsItem>
        <DescriptionsItem label="摘要">{{ record.desc ? record.desc : '-' }}</DescriptionsItem>
        <DescriptionsItem label="备注">{{ record.remark ? record.remark : '-' }}</DescriptionsItem>
        <DescriptionsItem label="备注">{{ record.remark ? record.remark : '-' }}</DescriptionsItem>
        <DescriptionsItem label="附件"
          ><ul>
            <li v-for="(item, index) in record.files" :key="item">
              <a :href="item" target="_blank" @click="handlePreview(item, $event)">{{ `附件${index + 1}` }}</a>
            </li>
          </ul>
        </DescriptionsItem>
      </Descriptions>

      <!-- 收入明细 -->
      <Descriptions title="收入明细" />
      <Alert message="仅提供关联销售单号，以供查询" type="info" show-icon />
      <DescriptionsItem>
        <BasicTable @register="register" :canResize="false">
          <template #bodyCell="{ text, column, record }">
            <template v-if="column.key === 'amount'"> {{ record.amount }} {{ '￥' }}</template>
            <!-- <template v-if="column.key === 'is_check'">
              <Tag :color="checkMap[record.is_check]?.color"> {{ checkMap[record.is_check]?.text }}</Tag>
            </template>
            <template v-if="column.key === 'is_check2'">
              <Tag :color="checkMap[record.is_check2]?.color"> {{ checkMap[record.is_check2]?.text }}</Tag>
            </template> -->
            <template v-if="column.key === 'files'">
              <TableImg :size="60" style="height: 130px" :simpleShow="true" :imgList="text" />
            </template>
          </template>
        </BasicTable>
      </DescriptionsItem>
    </ScrollContainer>
    <PreviewFile @register="registerModal" />
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { statusMap, ischecks } from '../datas/datas'
import { ref } from 'vue'
import { Descriptions, DescriptionsItem, Tag, Alert } from 'ant-design-vue'
import { ScrollContainer } from '/@/components/Container'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { childrenColumns, updataexpenseDetails } from '../datas/OtherIncomeDrawer.data'
import { detailsOtherdetails } from '/@/api/financialDocuments/otherIncome'
import { useTable, BasicTable, TableImg } from '/@/components/Table'
import { useMessage } from '/@/hooks/web/useMessage'
import { useModal } from '/@/components/Modal'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { createImgPreview } from '/@/components/Preview'

const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

const emit = defineEmits(['register', 'relaod'])
const record = ref<any>({})
const status = ref()
const datasource = ref<any[]>([])
const total_price = ref()
const [registerDrawer, { closeDrawer }] = useDrawerInner(async (data) => {
  data.record.is_retax == 1 ? setColumns(await childrenColumns(data.type, [])) : setColumns(await updataexpenseDetails(data.type, []))
  total_price.value = data.record.total_price
  const { items }: any = await detailsOtherdetails({ id: data.record.id })
  record.value = items
  status.value = items.status
  datasource.value = [...items.items]
})

const [register, { setTableData, setColumns }] = useTable({
  showIndexColumn: true,
  dataSource: datasource
})
function close() {
  setTableData([])
  closeDrawer()
  emit('relaod')
}

async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  console.log(prefix)
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>
