<template>
  <BasicModal @register="register" title="附件上传" @ok="handleOk" width="800px">
    <Alert
      message="上传时,图片可进行展示,单execl,word,ppt等文件,只能进行初略展示,请下载进行查看"
      show-icon
      type="warning"
      style="margin-bottom: 15px"
    />
    <BasicForm @register="registerform">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template></BasicForm
    ></BasicModal
  >
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { UploadSchemas } from '../datas/OtherIncomeDrawer.data'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { UploadFile, Upload, message, Alert } from 'ant-design-vue'
import { ref, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { random } from 'lodash-es'

//id
const init_data = ref()
const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
  console.log(data)
  filesList.value =
    data?.files?.map((item) => {
      return {
        url: item,
        uid: random(1, 1000000000),
        name: item
      }
    }) ?? []
  setFieldsValue({
    files: data?.files ?? []
  })
  init_data.value = data
})
const [registerform, { setFieldsValue }] = useForm({
  schemas: UploadSchemas,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelCol: { span: 5 }
})

//附件
const filesList = ref<UploadFile[]>([])

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val.map((item) => item.url) })
  }
)

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    console.log(file)
    changeOkLoading(true)
    const result = await commonFileUpload(file, 'purchase')
    onSuccess!(result.path)
    filesList.value = filesList.value!.map((item) => {
      return {
        url: (item.url as string) || (item.response as string),
        uid: item.uid,
        name: item.name
      }
    })
    await setFieldsValue({
      files: filesList.value.map((item) => item.url)
    })
    // 判断是否所有的文件都已经上传成功，如果有uploading状态的就不修改按钮状态
    const isAllDone = filesList.value.every((item) => item.url)
    if (isAllDone) {
      changeOkLoading(false)
    }
  } catch (err) {
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}

const emit = defineEmits(['register', 'success'])
// 提交
async function handleOk() {
  try {
    changeOkLoading(true)
    Reflect.set(init_data.value, 'files', [])
    filesList.value.forEach((item) => {
      init_data.value.files.push(item.url)
    })
    closeModal()
    message.success('附件上传成功')
    changeOkLoading(false)
    emit('success', init_data.value)
    filesList.value = []
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  }
}
</script>
