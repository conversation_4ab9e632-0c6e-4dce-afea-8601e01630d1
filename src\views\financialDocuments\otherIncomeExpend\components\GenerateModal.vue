<template>
  <BasicModal @register="registerModal" width="50%" title="请填写以下信息" :bodyStyle="{ height: '500px' }" @ok="handleOk">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { ref } from 'vue'
import { getsetStatus } from '/@/api/financialDocuments/otherIncome'
const emit = defineEmits(['register', 'handleSubmit', 'success'])
console.log(emit)
/** 账单id */
const init_id = ref()
/** 注册from */
const [registerForm, { validate, setFieldsValue, resetFields }] = useForm({
  labelWidth: 150, //改下面的schemas的话要连src\views\erp\saleOrder\datas\OtherIncomeDrawer.ts里的auditSchema也改了
  schemas: [
    {
      field: 'foreign_currency_amount',
      label: '外汇金额',
      component: 'InputNumber',
      colProps: {
        span: 11
      },
      dynamicDisabled: true
    },
    {
      field: 'amount',
      label: '本次应收金额',
      component: 'InputNumber',
      componentProps: {
        min: 0.01,
        precision: 2,
        placeholder: '请输入本次应收金额'
      },
      colProps: {
        span: 11
      },
      required: true
    },
    {
      field: 'collection_at',
      label: '收款日期',
      component: 'DatePicker',
      itemHelpMessage: '流水日期',
      componentProps: {
        valueFormat: 'YYYY-MM-DD'
      },
      colProps: {
        span: 11
      },
      required: true
    },
    {
      field: 'notes',
      label: '对方付款人',
      required: true,
      component: 'Input',
      colProps: {
        span: 11
      }
    },
    {
      field: 'notes_account',
      label: '对方付款人账号',
      itemHelpMessage: '账号后4位',
      component: 'Input',
      colProps: {
        span: 11
      },
      required: true
    },
    {
      field: 'notes_bank',
      label: '对方付款银行/平台',
      component: 'Input',
      colProps: {
        span: 11
      },
      required: true
    },
    {
      field: 'account',
      label: '收款账号',
      component: 'Input',
      itemHelpMessage: '账号后4位',
      colProps: {
        span: 11
      },
      required: true
    },
    {
      field: 'account_name',
      label: '收款银行',
      component: 'Input',
      colProps: {
        span: 11
      },
      required: true
    },
    {
      field: 'payment_type',
      label: '款项类型',
      component: 'RadioGroup',
      defaultValue: 3,
      componentProps: {
        options: [
          {
            label: '定金',
            value: 1
          },
          {
            label: '最后一笔款',
            value: 2
          },
          {
            label: '全款',
            value: 3
          }
        ],
        disabled: true
      },
      colProps: {
        span: 11
      },
      required: true
    },
    {
      field: 'g_remark',
      label: '携带备注',
      component: 'InputTextArea',
      itemHelpMessage: '写明外汇币别与金额，及其他注释内容',
      componentProps: {
        autosize: { minRows: 3, maxRows: 6 }
      },
      colProps: {
        span: 11
      }
    }
  ],
  showSubmitButton: false,
  showResetButton: false,
  actionColOptions: {
    span: 24
  }
})

/** 注册Modal */
const [registerModal, { closeModal, changeOkLoading }] = useModalInner((data) => {
  resetFields()
  console.log(data)
  setFieldsValue({ amount: data.amount, foreign_currency_amount: data.foreign_currency_amount })
  init_id.value = data.id
})

/** 点击确认 */
const handleOk = async () => {
  try {
    changeOkLoading(true)
    let data = await validate()
    const params = {
      ...data,
      notes: data.notes || null,
      g_remark: data.g_remark || null,
      id: init_id.value
    }
    console.log(params)

    try {
      const msg: any = await getsetStatus(params)
      if (msg.news == 'success') {
        emit('success')
        closeModal()
      }
    } catch (error) {
      console.log(error)
      throw new Error(`${error}`)
    }
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  }
}
</script>
<style lang="less" scoped>
:deep(.ant-picker) {
  width: 100%;
}

:deep(.ant-descriptions-item-content) {
  display: inline-block;
}

:deep(.ant-descriptions-item) {
  padding: 0;
}
</style>
