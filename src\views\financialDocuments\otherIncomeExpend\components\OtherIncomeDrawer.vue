<template>
  <BasicDrawer @register="registerDrawer" v-bind="$attrs" showFooter width="90%" @ok="handleOk" @close="handleClose">
    <Descriptions v-if="getcapitalDetails" bordered class="mb-4">
      <DescriptionsItem label="流水日期">{{ init_record.occurrence_at }}</DescriptionsItem>
      <DescriptionsItem label="流水金额">{{ formateerNotCurrency.format(init_record.amount) }}</DescriptionsItem>
      <DescriptionsItem label="流水剩余金额">{{ formateerNotCurrency.format(init_record.amount_left) }}</DescriptionsItem>
    </Descriptions>
    <BasicForm v-if="getcapitalDetails" @register="registerFormcheck" style="margin-bottom: 10px" @field-value-change="fieldvaluechange" />
    <Descriptions title="其他收入单" />
    <BasicForm @register="registerForm" @field-value-change="fieldvaluechange">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg2Stocking"
          :custom-request="handleFileRequest"
          :multiple="true"
          :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
        >
          <a-button type="primary">
            <upload-outlined />
            Upload
          </a-button>
        </Upload>
      </template>
    </BasicForm>
    <Descriptions title="收入明细" />
    <DescriptionsItem>
      <BasicTable @register="registerTable" :canResize="false">
        <template #toolbar>
          <Popover placement="left" trigger="click" v-model:visible="texdisabled" v-bind="$attrs" @visible-change="handleVisibleChange">
            <template #content>
              <div class="flex">
                <div class="w-[300px] mr-2">
                  <span>采购单号</span>
                  <PagingApiSelect v-model:value="purchaseID" v-bind="purchasePagingSelectConfig" />
                </div>
                <div class="w-[300px] mr-2">
                  <span>供应商</span>
                  <Input v-model:value="supplierName" disabled />
                </div>
              </div>
              <div class="flex">
                <div class="w-[300px] mr-2">
                  <span>退税金额</span>
                  <InputNumber v-model:value="retaxnumber" controls :precision="2" :min="0" />
                </div>
                <div class="w-[300px] mr-2">
                  <span>退税税点</span>
                  <InputNumber v-model:value="tetaoppdie" :max="100" controls :precision="0" :min="0" />
                </div>
              </div>
              <div class="flex justify-end mt-2">
                <a-button type="primary" @click="handleAddGoods">确定添加</a-button>
              </div>
            </template>
            <Button type="primary" :disabled="is_retax_type !== 1">退税新增</Button>
          </Popover>
          <Button type="primary" @click="handleAdd" :disabled="currentEditbidabled">新增</Button>
          <Dropdown :disabled="currentEditbidabled">
            <a class="ant-dropdown-link" @click.prevent>
              <a-button>
                <template #icon><download-outlined /> </template>明细文件 <download-outlined />
              </a-button>
            </a>
            <template #overlay>
              <Menu @click="handleMenuClick">
                <MenuItem key="upload"><upload-outlined /> 导入明细</MenuItem>
                <MenuItem key="export"><download-outlined /> 模板</MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </template>
        <template #bodyCell="{ text, column, record }">
          <template v-if="column.key === 'action'">
            <TableAction :actions="createActions(record)" />
          </template>
          <!-- <template v-if="column.key === 'is_check'">
            <Tag :color="checkMap[record.is_check]?.color"> {{ checkMap[record.is_check]?.text }}</Tag>
          </template>
          <template v-if="column.key === 'is_check2'">
            <Tag :color="checkMap[record.is_check2]?.color"> {{ checkMap[record.is_check2]?.text }}</Tag>
          </template> -->
          <template v-if="column.key === 'files'">
            <TableImg :size="60" style="height: 130px" :simpleShow="true" :imgList="text" />
          </template>
        </template>
      </BasicTable>
    </DescriptionsItem>
    <!-- <UploadModal @register="registerUploadFilsModal" @success="handleSuccess" /> -->
    <ImpExcelModal @register="registerUploadModal" :dataCallBackFn="handleUploadData" ref="ImpExcelModalRef" />
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref, unref, watch, onMounted, nextTick } from 'vue'
import { BasicForm, useForm, PagingApiSelect } from '/@/components/Form'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import {
  schemasFn,
  updataexpenseDetails,
  excelHeader,
  schemas,
  childrenColumns,
  purchasePagingSelectConfig,
  purchasedata,
  supplierName,
  is_retax_type as is_retax_number
} from '../datas/OtherIncomeDrawer.data'
import { createRecClause } from '/@/api/financialDocuments/capitalFlow'
import { detailsOtherdetails, editOtherupdate } from '/@/api/financialDocuments/otherIncome'
import { cloneDeep } from 'lodash-es'
import {
  Popover,
  InputNumber,
  message,
  Upload,
  UploadFile,
  Descriptions,
  DescriptionsItem,
  Button,
  Dropdown,
  Menu,
  MenuItem,
  Input
} from 'ant-design-vue'
import { UploadOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { BasicTable, useTable, TableAction, ActionItem, TableImg } from '/@/components/Table'
import type { EditRecordRow } from '/@/components/Table'
import { onExpExcelTemplate } from '/@/utils/exportTemplate'
import { transformData2Import, IMP_EXCEL_END } from '../datas/importModal'
import { ImpExcelModal } from '/@/components/Excel'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { getDept, getInchargeList } from '/@/api/erp/systemInfo'
import { useModal } from '/@/components/Modal'
import defaultUser from '/@/utils/erp/defaultUser'
import { add } from '/@/utils/math'
import dayjs from 'dayjs'
import Decimal from 'decimal.js'
//子传父
const emit = defineEmits(['success', 'register'])
//状态
const isUpdate = ref(false)
// const id = ref()
//资金流水生成接口
const getcapitalDetails = ref(false)
//资金流水id
const capitalId = ref(0)
//资金流水剩余金额
const capital_cost = ref(0)
const init_record = ref()
/************************************************/
//附件
const filesList = ref<UploadFile[]>([])
//保存点击,其他禁用
const currentEditKeyRef = ref('')
//类别
const istype = ref()
//DC32715-AS6_PJ0017195-30162	的数据
const deletearr = ref<any>([])
//提示
const meesg = ref(false)
//work数组
const workList = ref<any>([])

//doc_id
const doc_id = ref()
//编辑唯一
const currentEditbidabled = ref(false)
//保存标识
const save = ref(false)
const props_data = ref()
//退税
const texdisabled = ref(false)
const purchaseID = ref()
const retaxnumber = ref()
const tetaoppdie = ref(13)
const is_retax_type = ref()
/************/

//初始化
const [registerForm, { resetFields, resetSchema, setFieldsValue, validate, getFieldsValue }] = useForm({
  baseColProps: { span: 12 },
  showActionButtonGroup: false,
  labelCol: { span: 6 }
})
//流水审核
const [registerFormcheck, { resetFields: resetFieldscheck, validate: validatecheck, updateSchema }] = useForm({
  baseColProps: { span: 24 },
  showActionButtonGroup: false,
  schemas,
  labelCol: { span: 6 }
})
//初始化tabel
const [registerTable, { setTableData, getDataSource, updateTableDataRecord, getColumns, setColumns, reload }] = useTable({
  title: '',
  showIndexColumn: false,
  actionColumn: {
    width: 250,
    title: '操作',
    dataIndex: 'action'
  }
})
const [registerDrawer, { closeDrawer, changeOkLoading }] = useDrawerInner(async (data) => {
  try {
    console.log(data)

    resetFields()
    isUpdate.value = data.isUpdate
    props_data.value = data
    currentEditbidabled.value = false
    istype.value = data.type
    const order = data.record && data.record.is_check2 == 2 ? true : false
    resetSchema(await schemasFn())
    if (unref(isUpdate)) {
      if (data.record.is_retax == 1) {
        setColumns(
          await childrenColumns(
            props_data.value.type,
            [],
            assignment,
            props_data.value.record && props_data.value.record.is_check2 == 2 ? true : false
          )
        )
      }
      is_retax_type.value = data.record.is_retax
      is_retax_number.value = data.record.is_retax
      doc_id.value = data.record.id
      const { items }: any = await detailsOtherdetails({ id: data.record.id })
      filesList.value = data.record?.files?.map((item) => ({ url: item, name: item, uid: item }))
      currentEditbidabled.value = items.is_retax !== 1 ? items.is_check2 == 2 : items.is_retax == 1 ? true : false
      setFieldsValue({ ...data.record, rate: data.record.exchange_rate })
      setTableData(items.items)
    } else {
      setColumns(await updataexpenseDetails(data.type, [], assignment, order))
      setTableData([])
      if (data.record) {
        init_record.value = data.record
        getcapitalDetails.value = true
        capitalId.value = data.record.id
        capital_cost.value = data.record.amount_left

        // 设置收款日期要大于等于流水日期
        nextTick(() => {
          updateSchema({
            field: 'collection_at',
            componentProps: {
              valueFormat: 'YYYY-MM-DD',
              disabledDate: (current) => {
                if (!current) {
                  return false
                }
                //补充下面的判断逻辑(设置收款日期要大于等于流水日期)
                const currentDate = dayjs(current) //当前时间
                const occurrenceDate = dayjs(init_record.value.occurrence_at.match(/^[^ ]+/)[0])
                if (currentDate.isBefore(occurrenceDate)) {
                  return true
                }
                const today = dayjs()
                const tomorrow = today.add(1, 'day')
                return current && current.diff(tomorrow, 'day') >= 0
              }
            }
          })
        })
      } else {
        getcapitalDetails.value = false
      }
      filesList.value = []
    }
    setFieldsValue({ applicant: defaultUser!.userId })
  } catch (err) {
    throw new Error(`${err}`)
  }
})

//赋值
async function assignment(shall: any) {
  if (!shall) return
  if (shall.account_name) {
    updateTableDataRecord(currentEditKeyRef.value, { account_code: shall.account_code })
  }
  if (shall.value === 5) {
    await setColumns(
      childrenColumns(
        props_data.value.type,
        [],
        assignment,
        props_data.value.record && props_data.value.record.is_check2 == 2 ? true : false
      )
    )
  } else {
    await setColumns(
      updataexpenseDetails(
        props_data.value.type,
        [],
        assignment,
        props_data.value.record && props_data.value.record.is_check2 == 2 ? true : false
      )
    )
  }
}

//action
function createActions(record: EditRecordRow): ActionItem[] {
  if (!record.editable) {
    return [
      {
        label: '编辑',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        // ifShow: record.is_check2 !== 2 && !record.is_cancel,
        ifShow: record.is_check2 !== 2 && !record.is_cancel && !currentEditbidabled.value,
        onClick: handleEdit.bind(null, record)
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          placement: 'left',
          confirm: handleDelete.bind(null, record)
        },
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        ifShow: (record.is_check2 !== 2 && !record.is_cancel && !currentEditbidabled.value) || is_retax_type.value == 1
      },
      {
        label: '取消明细',
        color: 'error',
        ifShow: record.is_check2 == 2 && !record.is_cancel,
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        popConfirm: {
          okText: '确定',
          title: '请检查当前取消的明细是否正确,取消过后不能在进行更改,是否取消当前明细',
          placement: 'left',
          confirm: Canceldetail.bind(null, record)
        }
      },
      {
        label: '复制',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        ifShow: record.is_check2 !== 2 && !record.is_cancel && !currentEditbidabled.value,
        onClick: handlecopylink.bind(null, record)
      }
    ]
  }
  return [
    {
      label: '保存',
      onClick: handleSave.bind(null, record)
    },
    {
      label: '取消',
      popConfirm: {
        title: '是否取消编辑',
        placement: 'right',
        confirm: handleCancel.bind(null, record)
      }
    }
  ]
}
// 存储编辑前的record
const beforeRecord = ref()

async function handleEdit(record: EditRecordRow) {
  currentEditbidabled.value = true
  save.value = true
  if (is_retax_type.value === 1) {
    record.account_name = '其他应收款-垫付税费'
    record.account_code = '10013'
  }
  currentEditKeyRef.value = record.key
  beforeRecord.value = cloneDeep(record)
  record.onEdit?.(true, false)
  if (record.corres_type === 5) {
    await setColumns(
      childrenColumns(
        props_data.value.type,
        [],
        assignment,
        props_data.value.record && props_data.value.record.is_check2 == 2 ? true : false
      )
    )
  } else {
    await setColumns(
      updataexpenseDetails(
        props_data.value.type,
        [],
        assignment,
        props_data.value.record && props_data.value.record.is_check2 == 2 ? true : false
      )
    )
  }
}

//删除tabel
async function handleDelete(record) {
  const key = record.par_work_id
  const da = await getDataSource()
  const updatedDa = da.filter((item) => item.par_work_id !== key)
  console.log(updatedDa)
  setTableData(updatedDa)

  // deleteTableDataRecord(record.key)
  if (record.id && istype.value !== 'add') {
    if (is_retax_type.value == 1) {
      const aarray = da.filter((item) => item.par_work_id == key)
      const object = aarray.map((item) => {
        let temporary = {}
        for (let colName of getColumns()) {
          if (colName.key !== 'action') {
            temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
          }
          temporary['type'] = 3
          temporary['inCharge'] = record.inCharge
        }
        return temporary
      })
      deletearr.value.push(...object)
    } else {
      const dataobj = {
        account_code: record.account_code,
        account_name: record.account_name,
        amount: record.amount,
        dept_id: record.dept_id,
        id: record.id,
        parent_id: record.parent_id,
        type: 3,
        work_id: record.work_id,
        inCharge: record.inCharge,
        remark: record.remark,
        rate: record.rate,
        currency: record.currency,
        desc: record.desc,
        par_work_id: record.par_work_id,
        basic_work_id: record.basic_work_id,
        sale_work_id: record.sale_work_id
      }

      deletearr.value.push(dataobj)
    }
  }

  const costdata = updatedDa
    .filter((item) => {
      return !item.is_cancel && item.amount
    })
    .reduce((pre, item) => {
      return add(pre, item.amount, 2)
    }, 0)

  const foreigndata = updatedDa
    .filter((item) => {
      return !item.is_cancel && item.amount
    })
    .reduce((pre, item) => {
      return add(pre, item.amount, 2)
    }, 0)

  await setFieldsValue({ amount: costdata, foreign_currency_amount: foreigndata })
}
watch(
  () => is_retax_type.value,
  async (val) => {
    currentEditbidabled.value = val == 1 ? true : false
    await setColumns(
      childrenColumns(
        props_data.value.type,
        [],
        assignment,
        props_data.value.record && props_data.value.record.is_check2 == 2 ? true : false
      )
    )
    if (istype.value !== 'add') {
      console.log('1')

      const da = await getDataSource()
      const object = da.map((item) => {
        let temporary = {}
        for (let colName of getColumns()) {
          if (colName.key !== 'action') {
            temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
          }
          temporary['type'] = 3
          temporary['inCharge'] = props_data.value.record.inCharge
        }
        return temporary
      })
      deletearr.value.push(...object)
    }
    setTableData([])
  }
)

//添加明细
function handleAddGoods() {
  // 验证必填字段
  if (!purchaseID.value) return message.warning('请先选择采购单')
  if (!retaxnumber.value) return message.warning('请填写退税金额')
  if (!tetaoppdie.value) return message.warning('请填写发票税点')

  // 将输入值转换为 Decimal 类型
  const retaxnumberDecimal = new Decimal(retaxnumber.value)
  const tetaoppdieDecimal = new Decimal(tetaoppdie.value).dividedBy(100)
  const additionalTaxRate = new Decimal(purchasedata.value.add_point).dividedBy(100)

  // 计算核心公式
  const divisionResult = retaxnumberDecimal.dividedBy(tetaoppdieDecimal)
  const partOne = divisionResult.plus(retaxnumberDecimal)
  const partTwo = partOne.dividedBy(new Decimal(1).plus(additionalTaxRate))
  const result = partOne.minus(partTwo).toDecimalPlaces(2)
  const difference = retaxnumberDecimal.minus(result)
  const Gbuilrd = difference.times(0.2).toDecimalPlaces(2)
  const finance = difference.times(0.3).toDecimalPlaces(2)
  const dempoent = difference.times(0.5).toDecimalPlaces(2)

  // 生成新的行数据
  const newRowDataItem = [
    {
      currency: '人民币',
      rate: '人民币-1',
      source_uniqid: purchasedata.value.source_uniqid,
      parent_strid: purchasedata.value.strid,
      account_name: '其他应收款-垫付税费',
      supplier_name: supplierName.value,
      account_code: '10013',
      department: '财务部',
      dept_id: 25,
      amount: result.toNumber(),
      par_work_id: purchasedata.value.work_id,
      basic_work_id: purchasedata.value.sale_work_id,
      sale_work_id: purchasedata.value.sale_work_id
    },
    {
      currency: '人民币',
      rate: '人民币-1',
      source_uniqid: purchasedata.value.source_uniqid,
      parent_strid: purchasedata.value.strid,
      account_name: '其他业务收入-退税服务收入',
      supplier_name: supplierName.value,
      account_code: '10036',
      department: 'gbuilder',
      dept_id: 92,
      amount: Gbuilrd.toNumber(),
      par_work_id: purchasedata.value.work_id,
      basic_work_id: purchasedata.value.sale_work_id,
      sale_work_id: purchasedata.value.sale_work_id
    },
    {
      currency: '人民币',
      rate: '人民币-1',
      source_uniqid: purchasedata.value.source_uniqid,
      parent_strid: purchasedata.value.strid,
      supplier_name: supplierName.value,
      account_name: '其他业务收入-退税服务收入',
      account_code: '10036',
      department: '财务部',
      dept_id: 25,
      amount: finance.toNumber(),
      par_work_id: purchasedata.value.work_id,
      basic_work_id: purchasedata.value.sale_work_id,
      sale_work_id: purchasedata.value.sale_work_id
    },
    {
      currency: '人民币',
      rate: '人民币-1',
      source_uniqid: purchasedata.value.source_uniqid,
      parent_strid: purchasedata.value.strid,
      supplier_name: supplierName.value,
      account_name: '其他业务收入-退税服务收入',
      account_code: '10036',
      department: purchasedata.value.dept_name,
      dept_id: purchasedata.value.dept_id,
      amount: dempoent.toNumber(),
      par_work_id: purchasedata.value.work_id,
      basic_work_id: purchasedata.value.sale_work_id,
      sale_work_id: purchasedata.value.sale_work_id,
      clear_department: purchasedata.value.clear_department,
      clear_dept_id: purchasedata.value.clear_dept_id
    }
  ]

  // 更新表格数据
  const dataSource = [...newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)

  // 重新计算总金额
  const da = getDataSource()
  const costdata = da.filter((item) => !item.is_cancel && item.amount).reduce((pre, item) => add(pre, item.amount, 2), 0)
  const foreigndata = da
    .filter((item) => !item.is_cancel && item.foreign_currency_amount)
    .reduce((pre, item) => add(pre, item.foreign_currency_amount, 2), 0)
  setFieldsValue({ amount: costdata, foreign_currency_amount: foreigndata })

  // 重置表单和状态
  texdisabled.value = false
  purchaseID.value = ''
  retaxnumber.value = ''
  supplierName.value = ''
  purchasedata.value = {}
}

function handleVisibleChange(value) {
  texdisabled.value = currentEditbidabled.value == false ? false : value
}

async function handleAdd() {
  const formdata = await getFieldsValue()
  console.log(formdata)

  const newRowDataItem = {
    currency: formdata.currency,
    rate: formdata.exchange_rate,
    source_uniqid: null,
    account_name: null,
    account_code: null,
    department: null
  }
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
}

//格式化某一行的数据
function formatObject(record) {
  // getDataSource()有我们不需要的属性,所以需要清除
  let temporary = {}
  for (let colName of getColumns()) {
    if (colName.key !== 'action') {
      temporary[`${colName.dataIndex}`] = record[`${colName.dataIndex}`]
    }
  }
  return temporary
}

//复制明细
function handlecopylink(record) {
  const newrecord = formatObject(record)
  delete newrecord.id
  const newRowDataItem = {
    ...newrecord
  }
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
  const da = getDataSource()
  const costdata = da
    .filter((item) => {
      return !item.is_cancel && item.amount
    })
    .reduce((pre, item) => {
      return add(pre, item.amount, 2)
    }, 0)

  const foreigndata = da
    .filter((item) => {
      return !item.is_cancel && item.foreign_currency_amount
    })
    .reduce((pre, item) => {
      return add(pre, item.foreign_currency_amount, 2)
    }, 0)
  setFieldsValue({ amount: costdata, foreign_currency_amount: foreigndata })
}
//保存
async function handleSave(record: EditRecordRow) {
  // 校验
  const valid = await record.onValid?.()
  if (valid) {
    try {
      if (is_retax_type.value == 1) {
        if (!record.source_uniqid) {
          message.error({ content: '请选择关联销售单号' })
          return
        }
      }
      if (!record.department) {
        message.error({ content: '请填写部门' })
        return
      }
      if (!record.account_code || !record.account_name) {
        message.error({ content: '请填写科目' })
        return
      }
      if (!record.amount) {
        message.error({ content: '请填写金额' })
        return
      }
      if (record.corres_type && !record.corres_pondent) {
        message.error({ content: '选取往来单位' })
        return
      }
      if (!record.corres_type && record.corres_pondent) {
        message.error({ content: '选取往来单位类型' })
        return
      }

      const da = await getDataSource()
      const costdata = da
        .filter((item) => {
          return !item.is_cancel && item.amount
        })
        .reduce((pre, item) => {
          return add(pre, item.amount, 2)
        }, 0)
      const foreigndata = da
        .filter((item) => {
          return !item.is_cancel && item.foreign_currency_amount
        })
        .reduce((pre, item) => {
          return add(pre, item.foreign_currency_amount, 2)
        }, 0)
      setFieldsValue({ amount: costdata, foreign_currency_amount: foreigndata })
      //TODO 此处将数据提交给服务器保存
      // ...
      // 保存之后提交编辑状态

      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
        currentEditbidabled.value = false
        save.value = false
      }
      message.success({ content: '数据已保存', key: 'saving' })
      meesg.value = false
    } catch (error) {
      message.error({ content: '保存失败', key: 'saving' })
      throw new Error(`${error}`)
    }
  } else {
    message.error({ content: '请填写正确的数据', key: 'saving' })
  }
}

//取消
function handleCancel(record: EditRecordRow) {
  currentEditbidabled.value = false
  save.value = false
  currentEditKeyRef.value = ''
  updateTableDataRecord(record.key, {
    amount: beforeRecord.value.amount,
    source_uniqid: beforeRecord.value.source_uniqid,
    account_name: beforeRecord.value.account_name,
    account_code: beforeRecord.value.account_code,
    deptname: beforeRecord.value.deptname,
    desc: beforeRecord.value.desc,
    basic_work_id: beforeRecord.value.basic_work_id,
    sale_work_id: beforeRecord.value.sale_work_id,
    corres_pondent: beforeRecord.value.corres_pondent,
    corres_type: beforeRecord.value.corres_type,
    strid: beforeRecord.value.strid,
    currency: beforeRecord.value.currency,
    rate: beforeRecord.value.rate,
    remark: beforeRecord.value.remark,
    cleardeptname: beforeRecord.value.cleardeptname
  })

  record.onEdit?.(false, false)
}

//取消时间
async function Canceldetail(record) {
  const currentDate = new Date()
  const year = currentDate.getFullYear()
  const month = String(currentDate.getMonth() + 1).padStart(2, '0')
  const day = String(currentDate.getDate()).padStart(2, '0')
  const hours = ('0' + currentDate.getHours()).slice(-2)
  const minutes = ('0' + currentDate.getMinutes()).slice(-2)
  const seconds = ('0' + currentDate.getSeconds()).slice(-2)
  const datasource = await getDataSource()
  const costdata = datasource
    .filter((item) => {
      return item.id !== record.id && !item.is_cancel
    })
    .reduce((pre, item) => {
      return add(pre, item.amount, 2)
    }, 0)

  const foreigndata = datasource
    .filter((item) => {
      return item.id !== record.id && !item.is_cancel
    })
    .reduce((pre, item) => {
      return add(pre, item.foreign_currency_amount, 2)
    }, 0)
  setFieldsValue({ amount: costdata, foreign_currency_amount: foreigndata })

  record.cancel_at = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  record.is_cancel = 1
  reload()
}

// 格式化提交的数据
function formatSubmit() {
  // getDataSource()有我们不需要的属性,所以需要清除
  const dataSource = getDataSource().map((item) => {
    let temporary = {}
    for (let colName of getColumns()) {
      if (colName.key !== 'action') {
        temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
      }
    }
    return temporary
  })
  return dataSource
}
//导入
//execl上传
const [registerUploadModal, { openModal }] = useModal()
function handleMenuClick({ key }) {
  if (key === 'export') {
    onExpExcelTemplate(excelHeader)
  } else if (key === 'upload') {
    // openAgrDrawer(true, {})
    openModal(true, {
      sheetName: 'Sheet1',
      headerRow: 1,
      startCell: 'A2',
      endCell: `H${IMP_EXCEL_END}`
    })
  }
}
const ImpExcelModalRef = ref<InstanceType<typeof ImpExcelModal>>()
async function handleUploadData(data) {
  ImpExcelModalRef.value?.changeLoading(true)
  const hide = message.loading('正在导入数据，请稍后...', 0)
  try {
    const newData = transformData2Import(data)
    //看导入前table是否有数据
    const initn_table = await formatSubmit()
    if (initn_table) {
      newData.unshift(...initn_table)
    }

    await setTableData(newData)
    return Promise.resolve('OK')
  } catch (err) {
    return Promise.reject('Fail')
  } finally {
    hide()
    ImpExcelModalRef.value?.changeLoading(false)
  }
}

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val?.map((item) => item.url) ?? [] })
  }
)
//附件上传
async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  filesList.value = filesList.value!.map((item) => {
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: item.name
    }
  })
  await setFieldsValue({
    files: filesList.value.map((item) => item.url)
  })
}

const chargeList = ref()
const statusdata = ref()
onMounted(async () => {
  chargeList.value = await getInchargeList()
  statusdata.value = await getDept()
})
//提交
async function handleOk() {
  const formdata = await validate()
  delete formdata.rate
  const tabledata: any = await formatSubmit()
  const isChancel: any = ref([])
  try {
    changeOkLoading(true)
    for (let item of tabledata) {
      if (item.is_cancel == 1) {
        isChancel.value.push(item.is_cancel)
      }
      if (item.is_check2 == 2 && item.is_cancel !== 1) {
        changeOkLoading(false)
        message.error('请先取消明细在提交')
        return
      }
      if (item.amount == 0 && is_retax_type.value !== 1) {
        changeOkLoading(false)
        message.error('明细金额不能为0,请重新填写')
        return
      }
      item.inCharge = formdata.inCharge
      if (item.rate == '人民币-1') {
        item.rate = 1
      }
      item.corres_type ||= null
      item.corres_pondent ||= null
      item.foreign_currency_amount ||= 0
      delete item.source_uniqid
      delete item.strid
      delete item.reject_remark2
      delete item.department
      delete item.clear_department
      delete item.parent_strid
      delete item.supplier_name
    }
    if (isUpdate.value) {
      formdata['id'] = doc_id.value
    }
    if (tabledata.length == 0) {
      changeOkLoading(false)
      message.error({ content: '请添加明细' })
      return
    } else {
      tabledata.push(...deletearr.value)
    }

    const params = { doc: formdata, items: tabledata }

    if (isUpdate.value && isChancel.value.length > 0 && isChancel.value.length == tabledata.length) {
      if (new Set(isChancel.value).size == 1) {
        params.doc['status'] = 16
      }
    }
    if (getcapitalDetails.value) {
      const checkform = await validatecheck()
      checkform['amount'] = formdata.amount
      params['id'] = capitalId.value
      params['status_data'] = checkform
      if (params.doc.amount > capital_cost.value) {
        changeOkLoading(false)
        message.error({ content: '收入金额不能大于剩余金额' })
        return
      }
    }
    if (save.value == true) {
      changeOkLoading(false)
      return message.error('请先保存')
    }
    const res = getcapitalDetails.value
      ? await createRecClause({ ...params, receivable: params.doc.total_price, fund_id: capitalId.value })
      : await editOtherupdate(params)
    if (res.news == 'success') {
      closeDrawer()
      workList.value = []
      emit('success')
    }
    setTimeout(() => {
      changeOkLoading(false)
    }, 1000)
  } catch (e) {
    changeOkLoading(false)
    console.log(e)
  }
}
function handleClose() {
  if (getcapitalDetails.value) {
    resetFieldscheck()
  }
  is_retax_type.value = undefined
  currentEditbidabled.value = false
  save.value = false
  currentEditKeyRef.value = ''
  workList.value = []
  deletearr.value = []
  filesList.value = []
  is_retax_type.value = null
}

async function fieldvaluechange(key, value) {
  console.log(key, value)
  if (key == 'is_retax') {
    is_retax_type.value = value
    if (value == 1) {
      setFieldsValue({ exchange_rate: '1.000000', rate: '1.000000', currency: '人民币' })
    }
  } else if (key == 'exchange_rate' || key == 'rate') {
    const data = getDataSource()
    const formdata = await getFieldsValue()
    data.forEach((item) => {
      item.rate = value
      item.currency = formdata.currency
      item.amount = undefined
      item.foreign_currency_amount = undefined
    })
    setFieldsValue({
      amount: 0,
      foreign_currency_amount: 0
    })
    setTableData(data)
  }
}
</script>
