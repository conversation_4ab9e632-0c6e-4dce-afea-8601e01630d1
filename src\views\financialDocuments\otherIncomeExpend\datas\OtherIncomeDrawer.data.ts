import { getApplicantList, getDept, getInchargeList } from '/@/api/erp/systemInfo'
import type { BasicColumn, FormSchema } from '/@/components/Table'
import { getcustomerList, getCategory } from '/@/api/financialDocuments/otherIncome'
import { Rule } from 'ant-design-vue/lib/form'

import { getCreatorList } from '/@/api/financialDocuments/public'
import { getErpSupplier, getWorkList } from '/@/api/commonUtils'
import { h, ref } from 'vue'
import { Tag } from 'ant-design-vue'
import { ischecks } from './datas'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash-es'
import { getPurchaseOrderList } from '/@/api/erp/purchaseOrder'
import { mul } from '/@/utils/math'
import { getRmbquot } from '/@/api/erp/sales'
import Decimal from 'decimal.js'
// import { getRmbquot } from '/@/api/erp/sales'

// import { getRmbquot } from '/@/api/erp/sales'

// const typeOptions: Recordable[] = [
//   { label: '正常订单', value: 1 },
//   { label: '补货订单', value: 2 },
//   { label: '佣金', value: 20 },
//   { label: '共摊', value: 21 },
//   { label: '手续费', value: 22 }
// ]
// const statusOptions = [
//   { label: '待审核', value: 0 },
//   { label: '已审核', value: 1 },
//   { label: '待执行', value: 3 },
//   { label: '执行中', value: 4 }
// ]
export const statusMap = {
  0: { color: 'pink', text: '待审核' },
  1: { color: 'green', text: '已审核' },
  3: { color: 'cyan', text: '待执行' },
  4: { color: 'blue', text: '执行中' },
  5: { color: 'purple', text: '备货中' },
  6: { color: 'orange', text: '已在库' }
}
export const corresType = [
  { label: '员工', value: 1 },
  { label: '部门', value: 2 },
  { label: '客户', value: 3 },
  { label: '供应商', value: 4 }
]
export const canceltypeOptions = {
  0: { label: '未取消', color: '' },
  1: { label: '取消', color: 'red' }
}
function getWorkListFn(type) {
  if (!type) {
    return getCreatorList
  }
  if (type == 1) {
    return getCreatorList
  }
  if (type == 2) {
    return getDept
  }
  if (type == 3) {
    return getcustomerList
  }
  if (type == 4) {
    return getErpSupplier
  }
}

export const urgent_level_options = [
  { label: '一般', value: 1 },
  { label: '紧急', value: 2 }
]

//改下面的话,要连src\views\erp\saleOrder\datas\OtherIncomeDrawer.ts里的baseInfoSchema也一起改
export const is_retax_type = ref()
export async function schemasFn(): Promise<FormSchema[]> {
  const schemas: FormSchema[] = [
    {
      field: 'urgent_level',
      label: '紧急状态',
      required: true,
      component: 'Select',
      componentProps: {
        options: urgent_level_options
      }
    },
    {
      field: 'is_retax',
      label: '是否退税',
      required: true,
      component: 'Select',
      componentProps: {
        options: [
          { label: '是', value: 1 },
          { label: '否', value: 0 }
        ],
        onChange(val) {
          is_retax_type.value = val
        }
      }
    },
    {
      field: 'dept_id',
      label: '部门',
      component: 'PagingApiSelect',
      componentProps: {
        api: getDept,
        params: { status: 1, is_audit: 1 },
        resultField: 'items',
        labelField: 'name',
        valueField: 'id',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'id',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      },
      required: true
    },
    {
      field: 'inCharge',
      label: '负责人',
      required: true,
      component: 'ApiSelect',
      componentProps: () => {
        return {
          api: getInchargeList,

          resultField: 'items',
          selectProps: {
            fieldNames: {
              value: 'id',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'applicant',
      label: '申请人',
      required: true,
      component: 'ApiSelect',
      componentProps: () => {
        return {
          api: getApplicantList,
          resultField: 'items',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'id',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            disabled: true
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'foreign_currency_amount',
      label: '外汇金额总额',
      component: 'InputNumber',
      defaultValue: 0,
      componentProps: ({}) => ({
        disabled: true,
        precision: 4,
        min: 0
      })
    },
    {
      field: 'amount',
      label: '总金额',
      required: true,
      component: 'InputNumber',
      defaultValue: 0,
      componentProps: {
        min: 0.01,
        precision: 4,
        disabled: true
      }
    },
    {
      field: 'remark',
      label: '单据备注',
      component: 'InputTextArea'
    },
    {
      field: 'desc',
      label: '单据描述',
      component: 'InputTextArea'
    },
    {
      field: 'rate',
      label: '汇率',
      component: 'ApiSelect',
      defaultValue: '1.000000',
      required: true,
      componentProps: ({ formModel }) => {
        return {
          api: getRmbquot,
          resultField: 'items',
          selectProps: {
            fieldNames: { value: 'fBuyPri', label: 'name' },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            onChange(val, shall) {
              if (val == '1.000000') {
                formModel.fg_amount = undefined
                formModel.total_price = undefined
              }
              formModel.exchange_rate = val
              formModel.total_price = mul(val, formModel.fg_amount || 0, 2)
              formModel.currency = shall.name.split('-')[0]
            },
            disabled: formModel.is_retax == 1 ? true : false
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'exchange_rate',
      label: '汇率比例',
      component: 'InputNumber',
      componentProps: ({ formModel }) => {
        return {
          precision: 4,
          min: 0,
          max: 100,
          onChange(val) {
            formModel.total_price = mul(val, formModel.fg_amount || 0, 2)
          }
        }
      },
      defaultValue: '1.000000',
      dynamicDisabled({ model }) {
        return model.is_retax == 1 ? true : false
      }
    },
    {
      field: 'currency',
      label: '应付金额',
      component: 'Input',
      defaultValue: '人民币',
      show: false
    },
    {
      field: 'files',
      label: '附件',
      component: 'Upload',
      slot: 'Files',
      helpMessage: '票据附件与明细附件至少上传一个'
    }
  ]
  return schemas
}
const hasBeenSet = ref(false)
const num = ref()
// function RouleFn(text) {
//   if (!text) {
//     return Promise.resolve('不能为空')
//   } else {
//     return Promise.resolve('')
//   }
// }
export function updataexpenseDetails(type: string, salesList?: any[], hand?: Function, order?: any): any {
  const columns: BasicColumn[] = [
    {
      title: '关联销售单号',
      dataIndex: 'source_uniqid',
      editComponent: 'PagingApiSelect',
      editComponentProps: ({ record }) => {
        return {
          api: getWorkList,
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          params: {
            types: [3, 27],
            status: [1, 2, 3, 4, 5, 15]
          },
          // pagingSize: 20,
          searchParamField: 'source_uniqid',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'source_uniqid',
              label: 'source_uniqid'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'source_uniqid',
            allowClear: true,
            onChange: (_, shall) => {
              if (!shall) {
                record.source_uniqid = undefined
              }
              const idx = salesList?.value?.findIndex((item) => {
                return item.id === shall.id
              })
              // salesList缺少选项导致提交缺少work_id
              if (idx === -1) salesList.value?.push(shall)
              hand && hand(shall)
              hasBeenSet.value = false
              record.basic_work_id = shall.id
              record.sale_work_id = shall.id
              record.department = shall.department_name
              record.dept_id = shall.dept_id
              record.clear_department = shall.clear_department
              record.clear_dept_id = shall.clear_dept_id
            },
            style: {
              width: '100%'
            }
          }
        }
      },
      // ifShow: order == 1 || type == 'add',
      width: 250,
      resizable: true,
      editRow: true
    },
    {
      title: '关联采购单号',
      dataIndex: 'parent_strid',
      editComponent: 'PagingApiSelect',
      // editComponentProps: ({ record }) => {
      //   return {
      //     api: getWorkList,
      //     resultField: 'items',
      //     searchMode: true,
      //     pagingMode: true,
      //     pagingSize: 20,
      //     params: {
      //       types: [4],
      //       status: [1, 2, 3, 4, 5, 15]
      //     },
      //     searchParamField: 'strid',
      //     selectProps: {
      //       fieldNames: {
      //         key: 'key',
      //         value: 'purchase_strid',
      //         label: 'purchase_strid'
      //       },
      //       showSearch: true,
      //       placeholder: '请选择',
      //       optionFilterProp: 'purchase_strid',
      //       allowClear: true,
      //       style: {
      //         width: '100%'
      //       }
      //     }
      //   }
      // },
      defaultHidden: true,
      editDynamicDisabled(record) {
        return record.record.order_no ? true : false
      },
      width: 250,
      ifShow: ![3, 4].includes(order),
      editRow: true,
      resizable: true
    },
    {
      title: '供应商',
      width: 250,
      dataIndex: 'supplier_name',
      ifShow: ![3, 4].includes(order),
      resizable: true
    },
    {
      title: '明细单号',
      dataIndex: 'strid',
      ifShow: ['detail', 'edit'].includes(type),
      resizable: true
    },
    {
      title: '驳回备注',
      dataIndex: 'reject_remark2',
      ifShow: order,
      resizable: true
    },
    {
      title: '收入科目名称',
      dataIndex: 'account_name',
      editComponent: 'ApiSelect',
      width: 250,
      editRow: true,
      resizable: true,
      // editRule: RouleFn,
      editComponentProps: ({ record }) => {
        return {
          api: getCategory,
          resultField: 'items',
          labelField: 'account_name',
          valueField: 'account_name',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'account_name',
              label: 'account_name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'account_name',
            allowClear: true,
            onChange: async (_, shall) => {
              if (!shall) {
                record.account_name = undefined
                record.account_code = undefined
              }
              hand && hand(shall)
            },
            style: {
              width: '100%'
            },
            disabled: is_retax_type.value === 1 ? true : false
          },
          params: {
            status: 1
          }
        }
      }
    },
    {
      title: '汇率',
      dataIndex: 'rate',
      width: 250,
      editRow: true,
      resizable: true,
      editComponent: 'Input',
      editDynamicDisabled: true
    },
    {
      title: '描述',
      dataIndex: 'desc',
      editComponent: 'Textarea',
      width: 200,
      editRow: true,
      resizable: true,
      editComponentProps: {
        autosize: { minRows: 3, maxRows: 6 },
        style: {
          width: '100%'
        }
      }
    },
    {
      title: '备注',
      dataIndex: 'remark',
      editComponent: 'Textarea',
      width: 200,
      editRow: true,
      resizable: true,
      editComponentProps: {
        autosize: { minRows: 3, maxRows: 6 },
        style: {
          width: '100%'
        }
      }
    },
    {
      title: '外币金额',
      dataIndex: 'foreign_currency_amount',
      editComponent: 'InputNumber',
      editComponentProps: ({ record }) => {
        return {
          // max: num.value,
          precision: 2,
          step: 0.01,
          style: {
            width: '100%'
          },
          onChange: (val) => {
            record.amount = new Decimal(val).times(record.rate).toDecimalPlaces(2).toNumber()
          }
        }
      },
      width: 150,
      editRow: true,
      resizable: true,
      editDynamicDisabled({ record }) {
        return ['人民币', 'CNY'].includes(record.currency) ? true : false
      }
    },
    {
      title: '收入金额',
      dataIndex: 'amount',
      editComponent: 'InputNumber',
      // editRule: RouleFn,
      editComponentProps: ({ record }) => {
        if (hasBeenSet.value == false && record.amount) {
          hasBeenSet.value = true
          num.value = record.amount
        }
        return {
          // min: 0,
          // max: num.value,
          precision: 2,
          step: 0.01,
          style: {
            width: '100%'
          }
        }
      },
      width: 150,
      editRow: true,
      resizable: true,
      editDynamicDisabled({ record }) {
        return ['人民币', 'CNY'].includes(record.currency) ? false : true
      }
    },
    {
      title: '收入部门',
      dataIndex: 'department',
      editComponent: 'ApiSelect',
      // editRule: RouleFn,
      editComponentProps: ({ record }) => {
        return {
          api: getDept,
          params: { status: 1, is_audit: 1 },
          resultField: 'items',
          // searchMode: true,
          // pagingMode: true,
          // labelField: 'name',
          // valueField: 'id',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'name',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            disabled: record.source_uniqid,
            onChange(_, shall) {
              record.dept_id = shall.id
            }
          }
        }
      },
      editDynamicDisabled(record) {
        return record.record.source_uniqid ? true : false
      },
      width: 200,
      editRow: true,
      resizable: true
    },
    {
      title: '结算部门',
      dataIndex: 'clear_department',
      editComponent: 'PagingApiSelect',
      // editRule: RouleFn,
      editComponentProps: ({ record }) => {
        return {
          api: getDept,
          params: { status: 1, is_audit: 1 },
          resultField: 'items',
          labelField: 'name',
          valueField: 'id',
          searchMode: true,
          pagingMode: true,
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'name',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            style: {
              width: '100%'
            },
            onChange(_, shall) {
              record.clear_dept_id = shall.id
            }
          }
        }
      },
      editDynamicDisabled: true,
      width: 200,
      editRow: true,
      resizable: true
    },
    {
      title: '往来单位类型',
      dataIndex: 'corres_type',
      editComponent: 'Select',
      editComponentProps: ({ record }) => {
        return {
          allowClear: true,
          options: [
            { label: '员工', value: 1 },
            { label: '部门', value: 2 },
            { label: '客户', value: 3 },
            { label: '供应商', value: 4 },
            { label: '其他', value: 5 }
          ],
          style: {
            width: '100%'
          },
          onChange: (_, shall) => {
            record.corres_pondent = null
            hand && hand(shall)
          }
        }
      },
      width: 200,
      editRow: true,
      resizable: true
    },
    {
      title: '往来单位',
      dataIndex: 'corres_pondent',
      editComponent: 'PagingApiSelect',
      editComponentProps: (fromat) => {
        return {
          api: getWorkListFn(fromat.record.corres_type),
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          params: {
            type: 3,
            status: [1, 3, 4, 5, 15]
          },
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'name',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            style: {
              width: '100%'
            }
          }
        }
      },
      width: 200,
      resizable: true,
      editRow: true
    },
    {
      title: '收入科目代码',
      dataIndex: 'account_code',
      width: 200,
      editRow: true,
      // editRule: RouleFn,
      editComponent: 'Input',
      editDynamicDisabled: true,
      resizable: true
    },
    {
      title: '货币',
      dataIndex: 'currency',
      width: 200,
      // editRule: RouleFn,
      editRow: true,
      editComponent: 'Input',
      editDynamicDisabled: true,
      resizable: true
    },
    {
      title: '财务审核',
      dataIndex: 'is_check2',
      width: 100,
      ifShow: ['detail', 'edit'].includes(type),
      resizable: true,
      customRender: ({ text }) => {
        return h(Tag, { color: ischecks[text]?.color }, () => ischecks[text]?.text)
      }
    },
    {
      title: '是否取消',
      dataIndex: 'is_cancel',
      width: 100,
      customRender: ({ value }) => {
        return value ? h(Tag, { color: canceltypeOptions[value].color }, canceltypeOptions[value].label) : '-'
      },
      resizable: true
    },
    {
      title: '取消时间',
      dataIndex: 'cancel_at',
      width: 150,
      customRender: ({ value }) => {
        // return value ? value.split(' ')[0].slice(0, 10) : '-'
        return value ? value : '-'
      },
      resizable: true
    },

    {
      title: 'id',
      dataIndex: 'id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'work_id',
      dataIndex: 'work_id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'sale_work_id',
      dataIndex: 'sale_work_id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'basic_work_id',
      dataIndex: 'basic_work_id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'par_work_id',
      dataIndex: 'par_work_id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'dept_id',
      dataIndex: 'dept_id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'clear_dept_id',
      dataIndex: 'clear_dept_id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'order',
      dataIndex: 'order',
      width: 100,
      defaultHidden: true
    }
  ]
  return columns
}
export const UploadSchemas: FormSchema[] = [
  {
    field: 'files',
    label: '附件上传',
    component: 'Upload',
    slot: 'Files',
    required: true,
    rules: [
      {
        required: true,
        validator: async (_rule: Rule, value: string) => {
          if (!value || value.length === 0) return Promise.reject('请上传附件')
          return Promise.resolve()
        }
        // trigger: 'change'
      }
    ],
    colProps: {
      span: 24
    }
  }
]
export const excelHeader = ['收入摘要', '收入金额']

export const schemas: FormSchema[] = [
  {
    field: 'collection_at',
    label: '收款日期',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      disabledDate: (current) => {
        if (!current) {
          return false
        }
        const today = dayjs()
        const tomorrow = today.add(1, 'day')
        return current && current.diff(tomorrow, 'day') >= 0
      }
    },
    colProps: {
      span: 6
    },
    required: true
  },
  {
    field: 'notes',
    label: '付款人',
    component: 'Input',
    colProps: {
      span: 6
    }
  },
  {
    field: 'payment_type',
    label: '款项类型',
    component: 'RadioGroup',
    defaultValue: 3,
    componentProps: {
      options: [
        {
          label: '定金',
          value: 1
        },
        {
          label: '最后一笔款',
          value: 2
        },
        {
          label: '全款',
          value: 3
        }
      ],
      disabled: true
    },
    colProps: {
      span: 6
    },
    required: true
  },
  {
    field: 'g_remark',
    label: '携带备注',
    component: 'InputTextArea',
    componentProps: {
      autosize: { minRows: 3, maxRows: 6 }
    },
    colProps: {
      span: 6
    }
  }
]

export const childrenColumns = (type: string, salesList?: any[], hand?: Function, order?: any): BasicColumn[] => {
  const newDetailsColumns: Array<any> = []
  const datas = cloneDeep(updataexpenseDetails(type, salesList, hand, order))
  for (const item of datas) {
    if (item.dataIndex == 'corres_pondent') {
      item.editComponent = 'Input'
      item.editComponentProps = {}
    }
    if (item.dataIndex == 'parent_strid') {
      item.defaultHidden = is_retax_type.value == 1 ? false : true
    }
    newDetailsColumns.push(item)
  }
  return newDetailsColumns
}

export const purchasedata = ref()
export const supplierName = ref()
export const purchasePagingSelectConfig = {
  resultField: 'items',
  api: (params) => getPurchaseOrderList({ ...params, is_ticket: 1 }),
  searchMode: true,
  pagingMode: true,
  pagingSize: 20,
  searchParamField: 'strid',
  selectProps: {
    fieldNames: {
      key: 'key',
      value: 'strid',
      label: 'strid'
    },
    showSearch: true,
    placeholder: '请选择',
    optionFilterProp: 'strid',
    allowClear: true,
    onChange: async (_, shall) => {
      console.log(shall)
      purchasedata.value = shall
      supplierName.value = shall?.supplier_name
    },
    style: {
      width: '100%'
    }
  }
}
