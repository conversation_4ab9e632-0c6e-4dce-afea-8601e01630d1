import type { BasicColumn } from '/@/components/Table'
import { cloneDeep } from 'lodash-es'
import type { FormProps } from '/@/components/Form'
import { formatter } from '/@/utils/erp/formatterPrice'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { h } from 'vue'
import { Tag } from 'ant-design-vue'
import { GET_STATUS_SCHEMA, NEW_STATUS_FORMCONFIG } from '/@/const/status'

const statusOptions = [
  { label: '未审核', value: 0 },
  { label: '已审核', value: 1 },
  { label: '已结束', value: 15 },
  { label: '已作废', value: 16 }
]
export const statusMaps = {
  0: { text: '未审核', color: 'red' },
  1: { text: '已审核', color: 'green' },
  15: { text: '已结束', color: 'grey' },
  16: { text: '已作废', color: 'grey' }
}
const urgentlevel = {
  1: { color: '', text: '一般' },
  2: { color: 'red', text: '紧急' }
}
const urgentleveloption = [
  { label: '一般', value: 1 },
  { label: '紧急', value: 2 }
]

//status
export const statusMap = {
  0: { color: '', text: '待执行' },
  1: { color: 'green', text: '生效' },
  3: { color: 'cyan', text: '待执行' },
  4: { color: 'blue', text: '执行中' },
  5: { color: 'purple', text: '备货中' },
  6: { color: 'orange', text: '已在库' }
}
export const ischecks = {
  0: { color: '', text: '未审核' },
  1: { color: 'green', text: '已审核' },
  2: { color: 'red', text: '驳回' }
}
export const columns: BasicColumn[] = [
  {
    title: '其他收入单号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '紧急状态',
    dataIndex: 'urgent_level',
    width: 150,
    resizable: true,
    customRender: ({ record }) => {
      return h(Tag, { color: urgentlevel[record.urgent_level]?.color }, () => urgentlevel[record.urgent_level]?.text)
    }
  },
  {
    title: '摘要',
    dataIndex: 'desc',
    width: 150,
    resizable: true
  },
  {
    title: '结算货币',
    dataIndex: 'currency',
    width: 100,
    resizable: true
  },
  {
    title: '结算货币汇率',
    dataIndex: 'exchange_rate',
    width: 100,
    resizable: true
  },
  {
    title: '外汇金额',
    dataIndex: 'foreign_currency_amount',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return formatter.format(value)
    }
  },
  {
    title: '收入金额(rmb)',
    dataIndex: 'amount',
    width: 150,
    resizable: true,
    customRender: ({ record }) => {
      return ['CNY', '人民币'].includes(record.currency)
        ? formatter.format(record.amount)
        : formatter.format(record.amount * record.exchange_rate)
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    resizable: true
    // customRender: ({ record }) => {
    //   return statusOptions[record.status].label
    // }
  },
  {
    title: '财务审核',
    dataIndex: 'is_check2',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return h(Tag, { color: ischecks[text]?.color }, () => ischecks[text]?.text)
    }
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 100,
    resizable: true
  },
  {
    title: '创建日期',
    dataIndex: 'created_at',
    width: 150,
    resizable: true
  },
  {
    title: '更新日期',
    dataIndex: 'updated_at',
    width: 150,
    resizable: true
  },
  {
    title: '审核日期',
    dataIndex: 'status_at',
    width: 150,
    resizable: true
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 100,
    resizable: true
  },
  {
    title: '申请人',
    dataIndex: 'applicant_name',
    width: 100,
    resizable: true
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name',
    width: 100,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    resizable: true
  },
  {
    title: '附件',
    dataIndex: 'files',
    width: 200,
    resizable: true
  }
]

// 收入明细
export const revenueDetailsColumns: BasicColumn[] = [
  {
    title: '收入销售单号',
    dataIndex: 'strid',
    width: 100,
    resizable: true
  },
  {
    title: '收入关联金额',
    dataIndex: 'amount',
    width: 100,
    resizable: true
  },
  {
    title: '收入关联时间',
    dataIndex: 'created_at',
    width: 100,
    resizable: true
  }
]

// 编辑收入明细
export const editrevenueDetailsColumns: any[] = cloneDeep(revenueDetailsColumns).map((item) => {
  item['edit'] = true
  item['editable'] = true
  return item
})

//搜索菜单数据
// const statusOptions1 = [
//   { label: '待审核', value: 0 },
//   { label: '生效', value: 1 },
//   { label: '待执行', value: 3 },
//   { label: '执行中', value: 4 },
//   { label: '备货中', value: 5 },
//   { label: '已在库', value: 6 }
// ]

const status_schema = GET_STATUS_SCHEMA(statusOptions)

export const formConfig: Partial<FormProps> = {
  ...NEW_STATUS_FORMCONFIG,
  //自动展开行
  fieldMapToTime: [['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]],
  schemas: [
    status_schema,
    {
      field: 'strid',
      label: '其他收入单号',
      component: 'Input'
    },
    {
      field: 'item_strid',
      label: '明细单号',
      component: 'Input'
    },
    {
      field: 'source_uniqid',
      label: '销售单号',
      component: 'Input'
    },
    // {
    //   field: 'status',
    //   label: '状态',
    //   component: 'Select',
    //   componentProps: {
    //     options: statusOptions
    //   },
    //
    // },
    {
      field: 'urgent_level',
      label: '紧急状态',
      component: 'Select',
      componentProps: {
        options: urgentleveloption
      }
    },
    {
      field: 'desc',
      label: '摘要',
      component: 'Input'
    },
    {
      field: 'item_desc',
      label: '明细摘要',
      component: 'Input'
    },
    {
      field: 'remark',
      label: '备注',
      component: 'Input'
    },
    {
      field: 'creator',
      label: '创建人',

      component: 'PagingApiSelect',
      componentProps: {
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        api: getCreatorList,
        resultField: 'items',
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'applicant',
      label: '申请人',
      component: 'PagingApiSelect',
      componentProps: {
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        api: getCreatorList,
        resultField: 'items',
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'inCharge',
      label: '负责人',
      component: 'PagingApiSelect',

      componentProps: {
        api: getCreatorList,
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        resultField: 'items',
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    // //字段待定
    {
      field: 'created_at',
      label: '创建日期',
      component: 'SingleRangeDate',
      componentProps: {
        showTime: false,
        style: {
          width: '100%'
        },
        allowEmpty: [true, true]
      }
    }
  ]
}
