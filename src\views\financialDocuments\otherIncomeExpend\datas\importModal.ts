export const IMP_EXCEL_END = 20000
export function transformData2Import(data: Recordable[]): any[] {
  const fieldMap = {
    amount: '收入金额',
    desc: '收入摘要'
  }

  return data.map((obj) => {
    const cookedData: any = {
      amount: '',
      desc: ''
    }
    for (const key in cookedData) {
      if (fieldMap[key]) {
        cookedData[key] = obj[fieldMap[key]]
      }
    }
    // 去除空格
    cookedData.amount = Number(String(cookedData.amount ? cookedData.amount : '').replace(/\s/g, ''))
    cookedData.desc = String(cookedData.desc ? cookedData.desc : '').replace(/\s/g, '')

    // console.log(cookedData, 'cookedData')
    return cookedData
  })
}
