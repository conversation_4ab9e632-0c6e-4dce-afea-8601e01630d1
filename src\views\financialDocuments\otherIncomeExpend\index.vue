<template>
  <div>
    <BasicTable :data-cachekey="routePath" @register="registerTable">
      <template #toolbar>
        <!-- <Tooltip>
          <template #title>选中销售订单生成收款单</template>
          <Button v-if="hasPermission([110])" @click="debouncedGenerate" :disabled="generateBtnStatus">生成收款单</Button>
        </Tooltip> -->
        <Button v-if="hasPermission([270])" type="primary" @click="handleCreate">
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </Button>
      </template>
      <template #bodyCell="{ text, column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
        <template v-if="column.key === 'amount'">
          {{ record.receivable }} {{ ['CNY', '人民币'].includes(record.currency) ? '￥' : '＄' }}
        </template>
        <template v-if="column.key === 'files'">
          <TableImg :imgList="text" :simpleShow="true" />
        </template>
        <template v-if="column.key === 'status'">
          <Tag :color="statusMaps[record.status]?.color"> {{ statusMaps[record.status]?.text }}</Tag>
        </template>
      </template>
    </BasicTable>

    <OtherIncomeDrawer @register="registerDrawer" @success="handleSuccess" />
    <DetailsDrawer @register="registerDetailsDrawer" />
    <GenerateModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script setup lang="ts">
import { usePermission } from '/@/hooks/web/usePermission'
// import { ref, unref } from 'vue'
import { Button, message, Tag } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { getOthergetList, deleteOtherremove } from '/@/api/financialDocuments/otherIncome'
import { BasicTable, useTable, TableAction, TableImg } from '/@/components/Table'
import type { ActionItem, EditRecordRow } from '/@/components/Table'
import { useDrawer } from '/@/components/Drawer'
import { columns, formConfig, statusMaps } from './datas/datas'
import OtherIncomeDrawer from './components/OtherIncomeDrawer.vue'
import DetailsDrawer from './components/DetailsDrawer.vue'
import GenerateModal from './components/GenerateModal.vue'
import { useModal } from '/@/components/Modal'
import { useRoute } from 'vue-router'

const route = useRoute()
const { path: routePath } = route
const { hasPermission } = usePermission()
//初始化
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerDetailsDrawer, { openDrawer: openDetailsDrawer, setDrawerProps: setDetailsDrawerProps }] = useDrawer()
const [registerTable, { reload, clearSelectedRowKeys }] = useTable({
  title: '其他收入单',
  showIndexColumn: true,
  api: getOthergetList,
  afterFetch: IncomeListafter,
  useSearchForm: true,
  formConfig: formConfig,
  columns,
  rowKey: 'id',
  showTableSetting: true,
  actionColumn: hasPermission([271, 272, 273, 274])
    ? {
        width: 190,
        title: '操作',
        dataIndex: 'action',
        fixed: 'right'
      }
    : void 0
  // rowSelection: {
  //   type: 'checkbox',
  //   onChange: handleChange,
  //   getCheckboxProps: (record) => {
  //     if (record.status === 0 || record.status === 15 || record.is_audit == 1) {
  //       return { disabled: true }
  //     } else {
  //       return { disabled: false }
  //     }
  //   }
  // }
})

/** 选中 */
// const generateBtnStatus = ref(true)
// function handleChange() {
//   if (getSelectRows().length == 0) {
//     generateBtnStatus.value = true
//   } else {
//     generateBtnStatus.value = false
//   }
// }
/** 注册modal */

const [registerModal, { openModal }] = useModal()

/** 生成收款单 */
// const debouncedGenerate = debounce(handleGenerate, 150)
// async function handleGenerate() {
//   const selectRowsattr: Array<number> = []
//   const show = ref(false)
//   try {
//     getSelectRows().forEach((item) => {
//       selectRowsattr.push(item.client_id)
//       return item.work_id
//     })
//     for (let item of getSelectRows()) {
//       if (item.payment_type && item.payment_type !== 1) {
//         message.warning('该采购订单已完成付款单尾款或全款生成')
//         show.value = true
//         break
//       }
//     }
//     if (!show.value) {
//       if (new Set(selectRowsattr).size !== 1) {
//         message.error('请选择同一客户的订单!')
//       } else {
//         openModal(true, { selectRowsData: getSelectRows() })
//       }
//     }
//   } catch (error) {
//     message.success('生成收款单失败!')
//     throw new Error(`${error}`)
//   }
// }
//action
function createActions(record: EditRecordRow): ActionItem[] {
  console.log(record.status !== 2 && record.is_check2 !== 2)

  return [
    {
      icon: 'ant-design:file-search-outlined',
      label: '审核',
      disabled: record.status !== 2 && record.is_check2 !== 0,
      onClick: handleexamine.bind(null, record),
      ifShow: hasPermission([271])
    },
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleUpdate.bind(null, record),
      disabled: (record.status !== 0 && record.is_check2 !== 2) || (record.status == 16 && record.is_check2 == 2),
      ifShow: hasPermission([272])
    }
  ]
}

function createDropDownActions(record: EditRecordRow): ActionItem[] {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission([273])
    },
    {
      icon: 'ant-design:delete-outlined',
      color: 'error',
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        placement: 'left',
        confirm: handleDelete.bind(null, record),
        disabled: record.status !== 0
      },
      // disabled: record.status !== 0,
      ifShow: hasPermission([274])
    }
  ]
}

//创建
function handleCreate() {
  openDrawer(true, {
    isUpdate: false
  })
  setDrawerProps({ title: '新增收入单' })
}
//详情
function handleDetail(record) {
  setDetailsDrawerProps({ title: '其他收入单详情' })
  openDetailsDrawer(true, {
    record,
    type: 'detail'
  })
}
//更新
function handleUpdate(record) {
  openDrawer(true, {
    isUpdate: true,
    record,
    type: 'edit'
  })
  setDrawerProps({ title: '更新收入单信息' })
}
//删除
async function handleDelete(record) {
  await deleteOtherremove({ id: record.id })
  message.success('删除成功')
  reload()
  // deleteTableDataRecord(record.id)
}

function handleSuccess() {
  reload()
  clearSelectedRowKeys()
}
//数据处理
function IncomeListafter(data) {
  clearSelectedRowKeys()
  return data.client_name
}
//单据审核
async function handleexamine(record: any) {
  openModal(true, record)
}
</script>
