<template>
  <BasicModal @register="registerModal" width="50%" title="请填写以下信息" :bodyStyle="{ height: '500px' }" @ok="handleOk">
    <BasicForm @register="registerForm" />
    <Card title="本次应收金额">
      <template v-for="(item, index) in cloneDeepSelectRowsData" :key="item.strid">
        <Descriptions>
          <DescriptionsItem :label="`销售单号 : ${item.strid}`" :labelStyle="{ color: '#909399', width: '50%' }">
            <Form :model="item" ref="formRef">
              <FormItem name="receivable" label="金额" :rules="formRulesFn(index)">
                <InputNumber :formatter="(value) => `¥${value}`" v-model:value="item.receivable" :min="0.0001" :precision="4" />
              </FormItem>
            </Form>
          </DescriptionsItem>
        </Descriptions>
      </template>
    </Card>
  </BasicModal>
</template>

<script setup lang="ts">
import { addBatch } from '/@/api/financialDocuments/receiptOrder'

import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, Rule, useForm } from '/@/components/Form'
import { Card, Descriptions, DescriptionsItem, InputNumber, message, Form, FormItem } from 'ant-design-vue'
import { ref } from 'vue'
import { cloneDeep } from 'lodash-es'
const emit = defineEmits(['register', 'handleSubmit', 'success'])
console.log(emit)

const cloneDeepSelectRowsData = ref<Array<any>>([])
const selectRowsData = ref<Array<any>>([])
const formRef = ref()

/** 校验 */
const formRulesFn = (index): any => {
  return [
    {
      required: true,
      // trigger: 'change',
      validator: async (_rule: Rule, value: string) => {
        if (Number(value) <= 0) {
          return Promise.reject('输入的收款金额必须大于0！')
        }
        if (Number(value) > Number(selectRowsData.value[index].receivable)) {
          return Promise.reject('输入的收款金额不能大于当前销售订单的未收金额！')
        }
        return Promise.resolve()
      }
    }
  ]
}

/** 注册from */
const [registerForm, { validate, resetFields }] = useForm({
  labelWidth: 120,
  schemas: [
    {
      field: 'collection_at',
      label: '收款日期',
      component: 'DatePicker',
      componentProps: {
        valueFormat: 'YYYY-MM-DD'
      },
      colProps: {
        span: 11
      },
      required: true
    },
    {
      field: 'notes',
      label: '付款人',
      component: 'Input',
      colProps: {
        span: 11
      },
      required: true
    },
    {
      field: 'payment_type',
      label: '款项类型',
      component: 'RadioGroup',
      defaultValue: 3,
      componentProps: {
        options: [
          {
            label: '定金',
            value: 1
          },
          {
            label: '最后一笔款',
            value: 2
          },
          {
            label: '全款',
            value: 3
          }
        ],
        disabled: true
      },
      colProps: {
        span: 11
      },
      required: true
    },
    {
      field: 'g_remark',
      label: '携带备注',
      component: 'InputTextArea',
      componentProps: {
        autosize: { minRows: 3, maxRows: 6 }
      },
      colProps: {
        span: 11
      }
    }
  ],
  showSubmitButton: false,
  showResetButton: false,
  actionColOptions: {
    span: 24
  }
})

/** 注册Modal */
const [registerModal, { closeModal, changeOkLoading }] = useModalInner((data) => {
  console.log(data)

  resetFields()
  selectRowsData.value = data.selectRowsData
  // 将receivable的默认值去除，使用另外的字段记录之前的receivable
  cloneDeepSelectRowsData.value = cloneDeep(data.selectRowsData).map((item) => ({
    ...item,
    receivable: null,
    log_receivable: item.receivable
  }))
  console.log(cloneDeepSelectRowsData.value)
})

/** 点击确认 */
const handleOk = async () => {
  try {
    await changeOkLoading(true)

    let data = await validate()
    let works: Array<{ work_id: number; amount: number }> = []

    // 校验
    for (let item of formRef.value) {
      await item.validate()
    }

    cloneDeepSelectRowsData.value.forEach((item) => {
      works.push({ work_id: item.work_id, amount: item.receivable })
      data.client_id = item.client_id
    })

    const mes = ref()
    try {
      mes.value = await addBatch({ ...data, works, clause: 3 })
      console.log({ ...data, works })
      emit('success')
      await closeModal()
    } finally {
      if (mes.value.type == 'error') {
        message.error('生成收款单失败！')
      } else {
        message.success('成功生成收款单！')
      }
    }
    changeOkLoading(false)
  } catch (error) {
    changeOkLoading(false)
    console.error(error)
  }
}
</script>
<style lang="less" scoped>
:deep(.ant-picker) {
  width: 100%;
}

:deep(.ant-descriptions-item-content) {
  display: inline-block;
}

:deep(.ant-descriptions-item) {
  padding: 0;
}
</style>
