<template>
  <BasicModal @register="registerAddModal" title="付款单添加流水" width="70%" :bodyStyle="{ height: '700px' }" @ok="handleOk">
    <BasicTable @register="registerFilterTable" />
  </BasicModal>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import { getFundList } from '/@/api/financialDocuments/public'
import { getClientList } from '/@/api/financialDocuments/public'

import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicTable, useTable } from '/@/components/Table'
import { ref } from 'vue'
import { getErpSupplier } from '/@/api/commonUtils'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import dayjs from 'dayjs'
import { message, Modal } from 'ant-design-vue'
const emit = defineEmits(['register', 'handleAddOk'])
console.log(emit)
const pathname = window.location.pathname
/** 存储上层传过来的流水表格数据 */
const fundTableData = ref<Array<any>>([])

/** 付款日期 */
const paymentAt = ref<any>()
const recordData = ref()

/** 注册添加流水Modal，注册添加流水Modal，刚进来会触发*/
const [registerAddModal, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  await getForm().resetFields()
  fundTableData.value = cloneDeep(data.fundTableData)
  paymentAt.value = cloneDeep(data.paymentAt)
  recordData.value = cloneDeep(data.recordData)

  // 默认筛选
  await getForm().setFieldsValue({ occurrence_at: paymentAt.value })

  // reload()
  clearSelectedRowKeys()
})

/** 注册筛选流水表格 */
const [registerFilterTable, { getSelectRows, clearSelectedRowKeys, getForm }] = useTable({
  title: '',
  api: getFundList,
  searchInfo: { type: 2 },
  afterFetch: (data) => {
    clearSelectedRowKeys()
    const newData = data.map((item) => {
      item.cloneAmountLeft = cloneDeep(item.amount_left)
      item.amount_allot = 0
      return item
    })
    return newData
  },
  showIndexColumn: false,
  useSearchForm: true,
  canResize: false,
  formConfig: {
    showAdvancedButton: false,
    schemas: [
      {
        field: 'occurrence_at',
        label: '付款日期',
        component: 'DatePicker',
        helpMessage: '付款单付款日期必须和流水单付款日期一致才能关联流水!',
        componentProps: {
          valueFormat: 'YYYY-MM-DD'
        }
      },
      {
        field: 'client_id',
        label: '客户',
        component: 'ApiSelect',
        componentProps: {
          api: getClientList,
          selectProps: {
            fieldNames: { value: 'id', label: 'name' },
            optionFilterProp: 'name',
            showSearch: true,
            placeholder: '请选择',
            allowClear: true
          },
          resultField: 'items'
        }
      },
      {
        field: 'supplier_id',
        label: '供应商',
        component: 'PagingApiSelect',
        componentProps: () => {
          return {
            api: getErpSupplier,
            searchMode: true,
            pagingMode: true,
            returnParamsField: 'id',

            selectProps: {
              fieldNames: { key: 'key', value: 'id', label: 'name' },
              optionFilterProp: 'name',
              showSearch: true,
              placeholder: '请选择',
              allowClear: true
            },
            resultField: 'items'
          }
        }
      },
      {
        field: 'to_plaform',
        label: '收款资金资料',
        component: 'Input'
      },
      {
        field: 'insurance_strid',
        label: '信保单号',
        component: 'Input'
      },
      {
        field: 'amount',
        label: '金额',
        component: 'InputNumber',
        componentProps: {
          min: 0,
          precision: 2
        }
      },
      {
        field: 'remark',
        label: '备注',
        component: 'InputTextArea'
      }
    ],
    labelWidth: 120,
    actionColOptions: {
      span: 24
    },
    baseColProps: {
      span: 8
    }
  },
  columns: [
    {
      title: '流水单号',
      dataIndex: 'strid',
      width: 200
    },
    {
      title: '创建日期',
      dataIndex: 'created_at',
      width: 100,
      customRender: ({ text }) => {
        return text ? dayjs(text).format('YYYY-MM-DD') : '-'
      }
    },
    {
      title: '付款日期',
      dataIndex: 'occurrence_at',
      width: 100,
      customRender: ({ text }) => {
        return text ? dayjs(text).format('YYYY-MM-DD') : '-'
      }
    },
    {
      title: '客户',
      dataIndex: 'client_name',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    },
    {
      title: '供应商',
      dataIndex: 'supplier_name',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    },
    {
      title: '付款资金资料',
      dataIndex: 'from_plaform',
      width: 100
    },
    {
      title: '收款资金资料',
      dataIndex: 'to_plaform',
      width: 100
    },
    {
      title: '币种',
      dataIndex: 'from_currency',
      width: 100,
      resizable: true
    },
    {
      title: '汇率',
      dataIndex: 'rate',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(Number(text)) : '0.00'
      }
    },
    {
      title: '外汇金额',
      dataIndex: 'fg_amount',
      width: 100,
      defaultHidden: pathname == '/s/',
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(Number(text)) : '0.00'
      }
    },
    {
      title: '外汇剩余金额',
      dataIndex: 'fg_amount_left',
      width: 100,
      defaultHidden: pathname == '/s/',
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(Number(text)) : '0.00'
      }
    },
    {
      title: '金额',
      dataIndex: 'amount',
      width: 100,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(Number(text)) : '0.00'
      }
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 100,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    }
  ],
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: (record) => {
      let flag = false
      // 流水时间要一致才能关联流水
      // if (dayjs(record.occurrence_at).format('YYYY-MM-DD') !== paymentAt.value) {
      //   flag = true
      // }

      for (let item of fundTableData.value) {
        if (item.strid === record.strid) {
          flag = true
          break
        }
      }
      return {
        disabled: flag
      }
    }
  }
})

/** 点击确认 */
const handleOk = async () => {
  await changeOkLoading(true)
  let data: Array<any> = []
  let flag = false
  let currency = false
  let amountleft = false
  getSelectRows().map((item) => {
    console.log(item)
    if (dayjs(item.occurrence_at).format('YYYY-MM-DD') !== paymentAt.value) {
      flag = true
    }
    // 币种判断逻辑：
    // 1. 如果流水币种是CNY或人民币，付款币种也必须是CNY或人民币中的一种
    // 2. 其他币种（如欧元）必须与付款单币种完全匹配
    const isFlowCNY = ['CNY', '人民币'].includes(item.from_currency)
    const isPaymentCNY = ['CNY', '人民币'].includes(recordData.value.currency)
    const currencyMatches = item.from_currency === recordData.value.currency

    // 币种不匹配的情况：
    // 1. 流水是人民币但付款不是人民币
    // 2. 流水不是人民币且与付款币种不匹配
    if (pathname !== '/s/' && ((isFlowCNY && !isPaymentCNY) || (!isFlowCNY && !currencyMatches))) {
      currency = true
    }
    // 对于非人民币的外币，检查剩余外汇金额
    if (pathname !== '/s/' && item.fg_amount_left == 0 && !isFlowCNY) {
      amountleft = true
    }
    data.push(item)
  })
  if (currency) {
    changeOkLoading(false)
    return message.error('付款币种与流水币种不一致，请重新选择')
  }
  if (amountleft) {
    changeOkLoading(false)
    return message.error('流水剩余外汇金额不能为0，请重新选择')
  }
  if (flag) {
    Modal.confirm({
      title: '付款日期与流水日期不一致，确定要继续关联？',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        clearSelectedRowKeys()
        closeModal()
        emit('handleAddOk', data)
      }
    })
  } else {
    clearSelectedRowKeys()
    closeModal()
    emit('handleAddOk', data)
  }
  changeOkLoading(false)
}
</script>
<style lang="less" scoped>
:deep(.ant-picker) {
  width: 100%;
}
</style>
