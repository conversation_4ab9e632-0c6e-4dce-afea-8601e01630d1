<template>
  <BasicDrawer @register="registerExamineDrawer" v-bind="$attrs" showFooter width="90%">
    <template #footer>
      <Button @click="handleCancel">取消</Button>
      <Button type="success" @click="handlePassClick" :disabled="!generateBtnStatus" v-if="!isCashier">通过</Button>
      <Button type="danger" @click="debounceHandleReject" :disabled="generateBtnStatus">驳回</Button>
    </template>
    <ScrollContainer>
      <Descriptions title="付款单详情" :column="{ xxl: 4, xl: 2, lg: 2, md: 2, sm: 1, xs: 1 }">
        <template v-for="item in columnsFn()" :key="item.dataIndex">
          <DescriptionsItem :label="item.title">
            <!-- 财务审核 -->
            <template v-if="item.dataIndex == 'is_check'">
              <div v-check="record.is_check"> </div>
            </template>

            <!-- 款单类型 -->
            <template v-else-if="item.dataIndex == 'clause'">
              <div v-clause="record.clause"> </div>
            </template>

            <!-- 紧急状态 -->
            <template v-else-if="item.dataIndex == 'urgent_level'">
              <div v-emergency="record.urgent_level ?? 3"></div>
            </template>

            <!-- 款项类型 -->
            <template v-else-if="item.dataIndex == 'payment_type'">
              <div v-ptype="record.payment_type ?? 4"></div>
            </template>

            <!-- 状态 -->
            <template v-else-if="item.dataIndex == 'status'">
              <div v-status="record.status"></div>
            </template>

            <!-- 金额相关 -->
            <template v-else-if="keyPriceArr.includes(item.dataIndex as string)">
              {{ getDetailsPrice(record, item.dataIndex) }}
            </template>

            <!-- 开增值税票 -->
            <template v-else-if="item.dataIndex == 'is_open_tax'">
              <div v-OpenTax="record.is_open_tax"></div>
            </template>

            <!-- 开普票 -->
            <template v-else-if="item.dataIndex == 'is_open_ticket'">
              <div v-OpenTax="record.is_open_ticket"></div>
            </template>

            <!-- 附件 -->
            <template v-else-if="item.dataIndex == 'files'">
              <div v-for="(newVal, index) in record.files" :key="index">
                <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
              >
            </template>

            <!-- 我司签约主体特殊处理 -->
            <template v-else-if="item.dataIndex == 'contracting_party'">
              <component :is="contractingParty(record.id, record.contracting_party, !isCashier, () => {})" />
            </template>

            <template v-else>
              {{ record[item.dataIndex as string] ? record[item.dataIndex as string] : '-' }}
            </template>
          </DescriptionsItem>
        </template>
      </Descriptions>

      <!-- 关联任务列表 -->
      <BasicTable @register="registerWork" :canResize="false" />

      <!-- 项目总览 -->
      <Descriptions v-if="hasPermission(297)" title="项目关联下级单据" :column="1">
        <DescriptionsItem>
          <!-- 财务项目总览 -->
          <div v-loading="projectLoading" class="relative project-list w-full">
            <template v-if="projectList.length > 0">
              <RelateOrderComp :record="record" :projectList="projectList" :pagingTotal="pagingTotal" />
              <div class="p-4 text-right">
                <Pagination v-model:current="paging.page" :total="pagingTotal" v-model:pageSize="paging.pageSize" show-size-changer />
              </div>
            </template>
            <template v-else>
              <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
            </template>
          </div>
        </DescriptionsItem>
      </Descriptions>

      <!-- 采购单详情 -->
      <PurchaseDrawer @register="registerPurchaseDrawer" />

      <!-- 其他支出单详情 -->
      <OtherExpendDrawer @register="registerOtherExpendDrawer" />

      <!-- 退款单详情 -->
      <RefundDrawer @register="refundDrawer" />
    </ScrollContainer>
    <Modal
      v-model:visible="visibleCheckRemarkModal"
      title="财务审核备注"
      @ok="debounceHandlePass"
      :confirmLoading="confirmCheckRemarkModalLoading"
    >
      <BasicForm @register="registerCheckRemarkForm" />
    </Modal>
    <PreviewFile @register="registerModal" />
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { addWorkListColumnsFn, columnsFn } from '../datas/datas'
import { getPaymentOrderDetails, paymentProject } from '/@/api/financialDocuments/paymentOrder'
import { examinePaymentOrder } from '/@/api/financialDocuments/paymentOrder'
import { openDrawerFn, vCheck, vStatus, getDetailsPrice, keyPriceArr, vOpenTax } from '../datas/fn'
import { vPtype, vEmergency, vClause, contractingParty } from '../../common'

import PurchaseDrawer from '/@/views/erp/purchaseOrder/components/purchaseDrawer.vue'
import OtherExpendDrawer from '/@/views/financialDocuments/otherExpend/components/DetailsDrawer.vue'
import RefundDrawer from '/@/views/financialDocuments/refund/components/refundDrawer.vue'

import { reactive, ref } from 'vue'
import { BasicDrawer, useDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable } from '/@/components/Table'
import { BasicForm, useForm } from '/@/components/Form'
import { Button, DescriptionsItem, message, Descriptions, Modal, Empty, Pagination } from 'ant-design-vue'
import { ScrollContainer } from '/@/components/Container'
import { cloneDeep, debounce } from 'lodash-es'
import { CheckRemarkFormSchemas } from '../datas/drawer'
import { createImgPreview } from '/@/components/Preview/index'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'

import { useMessage } from '/@/hooks/web/useMessage'
import { useModal } from '/@/components/Modal'
import RelateOrderComp from '/@/views/financialDocuments/paymentOrder/components/RelateOrderComp.vue'
import { usePermission } from '/@/hooks/web/usePermission'

const { hasPermission } = usePermission()
const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()
const emit = defineEmits(['success', 'register', 'registerExamineDrawer'])

const paging = reactive<{ page: number; pageSize: number }>({ page: 1, pageSize: 10 })
const projectLoading = ref<boolean>(false)
const projectList = ref<any[]>([])
const pagingTotal = ref<number>(0)
const generateBtnStatus = ref(true)
const receiptOrderDetails = ref()
const record: Recordable = ref({})
const isCashier = ref(false)
let newDetalisColumns: Array<any> = []

//是否显示付款单财务审核备注弹窗
const visibleCheckRemarkModal = ref(false)
const confirmCheckRemarkModalLoading = ref(false)

/** 注册 Work 表格 */
const [registerWork, { setTableData, getSelectRows, clearSelectedRowKeys, setColumns, setSelectedRowKeys }] = useTable({
  showIndexColumn: false,
  rowKey: 'work_id',
  pagination: true,
  rowSelection: {
    // type: 'checkbox',
    onChange: handleChange
  }
})
/** 注册抽屉，刚进来会触发 */
const [registerExamineDrawer, { changeLoading, closeDrawer }] = useDrawerInner(async (data) => {
  try {
    changeLoading(true)
    clearSelectedRowKeys()

    isCashier.value = data.isCashier
    newDetalisColumns = []
    record.value = data.record
    receiptOrderDetails.value = await getPaymentOrderDetails({ id: data.record.id })
    console.log(receiptOrderDetails.value)
    if (data.type == 'edit') {
      resetFields()
    }
    // 判断是否有查看项目总览的权限
    if (hasPermission(297)) initProject()

    // 判断审核的类型是否是采购单，如果是采购单需要增加销售总价和利润率字段
    cloneDeep(addWorkListColumnsFn(editClick, receiptOrderDetails.value.items?.work[0]?.type ?? 4, handlePreview)).forEach((item) => {
      if (item.dataIndex !== 'current_amount') {
        newDetalisColumns.push(item)
      }
    })

    setColumns(newDetalisColumns)
    setTableData(receiptOrderDetails.value.items.work)
    changeLoading(false)
  } catch (error) {
    changeLoading(false)
    throw new Error(`${error}`)
  }
})

/** 注册采购单详情 */
const [registerPurchaseDrawer, { openDrawer: openPurchaseDrawer }] = useDrawer()

/** 注册其他支出单详情 */
const [registerOtherExpendDrawer, { openDrawer: openOtherExpendDrawer }] = useDrawer()

/** 注册退款单详情 */
const [refundDrawer, { openDrawer: openRefundDrawer }] = useDrawer()

const debounceHandlePass = debounce(_handlePass, 500)

/** 选中 */
async function handleChange(data, record) {
  console.log(data, record)
  let columns = cloneDeep(newDetalisColumns)

  const workids = receiptOrderDetails.value?.items?.work.map((item) => item.work_id) ?? []
  if (workids.length !== 1) {
    if (record.length > 0 && record[0].type == 8 && data.length == 1) {
      setSelectedRowKeys(workids)
      data = workids
    } else if (record.length > 0 && record[0].type == 8 && data.length < workids.length) {
      setSelectedRowKeys([])
      data = []
    }
  }

  columns.splice(2, 0, {
    title: '驳回原因',
    dataIndex: 'reject_remark',
    editComponent: 'Textarea',
    editRow: true,
    width: 250,
    resizable: true,
    editRender: (data) => {
      if (!data.text) {
        return '-'
      } else {
        return data.text
      }
    }
  })

  if (data.length !== 0 && record[0] && [4, 8, 11].includes(record[0].type)) {
    setColumns(columns)
    // 解决第一次选择驳回原因不展开的问题
    await record[record.length - 1].onEdit?.(true, false)
    receiptOrderDetails.value.items?.work.map(async (item) => {
      if (data.includes(item.work_id)) {
        await item.onEdit?.(true, false)
      } else {
        await item.onEdit?.(false, true)
        item.reject_remark = ''
      }

      return item
    })
  } else {
    setColumns(newDetalisColumns)
  }

  // setColumns()
  if (getSelectRows().length == 0) {
    generateBtnStatus.value = true
  } else {
    generateBtnStatus.value = false
  }
}

/** 点击通过按钮 */
function handlePassClick() {
  // 验证签约主体是否已选择
  if (!record.value.contracting_party) {
    message.error('请先选择我司签约主体！')
    return
  }

  // 验证通过，显示备注弹窗
  visibleCheckRemarkModal.value = true
}

/** 通过 */
async function _handlePass() {
  try {
    await changeLoading(true)
    confirmCheckRemarkModalLoading.value = true
    const { check_remark } = await validate()
    await examinePaymentOrder({ works: [], id: record.value.id, is_check: 1, check_remark })
    visibleCheckRemarkModal.value = false
    closeDrawer()
    emit('success')
    message.success('审核成功！')
    confirmCheckRemarkModalLoading.value = false
    changeLoading(false)
  } catch (error) {
    confirmCheckRemarkModalLoading.value = false
    changeLoading(false)
    message.error('审核失败！')
    throw new Error(`${error}`)
  }
}
// const handlePass = debounce(_handlePass, 200)

const debounceHandleReject = debounce(handleReject, 500)
//驳回备注
/** 驳回 */
async function handleReject() {
  try {
    const tabledata = getSelectRows()
    const selectRowsData = ref({})
    const typerejrct = tabledata.some((item) => item.reject_remark != null && item.reject_remark !== '')

    if (tabledata[0].type == 8) {
      if (!typerejrct) {
        return message.error('请至少填写一条驳回原因！')
      }
      selectRowsData.value = tabledata.map((item) => {
        return { work_id: item.work_id, reject_remark: item.reject_remark }
      })
    } else {
      selectRowsData.value = tabledata.map((item) => {
        if (!item.reject_remark) {
          throw new Error('请填写驳回原因！')
        }
        return { work_id: item.work_id, reject_remark: item.reject_remark }
      })
    }
    // const selectRowsData = getSelectRows().map((item) => {
    //   // return item.work_id
    //   if (!item.reject_remark) {
    //     throw new Error('请填写驳回原因！')
    //   }
    //   return { work_id: item.work_id, reject_remark: item.reject_remark }
    // })
    console.log({ works: selectRowsData, id: record.value.id, is_check: 2, paymentId: record.value.id })
    await examinePaymentOrder({ works: selectRowsData.value, id: record.value.id, is_check: 2 })
    emit('success')
    message.success('驳回成功！')
    closeDrawer()
  } catch (error: any) {
    message.error(error.message)
    throw new Error(`${error}`)
  }
}

/** 取消 */
function handleCancel() {
  closeDrawer()
}

/** 单号点击回调 */
async function editClick(records: Recordable) {
  await openDrawerFn({
    records,
    openPurchaseDrawer,
    openOtherExpendDrawer,
    openRefundDrawer,
    is_finance: receiptOrderDetails.value.is_finance
  })
}

const [registerCheckRemarkForm, { resetFields, validate }] = useForm({
  labelWidth: 120,
  schemas: CheckRemarkFormSchemas,
  showActionButtonGroup: false
})

// 预览
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}

async function initProject() {
  try {
    projectLoading.value = true
    const { items, total } = await paymentProject({ ...paging, id: record.value.id })
    projectList.value = items
    pagingTotal.value = total
  } catch (err) {
    createMessage.error('获取项目列表失败')
  } finally {
    projectLoading.value = false
  }
}
</script>

<style lang="less" scoped>
.project-list {
  ::v-deep(.ant-empty) {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding-top: 30px;
    padding-bottom: 30px;
  }
}
</style>
