<template>
  <BasicModal @register="registerPaymentModal" width="30%" title="已付款" :bodyStyle="{ height: '500px' }" @ok="handleOk">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { getFinancialInformation } from '/@/api/financialDocuments/capitalFlow'
import { BasicForm, FormSchema, useForm } from '/@/components/Form'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { ref } from 'vue'
import { cashierChangeStatus } from '/@/api/financialDocuments/paymentOrder'
import { message } from 'ant-design-vue'
import { debounce } from 'lodash-es'
import { getDeptSelectTree } from '/@/api/admin/dept'
import { DefaultOptionType } from 'ant-design-vue/es/vc-tree-select/TreeSelect'
import { handlePlaformChange } from '../../capitalFlow/datas/fn'

const emit = defineEmits(['register', 'success'])
const recordData = ref<any>()
const pathname = window.location.pathname

/** 注册Modal，刚进来会触发*/
const [registerPaymentModal, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  recordData.value = data.record
  await resetFields()
  await setFieldsValue({
    occurrence_at: dayjs(Date.now()).format('YYYY-MM-DD'),
    is_relfund: false
  })
})

const schemas1: FormSchema[] = [
  {
    field: 'is_relfund',
    label: '是否自动生成流水',
    component: 'Switch',
    componentProps: {
      onChange: (value: boolean) => {
        handleChangeStatus(value)
      }
    }
  },
  {
    field: 'occurrence_at',
    label: '付款日期',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      disabledDate: (current) => {
        if (!current) {
          return false
        }
        const today = dayjs()
        const tomorrow = today.add(1, 'day')
        return current && current.diff(tomorrow, 'day') >= 0
      }
    },
    required: true
  },
  {
    field: 'remark',
    label: '付款备注',
    component: 'InputTextArea'
  }
]

function schemas2(): FormSchema[] {
  return [
    {
      field: 'is_relfund',
      label: '是否自动生成流水',
      component: 'Switch',
      componentProps: {
        onChange: (value: boolean) => {
          handleChangeStatus(value)
        }
      }
    },
    {
      field: 'occurrence_at',
      label: '付款日期',
      component: 'DatePicker',
      componentProps: {
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        disabledDate: (current) => {
          if (!current) {
            return false
          }
          const today = dayjs()
          const tomorrow = today.add(1, 'day')
          return current && current.diff(tomorrow, 'day') >= 0
        }
      },
      required: true
    },
    {
      field: 'from_plaform',
      label: '付款资金资料',
      component: 'ApiSelect',
      componentProps: ({ formModel }) => {
        return {
          api: getFinancialInformation,
          selectProps: {
            showSearch: true,
            placeholder: '请选择',
            fieldNames: { value: 'name', label: 'name' },
            allowClear: true
          },
          onChange: (_, shall) => {
            formModel.from_plaform_id = shall.a_id
            handlePlaformChange(shall, { setFieldsValue })
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      },
      required: true
    },
    {
      field: 'currency',
      label: '结算货币',
      component: 'Input',
      defaultValue: '人民币',
      required: ({ model }) => {
        return !['人民币', 'CNY'].includes(model.currency) && pathname !== '/s/'
      },
      show: ({ model }) => {
        return !['人民币', 'CNY'].includes(model.currency) && pathname !== '/s/'
      },
      dynamicDisabled: true
    },
    {
      field: 'rate',
      label: '汇率',
      component: 'InputNumber',
      defaultValue: '1.000000',
      required: ({ model }) => {
        return !['人民币', 'CNY'].includes(model.currency) && pathname !== '/s/'
      },
      show: ({ model }) => {
        return !['人民币', 'CNY'].includes(model.currency) && pathname !== '/s/'
      },
      componentProps: {
        precision: 6,
        min: 0
      }
    },
    {
      field: 'fg_amount',
      label: '外币金额',
      component: 'InputNumber',
      required: ({ model }) => {
        return !['人民币', 'CNY'].includes(model.currency) && pathname !== '/s/'
      },
      show: ({ model }) => {
        return !['人民币', 'CNY'].includes(model.currency) && pathname !== '/s/'
      },
      componentProps: {
        precision: 2,
        min: 0
      },
      dynamicDisabled: true
    },
    {
      field: 'dept_id',
      label: '部门',
      required: true,
      component: 'ApiTreeSelect',
      componentProps: {
        api: getDeptSelectTree,
        treeSelectProps: {
          fieldNames: { children: 'children', key: 'id', value: 'id', label: 'name' },
          placeholder: '请选择',
          showSearch: true,
          filterTreeNode: (search: string, item: DefaultOptionType) => {
            if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
            return false
          }
        }
      }
    },
    {
      field: 'fee',
      label: '流水手续费',
      component: 'InputNumber',
      componentProps: {
        precision: 2,
        min: 0,
        onChange: (val) => {
          updateSchema({
            field: 'account_name',
            required: val && val > 0 ? true : false
          })
        }
      }
    },
    {
      field: 'account_name',
      label: '支出科目名称',
      component: 'Select',
      itemHelpMessage: `手续费的科目选择
    1、跟订单有关产生的手续费其他业务支出-手续费
    2、跟订单无关且不是公共部门:营业费用-其他
    3、公共费用或银行短信费等:财务费用-手续典`,
      componentProps: ({ formModel }) => {
        return {
          options: [
            { label: '其他业务支出-手续费', value: '其他业务支出-手续费', code: 9997 },
            { label: '财务费用-手续费', value: '财务费用-手续费', code: 9319 },
            { label: '营业费用-其他', value: '营业费用-其他', code: 329 }
          ],
          onChange(_, shall) {
            formModel.account_code = shall.code
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'account_code',
      label: '支出科目编码',
      component: 'Input',
      show: false
    },
    {
      field: 'to_plaform',
      label: '收款资金资料',
      component: 'Input',
      componentProps: () => {
        return {}
      },
      itemProps: {
        validateTrigger: 'blur'
      }
      // required: true
    },
    {
      field: 'remark',
      label: '付款备注',
      component: 'InputTextArea'
    },
    {
      field: 'from_plaform_id',
      label: 'from_plaform_id',
      component: 'Input',
      show: false,
      ifShow: true
    }
  ]
}

/** 注册Form */
const [registerForm, { setFieldsValue, resetFields, resetSchema, validate, updateSchema }] = useForm({
  baseColProps: { span: 24 },
  labelWidth: 120,
  showActionButtonGroup: false,
  schemas: schemas1
})

/** 是否生成流水change事件 */
const handleChangeStatus = async (value: boolean) => {
  await setFieldsValue({ from_plaform: undefined, to_plaform: '', remark: '' })
  if (value) {
    await resetSchema(schemas2())
    await setFieldsValue({
      rate: recordData.value.rate,
      currency: recordData.value.currency || '人民币',
      fg_amount: recordData.value.foreign_currency_amount || 0
    })
  } else {
    await resetSchema(schemas1)
  }
}

/** 点击确认 */
const _handleOk = async () => {
  try {
    await changeOkLoading(true)
    let data = await validate()
    data.is_relfund = data.is_relfund == false ? 0 : 1
    console.log(data)

    await cashierChangeStatus({ ...data, id: recordData.value.id })
    await closeModal()
    message.success('操作成功!')
    setTimeout(() => {
      changeOkLoading(false)
    }, 3000)
    emit('success')
  } catch (error) {
    changeOkLoading(false)
    throw new Error(`${error}`)
  }
}
const handleOk = debounce(_handleOk, 200)
</script>
<style lang="less" scoped>
:deep(.ant-picker) {
  width: 100%;
}
</style>
