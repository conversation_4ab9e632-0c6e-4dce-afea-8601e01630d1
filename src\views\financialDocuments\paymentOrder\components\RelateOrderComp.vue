<template>
  <div>
    <Collapse :bordered="false" destroyInactivePanel>
      <template #expandIcon="{ isActive }">
        <caret-right-outlined :rotate="isActive ? 90 : 0" />
      </template>
      <CollapsePanel v-for="item in projectList" :key="item.id">
        <template #header>
          <div class="flex items-center">
            <div> 项目名称：{{ item.project_name }} - 客户：{{ item.client_id }} - 单号：{{ item?.strids.join('、') ?? '-' }}</div>
          </div>
        </template>
        <BasicTable
          :key="item.id"
          v-model:expandedRowKeys="expandedRowKeys"
          :actionColumn="{
            width: 450,
            title: '操作',
            dataIndex: 'action'
          }"
          :api="(params) => getProjectSalesOrder({ ...params, id: item.project_number })"
          :canResize="false"
          :columns="columns"
          :expandIconColumnIndex="-1"
          :showIndexColumn="false"
          rowKey="id"
        >
          <template #bodyCell="{ column, record: cellRecord }">
            <template v-if="column.dataIndex === 'action'">
              <TableAction :actions="createActions(cellRecord)" />
            </template>
          </template>
          <template #expandedRowRender="{ record: cellRecord, expanded }">
            <div>
              <RelateComp v-if="expanded" :type="childTableType[cellRecord.id]" :record="cellRecord" />
            </div>
          </template>
        </BasicTable>
      </CollapsePanel>
    </Collapse>
  </div>
</template>

<script lang="ts" setup>
import { Collapse, CollapsePanel } from 'ant-design-vue'
import { ref } from 'vue'
import { CaretRightOutlined } from '@ant-design/icons-vue'
import { getProjectSalesOrder } from '/@/api/projectOverview'
import { columns } from '/@/views/projectOverview/projectOverview/datas/datas'
import { type ActionItem, BasicTable, TableAction } from '/@/components/Table'

import RelateComp from './relateComp/RelateComp.vue'

const expandedRowKeys = ref<number[]>([])
const childTableType = ref<{ [key: number]: 'erp' | 'finance' | 'product' }>({})
withDefaults(
  defineProps<{
    record: any
    projectList: any
    pagingTotal: number
  }>(),
  {
    record: {}, // 默认值为true
    projectList: [],
    pagingTotal: 0
  }
)

function createActions(record: any): ActionItem[] {
  return [
    {
      label: '查看订单产品',
      onClick: handleViewRelate.bind(null, { record, type: 'product' })
    },
    {
      label: '查看关联业务单据',
      onClick: handleViewRelate.bind(null, { record, type: 'erp' })
    },
    {
      label: '查看关联财务单据',
      onClick: handleViewRelate.bind(null, { record, type: 'finance' })
    }
  ]
}

function handleViewRelate({ record, type }) {
  expandedRowKeys.value.includes(record.id)
    ? childTableType.value[record.id] === type
      ? (expandedRowKeys.value = expandedRowKeys.value.filter((item) => item !== record.id))
      : (childTableType.value[record.id] = type)
    : expandedRowKeys.value.push(record.id) && (childTableType.value[record.id] = type)
}
</script>
