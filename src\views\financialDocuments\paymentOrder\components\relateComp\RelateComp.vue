<template>
  <div class="relate-comp-container">
    <Row :gutter="[20, 50]">
      <template v-for="key in Object.keys(curComp)" :key="key">
        <Col :span="24" v-if="curComp[key].hasDataSource">
          <!--          <BasicTable-->
          <!--            v-if="curComp[key].ifShow"-->
          <BasicTable
            :showTableSetting="true"
            :tableSetting="tableSetting"
            :title="curComp[key].title"
            :scroll="{ y: 300 }"
            :showIndexColumn="false"
            :columns="curComp[key].columns"
            :api="curComp[key].api"
            :after-fetch="(val) => handleAfterFetchApi(val, curComp[key])"
          >
            <template #bodyCell="{ column, record: cellRecord }">
              <template v-if="column.dataIndex === curComp[key].slotField">
                <a-button class="table-ellipsis-btn" type="link" @click="handleView(cellRecord, key)">
                  {{ cellRecord[curComp[key].slotField] }}
                </a-button>
              </template>
            </template>
            <template v-for="(slotContent, slotName) in curComp[key].compSlots" :key="slotName" #[slotName]="data">
              <div :key="JSON.stringify(data)" v-slotnode="{ slotContent, data }"></div>
            </template>
          </BasicTable>
        </Col>
      </template>
      <Col v-if="compHasDataSource" :span="24">
        <Empty />
      </Col>
    </Row>
    <template v-for="key in Object.keys(drawerComp)" :key="key">
      <component :is="drawerComp[key].comp" @register="drawerComp[key].register" />
    </template>
  </div>
</template>
<script setup lang="ts">
import { BasicTable, TableSetting } from '/@/components/Table'
import { Row, Col, Empty } from 'ant-design-vue'
import { ref, markRaw, nextTick, watchEffect, computed, render } from 'vue'
import { mapErpComp, mapFinanceComp } from '/@/views/projectOverview/overviewRelate/datas/datas'
import PurchaseDrawer from '/@/views/erp/purchaseOrder/components/purchaseDrawer.vue'
import InWarehouseDrawer from '/@/views/erp/inWarehouse/components/EditDrawer.vue'
import OutWarehouseDrawer from '/@/views/erp/outWarehouse/components/OutWarehouseDrawer.vue'
import WarehouseTransformDrawer from '/@/views/erp/warehouseTransfer/components/AddtransferDrawer.vue'
import InventoryDrawer from '/@/views/erp/inventoryTable/components/AddinventtorytableDrawer.vue'
import RetreatDrawer from '/@/views/erp/retreat/components/RetreatDrawer.vue'
import DetectionDrawer from '/@/views/erp/qc/components/QcDrawer.vue'
import ReceiptDrawer from '/@/views/financialDocuments/receiptOrder/components/DetailsDrawer.vue'
import PaymentDrawer from '/@/views/financialDocuments/paymentOrder/components/DetailsDrawer.vue'
import OtherIncomeDrawer from '/@/views/financialDocuments/otherIncomeExpend/components/DetailsDrawer.vue'
import OtherExpendDrawer from '/@/views/financialDocuments/otherExpend/components/DetailsDrawer.vue'
import RefundDrawer from '/@/views/financialDocuments/refund/components/refundDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { childColumns } from '/@/views/projectOverview/projectOverview/datas/datas'
import { getProjectSalesRequest } from '/@/api/projectOverview'
// import { cloneDeep } from 'lodash-es'
// import { pushItems } from '/@/views/financialDocuments/paymentOrder/components/relateComp/datas/datas'

const tableSetting: TableSetting = {
  redo: true,
  form: false,
  size: false,
  setting: false,
  fullScreen: false
}

const props = withDefaults(
  defineProps<{
    record: any
    type: 'erp' | 'finance' | ''
  }>(),
  {
    record: {},
    type: ''
  }
)

const [registerPurchaseDrawer, purchaseEvent] = useDrawer()
const [registerInWarehouseDrawer, inWarehouseEvent] = useDrawer()
const [registerOutWarehouseDrawer, outWarehouseEvent] = useDrawer()
const [registerWarehouseTransformDrawer, warehouseTransformEvent] = useDrawer()
const [registerInventoryDrawer, inventoryEvent] = useDrawer()
const [registerRetreatDrawer, retreatEvent] = useDrawer()
const [registerDetectionDrawer, DetectionEvent] = useDrawer()
const [registerReceiptDrawer, receiptEvent] = useDrawer()
const [registerPaymentDrawer, paymentEvent] = useDrawer()
const [registerOtherIncomeDrawer, otherIncomeEvent] = useDrawer()
const [registerOtherExpendDrawer, otherExpendEvent] = useDrawer()
const [registerRefundDrawer, refundEvent] = useDrawer()

const curDrawer = ref({})
const curComp = ref({})

const compHasDataSource = computed(() => {
  return Object.keys(curComp.value).every((tableName) => !curComp.value[tableName].hasDataSource)
})

function createErpComp() {
  curComp.value = mapErpComp(props.record.id, 0)
}

function createFinanceComp() {
  curComp.value = mapFinanceComp(props.record.id, 0)
}

function createProductComp() {
  curComp.value = {
    Product: {
      title: '订单产品',
      columns: childColumns,
      api: (params) => getProjectSalesRequest({ ...params, id: props.record.id }),
      slotField: '',
      ifShow: true,
      hasDataSource: true
    }
  }
}

watchEffect(() => {
  const mapType = {
    erp: createErpComp,
    finance: createFinanceComp,
    product: createProductComp
  }
  if (!props.type) {
    curComp.value = {}
    return
  }
  mapType[props.type]?.()
  // console.log(curComp.value)
})

const drawerComp = ref({
  Purchase: { register: registerPurchaseDrawer, comp: markRaw(PurchaseDrawer), event: purchaseEvent },
  InWarehouse: { register: registerInWarehouseDrawer, comp: markRaw(InWarehouseDrawer), event: inWarehouseEvent },
  OutWarehouse: { register: registerOutWarehouseDrawer, comp: markRaw(OutWarehouseDrawer), event: outWarehouseEvent },
  WarehouseTransform: {
    register: registerWarehouseTransformDrawer,
    comp: markRaw(WarehouseTransformDrawer),
    event: warehouseTransformEvent
  },
  Inventory: { register: registerInventoryDrawer, comp: markRaw(InventoryDrawer), event: inventoryEvent },
  Retreat: { register: registerRetreatDrawer, comp: markRaw(RetreatDrawer), event: retreatEvent },
  QualityDetection: { register: registerDetectionDrawer, comp: markRaw(DetectionDrawer), event: DetectionEvent },
  Receipt: { register: registerReceiptDrawer, comp: markRaw(ReceiptDrawer), event: receiptEvent },
  FinancePayment: { register: registerPaymentDrawer, comp: markRaw(PaymentDrawer), event: paymentEvent },
  Payment: { register: registerPaymentDrawer, comp: markRaw(PaymentDrawer), event: paymentEvent },
  OtherIncome: { register: registerOtherIncomeDrawer, comp: markRaw(OtherIncomeDrawer), event: otherIncomeEvent },
  OtherExpend: { register: registerOtherExpendDrawer, comp: markRaw(OtherExpendDrawer), event: otherExpendEvent },
  FinanceOtherExpend: { register: registerOtherExpendDrawer, comp: markRaw(OtherExpendDrawer), event: otherExpendEvent },
  Refund: { register: registerRefundDrawer, comp: markRaw(RefundDrawer), event: refundEvent }
})

function handleView(record, comp) {
  curDrawer.value = comp
  nextTick(() => {
    drawerComp.value[curDrawer.value]?.event?.openDrawer(true, mapDrawerData(record))
    drawerComp.value[curDrawer.value]?.event?.setDrawerProps({ showFooter: false })
  })
}

function mapDrawerData(record) {
  const mapData = {
    Purchase: { record, isUpdate: false, type: 'detail' },
    InWarehouse: { isUpdate: false, type: 'detail', cardItem: record },
    OutWarehouse: { record, type: 'view' },
    WarehouseTransform: { record, isUpdate: false, type: 'detail' },
    Inventory: { record, isUpdate: false, type: 'detail' },
    Retreat: { record, type: 'detail' },
    QualityDetection: { record, type: 'detail' },
    Receipt: { id: record.id },
    Payment: { id: record.id },
    FinancePayment: { id: record.id },
    OtherIncome: { record },
    OtherExpend: { record, type: 'detail' },
    FinanceOtherExpend: { record, type: 'detail' },
    Refund: { type: 'detail', record }
  }
  return mapData[curDrawer.value]
}

function handleAfterFetchApi(val, data) {
  if (val.length === 0) data.hasDataSource = false
  return val
}

const vSlotnode = {
  mounted(el, binding) {
    const { slotContent, data } = binding.value
    render(slotContent(data), el)
  }
}
// function transformColumns(
//   columns,
//   {
//     replace,
//     remove,
//     push
//   }: { replace?: Array<{ field: string; replaceField: string }>; remove?: string[]; push?: { idx: number; items: BasicColumn[] } }
// ) {
//   if (remove && remove?.length > 0) columns = columns.filter((item) => !remove.includes(item.dataIndex))
//
//   if (replace && replace?.length > 0) {
//     for (const item of columns) {
//       for (const key of replace) {
//         const { field, replaceField } = key
//         if (field.includes(item.dataIndex)) {
//           item.dataIndex = replaceField
//         }
//       }
//     }
//   }
//
//   if (push && push?.items?.length > 0) columns.splice(push.idx, 0, ...push.items)
//
//   return cloneDeep(columns)
// }
</script>

<style lang="less" scoped>
.relate-comp-container {
  ::v-deep(.ant-table) {
    margin: 0 !important;
  }
}
</style>
