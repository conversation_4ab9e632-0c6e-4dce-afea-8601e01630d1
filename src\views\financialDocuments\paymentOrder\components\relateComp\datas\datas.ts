import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'

export const pushItems = [
  {
    title: '应付金额',
    dataIndex: 'sale_amount',
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(Math.ceil(text * 100) / 100) : '0.00'
    }
  },
  {
    title: '本次应付金额',
    dataIndex: 'sale_amount_cost',
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(text) : '0.00'
    }
  },
  {
    title: '本次已付金额',
    dataIndex: 'sale_amount_paid',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(text) : '0.00'
    }
  }
]
