import { render } from 'vue'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { getPurchaseOrderList } from '/@/api/erp/purchaseOrder'
import { getOtherExpendList } from '/@/api/financialDocuments/otherExpend'
import { getFundManage } from '/@/api/financialDocuments/refund'
import { message } from 'ant-design-vue'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
const saleOrderStore = useSaleOrderStore()

/** 财务审批 */
export function financeExamine(check) {
  const mapValue = {
    0: { text: '待审批', color: '' },
    1: { text: '已审批', color: 'success' },
    2: { text: '驳回', color: 'red' }
  }
  return useRender.renderTag(mapValue[check]?.text, mapValue[check]?.color)
}
/** 主管审批 */
export function SupervisorExamine(check) {
  const mapValue = {
    0: { text: '待审批', color: '' },
    1: { text: '已审批', color: 'success' }
  }
  return useRender.renderTag(mapValue[check]?.text, mapValue[check]?.color)
}

/** 判断状态 */
export function determineStatus(status) {
  const mapValue = {
    0: { text: '未付款', color: '' },
    1: { text: '已付款', color: 'success' },
    2: { text: '已结束', color: 'error' }
  }
  return useRender.renderTag(mapValue[status]?.text, mapValue[status]?.color)
}

/** 是否开增值税发票 */
export function Isopentax(check) {
  const mapValue = {
    0: { text: '不开票' },
    1: { text: '开票' },
    nell: { text: '-' }
  }
  return useRender.renderTag(mapValue[check]?.text)
}

/** 判断状态自定义指令 */
export const vStatus = {
  beforeUpdate(el, binding) {
    const vnode = determineStatus(binding.value)
    render(vnode, el)
  }
}

/** 财务审批状态自定义指令 */
export const vCheck = {
  beforeUpdate(el, binding) {
    const vnode = financeExamine(binding.value)
    render(vnode, el)
  }
}
/** 主管审批状态自定义指令 */
export const vPorn = {
  beforeUpdate(el, binding) {
    const vnode = SupervisorExamine(binding.value)
    render(vnode, el)
  }
}

/** 增值税开票自定义指令 */
export const vOpenTax = {
  beforeUpdate(el, binding) {
    const vnode = Isopentax(binding.value)
    render(vnode, el)
  }
}

/** 详情页面的金额相关*/
export const keyPriceArr = ['amount', 'amount_cost', 'amount_paid']
export const getDetailsPrice = (record, field) => {
  if (record[field]) {
    if (field == 'amount_cost' || field == 'amount_paid') {
      return formateerNotCurrency.format(record[field], 2)
    } else {
      return formateerNotCurrency.format(record[field])
    }
  } else {
    return '0.00'
  }
}

/** 单号点击回调 */
export async function openDrawerFn({
  records,
  openPurchaseDrawer,
  openOtherExpendDrawer,
  openRefundDrawer,
  is_finance
}: {
  records: Recordable
  openPurchaseDrawer: Function
  openOtherExpendDrawer: Function
  openRefundDrawer: Function
  is_finance: number
}) {
  console.log(records)
  const { type, strid } = records
  // 获取当前款单的详情信息
  try {
    if (type == 4) {
      // 采购单
      const details = await getPurchaseOrderList({ strid })
      openPurchaseDrawer(true, { record: details.items[0], isUpdate: false, type: 'detail' })
    } else if (type == 8) {
      // 其他支出单
      const details = await getOtherExpendList({ item_strid: strid, is_sale: 1, is_finance })

      openOtherExpendDrawer(true, { record: details.items[0], type: 'detail' })
    } else if (type == 11) {
      // 退款单
      const details = await getFundManage({ strid })
      openRefundDrawer(true, { record: details.items[0], type: 'detail' })
    }
  } catch (error) {
    message.error('获取详情失败！')
    throw new Error(`${error}`)
  }
}

/** -----筛选----- */
/** 判断款单类型 */
export const getClauseOptions = () => {
  const options: any = []
  for (const item in saleOrderStore.orderType) {
    options.push({ label: saleOrderStore.orderType[item], value: item })
  }
  return options
}
