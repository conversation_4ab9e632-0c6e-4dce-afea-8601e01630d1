<template>
  <BasicModal @register="registerAddModal" title="收款单添加流水" width="70%" :bodyStyle="{ height: '700px' }" @ok="handleOk">
    <BasicTable @register="registerFilterTable" />
  </BasicModal>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import { getFundList } from '/@/api/financialDocuments/public'
import { getClientList } from '/@/api/financialDocuments/public'

import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicTable, useTable } from '/@/components/Table'
import { ref } from 'vue'
import { getErpSupplier } from '/@/api/commonUtils'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import dayjs from 'dayjs'
import { nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'

const emit = defineEmits(['register', 'handleAddOk'])
console.log(emit)

/** 存储上层传过来的数据 */
const fundTableData = ref<Array<any>>([])

/** 付款日期 */
const collectionAt = ref<any>()
const recordData = ref<any>()
const pathname = window.location.pathname

let schemas: any = ref([
  {
    field: 'occurrence_at',
    label: '收款日期',
    component: 'DatePicker',
    helpMessage: '收款单收款日期必须和流水单收款日期一致才能关联流水!',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'client_id',
    label: '客户',
    component: 'ApiSelect',
    componentProps: {
      api: getClientList,
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      },
      resultField: 'items'
    }
  },
  {
    field: 'supplier_id',
    label: '供应商',
    component: 'PagingApiSelect',
    componentProps: () => {
      return {
        api: getErpSupplier,
        searchMode: true,
        pagingMode: true,
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        },
        resultField: 'items',
        itEmpty: true
      }
    }
  },
  {
    field: 'fund_strid',
    label: '流水单号',
    component: 'Input',
    required: true,
    componentProps: () => {
      return {
        onChange: () => {
          nextTick(() => {
            changeOne(getForm().getFieldsValue())
          })
        }
      }
    }
  },
  {
    field: 'from_plaform',
    label: '付款资金资料',
    component: 'Input',
    required: true,
    componentProps: () => {
      return {
        onChange: () => {
          nextTick(() => {
            changeOne(getForm().getFieldsValue())
          })
        }
      }
    }
  },
  {
    field: 'insurance_strid',
    label: '信保单号',
    component: 'Input',
    required: true,
    componentProps: {
      onChange: () => {
        nextTick(() => {
          changeOne(getForm().getFieldsValue())
        })
      }
    }
  },
  {
    field: 'amount',
    label: '金额',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 0,
      precision: 2,
      onChange: () => {
        nextTick(() => {
          changeOne(getForm().getFieldsValue())
        })
      }
    }
  },
  {
    field: 'fg_amount',
    label: '外汇金额',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      precision: 2
    }
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    required: true,
    componentProps: () => {
      return {
        onChange: async () => {
          nextTick(() => {
            changeOne(getForm().getFieldsValue())
          })
        }
      }
    }
  },
  {
    field: 'check_remark',
    label: '核对备注',
    component: 'InputTextArea',
    required: true,
    componentProps: () => {
      return {
        onChange: async () => {
          nextTick(() => {
            changeOne(getForm().getFieldsValue())
          })
        }
      }
    }
  }
])

/** 注册添加流水Modal，刚进来会触发 */
const [registerAddModal, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  await getForm().resetFields()
  setTableData([])
  await getForm().updateSchema(schemas.value)
  fundTableData.value = cloneDeep(data.fundTableData)
  collectionAt.value = cloneDeep(data.collectionAt)
  recordData.value = cloneDeep(data.recordData)
  console.log(data)

  setProps({ rowSelection: { type: data.isRadio ? 'radio' : 'checkbox' } })
  console.log(collectionAt.value)
  console.log(fundTableData.value)

  // 默认筛选
  await getForm().setFieldsValue({ occurrence_at: collectionAt.value })

  await getForm().clearValidate()

  // reload()

  clearSelectedRowKeys()
})

/** 注册筛选流水表格 */
const [registerFilterTable, { getSelectRows, clearSelectedRowKeys, getForm, setTableData, setProps }] = useTable({
  title: '',
  api: getFundList,
  searchInfo: { type: 1 },
  afterFetch: (data) => {
    clearSelectedRowKeys()
    const newData = data.map((item) => {
      item.cloneAmountLeft = cloneDeep(item.amount_left)
      item.clonefgamountleft = cloneDeep(item.fg_amount_left)
      item.amount_allot = 0
      return item
    })
    return newData
  },
  showIndexColumn: false,
  canResize: false,
  useSearchForm: true,
  immediate: false,
  formConfig: {
    labelWidth: 120,
    actionColOptions: {
      span: 24
    },
    showAdvancedButton: false,
    resetFunc: (): any => {
      getForm().updateSchema(schemas.value)
    },
    baseColProps: {
      span: 8
    },
    schemas: schemas.value
  },
  columns: [
    {
      title: '流水单号',
      dataIndex: 'strid',
      resizable: true,
      width: 200
    },
    {
      title: '创建日期',
      dataIndex: 'created_at',
      resizable: true,
      width: 100,
      customRender: ({ text }) => {
        return text ? dayjs(text).format('YYYY-MM-DD') : '-'
      }
    },
    {
      title: '收款日期',
      dataIndex: 'occurrence_at',
      resizable: true,
      width: 100,
      customRender: ({ text }) => {
        return text ? dayjs(text).format('YYYY-MM-DD') : '-'
      }
    },

    {
      title: '客户',
      dataIndex: 'client_name',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    },
    {
      title: '供应商',
      dataIndex: 'supplier_name',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    },
    {
      title: '付款资金资料',
      dataIndex: 'from_plaform',
      resizable: true,
      width: 100
    },
    {
      title: '收款资金资料',
      dataIndex: 'to_plaform',
      resizable: true,
      width: 100
    },

    {
      title: '币种',
      dataIndex: 'from_currency',
      width: 100,
      resizable: true
    },
    {
      title: '汇率',
      dataIndex: 'rate',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(Number(text)) : '0.00'
      }
    },
    {
      title: '外汇金额',
      dataIndex: 'fg_amount',
      width: 100,
      resizable: true,
      defaultHidden: pathname == '/s/',
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(Number(text)) : '0.00'
      }
    },
    {
      title: '外汇剩余金额',
      dataIndex: 'fg_amount_left',
      width: 100,
      resizable: true,
      defaultHidden: pathname == '/s/',
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text) : '0.00'
      }
    },
    {
      title: '金额',
      dataIndex: 'amount',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(Number(text)) : '0.00'
      }
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    },
    {
      title: '核对备注',
      dataIndex: 'check_remark',
      width: 100,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    }
  ],
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: (record) => {
      let flag = false

      // 流水时间要一致才能关联流水
      // if (dayjs(record.occurrence_at).format('YYYY-MM-DD') !== collectionAt.value) {
      //   flag = true
      // }

      for (let item of fundTableData.value) {
        if (item.strid === record.strid) {
          flag = true
          break
        }
      }
      return {
        disabled: flag
      }
    }
  }
})

/** 点击确认 */
const handleOk = async () => {
  await changeOkLoading(true)
  let data: Array<any> = []
  let flag = false
  let currency = false
  let amountleft = false

  getSelectRows().map((item) => {
    console.log(item.occurrence_at, collectionAt.value)
    if (dayjs(item.occurrence_at).format('YYYY-MM-DD') !== collectionAt.value) {
      flag = true
    }
    const isFlowCNY = ['CNY', '人民币'].includes(item.from_currency)
    const isPaymentCNY = ['CNY', '人民币'].includes(recordData.value.currency)
    const currencyMatches = item.from_currency === recordData.value.currency
    if (pathname !== '/s/' && ((isFlowCNY && !isPaymentCNY) || (!isFlowCNY && !currencyMatches))) {
      currency = true
    }
    if (pathname !== '/s/' && item.fg_amount_left == 0 && !isFlowCNY) {
      amountleft = true
    }
    data.push(item)
  })
  if (currency) {
    changeOkLoading(false)
    return message.error('收款币种与流水币种不一致，请重新选择')
  }
  if (amountleft) {
    changeOkLoading(false)
    return message.error('流水剩余外汇金额不能为0，请重新选择')
  }
  if (flag) {
    Modal.confirm({
      title: '收款日期与流水日期不一致，确定要继续关联？',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        clearSelectedRowKeys()
        closeModal()
        emit('handleAddOk', data)
      }
    })
  } else {
    clearSelectedRowKeys()
    closeModal()
    emit('handleAddOk', data)
  }
  changeOkLoading(false)
}

/** 筛选条件四选一 */
const changeOne = (obj) => {
  let { amount, remark, from_plaform, insurance_strid, fund_strid, check_remark } = obj
  getForm().clearValidate()
  if (from_plaform) {
    updateS('from_plaform')
  } else if (remark) {
    updateS('remark')
  } else if (amount) {
    updateS('amount')
  } else if (insurance_strid) {
    updateS('insurance_strid')
  } else if (fund_strid) {
    updateS('fund_strid')
  } else if (check_remark) {
    updateS('check_remark')
  } else {
    getForm().updateSchema(schemas.value)
  }
}

const updateS = (fieldName) => {
  let arr = [
    {
      field: 'from_plaform',
      required: true
    },
    {
      field: 'remark',
      required: true
    },
    {
      field: 'amount',
      required: true
    },
    {
      field: 'insurance_strid',
      required: true
    },
    {
      field: 'fund_strid',
      required: true
    },
    {
      field: 'check_remark',
      required: true
    }
  ]
  arr.forEach((item) => {
    if (item.field == fieldName) {
      getForm().updateSchema([
        {
          field: item.field,
          required: true
        }
      ])
    } else {
      getForm().updateSchema([
        {
          field: item.field,
          required: false
        }
      ])
    }
  })
}
</script>
<style lang="less" scoped>
:deep(.ant-picker) {
  width: 100%;
}
</style>
