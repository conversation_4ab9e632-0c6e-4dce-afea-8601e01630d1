<template>
  <BasicDrawer @register="registerDrawer" v-bind="$attrs" showFooter width="90%" destroyOnClose>
    <template #footer>
      <Button @click="handleCancel">取消</Button>
      <Popconfirm
        title="关联后该订单将不可以修改！"
        placement="topRight"
        @confirm="debounceHandleOk"
        :disabled="currentEditKeyRef != '' || relateLoading"
      >
        <Button :loading="relateLoading" type="primary" :disabled="currentEditKeyRef != '' || relateLoading">关联</Button>
      </Popconfirm>
    </template>
    <BasicForm @register="registerForm" />

    <!-- 关联任务列表 -->
    <Descriptions title="关联任务" />
    <DescriptionsItem>
      <BasicTable @register="registerWork" :canResize="false" @edit-end="handChangeamonut">
        <template #toolbar>
          <a-button type="primary" size="small" @click="handleUpdateReceivableAmount">按流水汇率更新本次应收金额</a-button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction :actions="createActions(record, false)" />
          </template>
        </template>
      </BasicTable>
    </DescriptionsItem>

    <!-- 关联流水列表 -->
    <Descriptions title="关联流水" />
    <DescriptionsItem>
      <BasicTable @register="registerFund" :canResize="false">
        <template #toolbar>
          <Button type="primary" size="small" @click="openAddModal" :disabled="currentEditKeyRef != '' || fundTableData.length >= 1"
            >新增</Button
          >
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction :actions="createActions(record, true)" />
          </template>
        </template>
      </BasicTable>
    </DescriptionsItem>

    <!-- 筛选流水 -->
    <AddFundModal @register="registerAddModal" @handle-add-ok="handleAddOk" />
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { updateFormSchema, addFundListColumnsFn, addWorkListColumnsFn } from '../datas/datas'
import AddFundModal from './AddFundModal.vue'
import { addCurrentAmount } from '/@/views/financialDocuments/common'

import { ref, computed } from 'vue'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import { Button, Descriptions, DescriptionsItem, message, Popconfirm } from 'ant-design-vue'
import { cloneDeep, debounce, maxBy } from 'lodash-es'
import { useModal } from '/@/components/Modal'
import { getReceiptOrderDetails } from '/@/api/financialDocuments/receiptInformation'
import { bindFunds } from '/@/api/financialDocuments/receiptOrder'
import { add, mul, sub } from '/@/utils/math'

const emit = defineEmits(['register', 'success', 'registerWork', 'registerFund', 'registerDrawer', 'registerForm', 'registerTable'])

const relateLoading = ref(false)
const currentEditKeyRef = ref('')
const recordData = ref([])
/** 收款日期 */
const collectionAt = ref()
// const receiptOrderDetails = ref()
/** 传当前流水表格数据添加流水modal，用于判断是否可选 */
const fundTableData = ref<Array<any>>([])
/** 存储编辑前的record */
const beforeRecord = ref()

// 所有detail的返回数据
const detailsGather = ref({})
const workList = computed(() => {
  const arr = Object.values(detailsGather.value).flat(1)
  return arr.map((item) => ({ ...item, work: item.work.map((work) => ({ ...work, docFundId: item.id })) }))
})

// 计算detail中的work明细
const compDetailWork = computed(() => workList.value.reduce((arr, item) => arr.concat(item.work), []))

// 用收款单的id直接读取每个收款单数据
const compDoc = computed(() => {
  const docGather = {}
  for (const item of recordData.value) {
    docGather[item.id] = item
  }
  return docGather
})

/** 注册from */
const [registerForm, { resetFields, setFieldsValue, validate, resetSchema, updateSchema, getFieldsValue }] = useForm({
  baseColProps: { span: 7 },
  labelWidth: 150,
  showActionButtonGroup: true,
  showResetButton: false,
  showSubmitButton: false
})

/** 注册work表格 */
const [registerWork, { setTableData, getDataSource, getColumns, setColumns, updateTableDataRecord }] = useTable({
  showIndexColumn: false,
  pagination: true,
  rowKey: 'work_id'
})

/** 注册fund表格 */
const [
  registerFund,
  {
    setTableData: setTableDataFund,
    getDataSource: getDataSourceFund,
    getColumns: getColumnsFund,
    deleteTableDataRecord: deleteTableDataRecordFund,
    setColumns: setColumnsFund,
    updateTableDataRecord: updateTableDataRecordFund
  }
] = useTable({
  showIndexColumn: false,
  canResize: true,
  // maxHeight: 300,
  scroll: { y: 300 },
  pagination: false,
  actionColumn: {
    width: 170,
    title: '操作',
    dataIndex: 'action'
  }
})

/** 注册添加流水Modal */
const [registerAddModal, { openModal }] = useModal()

/** 注册抽屉,刚进来会触发 */
const [registerDrawer, { setDrawerProps, closeDrawer, changeLoading }] = useDrawerInner(async (data) => {
  try {
    changeLoading(true)
    resetFields()
    setTableData([])
    detailsGather.value = {}
    fundTableData.value = []

    recordData.value = data.selectRow
    currentEditKeyRef.value = ''

    console.log(recordData.value)
    const detailList = await Promise.all(data.selectRow.map((item) => getReceiptOrderDetails({ id: item.id })))
    console.log(detailList)

    // 根据每个收款单的id记录每个收款单的详情
    data.selectRow.forEach((item, idx) => {
      detailsGather.value[item.id] = detailList[idx].items
    })
    // detailsGather.value = detailList.map((item) => item.items)

    resetSchema(updateFormSchema)

    // 给收款日期添加个change事件，在打开流水modal时传入当前选择的日期
    updateSchema({
      field: 'collection_at',
      componentProps: {
        onChange: (val) => {
          collectionAt.value = val
          console.log(collectionAt.value)
        }
      }
    })
    const [firstWork] = workList.value
    // 计算最晚的收款日期
    const collectionArr = workList.value.map((item) => item.collection_at)
    const collection = maxBy(collectionArr, (v) => +new Date(v))

    // 计算总应收和本次应收
    const amount_ans = workList.value.map((item) => +item.amount_ans).reduce((total, item) => add(total, +item), 0)
    const amount = workList.value.map((item) => +item.amount).reduce((total, item) => add(total, +item), 0)
    const formData = {
      collection_at: collection,
      processor: firstWork.processor,
      notes: firstWork.notes,
      g_remark: firstWork.g_remark,
      amount_ans: amount_ans,
      amount: amount
    }

    setFieldsValue(formData)

    // setFieldsValue(data.record)

    // 设置work表格和fund表格的列
    setColumns(addWorkListColumnsFn(1, true, updateTableDataRecord))
    setColumnsFund(addFundListColumnsFn(editChange, 'batch'))

    // 塞work表格和fund表格的数据
    setTableData(addCurrentAmount(compDetailWork.value))
    // console.log(data.record)
    console.log(compDetailWork)

    setTableDataFund([])

    changeLoading(false)
  } catch (error) {
    changeLoading(false)
    throw new Error(`${error}`)
  }
})

function createActions(record: EditRecordRow, delStatus: boolean): Recordable[] {
  let editButtonList: ActionItem[] = [
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
      onClick: handleUpdate.bind(null, record),
      ifShow: delStatus
    },
    {
      icon: 'ant-design:delete-outlined',
      color: 'error',
      label: '删除',
      disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
      popConfirm: {
        title: '是否确认删除',
        placement: 'right',
        confirm: handleDelete.bind(null, record)
      },
      ifShow: delStatus
    }
  ]

  if (!record.editable) {
    return editButtonList
  }
  return [
    {
      label: '保存',
      onClick: handleSave.bind(null, record)
    },
    {
      label: '取消',
      onClick: handleEditCancel.bind(null, record)
    }
  ]
}

/** 打开添加流水Modal */
const openAddModal = () => {
  openModal(true, {
    collectionAt: collectionAt.value,
    fundTableData: fundTableData.value,
    isRadio: true
  })
}

/** 编辑 */
async function handleUpdate(record: EditRecordRow) {
  console.log(cloneDeep(record.amount_allot))
  beforeRecord.value = cloneDeep(record)
  console.log(beforeRecord.value.amount_allot, 'beforeRecord')
  currentEditKeyRef.value = cloneDeep(record.key)

  await record.onEdit?.(true)
}

/** 删除 */
function handleDelete(record) {
  deleteTableDataRecordFund(record.key)
  editChange()
}

/** 保存 */
async function handleSave(record: EditRecordRow) {
  const valid = await record.onValid?.()

  if (valid) {
    const pass = record.onEdit?.(false, true)
    if (pass) {
      currentEditKeyRef.value = ''
    }
  }
}

/** 取消编辑 */
async function handleEditCancel(record: EditRecordRow) {
  console.log(cloneDeep(beforeRecord.value.amount_allot), 'beforeRecord')
  console.log(record.amount_allot, 'record')
  await record.onEdit?.(false, true)
  updateTableDataRecordFund(record.key, beforeRecord.value)
  currentEditKeyRef.value = ''
  editChange()
}

/** 取消 */
function handleCancel() {
  closeDrawer()
}

/** 格式提交的数据 */
function formatSubmit() {
  let fundDataSource: Array<Recordable> = []
  let workDataSource: Array<Recordable> = []

  if (getDataSourceFund().length === 0) {
    message.error('请选择需要关联的流水')
    return null
  }
  const [fundData] = getDataSourceFund()
  const { cloneAmountLeft, amount_allot } = fundData
  if (sub(+cloneAmountLeft, +amount_allot) > 0) {
    message.error('请确保流水剩余金额为0！')
    return null
  }

  // getDataSource()有我们不需要的属性,所以需要清除
  fundDataSource = getDataSourceFund().map((item) => {
    let temporary = {}
    for (let colName of getColumnsFund()) {
      if (colName.key !== 'action') {
        temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
      }
    }
    return temporary
  })
  workDataSource = getDataSource().map((item) => {
    let temporary = {}
    for (let colName of getColumns()) {
      if (colName.key !== 'action') {
        temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
      }
    }
    // 返回多一个所属的收款单的id
    temporary.docFundId = item.docFundId
    return temporary
  })

  let totalAmountAllocation = 0
  for (let fundItem of fundDataSource) {
    totalAmountAllocation = add(totalAmountAllocation, Number(fundItem.amount_allot))
  }
  console.log(totalAmountAllocation, Number(getFieldsValue().amount_ans))

  return { work: workDataSource, fund: fundDataSource }
}

const debounceHandleOk = debounce(_handleOk, 200)
/** 点击关联 */
async function _handleOk() {
  try {
    relateLoading.value = true
    setDrawerProps({ confirmLoading: true })
    const newDataSource = formatSubmit()
    const data = await validate()
    console.log(newDataSource)
    console.log(data)

    if (!newDataSource || (newDataSource.work.length === 0 && newDataSource.fund.length === 0)) {
      setDrawerProps({ confirmLoading: false })
      relateLoading.value = false
      return
    }

    const { work, fund } = cloneDeep(newDataSource)

    const submitData = Object.keys(detailsGather.value).map((id: number) => {
      // 筛选当前ID相关的工作项
      const filteredWork = work.filter((item) => +item.docFundId === +id)
      // 提取工作项基本信息
      const curWork = filteredWork.map((item) => ({
        work_id: item.work_id,
        amount: item.amount
      }))
      // 计算工作项金额总和
      const curWorkFund = filteredWork.reduce((total, item) => add(total, Number(item.amount) || 0), 0)
      // 构建提交数据
      return {
        fdoc_id: id,
        amount: compDoc.value[id].amount_ans,
        processor: data.processor,
        g_remark: data.g_remark,
        fund_at: data.fund_at,
        collection_at: collectionAt.value,
        works: curWork,
        funds: fund.map((item) => ({
          id: item.id,
          amount_left: 0,
          amount_allot: curWorkFund
        }))
      }
    })
    // console.log({
    //   fdoc_id: recordData.value.id,
    //   amount: data.amount_ans,
    //   processor: data.processor,
    //   works: newDataSource.work,
    //   funds: newDataSource.fund,
    //   collection_at: collectionAt.value,
    //   g_remark: data.g_remark
    // })
    // console.log(submitData)

    // 关联流水
    await bindFunds({ bindLists: submitData })
    message.success('关联成功！')

    closeDrawer()
    setDrawerProps({ confirmLoading: false })
    emit('success', recordData.value)
    relateLoading.value = false
  } catch (error) {
    message.error('关联失败！')
    setDrawerProps({ confirmLoading: false })
    relateLoading.value = false
    throw new Error(`${error}`)
  }
}
// const handleOk = debounce(_handleOk, 200)

/** 本次分配金额change事件 */
function editChange() {
  let dataSource = getDataSource()
  let fundDataSource = getDataSourceFund()
  // 当前需要操作的current_amount的索引
  let currentIndex = 0
  let fundTotalAllot = 0

  fundDataSource.map((item) => {
    fundTotalAllot = add(fundTotalAllot, item.amount_allot)
  })

  for (let i in dataSource) {
    dataSource[i].current_amount = dataSource[i].amount
    if (fundTotalAllot >= dataSource[i].amount && Number(i) !== dataSource.length - 1) {
      dataSource[currentIndex].current_amount = 0
      fundTotalAllot = sub(fundTotalAllot, dataSource[currentIndex].amount)
      currentIndex++
      console.log(fundTotalAllot)
    }
  }
  console.log(currentIndex, 'currentIndex')
  dataSource[currentIndex].current_amount = sub(dataSource[currentIndex].amount, fundTotalAllot)
}

/** 添加流水回调 */
const handleAddOk = (val: Array<any>) => {
  let fundData: Array<any> = []
  for (let item of val) {
    fundData = getDataSourceFund()
    fundData.push(item)
  }

  setTableDataFund(fundData)
  fundTableData.value = fundData
}

/** 更改本次应收金额 */
async function handChangeamonut({ record, value }) {
  let fundsub = 0
  getDataSource().forEach((item) => {
    fundsub = add(fundsub, item.amount)
  })
  setFieldsValue({ amount_ans: fundsub })
  let fundAmount = 0
  getDataSourceFund().forEach((item) => {
    fundAmount = add(fundAmount, item.amount_allot)
  })
  updateTableDataRecord(record.key, { current_amount: sub(value, fundAmount) })
  // receiptOrderDetails.value.items.work.forEach((item) => {
  //   if (item.work_id == record.work_id) {
  //     item.amount = value
  //   }
  // })
  // const amount_ans_init = receiptOrderDetails.value.items.work.reduce((pre, cur) => {
  //   return add(pre, Number(cur.amount), 2)
  // }, 0)
  // recordData.value.amount_ans = amount_ans_init
  // recordData.value.g_remark = data.g_remark
  // setFieldsValue({ ...recordData.value })

  // setTableData(addCurrentAmount(receiptOrderDetails.value.items.work))
}

function handleUpdateReceivableAmount() {
  const fundArr = getDataSourceFund()
  const [firstFund] = fundArr || []
  const rate = firstFund?.rate || null
  if (!rate) return

  const tableData = cloneDeep(getDataSource())
  for (const item of tableData) {
    const price = mul(+rate, +item.foreign_currency_amount)
    item.amount = roundToDecimalPlaces(price, 2) // 梁工说四舍五入没事
    item.current_amount = roundToDecimalPlaces(price, 2) // 梁工说四舍五入没事
  }
  const amount = tableData.reduce((total, item) => add(+item.amount, total), 0)
  setFieldsValue({ amount_ans: amount })
  setTableData(tableData)
  // const rate = getDataSource()
  editChange()
}

/**
 * 将数值四舍五入到指定小数位数
 * @param {number} value - 要四舍五入的数值
 * @param {number} decimalPlaces - 小数位数
 * @returns {number} 四舍五入后的数值
 */
function roundToDecimalPlaces(value: number, decimalPlaces: number): number {
  const factor = Math.pow(10, decimalPlaces)
  return Math.round(value * factor) / factor
}
</script>

<style lang="less" scoped>
:deep(.ant-picker) {
  width: 100%;
}

:deep(.vben-editable-cell__wrapper) {
  display: inline !important;
}
</style>
