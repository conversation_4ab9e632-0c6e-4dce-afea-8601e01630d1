<template>
  <BasicDrawer @register="registerExamineDrawer" v-bind="$attrs" showFooter width="90%">
    <template #footer>
      <Button @click="handleCancel">取消</Button>
      <!-- <Button type="success" @click="debounceHandlePass" :disabled="!generateBtnStatus" v-if="!isCashier">通过</Button> -->
      <Button type="danger" @click="debounceHandleReject" :disabled="generateBtnStatus">驳回</Button>
    </template>
    <ScrollContainer>
      <Descriptions title="收款单详情" :column="{ xxl: 4, xl: 2, lg: 2, md: 2, sm: 1, xs: 1 }">
        <template v-for="item in columnsFn()" :key="item.dataIndex">
          <DescriptionsItem :label="item.title">
            <!-- 财务审核 -->
            <template v-if="item.dataIndex == 'is_check'">
              <div v-check="record.is_check"> </div>
            </template>

            <!-- 款单类型 -->
            <template v-else-if="item.dataIndex == 'clause'">
              <div v-clause="record.clause"> </div>
            </template>

            <!-- 紧急状态 -->
            <template v-else-if="item.dataIndex == 'urgent_level'">
              <div v-emergency="record.urgent_level ?? 3"></div>
            </template>

            <!-- 款项类型 -->
            <template v-else-if="item.dataIndex == 'payment_type'">
              <div v-ptype="record.payment_type ?? 4"></div>
            </template>
            <template v-else-if="item.dataIndex == 'files'">
              <div v-for="(newVal, index) in record.files" :key="index">
                <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
              >
            </template>
            <!-- 状态 -->
            <template v-else-if="item.dataIndex == 'status'">
              <div v-status="record.status"></div>
            </template>

            <template v-else>
              {{ record[item.dataIndex as string] ? record[item.dataIndex as string] : '-' }}
            </template>
            <!-- 本次已收 -->
          </DescriptionsItem>
        </template>
      </Descriptions>

      <!-- 关联任务列表 -->
      <BasicTable @register="registerWork" :canResize="false" />
      <PreviewFile @register="registerModal" />
    </ScrollContainer>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { columnsFn, detalisColumns } from '../datas/datas'
import { getReceiptOrderDetails, getsetReject } from '/@/api/financialDocuments/receiptOrder'
import { vStatus } from '../datas/fn'
import { vPtype, vEmergency, vClause } from '../../common'

import { ref } from 'vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable } from '/@/components/Table'
import { Button, DescriptionsItem, Descriptions, message } from 'ant-design-vue'
import { ScrollContainer } from '/@/components/Container'
import { cloneDeep, debounce } from 'lodash-es'
import { useModal } from '/@/components/Modal'
import { useMessage } from '/@/hooks/web/useMessage'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { createImgPreview } from '/@/components/Preview/index'

const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()
const emit = defineEmits(['success', 'register', 'registerExamineDrawer'])

const generateBtnStatus = ref(true)
const receiptOrderDetails = ref()
const record: Recordable = ref({})
const isCashier = ref(false)
let newDetalisColumns: Array<any> = []

/** 注册 Work 表格 */
const [registerWork, { setTableData, getSelectRows, clearSelectedRowKeys, setColumns, setSelectedRowKeys }] = useTable({
  showIndexColumn: false,
  rowKey: 'work_id',
  pagination: true,
  rowSelection: {
    // type: 'checkbox',
    onChange: handleChange
  }
})

/** 选中 */
async function handleChange(data, record) {
  let columns = cloneDeep(newDetalisColumns)
  const workids = receiptOrderDetails.value?.items.work.map((item) => item.work_id) ?? []
  if (workids.length !== 1) {
    if (record.length > 0 && record[0].type == 7 && data.length == 1) {
      setSelectedRowKeys(workids)
      data = workids
    } else if (record.length > 0 && record[0].type == 7 && data.length < workids.length) {
      setSelectedRowKeys([])
      data = []
    }
  }
  columns.splice(2, 0, {
    title: '驳回原因',
    dataIndex: 'reject_remark',
    editComponent: 'Textarea',
    editRow: true,
    width: 250,
    resizable: true,
    editRender: (data) => {
      if (!data.text) {
        return '-'
      } else {
        return data.text
      }
    }
  })
  if (data.length !== 0) {
    await setColumns(columns)
    // 解决第一次选择驳回原因不展开的问题
    await record[record.length - 1].onEdit?.(true, false)
    receiptOrderDetails.value.items?.work.map(async (item) => {
      if (data.includes(item.work_id)) {
        await item.onEdit?.(true, false)
      } else {
        await item.onEdit?.(false, true)
        item.reject_remark = ''
      }

      return item
    })
  } else {
    setColumns(newDetalisColumns)
  }

  // setColumns()
  if (getSelectRows().length == 0) {
    generateBtnStatus.value = true
  } else {
    generateBtnStatus.value = false
  }
}

/** 注册抽屉，刚进来会触发 */
const [registerExamineDrawer, { changeLoading, closeDrawer }] = useDrawerInner(async (data) => {
  try {
    changeLoading(true)
    clearSelectedRowKeys()

    isCashier.value = data.isCashier
    newDetalisColumns = []
    record.value = data.record
    receiptOrderDetails.value = await getReceiptOrderDetails({ id: data.record.id })
    console.log(receiptOrderDetails.value)

    cloneDeep(detalisColumns(receiptOrderDetails.value.items.work[0]?.type, !isCashier.value)).forEach((item, index) => {
      if (item.dataIndex !== 'current_amount') {
        newDetalisColumns.push(item)
      }
      if (record.value.fund && record.value.fund.length === 0 && item.dataIndex == 'amount') {
        newDetalisColumns[index].title = '本次应收金额'
      }
    })

    setColumns(newDetalisColumns)
    setTableData(receiptOrderDetails.value.items.work)
    changeLoading(false)
  } catch (error) {
    changeLoading(false)
    throw new Error(`${error}`)
  }
})

const debounceHandleReject = debounce(handleReject, 500)

/** 驳回 */
async function handleReject() {
  try {
    const tabledata = getSelectRows()
    const selectRowsData = ref({})
    const typerejrct = tabledata.some((item) => item.reject_remark != null && item.reject_remark !== '')
    if (tabledata[0].type == 7) {
      if (!typerejrct) {
        return message.error('请至少填写一条驳回原因！')
      }
      selectRowsData.value = tabledata.map((item) => {
        return { work_id: item.work_id, reject_remark: item.reject_remark }
      })
    } else {
      selectRowsData.value = tabledata.map((item) => {
        if (!item.reject_remark) {
          throw new Error('请填写驳回原因！')
        }
        return { work_id: item.work_id, reject_remark: item.reject_remark }
      })
    }

    await getsetReject({ works: selectRowsData.value, id: record.value.id, is_check: 2 })
    emit('success')
    message.success('驳回成功！')
    closeDrawer()
  } catch (error: any) {
    message.error(error.message)
    // throw new Error(`${error}`)
  }
}

/** 取消 */
function handleCancel() {
  closeDrawer()
}

// 预览
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  console.log(prefix)
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>

<style lang="less" scoped></style>
