import { render } from 'vue'
import { useRender } from '/@/components/Table/src/hooks/useRender'

export const mapStatus = {
  0: { text: '未收款' },
  1: { text: '已收款', color: 'success' },
  3: { text: '待提现', color: '' }
}

/** 判断状态 */
export function determineStatus(status): any {
  if (status === 'options') {
    return [
      { label: '未收款', value: 0 },
      { label: '已收款', value: 1 },
      { label: '待提现', value: 3 }
    ]
  } else {
    return useRender.renderTag(mapStatus[status]?.text, mapStatus[status]?.color)
  }
}

/** 判断状态自定义指令 */
export const vStatus = {
  beforeUpdate(el, binding) {
    const vnode = determineStatus(binding.value)
    render(vnode, el)
  }
}

/** 判断类型 */
export function determineType(type) {
  const mapValue = {
    3: { text: '销售订单', color: '' },
    7: { text: '其他收入单', color: '' },
    // 9: { text: '退货单', color: '' },
    11: { text: '退款单', color: '' },
    27: { text: '费用单', color: '' }
  }
  return useRender.renderTag(mapValue[type]?.text, mapValue[type]?.color)
}
