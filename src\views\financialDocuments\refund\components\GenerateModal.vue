<template>
  <BasicModal @register="registerModal" width="70%" title="请填写以下信息" :bodyStyle="{ height: '600px' }" @ok="handleOk">
    <BasicForm @register="registerForm">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template></BasicForm
    >
    <Card title="本次退款金额">
      <template v-for="(item, index) in cloneDeepSelectRowsData" :key="item.strid">
        <Descriptions>
          <DescriptionsItem :label="`退款单号 : ${item.strid}`" :labelStyle="{ color: '#909399', width: '30%' }">
            <Form :model="item" ref="formRef" layout="inline" :rules="formRulesFn(index)">
              <FormItem name="amount" label="金额">
                <InputNumber :formatter="(value) => `¥${value}`" v-model:value="item.amount" disabled />
              </FormItem>
              <FormItem name="payment_type" label="款项类型">
                <RadioGroup v-model:value="item.payment_type" :options="radioOpt" defaultValue="3" disabled />
              </FormItem>
            </Form>
          </DescriptionsItem>
        </Descriptions>
      </template>
    </Card>
  </BasicModal>
</template>

<script setup lang="ts">
import { addBatch } from '/@/api/financialDocuments/receiptOrder'

import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, Rule, useForm } from '/@/components/Form'
import { Card, Descriptions, DescriptionsItem, InputNumber, message, Form, FormItem, UploadFile, Upload, RadioGroup } from 'ant-design-vue'
import { ref, watch } from 'vue'
import { cloneDeep, isNull, isUndefined } from 'lodash-es'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { PlusOutlined } from '@ant-design/icons-vue'
import { commonFileUpload } from '/@/api/commonUtils/upload'

const emit = defineEmits(['register', 'handleSubmit', 'success'])

const cloneDeepSelectRowsData = ref<Array<any>>([])
const selectRowsData = ref<Array<any>>([])
const formRef = ref()
const deptID = ref()
const radioOpt = [
  {
    label: '定金',
    value: 1
  },
  {
    label: '最后一笔款',
    value: 2
  },
  {
    label: '全款',
    value: 3
  }
]

/** 校验 */
const formRulesFn = (index): any => {
  return {
    amount: [
      {
        required: true,
        // trigger: 'change',
        validator: async (_rule: Rule, value: string) => {
          if (Number(value) <= 0) {
            return Promise.reject('输入的收款金额必须大于0！')
          }
          if (Number(value) > Number(selectRowsData.value[index].amount)) {
            return Promise.reject('输入的收款金额不能大于当前退款订单的金额！')
          }
          return Promise.resolve()
        }
      }
    ],
    payment_type: [
      {
        required: true,
        trigger: 'change'
      }
    ]
  }
}

/** 注册Form */
const [registerForm, { validate, resetFields, setFieldsValue }] = useForm({
  labelWidth: 150,
  schemas: [
    {
      field: 'collection_at',
      label: '收款日期',
      component: 'DatePicker',
      componentProps: {
        valueFormat: 'YYYY-MM-DD'
      },
      colProps: {
        span: 7
      },
      required: true
    },
    {
      field: 'notes',
      label: '对方付款人',
      component: 'Input',
      colProps: {
        span: 7
      },
      required: true
    },
    {
      field: 'notes_account',
      label: '对方付款人账号',
      component: 'Input',
      colProps: {
        span: 7
      },
      required: true
    },
    {
      field: 'notes_bank',
      label: '对方付款银行/平台',
      component: 'Input',
      colProps: {
        span: 7
      },
      required: true
    },
    {
      field: 'account',
      label: '收款账号',
      component: 'Input',
      colProps: {
        span: 7
      },
      required: true
    },
    {
      field: 'account_name',
      label: '收款银行',
      component: 'Input',
      colProps: {
        span: 7
      },
      required: true
    },
    // {
    //   field: 'payment_type',
    //   label: '款项类型',
    //   component: 'RadioGroup',
    //   defaultValue: 3,
    //   componentProps: {
    //     options: [
    //       {
    //         label: '定金',
    //         value: 1
    //       },
    //       {
    //         label: '最后一笔款',
    //         value: 2
    //       },
    //       {
    //         label: '全款',
    //         value: 3
    //       }
    //     ],
    //     disabled: true
    //   },
    //   colProps: {
    //     span: 11
    //   },
    //   required: true
    // },
    {
      field: 'g_remark',
      label: '携带备注',
      component: 'InputTextArea',
      componentProps: {
        autosize: { minRows: 3, maxRows: 6 }
      },
      colProps: {
        span: 7
      }
    },
    {
      field: 'files',
      label: '附件上传',
      required: true,
      component: 'Upload',
      slot: 'Files',
      // helpMessage: '附件必须放水单，否则财务无法识别对应哪一笔流水，较大概率重填',
      colProps: {
        span: 7
      }
    }
  ],
  showSubmitButton: false,
  showResetButton: false,
  actionColOptions: {
    span: 24
  }
})

/** 注册Modal */
const [registerModal, { closeModal, changeOkLoading }] = useModalInner((data) => {
  console.log(data)
  deptID.value = data.selectRowsData[0].dept_id
  filesList.value = []
  resetFields()
  selectRowsData.value = data.selectRowsData
  cloneDeepSelectRowsData.value = cloneDeep(data.selectRowsData).map((item) => ({
    ...item,
    log_amount: item.amount,
    payment_type: 3
  }))
  console.log(cloneDeepSelectRowsData.value)
})

/** 点击确认 */
const handleOk = async () => {
  try {
    changeOkLoading(true)
    let data = await validate()
    const isEmptyFile = data.files.some((item) => isUndefined(item) || isNull(item))
    if (isEmptyFile) {
      changeOkLoading(false)
      return message.error('请等待文件上传完成，再提交！')
    }
    let works: Array<{ work_id: number; amount: number }> = []

    // 校验
    for (let item of formRef.value) {
      await item.validate()
    }

    cloneDeepSelectRowsData.value.forEach((item) => {
      works.push({ work_id: item.work_id, amount: item.amount, payment_type: item.payment_type })
      data.client_id = item.client_id
    })
    const mes = ref()
    try {
      mes.value = await addBatch({ ...data, works, clause: 6, dept_id: deptID.value })
      console.log({ ...data, works })
      emit('success')
      closeModal()
      changeOkLoading(false)
    } finally {
      if (mes.value.type == 'error') {
        message.error('生成收款单失败！')
      } else {
        message.success('成功生成收款单！')
      }
    }
  } catch (err) {
    changeOkLoading(false)
    throw new Error(err)
  }
}

//附件
const filesList = ref<UploadFile[]>([])

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val.map((item) => item.url) })
  }
)

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    console.log(file)
    const curFile = filesList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({
      files: filesList.value.map((item) => item.url)
    })
  } catch (err) {
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}
</script>
<style lang="less" scoped>
:deep(.ant-picker) {
  width: 100%;
}

:deep(.ant-descriptions-item-content) {
  display: inline-block;
}

:deep(.ant-descriptions-item) {
  padding: 0;
}
</style>
