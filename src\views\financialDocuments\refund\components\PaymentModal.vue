<template>
  <BasicModal @register="registerModal" title="请确认收款信息" @ok="handleOk">
    <template #insertFooter>
      <Button v-if="!clientShow" type="success" @click="handclient">修改客户信息</Button>
      <Button v-else type="success" @click="saveclient">保存客户信息</Button>
    </template>
    <BasicForm @register="registerForm">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { Button, message, Upload, UploadFile } from 'ant-design-vue'
import { FromschemasFn } from '../datas/modal'
import { ref } from 'vue'
import { getClient, updateClient } from '/@/api/baseData/client'
import { useMessage } from '/@/hooks/web/useMessage'
import { addBatch } from '/@/api/financialDocuments/paymentOrder'
import { PlusOutlined } from '@ant-design/icons-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'

const emit = defineEmits(['register', 'handleSubmit', 'success'])
const clientShow = ref(false)
const { createConfirm, createMessage } = useMessage()
/** 注册from */
const [registerForm, { updateSchema, setFieldsValue, validate }] = useForm({
  labelWidth: 100,
  schemas: FromschemasFn(true),
  showSubmitButton: false,
  showResetButton: false,
  baseColProps: { span: 24 }
})

// //客户id
// const init_client_id = ref()
const PropsData = ref<any>()
const filesList = ref<UploadFile[]>([])
/** 注册Modal */
const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(async (data) => {
  console.log(data)
  try {
    changeLoading(true)
    filesList.value = []
    PropsData.value = data.selectedRow
    const { items } = await getClient({ id: data.selectedRow[0].client_id })

    setFieldsValue(items[0])
    clientShow.value = false
    await updateSchema(FromschemasFn(true))
  } finally {
    changeLoading(false)
  }
})

/** 点击确认 */
async function handleOk() {
  try {
    await changeLoading(true)
    await changeOkLoading(true)
    let works: Array<work> = []
    PropsData.value.forEach((item) => {
      works.push({
        client_name: item.client_name,
        client_id: item.client_id,
        amount: item.amount,
        work_id: item.work_id
      })
    })
    const res = ref()

    const valid = await validate()
    if (!valid?.files || valid.files.length === 0) {
      changeLoading(false)
      changeOkLoading(false)
      return createMessage.error('附件为必填项')
    }
    createConfirm({
      title: '生成付款单',
      content: '确定将选中的销售退款单生成付款单吗?',
      iconType: 'warning',
      onOk: async () => {
        res.value = await addBatch({
          works,
          clause: 5,
          dept_id: PropsData.value[0].dept_id,
          payment_type: 3,
          g_remark: valid.g_remark,
          files: valid.files
        })
        if (res.value.news == 'success') {
          await closeModal()
          emit('success')
          return
        }
      }
    })
    changeOkLoading(false)
    changeLoading(false)
  } catch (err) {
    changeOkLoading(false)
    changeLoading(false)
    throw new Error(`${err}`)
  }
}
//修改
async function handclient() {
  clientShow.value = true
  await updateSchema(FromschemasFn(false))
}
//保存
async function saveclient() {
  const res = ref()
  try {
    const result = await validate()
    const params = {
      ...result,
      account: result.account,
      account_name: result.account_name,
      contact: result.contact,
      email: result.email || '',
      location: result.location || '',
      remark: result.remark || '',
      id: PropsData.value[0].client_id,
      name: result.name
    }
    console.log(result)
    res.value = await updateClient(params)
    console.log(res.value)

    if (res.value.msg == 'success') {
      // message.success('客户信息保存成功')
      clientShow.value = false
      await updateSchema(FromschemasFn(true))
      return
    }
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    if (res.value.msg == 'error') {
      message.error('客户信息保存失败')
    }
  }
}

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    console.log(file)
    changeOkLoading(true)
    const curFile = filesList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({
      files: filesList.value.map((item) => item.url)
    })
    // 判断是否所有的文件都已经上传成功，如果有uploading状态的就不修改按钮状态
    const isAllDone = filesList.value.every((item) => item.url)
    if (isAllDone) {
      changeOkLoading(false)
    }
  } catch (err) {
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}
</script>
