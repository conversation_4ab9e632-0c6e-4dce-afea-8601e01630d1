<template>
  <div>
    <BasicDrawer @register="refundDrawer" width="90%" destroyOnClose show-footer>
      <template #footer>
        <Button @click="hanldeCancel">取消</Button>
        <Button type="primary" :disabled="currentEditKeyRef == '' ? false : true" @click="handleSubmit">确定</Button>
      </template>
      <BasicForm @register="registerForm" />

      <template v-if="details.is_check2 === 2 && details.reject_remark">
        <Row>
          <Col class="w-110px text-right text-red-500"> <WarningFilled /><span class="ml-2">驳回原因：</span> </Col>
          <Col class="text-red-500"> {{ details.reject_remark }} </Col>
        </Row>
      </template>

      <Divider orientation="left">退款款单</Divider>
      <Alert message="仅在不退款时,可勾选确定并按+号展开编辑金额" type="info" show-icon />
      <BasicTable @register="registerTable" :data-source="paymentList">
        <template #expandedRowRender="{ record, index }">
          <BasicTable
            :showIndexColumn="false"
            :pagination="false"
            :actionColumn="{ width: 110, title: '操作', dataIndex: 'action' }"
            :data-source="record.fund"
            :columns="handlecolumnsFn(record.fund)"
            :can-resize="false"
          >
            <template #bodyCell="{ column, record: childrecord, index: childrIndex }">
              <template v-if="column.key === 'action'">
                <TableAction :actions="createActions(childrecord, index, childrIndex)" />
              </template>
            </template>
          </BasicTable>
        </template>
      </BasicTable>
    </BasicDrawer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { WarningFilled } from '@ant-design/icons-vue'
import { Alert, Button, message, Row, Col, Divider } from 'ant-design-vue'
import { cloneDeep, debounce } from 'lodash-es'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicTable, useTable, EditRecordRow, ActionItem, TableAction } from '/@/components/Table'

import { getFormSchemas, childColumns, refundshall } from '../datas/datas'
import { paymentList, tableColumn } from '../datas/fn'
import { createRefund, editRefund, getRefundDetail } from '/@/api/financialDocuments/refund'
import defaultUser from '/@/utils/erp/defaultUser'
import { add } from '/@/utils/math'

const pathname = window.location.pathname
const emit = defineEmits(['success', 'register', 'refundDrawer'])
const propsData = ref<any>()
const currentEditKeyRef = ref('')
const details = ref({})

/** 注册From */
const [registerForm, { setFieldsValue, validate, getFieldsValue, resetSchema, resetFields }] = useForm({
  baseColProps: { span: 12 },
  showActionButtonGroup: false,
  colon: true,
  labelAlign: 'right',
  labelCol: { style: { width: '100px', marginLeft: '10px' } }
})

/** 注册表格 */
const [registerTable, { getSelectRowKeys, setLoading, clearSelectedRowKeys, setColumns, setSelectedRowKeys }] = useTable({
  pagination: false,
  showIndexColumn: false,
  scroll: { y: '65vh' },
  rowKey: 'id',
  rowSelection: {
    fixed: 'left',
    type: 'checkbox',
    getCheckboxProps: () => {
      if (getFieldsValue()?.type === 1 || propsData.value?.type === 'detail') {
        return { disabled: true }
      } else {
        return { disabled: false }
      }
    }
  }
})

function createActions(record: EditRecordRow, index: number, childrIndex: number): Recordable[] {
  let editButtonList: ActionItem[] = [
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleUpdate.bind(null, record),
      disabled: propsData.value.type === 'detail' || getFieldsValue().type == 1
    }
  ]

  if (!record.editable) {
    return editButtonList
  }
  return [
    {
      label: '保存',
      onClick: handleSave.bind(null, record, index, childrIndex)
    },
    {
      label: '取消',
      onClick: handleEditCancel.bind(null, record)
    }
  ]
}

/** 抽屉进来时触发 */
const [refundDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
  await resetFields()
  paymentList.value = []
  propsData.value = cloneDeep(data)
  console.log(propsData.value, 'props')
  currentEditKeyRef.value = ''
  // 控制抽屉下方按钮的显示
  // setDrawerProps({ showFooter: ['edit', 'add'].includes(data.type) })

  if (data.type == 'add') {
    resetSchema(getFormSchemas(data.type, setLoading, setColumns, clearSelectedRowKeys, setSelectedRowKeys, 1, 1))
    setColumns(tableColumn(1))
    setFieldsValue({ applicant: defaultUser!.userId })
  } else {
    let detailsData = await getRefundDetail({ id: propsData.value.record.id })
    details.value = detailsData.items
    setColumns(tableColumn(detailsData.items.order))
    resetSchema(
      getFormSchemas(
        data.type,
        setLoading,
        setColumns,
        clearSelectedRowKeys,
        setSelectedRowKeys,
        detailsData.items.order,
        detailsData.items.type,
        detailsData.items.fdocLists
      )
    )

    if (detailsData.items.order === 1) {
      setFieldsValue({
        ...detailsData.items,
        sale_work_id: detailsData.items.parent_id
      })
    } else {
      setFieldsValue({
        ...detailsData.items,
        purchase_work_id: detailsData.items.parent_id
      })
    }
  }
})

/** 编辑 */
async function handleUpdate(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  await record.onEdit?.(true, false)
}

/** 保存 */
async function handleSave(record: EditRecordRow, index: number, childrIndex: number) {
  const valid = await record.onValid?.()
  if (valid) {
    const pass = record.onEdit?.(false, true)
    if (pass) {
      currentEditKeyRef.value = ''
    }
  }

  // 这里只能用这种办法，不知道为什么用 #Payment={'model,field'}绑定的值拿不到本次回退金额的值
  paymentList.value[index].fund[childrIndex] = cloneDeep(record)
}

/** 取消编辑 */
async function handleEditCancel(record: EditRecordRow) {
  await record.onEdit?.(false, false)
  currentEditKeyRef.value = ''
}

/** 确定 */
const handleSubmit = debounce(_handleSubmit, 200)
async function _handleSubmit() {
  try {
    setDrawerProps({ confirmLoading: true })
    const value = await validate()
    console.log(refundshall.value)

    let { applicant, inCharge, amount, type, order, foreign_currency_amount, contracting_party } = value

    if (getSelectRowKeys().length == 0 && type == 2) {
      message.error('请勾选退款款单!')
      return
    }
    if (amount == 0) {
      message.error('退款金额不能为0!')
      return
    }
    // 获取每个选中行的所有fund数组中的金额，根据货币类型决定计算哪个字段，再将金额相加，判断相加后是否等于0，如果大于0（其中有一条数据填写了退款金额）则通过
    const hasPrice = paymentList.value
      .filter((item) => getSelectRowKeys()?.includes(item.id))
      .map((item) => {
        const totalAmount = item.fund.reduce((acc, fundItem) => {
          // 当货币不是人民币时，计算 foreign_currency_amount 字段的合计
          if (pathname !== '/s/' && !['人民币', 'CNY'].includes(fundItem.from_currency)) {
            return acc + (Number(fundItem.foreign_currency_amount) || 0)
          } else {
            // 当是人民币时，计算 amount_fund 字段的合计
            return acc + (Number(fundItem.amount_fund) || 0)
          }
        }, 0)
        return totalAmount
      })
      .every((item) => +item > 0)
    if (!hasPrice) return message.error('勾选的数据至少要有一条的本次退款金额大于0')
    let fdocLists = paymentList.value
      .map((item) => {
        if (getSelectRowKeys()?.includes(item.id)) {
          // for (let child of item.fund) {
          //   if (Number(child.amount_fund) == 0) {
          //     throw new Error('本次回退金额不能为0')
          //   }
          // }
          return {
            id: item.id,
            funds: item.fund.map((fundItem) => {
              return {
                fund_id: fundItem.fund_id,
                amount_fund: fundItem.amount_fund,
                foreign_currency_amount: pathname == '/s/' ? undefined : fundItem.foreign_currency_amount
              }
            })
          }
        }
      })
      .filter((item) => item)

    for (const item of paymentList.value) {
      for (const val of fdocLists) {
        console.log(item, val)
        if (item.id == val.id) {
          // 根据货币类型决定计算哪个字段的合计
          const totalAmount = val.funds.reduce((a, b) => {
            // 当货币不是人民币时，计算 foreign_currency_amount 字段的合计
            if (pathname !== '/s/' && !['人民币', 'CNY'].includes(b.from_currency)) {
              return add(a, b.foreign_currency_amount || 0, 2)
            } else {
              // 当是人民币时，计算 amount_fund 字段的合计
              return add(a, b.amount_fund || 0, 2)
            }
          }, 0)

          if (Number(item.amount_fdocw) < totalAmount) {
            return message.error(`${item.strid} 的退款总金额不能大于任务关联款单金额`)
          }
        }
      }
    }

    let data = {
      work_id: value.sale_work_id ?? value.purchase_work_id,
      applicant,
      inCharge,
      amount: type == 2 ? undefined : amount,
      type,
      order,
      fdocLists,
      contracting_party,
      ...(() => {
        // 提取公共的父级工单判断逻辑
        const detailsValue = details.value as any
        const isParentWork = value.sale_work_id == detailsValue?.parent_id || value.purchase_work_id == detailsValue?.parent_id
        if (pathname === '/s/') {
          return {}
        }
        return {
          exchange_rate:
            refundshall.value?.exchange_rate !== undefined && refundshall.value?.exchange_rate !== null
              ? refundshall.value?.exchange_rate
              : isParentWork
              ? detailsValue?.exchange_rate
              : '1.000000',
          currency:
            refundshall.value?.currency !== undefined && refundshall.value?.currency !== null
              ? refundshall.value?.currency
              : isParentWork
              ? detailsValue?.currency
              : '人民币'
        }
      })(),
      foreign_currency_amount: foreign_currency_amount ?? 0
    }
    console.log('data', data)

    if (propsData.value.type !== 'detail') {
      propsData.value.type === 'edit' ? await editRefund({ ...data, id: propsData.value.record.id }) : await createRefund(data)
      closeDrawer()
      message.success(propsData.value.type === 'edit' ? '编辑成功!' : '新增成功!')
      emit('success')
    } else {
      closeDrawer()
    }
  } catch (e: any) {
    console.log(e)
    setDrawerProps({ confirmLoading: false })
  }
}

/** 取消 */
function hanldeCancel() {
  closeDrawer()
}

function handlecolumnsFn(fund: any[]) {
  const iscurrency = fund.some((item: any) => !['人民币', 'CNY'].includes(item.from_currency))
  const newcolumns = childColumns()
  if (pathname !== '/s/') {
    newcolumns.forEach((item: any) => {
      const dataIndex = item.dataIndex as string
      if (['fg_amount_left', 'rate', 'from_currency', 'foreign_currency_amount'].includes(dataIndex)) {
        item.defaultHidden = !iscurrency // 简化布尔值表达式
      } else if (dataIndex === 'amount_fund') {
        item.editDynamicDisabled = iscurrency
      }
    })
  } else {
    return newcolumns
  }
  return newcolumns
}
</script>
