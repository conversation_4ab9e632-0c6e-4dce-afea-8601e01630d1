import { BasicColumn, FormSchema } from '/@/components/Table'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { getClientList, getErpSupplier, getWorkList } from '/@/api/commonUtils'
import { auditStatus, checkStatus, paymentType, tableColumn, typeOfRefundNote } from './fn'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { sub } from '/@/utils/math'
import { paymentList } from './fn'
import { getPaymentList } from '/@/api/financialDocuments/refund'
import { cloneDeep } from 'lodash-es'
import { Rule } from 'ant-design-vue/lib/form/interface'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { useI18n } from '/@/hooks/web/useI18n'
import { GET_STATUS_SCHEMA } from '/@/const/status'
import Decimal from 'decimal.js'
import { cpgetList } from '/@/api/erp/purchaseOrder'
import { ref } from 'vue'

const pathname = window.location.pathname
export const refundshall = ref()

export const mapOrder = {
  1: { label: '销售退款', value: 1, color: 'pink' },
  2: { label: '采购退款', value: 2, color: 'orange' }
}

export const paymentStatusList = {
  0: { label: '未执行', value: 0, color: 'default' },
  1: { label: '已执行', value: 1, color: 'blue' },
  2: { label: '已完成', value: 2, color: '#87d068' }
}

export const mapType = {
  1: { label: '退款', value: 1, color: '#f90' },
  2: { label: '不退款', value: 2, color: '#2db7f5' }
}

const { tm } = useI18n()

export const fundManageColumns: BasicColumn[] = [
  {
    title: '退款单号',
    dataIndex: 'strid',
    width: 250,
    resizable: true
  },
  {
    title: '退款单类型',
    dataIndex: 'order',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return typeOfRefundNote(text)
    }
  },
  {
    title: '单号',
    dataIndex: 'type_strid',
    width: 200,
    resizable: true,
    customRender: ({ text }) => {
      return text ?? '-'
    }
  },
  {
    title: '消费订单或费用订单',
    dataIndex: 'is_consume',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      const map = tm('tag.tagColor')
      return map[text] ? useRender.renderTag(map[text].label, map[text].color) : text
    }
  },
  {
    title: '款项类型',
    dataIndex: 'type',
    resizable: true,
    helpMessage: '款项类型为不退款的不可生成付款单！',
    width: 120,
    customRender: ({ text }) => {
      return paymentType(text)
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ text }) => {
      return auditStatus(text)
    }
  },
  {
    title: '财务审批',
    dataIndex: 'is_check2',
    width: 120,
    customRender({ text }) {
      return checkStatus(text)
    }
  },
  {
    title: '退款金额',
    dataIndex: 'amount',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return formateerNotCurrency.format(text) ?? '0.00'
    }
  },
  {
    title: '供应商',
    width: 120,
    dataIndex: 'supplier_name',
    resizable: true,
    customRender: ({ text }) => {
      return text ?? '-'
    }
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return text ?? '-'
    }
  },
  {
    title: '客户名称',
    dataIndex: 'client_name',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return text ?? '-'
    }
  },

  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return text ?? '-'
    }
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return text ?? '-'
    }
  },
  {
    title: '审核人名称',
    dataIndex: 'auditor_name',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return text ?? '-'
    }
  },
  {
    title: '申请人名称',
    dataIndex: 'applicant_name',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return text ?? '-'
    }
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return text ?? '-'
    }
  },
  {
    dataIndex: 'check_at',
    title: '审核日期',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return text ?? '-'
    }
  }
]

/** 抽屉子表格 */
export function childColumns(): BasicColumn[] {
  return [
    {
      title: 'ID',
      dataIndex: 'fund_id',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ?? '-'
      }
    },
    {
      title: '流水单号',
      dataIndex: 'fund_strid',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ?? '-'
      }
    },
    {
      title: '付款资金资料',
      dataIndex: 'from_plaform',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ?? '-'
      }
    },
    {
      title: '收款资金资料',
      dataIndex: 'to_plaform',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ?? '-'
      }
    },
    {
      title: '手续费',
      dataIndex: 'fee',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ?? '-'
      }
    },
    {
      title: '币种',
      dataIndex: 'from_currency',
      width: 100,
      resizable: true,
      defaultHidden: true
    },
    {
      title: '汇率',
      dataIndex: 'rate',
      width: 100,
      resizable: true,
      defaultHidden: true
    },
    {
      title: '金额',
      dataIndex: 'amount',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text) : '-'
      }
    },
    {
      title: '剩余金额',
      dataIndex: 'amount_left',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text) : '-'
      }
    },
    {
      title: '剩余外币金额',
      dataIndex: 'fg_amount_left',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text) : '-'
      },
      defaultHidden: true
    },
    {
      title: '可回退金额',
      dataIndex: 'amount_allot',
      resizable: true,
      width: 100,
      customRender: ({ record }) => {
        return sub(record.amount_allot, record.amount_fund)
          ? formateerNotCurrency.format(sub(record.amount_allot, record.amount_fund))
          : '0.00'
      }
    },
    {
      title: '已回退金额',
      dataIndex: 'total_amount_fund',
      resizable: true,
      width: 100,
      customRender: ({ text }) => {
        return text ?? '0.00'
      }
    },
    {
      title: '本次回退金额',
      dataIndex: 'amount_fund',
      editRow: true,
      editComponent: 'InputNumber',
      width: 100,
      editComponentProps: ({ record }) => {
        return {
          precision: 2,
          max: record.amount_allot,
          min: 0.01,
          onChange: (val) => {
            console.log(val)
          }
        }
      },
      edit: true
    },
    {
      title: '本次回退外币金额',
      dataIndex: 'foreign_currency_amount',
      editRow: true,
      editComponent: 'InputNumber',
      width: 100,
      editComponentProps: ({ record }) => {
        return {
          precision: 2,
          max: record.fg_amount,
          min: 0.01,
          onChange: (val) => {
            console.log(val)
            record.amount_fund = new Decimal(val).mul(record.rate).toDecimalPlaces(2).toNumber()
          }
        }
      },
      edit: true,
      defaultHidden: true
    }
  ]
}

/** From */
export function getFormSchemas(
  type: 'add' | 'edit' | 'detail',
  setLoading: Function,
  setColumns: Function,
  clearSelectedRowKeys: Function,
  setSelectedRowKeys: Function,
  order: number,
  isRefundProps: number, // 是否退款
  detailsFdoc: Recordable = []
): FormSchema[] {
  return [
    {
      field: 'id',
      label: 'ID',
      component: 'InputNumber',
      ifShow: type === 'edit',
      show: false
    },
    {
      field: 'type',
      label: '是否退款',
      component: 'RadioButtonGroup',
      componentProps: ({ formActionType }) => {
        return {
          options: [
            { label: '退款', value: 1 },
            { label: '不退款', value: 2 }
          ],
          onChange: (val: number) => {
            // 控制表格是否可选中和子表格内是否有操作栏
            if (formActionType) {
              isRefundProps = val
              const { setFieldsValue } = formActionType
              setFieldsValue({ amount: '', foreign_currency_amount: '' })
              clearSelectedRowKeys()
              if (val == 2 && detailsFdoc.length !== 0) {
                setSelectedRowKeys(
                  detailsFdoc?.map((item) => {
                    return item.fdoc_id
                  })
                )
              }
            }
          }
        }
      },
      dynamicDisabled: ['detail'].includes(type),
      defaultValue: 1,
      required: true
    },
    {
      field: 'order',
      label: '退款类型',
      component: 'Select',
      defaultValue: 1,
      componentProps: ({ formActionType }) => {
        return {
          options: [
            { label: '销售退款', value: 1 },
            { label: '采购退款', value: 2 }
          ],
          disabled: ['edit', 'detail'].includes(type),
          onChange: (val: number) => {
            if (formActionType) {
              console.log(val, 'val')
              const { resetSchema, setFieldsValue } = formActionType
              paymentList.value = []
              setColumns(tableColumn(val))
              resetSchema(
                getFormSchemas(type, setLoading, setColumns, clearSelectedRowKeys, setSelectedRowKeys, val, isRefundProps, detailsFdoc)
              )
              setFieldsValue(val == 1 ? { sale_work_id: undefined } : { purchase_work_id: undefined })
            }
          }
        }
      },
      dynamicDisabled: ['detail'].includes(type),
      required: true
    },
    {
      field: 'sale_work_id',
      label: '销售退款任务',
      itemProps: {
        validateTrigger: 'blur'
      },
      component: 'PagingApiSelect',
      componentProps: ({ formModel }) => ({
        resultField: 'items',
        api: getWorkList,
        params: {
          status: [1, 2, 3, 4, 5, 15],
          types: [3, 27], // 海哥让改的
          auth: 4,
          from: 'refund'
        },
        searchMode: true,
        pagingMode: true,
        returnParamsField: 'id',
        searchParamField: 'source_uniqid',
        selectProps: {
          allowClear: true,
          fieldNames: { key: 'id', value: 'id', label: 'source_uniqid' },
          optionFilterProp: 'source_uniqid',
          showSearch: true,
          placeholder: '请选择'
        },

        onChange: async (val: number, shall) => {
          try {
            setLoading(true)
            if (!val) {
              paymentList.value = []
            } else {
              formModel.currency = shall?.currency
              formModel.exchange_rate = shall?.exchange_rate
              refundshall.value = shall
              const params = { work_id: val }
              paymentList.value = await getPaymentList(params)

              if (type == 'add' || isRefundProps == 1) {
                console.log('123')
                paymentList.value.forEach((paymentItem) => {
                  paymentItem.fund.forEach((fundItem) => {
                    console.log(fundItem)
                    fundItem.amount_allot = sub(fundItem.amount_allot, fundItem.total_amount_fund)
                    fundItem.amount_fund = 0
                    fundItem.foreign_currency_amount = 0
                  })
                })
              }
              console.log(detailsFdoc)
              // 这里需要拿详情的接口去修改本次回退金额的值，是拿详情接口的值，而不是拿getPaymentList接口的值
              if (detailsFdoc.length !== 0) {
                const idArr: any = []
                paymentList.value.forEach((paymentItem) => {
                  detailsFdoc?.forEach((detailsItem) => {
                    idArr.push(detailsItem.fdoc_id)
                    if (paymentItem.strid == detailsItem.fdoc_strid) {
                      paymentItem.fund = cloneDeep(detailsItem.fund)
                      console.log(paymentItem.fund)
                    }
                    paymentItem.fund.forEach((fundItem) => {
                      fundItem.amount_allot = sub(fundItem.amount_allot, fundItem.total_amount_fund)
                      if (!fundItem.amount_fund) {
                        fundItem.amount_fund = 0
                      }
                      if (fundItem.fg_amount_left == 0) {
                        fundItem.fg_amount_left = 0
                      }
                    })
                  })
                })
                setSelectedRowKeys(idArr)
              }
            }
            setLoading(false)
          } catch (error) {
            setLoading(false)
            console.log(error)
          }
        }
      }),
      rules: [
        {
          required: true,
          trigger: ['change', 'blur'],
          validator: async (_rule: Rule, value: string) => {
            console.log(value)

            // Number(value) > Number(selectRowsData.value[index].no_amount) ||
            if (!value) {
              return Promise.reject('请选择销售退款任务(只有未结算的销售订单才可以退款)')
            } else {
              return Promise.resolve()
            }
          }
        }
      ],
      dynamicDisabled: ['detail'].includes(type),
      required: true,
      ifShow: order == 1
    },
    {
      field: 'purchase_work_id',
      label: '采购退款任务',
      itemProps: {
        validateTrigger: 'blur'
      },
      component: 'PagingApiSelect',
      componentProps: ({ formModel }) => ({
        resultField: 'items',
        api: getWorkList,
        params: {
          status: [1, 2, 3, 4, 5, 15],
          types: [4], // 阿海让我改的
          auth: 4,
          from: 'refund'
        },
        searchMode: true,
        pagingMode: true,
        returnParamsField: 'id',
        searchParamField: 'strid',
        selectProps: {
          allowClear: true,
          fieldNames: { key: 'id', value: 'id', label: 'purchase_strid' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'strid'
        },

        onChange: async (val: number, shall) => {
          try {
            setLoading(true)
            if (!val) {
              paymentList.value = []
            } else {
              console.log(shall)

              formModel.currency = shall?.currency
              formModel.exchange_rate = shall?.exchange_rate
              refundshall.value = shall
              const params = { work_id: val, is_check: 1 }
              paymentList.value = await getPaymentList(params)

              if (type == 'add' || isRefundProps == 1) {
                paymentList.value.forEach((paymentItem) => {
                  paymentItem.fund.forEach((fundItem) => {
                    console.log(fundItem)
                    fundItem.amount_allot = sub(fundItem.amount_allot, fundItem.total_amount_fund)
                    fundItem.amount_fund = 0
                    fundItem.foreign_currency_amount = 0
                  })
                })
              }
              console.log(detailsFdoc)
              // 这里需要拿详情的接口去修改本次回退金额的值，是拿详情接口的值，而不是拿getPaymentList接口的值
              if (detailsFdoc.length !== 0) {
                const idArr: any = []
                paymentList.value.forEach((paymentItem) => {
                  detailsFdoc?.forEach((detailsItem) => {
                    idArr.push(detailsItem.fdoc_id)
                    if (paymentItem.strid == detailsItem.fdoc_strid) {
                      paymentItem.fund = cloneDeep(detailsItem.fund)
                    }
                    paymentItem.fund.forEach((fundItem) => {
                      fundItem.amount_allot = sub(fundItem.amount_allot, fundItem.total_amount_fund)
                      if (!fundItem.amount_fund) {
                        fundItem.amount_fund = 0
                      }
                      if (!fundItem.fg_amount_left) {
                        fundItem.foreign_currency_amount = 0
                      }
                    })
                  })
                })
                setSelectedRowKeys(idArr)
                console.log(paymentList.value)
              }
            }
            setLoading(false)
          } catch (error) {
            setLoading(false)
            console.log(error)
          }
        }
      }),
      rules: [
        {
          required: true,
          trigger: ['change', 'blur'],
          validator: async (_rule: Rule, value: string) => {
            // Number(value) > Number(selectRowsData.value[index].no_amount) ||
            if (!value) {
              return Promise.reject('请选择采购退款任务(只有未结算的采购订单才可以退款)')
            } else {
              return Promise.resolve()
            }
          }
        }
      ],
      dynamicDisabled: ['detail'].includes(type),
      required: true,
      ifShow: order == 2
    },
    {
      field: 'applicant',
      label: '申请人',
      component: 'PagingApiSelect',
      required: true,
      componentProps: {
        api: getCreatorList,
        resultField: 'items',
        returnParamsField: 'id',
        pagingMode: true,
        searchMode: true,
        selectProps: {
          fieldNames: { value: 'id', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        }
      },
      dynamicDisabled: true,
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'inCharge',
      label: '负责人',
      component: 'PagingApiSelect',
      required: true,
      componentProps: {
        api: getCreatorList,
        resultField: 'items',
        returnParamsField: 'id',
        pagingMode: true,
        searchMode: true,
        selectProps: {
          fieldNames: { value: 'id', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        }
      },
      dynamicDisabled: ['detail'].includes(type),
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'amount',
      label: '退款金额',
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        disabled: type === 'detail'
      },
      required: true,
      ifShow: ({ values }) => values.type === 1,
      dynamicDisabled({ model }) {
        return pathname !== '/s/' ? type === 'detail' || !['人民币', 'CNY'].includes(model.currency) : type === 'detail'
      }
    },
    {
      field: 'contracting_party',
      label: '我司签约主体',
      component: 'PagingApiSelect',
      componentProps: ({ formModel }) => ({
        api: cpgetList,
        resultField: 'items',
        disabledField: 'name',
        params: {
          work_id: formModel.work_id
        },
        selectProps: {
          fieldNames: { key: 'key', value: 'name', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          optionFilterProp: 'name'
        }
      }),
      itemProps: {
        validateTrigger: 'blur'
      },
      required({ model }) {
        return model.type == 1 && model.order == 1 ? true : false
      },
      show({ model }) {
        return model.type == 1 && model.order == 1 ? true : false
      },
      dynamicDisabled: ['detail'].includes(type)
    },
    {
      field: 'currency',
      label: '币种',
      component: 'Input',
      ifShow: ({ values }) =>
        pathname !== '/s/'
          ? values.sale_work_id || values.purchase_work_id
            ? !['人民币', 'CNY'].includes(values.currency) && values.type === 1
              ? true
              : false
            : false
          : false,
      dynamicDisabled: true
    },
    {
      field: 'exchange_rate',
      label: '汇率',
      component: 'Input',
      ifShow: ({ values }) =>
        pathname !== '/s/'
          ? values.sale_work_id || values.purchase_work_id
            ? !['人民币', 'CNY'].includes(values.currency) && values.type === 1
              ? true
              : false
            : false
          : false,
      dynamicDisabled: true
    },
    {
      field: 'foreign_currency_amount',
      label: '外币金额',
      component: 'InputNumber',
      componentProps: ({ formModel }) => ({
        min: 0,
        precision: 2,
        disabled: type === 'detail',
        onChange(val) {
          formModel.amount = ((val * (formModel.exchange_rate * 100)) / 100).toFixed(2)
        }
      }),
      required: true,
      ifShow: ({ values }) =>
        pathname !== '/s/'
          ? values.sale_work_id || values.purchase_work_id
            ? !['人民币', 'CNY'].includes(values.currency) && values.type === 1
              ? true
              : false
            : false
          : false,
      dynamicDisabled: ['detail'].includes(type)
    }
    // {
    //   field: 'payment',
    //   label: '退款款单',
    //   component: 'Divider',
    //   colProps: { span: 24 }
    // }
  ]
}

const status_schema = GET_STATUS_SCHEMA(auditStatus('status'))

export const searchFormSchema: FormSchema[] = [
  status_schema,
  {
    field: 'strid',
    label: '退款单号',
    component: 'Input'
  },
  {
    field: 'type_strid',
    label: '单号',
    component: 'Input'
  },
  {
    field: 'type',
    label: '款项类型',
    component: 'Select',
    componentProps: {
      options: paymentType('type'),
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'label'
    }
  },
  // {
  //   field: 'status',
  //   label: '状态',
  //   component: 'Select',
  //   componentProps: {
  //     options: auditStatus('status'),
  //     showSearch: true,
  //     allowClear: true,
  //     optionFilterProp: 'label'
  //   }
  // },
  {
    field: 'created_at',
    label: '创建日期',
    component: 'SingleRangeDate',
    componentProps: {
      showTime: false,
      allowEmpty: [true, true],
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  },
  {
    field: 'check_at',
    label: '审核日期',
    component: 'SingleRangeDate',
    componentProps: {
      showTime: false,
      allowEmpty: [true, true],
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%'
      }
    }
  },
  {
    field: 'client_id',
    label: '客户',
    component: 'PagingApiSelect',
    componentProps: {
      api: getClientList,
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      },
      resultField: 'items'
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'supplier_id',
    label: '供应商',
    component: 'PagingApiSelect',
    componentProps: () => {
      return {
        api: getErpSupplier,
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        },
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        resultField: 'items'
      }
    },
    colProps: { span: 8 },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getCreatorList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'applicant',
    label: '申请人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getCreatorList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'fund_strid',
    label: '流水单号',
    component: 'Input'
  }
]
