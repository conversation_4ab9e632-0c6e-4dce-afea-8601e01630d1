import { getRelationType } from '../../capitalFlow/datas/fn'

import { useRender } from '/@/components/Table/src/hooks/useRender'
import { ref } from 'vue'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { BasicColumn } from '/@/components/Table'

export const paymentList: any = ref([])

/** 抽屉表格字段显示 1:销售退款，2：采购退款 */
export function tableColumn(val: number): BasicColumn[] {
  return [
    {
      title: '单号',
      dataIndex: 'strid'
    },
    {
      title: '类型',
      dataIndex: 'type',
      customRender: ({ text }) => {
        return getRelationType(text)
      }
    },
    {
      title: val == 1 ? '应收金额' : '应付金额',
      dataIndex: 'amount',
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text) : '0.00'
      }
    },

    {
      title: val == 1 ? '本次已收金额' : '本次已付金额',
      dataIndex: val == 1 ? 'amount_rec' : 'amount_paid',
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text) : '0.00'
      }
    },
    {
      title: '任务关联款单金额',
      dataIndex: 'amount_fdocw'
    }
  ]
}

/**
 *  退款单类型
 * @param val 传值返回对应的类型，传对应的field（字符串类型）则用于筛选
 * @returns tag or options
 */
export function typeOfRefundNote(val): any {
  if (val === 'order') {
    return [
      { label: '销售退款', value: 1 },
      { label: '采购退款', value: 2 }
    ]
  } else {
    const mapValue = {
      1: { text: '销售退款', value: 1, color: '' },
      2: { text: '采购退款', value: 2, color: '' }
    }
    return mapValue[val] ? useRender.renderTag(mapValue[val]?.text, mapValue[val]?.color) : '-'
  }
}

/**
 *  款项类型
 * @param val 传值返回对应的类型，传对应的field（字符串类型）则用于筛选
 * @returns tag or options
 */
export function paymentType(val): any {
  if (val === 'type') {
    return [
      { label: '退款', value: 1 },
      { label: '不退款', value: 2 }
    ]
  } else {
    const mapValue = {
      1: { text: '退款', value: 1, color: '' },
      2: { text: '不退款', value: 2, color: '' }
    }
    return mapValue[val] ? useRender.renderTag(mapValue[val]?.text, mapValue[val]?.color) : '-'
  }
}

/**
 *  审核状态
 * @param val 传值返回对应的类型，传对应的field（字符串类型）则用于筛选
 * @returns tag or options
 */
export function auditStatus(val): any {
  if (val === 'status') {
    return [
      { label: '待审核', value: 0 },
      { label: '已审核', value: 1 },
      { label: '已结束', value: 15 }
    ]
  } else {
    const mapValue = {
      0: { text: '待审核', value: 0, color: '' },
      1: { text: '已审核', value: 1, color: 'success' },
      15: { text: '已结束', value: 15, color: 'warning' }
    }
    return mapValue[val] ? useRender.renderTag(mapValue[val]?.text, mapValue[val]?.color) : '-'
  }
}

export function checkStatus(val): any {
  const mapValue = {
    0: { label: '未审批', color: 'default' },
    1: { label: '审批通过', color: 'processing' },
    2: { label: '驳回', color: 'error' }
  }
  return mapValue[val] ? useRender.renderTag(mapValue[val]?.label, mapValue[val]?.color) : '-'
}
