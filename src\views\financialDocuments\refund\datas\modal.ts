import { FormSchema } from '/@/components/Form'
export const FromschemasFn = (type: Boolean): FormSchema[] => [
  {
    field: 'name',
    label: '客户名称',
    component: 'Input',
    required: true,
    componentProps: {
      disabled: true
    }
  },
  {
    field: 'id',
    label: '客户id',
    component: 'Input',
    required: true,
    componentProps: {
      disabled: true
    },
    ifShow: false
  },
  {
    field: 'account',
    label: '客户账户',
    component: 'Input',
    required: true,
    componentProps: {
      disabled: type
    }
  },
  {
    field: 'account_name',
    label: '账户名称',
    component: 'Input',
    required: true,
    componentProps: {
      disabled: type
    }
  },
  {
    field: 'contact',
    label: '联系方式',
    component: 'Input',
    required: true,
    componentProps: {
      disabled: type
    }
  },
  {
    field: 'email',
    label: '邮箱',
    component: 'Input',
    componentProps: {
      disabled: type
    }
  },

  {
    field: 'location',
    label: '地址',
    component: 'Input',
    componentProps: {
      disabled: type
    }
  },
  {
    field: 'bank',
    label: '开户行',
    component: 'Input',
    componentProps: {
      disabled: type
    }
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    componentProps: {
      disabled: type
    }
  },
  {
    field: 'g_remark',
    label: '收/付款备注',
    component: 'InputTextArea'
  },
  {
    field: 'files',
    label: '附件',
    component: 'Upload',
    slot: 'Files'
  }
]
