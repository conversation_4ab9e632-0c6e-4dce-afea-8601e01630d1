<template>
  <BasicModal
    @register="registerAddModal"
    :title="type == 1 ? '添加收入流水' : '添加支出流水'"
    width="70%"
    :bodyStyle="{ height: '500px' }"
    @ok="handleOk"
    destroyOnClose
  >
    <BasicTable @register="registerFilterTable" />
  </BasicModal>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import { getFundList } from '/@/api/financialDocuments/public'
import { addIncomeFundModalFormSchema, addParmentFundModalFormSchema, addFundModalColumns } from '../datas/datas'

import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicTable, useTable } from '/@/components/Table'
import { ref } from 'vue'

const emit = defineEmits(['register', 'handleAddOk'])
console.log(emit)

/** 存储上层传过来的流水表格数据 */
const fundTableData = ref<Array<any>>([])

/** 流水类型 */
const type = ref<number>()

/** 注册添加流水Modal，注册添加流水Modal，刚进来会触发*/
const [registerAddModal, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  fundTableData.value = cloneDeep(data.fundTableData)
  type.value = data.type

  // 初始化筛选配置
  if (type.value == 1) {
    await getForm().resetSchema(addIncomeFundModalFormSchema)
  } else if (type.value == 2) {
    await getForm().resetSchema(addParmentFundModalFormSchema)
  }
  console.log(data)

  reload()
  clearSelectedRowKeys()
})

/** 注册筛选流水表格 */
const [registerFilterTable, { getSelectRows, clearSelectedRowKeys, reload, getForm }] = useTable({
  api: getFundList,
  beforeFetch: (val) => {
    return {
      ...val,
      type: type.value
    }
  },
  afterFetch: (data) => {
    clearSelectedRowKeys()
    const newData = data.map((item) => {
      item.amount_melt = 0
      return item
    })
    return newData
  },
  showIndexColumn: false,
  useSearchForm: true,
  canResize: true,
  maxHeight: 190,
  formConfig: {
    labelWidth: 120,
    actionColOptions: {
      span: 24
    },
    baseColProps: {
      span: 8
    }
  },
  columns: addFundModalColumns,
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: (record) => {
      let flag = false

      // if (type.value == 1) {
      // 限制收入流水只能添加一条
      // if (fundTableData.value.length == 1 || (getSelectRows().length >= 1 && record.strid !== getSelectRows()[0]?.strid)) {
      //   flag = true
      // }
      // } else {
      for (let item of fundTableData.value) {
        if (item.strid === record.strid) {
          flag = true
          break
        }
      }
      // }
      return {
        disabled: flag
      }
    }
  }
})

/** 点击确认 */
const handleOk = async () => {
  await changeOkLoading(true)
  let parameter: {
    data: Array<any>
    type: number
  } = {
    data: [],
    type: type.value!
  }
  getSelectRows().forEach((item) => {
    // 默认本次分配金额是剩余金额
    // item['amount_allot'] = cloneDeep(item.amount_left)
    parameter.data.push(item)
  })

  clearSelectedRowKeys()
  closeModal()
  emit('handleAddOk', parameter)
  changeOkLoading(false)
}
</script>
<style lang="less" scoped>
:deep(.ant-picker) {
  width: 100%;
}
</style>
