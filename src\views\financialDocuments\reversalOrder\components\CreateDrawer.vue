<template>
  <BasicDrawer @register="registerCreateDrawer" v-bind="$attrs" showFooter width="90%" destroyOnClose>
    <template #footer>
      <Button @click="handleCancel">取消</Button>
      <Button type="primary" @click="debounceHandleOk" :disabled="currentEditKeyRef == '' ? false : true">确定</Button>
    </template>
    <!-- 头部 -->
    <BasicForm @register="registerForm" />

    <!-- 收入流水 -->
    <Descriptions title="收入流水" class="mt-5" />
    <BasicTable @register="registerIncomeTable">
      <template #toolbar>
        <Button type="primary" size="small" @click="openAddModal(1)" :disabled="currentEditKeyRef == '' ? false : true">新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record, true)" />
        </template>
      </template>
    </BasicTable>

    <!-- 支出流水 -->
    <Descriptions title="支出流水" class="mt-5" />
    <BasicTable @register="registerPaymentTable">
      <template #toolbar>
        <Button type="primary" size="small" @click="openAddModal(2)" :disabled="currentEditKeyRef == '' ? false : true">新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record, true)" />
        </template>
      </template>
    </BasicTable>

    <!-- 筛选流水 -->
    <AddFundModal @register="registerAddModal" @handle-add-ok="handleAddOk" />
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { createFormSchema } from '../datas/datas'
import { addFundListColumnsFn } from '../datas/datas'
import AddFundModal from '../components/AddFundModal.vue'
import { addReversalOrder, detailsReversalOrder, updateReversalOrder } from '/@/api/financialDocuments/reversalOrder'

import { ref } from 'vue'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { Button, Descriptions, message } from 'ant-design-vue'
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import { useModal } from '/@/components/Modal'
import { cloneDeep, debounce } from 'lodash-es'
import { computed } from 'vue'
import { add } from '/@/utils/math'
import defaultUser from '/@/utils/erp/defaultUser'

const emit = defineEmits(['success', 'register'])

const isUpdate = ref(false)
const record = ref()
const currentEditKeyRef = ref('')
const beforeRecord: any = ref()

/** 注册 Form */
const [registerForm, { resetFields, setFieldsValue, validate, clearValidate, resetSchema }] = useForm({
  baseColProps: { span: 8 },
  showActionButtonGroup: false,
  labelCol: { span: 5 }
})

/** 注册抽屉，刚进来会触发 */
const [registerCreateDrawer, { closeDrawer, changeLoading, changeOkLoading }] = useDrawerInner(async (data) => {
  resetFields()
  isUpdate.value = data.isUpdate
  record.value = data.record
  let detailData

  // 初始化
  setColumns(addFundListColumnsFn())
  setPaymentColumns(addFundListColumnsFn())
  resetSchema(createFormSchema)
  setFieldsValue({ amount: totalIncomeAmountMelt })
  clearValidate()

  if (data.isUpdate) {
    detailData = await detailsReversalOrder({ id: data.record.id })
    setTableData(detailData.items.fund1)
    setPaymentTableData(detailData.items.fund2)
    setFieldsValue({ ...detailData.items })
  } else {
    setTableData([])
    setPaymentTableData([])
    setFieldsValue({ inCharge: defaultUser!.userId, applicant: defaultUser!.userId })
    clearValidate()
  }
})

/** 注册 收入流水 表格 */
const [registerIncomeTable, { setTableData, getDataSource, getColumns, setColumns, deleteTableDataRecord, updateTableDataRecord }] =
  useTable({
    showIndexColumn: false,
    maxHeight: 200,
    actionColumn: {
      width: 110,
      title: '操作',
      dataIndex: 'action'
    },
    pagination: false
  })

/** 注册 支出流水 表格 */
const [
  registerPaymentTable,
  {
    setColumns: setPaymentColumns,
    setTableData: setPaymentTableData,
    getDataSource: getPaymentDataSource,
    deleteTableDataRecord: deletePaymentTableDataRecord,
    updateTableDataRecord: updatePaymentTableDataRecord,
    getColumns: getPaymentColumns
  }
] = useTable({
  showIndexColumn: false,
  maxHeight: 200,
  pagination: false,
  actionColumn: {
    width: 110,
    title: '操作',
    dataIndex: 'action'
  }
})

/** 注册添加流水Modal */
const [registerAddModal, { openModal }] = useModal()

/** 打开添加流水Modal type:1(收入流水)，2(支出流水) */
const openAddModal = async (type: number) => {
  await clearValidate()
  openModal(true, {
    type,
    fundTableData: type == 1 ? getDataSource() : getPaymentDataSource()
  })
}

function createActions(record: EditRecordRow, delStatus: boolean): Recordable[] {
  let editButtonList: ActionItem[] = [
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
      onClick: handleUpdate.bind(null, record),
      ifShow: delStatus
    },
    {
      icon: 'ant-design:delete-outlined',
      color: 'error',
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        placement: 'right',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        confirm: handleDelete.bind(null, record)
      },
      ifShow: delStatus
    }
  ]

  if (!record.editable) {
    return editButtonList
  }
  return [
    {
      label: '保存',
      onClick: handleSave.bind(null, record)
    },
    {
      label: '取消',
      onClick: handleEditCancel.bind(null, record)
    }
  ]
}

/** 计算收入流水总冲销金额 */
const totalIncomeAmountMelt = computed(() => {
  return getDataSource().reduce((prev, curr) => add(Number(prev), Number(curr.amount_melt)), 0)
})

/** 编辑 */
async function handleUpdate(record: EditRecordRow) {
  await clearValidate()
  currentEditKeyRef.value = record.key
  beforeRecord.value = cloneDeep(record)
  await record.onEdit?.(true)
}

/** 删除 */
function handleDelete(record) {
  deleteTableDataRecord(record.key)
  deletePaymentTableDataRecord(record.key)
}

/** 保存 */
async function handleSave(record: EditRecordRow) {
  const valid = await record.onValid?.()
  if (valid) {
    const pass = record.onEdit?.(false, true)
    if (pass) {
      currentEditKeyRef.value = ''
    }
  }
}

/** 取消编辑 */
async function handleEditCancel(record: EditRecordRow) {
  await record.onEdit?.(false, true)
  updateTableDataRecord(record.key, beforeRecord.value)
  updatePaymentTableDataRecord(record.key, beforeRecord.value)
  currentEditKeyRef.value = ''
}

/** 取消 */
function handleCancel() {
  closeDrawer()
}

/** 格式提交的数据 */
function formatSubmit() {
  let fundIncomeDataSource: Array<Recordable> = []
  let fundParmentDataSource: Array<Recordable> = []

  // 处理不需要的属性
  fundIncomeDataSource = getDataSource().map((item) => {
    let temporary = {}
    for (let colName of getColumns()) {
      if (colName.dataIndex == 'amount_melt') {
        temporary[`${colName.dataIndex}`] = Number(item[`${colName.dataIndex}`])
      } else if (colName.dataIndex == 'id') {
        if (isUpdate.value) {
          temporary['fund_id'] = item['fund_id']
          temporary['id'] = item['id']
        } else {
          temporary['fund_id'] = item[`${colName.dataIndex}`]
        }
      }
    }
    return temporary
  })

  fundParmentDataSource = getPaymentDataSource().map((item) => {
    let temporary = {}
    for (let colName of getPaymentColumns()) {
      if (colName.dataIndex == 'amount_melt') {
        temporary[`${colName.dataIndex}`] = Number(item[`${colName.dataIndex}`])
      } else if (colName.dataIndex == 'id') {
        if (isUpdate.value) {
          temporary['fund_id'] = item['fund_id']
          temporary['id'] = item['id']
        } else {
          temporary['fund_id'] = item[`${colName.dataIndex}`]
        }
      }
    }
    return temporary
  })

  // 校验支出流水的总冲销金额是否等于顶部冲销金额
  let totalParmentAmountMelt = getPaymentDataSource().reduce((prev, curr) => add(Number(prev), Number(curr.amount_melt)), 0)
  if (totalParmentAmountMelt !== totalIncomeAmountMelt.value) {
    message.error('收入流水与支出流水的冲销金额不一致，请重新核对！')
    changeLoading(false)
    return
  }

  return [fundIncomeDataSource, fundParmentDataSource]
}

const debounceHandleOk = debounce(_handleOk, 500)
/** 确认 */
async function _handleOk() {
  try {
    await changeLoading(true)
    await changeOkLoading(true)
    const data = await validate()

    const [fundIncomeDataSource, fundParmentDataSource]: any = formatSubmit()

    if (!isUpdate.value) {
      let parameter = {
        doc: data,
        fund1: fundIncomeDataSource,
        fund2: fundParmentDataSource
      }
      await addReversalOrder(parameter)
      message.success('创建成功！')
    } else {
      let parameter = {
        doc: { ...data, amount: Number(data.amount), doc_id: record.value.id },
        fund1: fundIncomeDataSource,
        fund2: fundParmentDataSource
      }
      await updateReversalOrder(parameter)
      message.success('更新成功！')
    }

    emit('success')
    await closeDrawer()
    changeLoading(false)
    changeOkLoading(false)
  } catch (err) {
    changeLoading(false)
    changeOkLoading(false)
    throw new Error(`${err}`)
  }
}
// const handleOk = debounce(_handleOk, 200)

/** 添加流水回调 */
const handleAddOk = (parameter: { data: Array<any>; type: number }) => {
  let fundData: Array<any> = []
  if (parameter.type == 1) {
    fundData = getDataSource()
    for (let item of parameter.data) {
      fundData.push(item)
    }
    setTableData(fundData)
  } else if (parameter.type == 2) {
    fundData = getPaymentDataSource()
    for (let item of parameter.data) {
      fundData.push(item)
    }
    setPaymentTableData(fundData)
  }
}
</script>

<style lang="less" scoped>
:deep(.ant-picker) {
  width: 100%;
}
:deep(.ant-input-number-affix-wrapper) {
  width: 100%;
}
</style>
