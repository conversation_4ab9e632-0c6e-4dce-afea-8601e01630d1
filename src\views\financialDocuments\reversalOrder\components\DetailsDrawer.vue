<template>
  <BasicDrawer @register="registerCreateDrawer" v-bind="$attrs" showFooter width="90%" destroyOnClose>
    <template #footer> </template>
    <!-- 头部 -->
    <BasicForm @register="registerForm" />

    <!-- 收入流水 -->
    <Descriptions title="收入流水" />
    <BasicTable @register="registerIncomeTable" />

    <!-- 支出流水 -->
    <Descriptions title="支出流水" />
    <BasicTable @register="registerPaymentTable" />
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { createFormSchema } from '../datas/datas'
import { detailsFundListColumnsFn } from '../datas/datas'
import { detailsReversalOrder } from '/@/api/financialDocuments/reversalOrder'

import { BasicForm, useForm } from '/@/components/Form'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { Descriptions } from 'ant-design-vue'
import { BasicTable, useTable } from '/@/components/Table'

const emit = defineEmits(['register'])
console.log(emit)

/** 注册 Form */
const [registerForm, { resetFields, setFieldsValue, updateSchema }] = useForm({
  baseColProps: { span: 8 },
  showActionButtonGroup: false,
  labelCol: { span: 5 },
  schemas: createFormSchema
})

/** 注册抽屉，刚进来会触发 */
const [registerCreateDrawer, {}] = useDrawerInner(async (data) => {
  resetFields()
  let detailData
  console.log(data.record)

  // 初始化
  setColumns(detailsFundListColumnsFn(data.record.status))
  setPaymentColumns(detailsFundListColumnsFn(data.record.status))
  updateSchema([
    {
      field: 'inCharge',
      componentProps: {
        selectProps: {
          disabled: true
        }
      }
    },
    {
      field: 'applicant',
      componentProps: {
        selectProps: {
          disabled: true
        }
      }
    }
  ])

  detailData = await detailsReversalOrder({ id: data.record.id })

  setFieldsValue(detailData.items)
  setTableData(detailData.items.fund1)
  setPaymentTableData(detailData.items.fund2)
})

/** 注册 收入流水 表格 */
const [registerIncomeTable, { setTableData, setColumns }] = useTable({
  showIndexColumn: false,
  maxHeight: 200,
  pagination: false
})

/** 注册 支出流水 表格 */
const [registerPaymentTable, { setColumns: setPaymentColumns, setTableData: setPaymentTableData }] = useTable({
  showIndexColumn: false,
  maxHeight: 200,
  pagination: false
})
</script>

<style lang="less" scoped>
:deep(.ant-picker) {
  width: 100%;
}
:deep(.ant-input-number-affix-wrapper) {
  width: 100%;
}
</style>
