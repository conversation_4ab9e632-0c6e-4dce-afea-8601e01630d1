import type { BasicColumn } from '/@/components/Table'
import type { FormSchema } from '/@/components/Table'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { getErpSupplier, getClientList } from '/@/api/commonUtils'
import { cloneDeep } from 'lodash-es'
import { examineStatus, getFundType } from './fn'
import dayjs from 'dayjs'
import { sub } from '/@/utils/math'
import { getDeptSelectTree } from '/@/api/admin/dept'
import { GET_STATUS_SCHEMA } from '/@/const/status'

/* 冲销单 */
export const columns: BasicColumn[] = [
  {
    title: '冲销单号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '创建日期',
    dataIndex: 'created_at',
    width: 100,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 120,
    resizable: true
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 100,
    resizable: true
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },

  {
    title: '申请人',
    dataIndex: 'applicant_name',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '审核人',
    dataIndex: 'auditor_name',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '审核状态',
    dataIndex: 'status',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return examineStatus(text)
    }
  },
  {
    title: '冲销金额',
    dataIndex: 'amount',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(text) : '0.00'
    }
  }
]

/** 创建冲销单顶部 */
export const createFormSchema: FormSchema[] = [
  // {
  //   field: 'collection_at',
  //   label: '冲销日期',
  //   required: true,
  //   component: 'DatePicker',
  //   componentProps: {
  //     valueFormat: 'YYYY-MM-DD'
  //   }
  // },

  {
    field: 'inCharge',
    label: '负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getCreatorList,
      searchMode: true,
      pagingMode: true,
      resultField: 'items',
      returnParamsField: 'id',
      selectProps: {
        disabled: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    required: true
  },
  {
    field: 'applicant',
    label: '申请人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getCreatorList,
      searchMode: true,
      pagingMode: true,
      resultField: 'items',
      returnParamsField: 'id',
      selectProps: {
        disabled: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    required: true
  },
  {
    field: 'amount',
    label: '冲销金额',
    required: true,
    component: 'InputNumber',
    componentProps: {},
    dynamicDisabled: true,
    defaultValue: 0,
    rules: [
      {
        required: true,
        message: '冲销金额不能为0,请添加收入流水！',
        validator: (_rule, value) => {
          if (value <= 0) {
            return Promise.reject('')
          } else {
            return Promise.resolve()
          }
        }
      }
    ]
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    required: true,
    componentProps: {
      api: getDeptSelectTree,
      immediate: false,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        placeholder: '请选择',
        showSearch: true,
        optionFilterProp: 'name',
        treeDefaultExpandAll: true,
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  }
]

/**  Drawer内流水列表 */
export function addFundListColumnsFn(): any {
  const fundListColumns: BasicColumn[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 50,
      resizable: true,
      customRender: ({ record, text }) => {
        return record.fund_id ? record.fund_id : text
      },
      ifShow: false
    },
    {
      title: '流水单号',
      dataIndex: 'strid',
      width: 200,
      resizable: true
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 50,
      resizable: true,
      customRender: ({ text }) => {
        return getFundType(text)
      }
    },
    {
      title: '付款资金资料',
      dataIndex: 'from_plaform',
      width: 100,
      resizable: true,
      customRender: ({ record }) => {
        return record.from_plaform ? record.from_plaform : '-'
      }
    },

    {
      title: '收款资金资料',
      dataIndex: 'to_plaform',
      width: 100,
      resizable: true,
      customRender: ({ record }) => {
        return record.to_plaform ? record.to_plaform : '-'
      }
    },

    {
      title: '手续费',
      dataIndex: 'fee',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text) : '0.00'
      }
    },
    {
      title: '金额',
      dataIndex: 'amount',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text) : '0.00'
      }
    },
    {
      title: '剩余金额',
      dataIndex: 'amount_left',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text) : '0.00'
      }
    },
    {
      title: '预计剩余金额',
      dataIndex: 'expectedAmount_left',
      width: 100,
      resizable: true,
      customRender: ({ record }) => {
        return formateerNotCurrency.format(sub(Number(record.amount_left), Number(record.amount_melt)))
      }
    },
    {
      title: '冲销金额',
      dataIndex: 'amount_melt',
      editComponent: 'InputNumber',
      width: 100,
      resizable: true,
      editRow: true,
      editComponentProps: ({ record }) => {
        return {
          min: 0,
          max: record.amount_left
          // onChange: () => {
          //   changeFn!(record.type)
          // }
        }
      },
      editRender: ({ text }) => {
        return text ? formateerNotCurrency.format(Number(text)) : '0.00'
      }
    }
  ]
  return fundListColumns
}

/**  详情内流水列表 */
export function detailsFundListColumnsFn(status: number): any {
  if (status == 1) {
    return cloneDeep(addFundListColumnsFn()).filter((item) => {
      if (item.dataIndex !== 'expectedAmount_left') {
        return item
      }
    })
  } else {
    return addFundListColumnsFn()
  }
}

/** 添加收入流水Modal */
export const addIncomeFundModalFormSchema: FormSchema[] = [
  {
    field: 'occurrence_at',
    label: '收款日期',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'client_id',
    label: '客户',
    component: 'ApiSelect',
    componentProps: {
      api: getClientList,
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      },
      resultField: 'items'
    }
  },
  {
    field: 'supplier_id',
    label: '供应商',
    component: 'ApiSelect',
    componentProps: () => {
      return {
        api: getErpSupplier,
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        },
        resultField: 'items'
      }
    }
  },
  {
    field: 'from_plaform',
    label: '付款资金资料',
    component: 'Input'
  },
  {
    field: 'to_plaform',
    label: '收款资金资料',
    component: 'Input'
  },
  {
    field: 'amount',
    label: '金额',
    component: 'Input'
  }
]

/** 添加流水Modal columns */
export const addFundModalColumns: BasicColumn[] = [
  {
    title: '流水单号',
    dataIndex: 'strid',
    width: 200
  },
  {
    title: '付款日期',
    dataIndex: 'occurrence_at',
    width: 100,
    customRender: ({ text }) => {
      return text ? dayjs(text).format('YYYY-MM-DD') : '-'
    }
  },
  {
    title: '客户',
    dataIndex: 'client_name',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '付款资金资料',
    dataIndex: 'from_plaform',
    width: 100
  },
  {
    title: '收款资金资料',
    dataIndex: 'to_plaform',
    width: 100
  },
  {
    title: '金额',
    dataIndex: 'amount',
    width: 100,
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(Number(text)) : '0.00'
    }
  }
]

/** 添加支出流水Modal */
export const addParmentFundModalFormSchema: FormSchema[] = cloneDeep(addIncomeFundModalFormSchema).map((item) => {
  if (item.field == 'occurrence_at') {
    item.label = '付款日期'
  } else if (item.field == 'from_plaform') {
    // item.label = '收款资金资料'
    // item.field = 'to_plaform'
  }
  return item
})

const status_schema = GET_STATUS_SCHEMA([
  { label: '未审核', value: 0 },
  { label: '审核通过', value: 1 }
])

/** 筛选 */
export const searchFromSchemas: FormSchema[] = [
  status_schema,
  {
    field: 'strid',
    label: '冲销单号',
    component: 'Input',
    componentProps: {
      allowClear: true
    }
  },
  {
    field: 'inCharge',
    label: '负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getCreatorList,
      searchMode: true,
      pagingMode: true,
      resultField: 'items',
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'applicant',
    label: '申请人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getCreatorList,
      searchMode: true,
      pagingMode: true,
      resultField: 'items',
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  }
  // {
  //   field: 'status',
  //   label: '状态',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: '未审核', value: 0 },
  //       { label: '审核通过', value: 1 }
  //     ],
  //     allowClear: true
  //   }
  // }
]
