import { useRender } from '/@/components/Table/src/hooks/useRender'

/** 审核状态 */
export function examineStatus(status) {
  const mapValue = {
    0: { text: '未审核' },
    1: { text: '审核通过', color: 'success' }
  }
  return useRender.renderTag(mapValue[status]?.text, mapValue[status]?.color)
}

/** 流水类型 */
export function getFundType(type) {
  const mapValue = {
    1: { text: '收入', color: 'green' },
    2: { text: '支出', color: 'red' }
  }
  return useRender.renderTag(mapValue[type]?.text, mapValue[type]?.color)
}
