<template>
  <div>
    <BasicTable :data-cachekey="routePath" @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleCreate" v-if="hasPermission([175])">
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
      </template>
    </BasicTable>

    <!-- 注册添加抽屉 -->
    <CreateDrawer @register="registerCreateDrawer" @success="handleSuccess" />

    <!-- 注册添加抽屉 -->
    <DetailsDrawer @register="registerDetailsDrawer" />
  </div>
</template>

<script setup lang="ts">
import { getReversalOrderList } from '/@/api/financialDocuments/reversalOrder'
import { columns, searchFromSchemas } from './datas/datas'
import { examineReversalOrder, deleteReversalOrder } from '/@/api/financialDocuments/reversalOrder'
import DetailsDrawer from './components/DetailsDrawer.vue'

import CreateDrawer from './components/CreateDrawer.vue'
import { Button, message } from 'ant-design-vue'
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import type { ActionItem, EditRecordRow } from '/@/components/Table'
import { useDrawer } from '/@/components/Drawer'
import { PlusOutlined } from '@ant-design/icons-vue'
import { usePermission } from '/@/hooks/web/usePermission'
import { useRoute } from 'vue-router'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'

const route = useRoute()
const { path: routePath } = route

const { hasPermission } = usePermission()

/** 注册 创建、编辑 Drawer */
const [registerCreateDrawer, { openDrawer, setDrawerProps }] = useDrawer()

/** 注册表格 */
const [registerTable, { reload }] = useTable({
  title: '冲销单',
  showIndexColumn: false,
  api: getReversalOrderList,
  columns,
  actionColumn: hasPermission([175, 176, 177, 178])
    ? {
        width: 180,
        title: '操作',
        dataIndex: 'action'
      }
    : void 0,
  showTableSetting: true,
  useSearchForm: true,
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas: searchFromSchemas
  }
})

/** 注册详情抽屉 */
const [registerDetailsDrawer, { openDrawer: openDetailsDrawer, setDrawerProps: setDetailsDrawerProps }] = useDrawer()

/** 操作按钮 */
function createActions(record: EditRecordRow): Recordable[] {
  let buttonList: ActionItem[] = [
    {
      icon: 'ant-design:file-search-outlined',
      label: '审核',
      popConfirm: {
        title: '是否确认审核通过，审核通过后将不可以修改！',
        placement: 'left',
        confirm: handleExamine.bind(null, record),
        disabled: record.status == 1
      },
      ifShow: hasPermission([177])
    },
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleUpdate.bind(null, record),
      disabled: record.status == 1,
      ifShow: hasPermission([178])
    }
  ]
  return buttonList
}

/** 操作按钮 */
function createDropDownActions(record: EditRecordRow): ActionItem[] {
  let buttonList: ActionItem[] = [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission([176])
    },
    {
      icon: 'ant-design:delete-outlined',
      label: '删除',
      color: 'error',
      popConfirm: {
        title: '是否确认删除',
        placement: 'left',
        confirm: handleDelete.bind(null, record),
        disabled: record.status == 1
      },
      ifShow: hasPermission([179])
    }
  ]

  return buttonList
}

/** 新增 */
function handleCreate() {
  openDrawer(true, {
    isUpdate: false
  })
  setDrawerProps({ title: '新增冲销单' })
}

/** 编辑 */
function handleUpdate(record, e) {
  e.stopPropagation()
  openDrawer(true, {
    record,
    isUpdate: true
  })
  setDrawerProps({ title: '更新冲销单' })
}

/** 审核 */
async function handleExamine(record) {
  try {
    console.log(record)
    await examineReversalOrder({ id: record.id })
    reload()
    message.success('审核成功！')
  } catch (error) {
    message.success('审核失败！')
    throw new Error(`${error}`)
  }
}

/** 删除 */
async function handleDelete(record) {
  try {
    await deleteReversalOrder({ id: record.id })
    reload()
    message.success('删除成功!')
  } catch (error) {
    message.success('删除失败')
    throw new Error(`${error}`)
  }
}

/** 查看详情 */
function handleDetail(record) {
  openDetailsDrawer(true, {
    record
  }),
    setDetailsDrawerProps({ title: '查看冲销单详情' })
}

/**  回调 */
function handleSuccess() {
  reload()
}
</script>
