<template>
  <BasicModal @register="registerauditModal" @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
      <template #Item="{ model, field }">
        <BasicTable :data-source="model[field]" @register="registerTable" :columns="columns" :show-index-column="false">
          <template #expandedRowRender="{ record: cellRecord }">
            <BasicTable
              :ref="(el) => (expandedRowRefs[cellRecord.request_id] = el)"
              :columns="tablecolum"
              :can-resize="false"
              :show-index-column="false"
              :data-source="cellRecord.item_request_sub"
              :actionColumn="{
                width: 200,
                title: '操作',
                dataIndex: 'action'
              }"
            >
              <template #toolbar>
                <div style="display: flex; justify-content: flex-start; width: 100%">
                  <Button type="primary" @click="handleAdd(cellRecord, 'add')">添加</Button>
                </div>
              </template>
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'imgs'">
                  <TableImg :imgList="record.imgs" :simpleShow="true" />
                </template>
                <template v-if="column.dataIndex === 'files'">
                  <div v-for="(newVal, index) in record.files" :key="index">
                    <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
                  >
                </template>
                <template v-if="column.key === 'action'">
                  <TableAction :actions="splitActions(record, cellRecord)" />
                </template> </template
            ></BasicTable>
          </template>
        </BasicTable>
      </template>
    </BasicForm>
    <splitModal @register="registerSplitModal" @handle-add="handlesplitadd" />
    <PreviewFile @register="registerModal" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicTable, TableActionType, TableImg, TableAction, ActionItem, useTable } from '/@/components/Table'
import { schemas, columns, tablecolum } from '../datas/audit.datas'
import { message, UploadFile, Upload, Button } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { ref, unref, watch } from 'vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import splitModal from './splitModal.vue'
import { cloneDeep } from 'lodash-es'
import { useMessage } from '/@/hooks/web/useMessage'
import { createImgPreview } from '/@/components/Preview'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { productionsaleProcesssetProcessIsFinish } from '/@/api/orderprocess/Salespurchasingprocess'
import Decimal from 'decimal.js'

const { createMessage } = useMessage()
const totalSplittable = ref<any>([])
const emit = defineEmits(['success', 'register'])

//附件
const filesList = ref<UploadFile[]>([])
const expandedRowRefs = ref<{ [key: string]: TableActionType | Nullable<any> }>({})
const [registerSplitModal, { openModal: opensplitModal }] = useModal()
const [registerModal, { openModal }] = useModal()
const [registerTable, { getDataSource }] = useTable({
  pagination: false
})
const init_split = ref()

const [registerauditModal, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  console.log(data)

  totalSplittable.value = []
  filesList.value = []
  resetFields()
  onMounteds(data)
})
const [registerForm, { setFieldsValue, updateSchema, validate, resetFields }] = useForm({
  baseColProps: { span: 7 },
  showActionButtonGroup: false,
  labelWidth: 100,
  schemas
})

watch(
  () => filesList.value,
  async (val) => {
    console.log(val)
    await setFieldsValue({ files: val?.map((item) => item.url) })
  }
)

function getTextBeforeLastDot(str) {
  const regex = /^(.*?)(\.[^.]*)?$/
  const match = str.match(regex)
  return match ? match[1] : str // 确保无匹配时返回原字符串
}

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    console.log(file)
    changeOkLoading(true)
    const names = getTextBeforeLastDot(file.name)
    const curFile = filesList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile, names)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({
      files: filesList.value.map((item) => item.url)
    })
    const isAllDone = filesList.value.every((item) => item.url)
    if (isAllDone) {
      changeOkLoading(false)
    }
  } catch (err) {
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}

//omountd
async function onMounteds(data) {
  await updateSchema([
    {
      field: 'production_item_id',
      componentProps: {
        options: [
          {
            value: data.record.production_item_id,
            label: data.record.name
          }
        ]
      }
    },
    {
      field: 'new_production_item_id',
      required: data.productiondata ? true : false,
      ifShow: data.productiondata ? true : false,
      componentProps: {
        options: data.productiondata
          ? [
              {
                value: data.productiondata.id,
                label: data.productiondata.name
              }
            ]
          : []
      }
    },
    {
      field: 'new_inCharge',
      defaultValue: data.productiondata ? data.productiondata.inCharge : undefined,
      required: data.productiondata ? true : false,
      ifShow: data.productiondata ? true : false,
      componentProps: {
        options: data.productiondata ? data.productiondata.inCharge_name : [],
        selectProps: {
          allowClear: true,
          mode: 'multiple',
          fieldNames: { key: 'id', value: 'id', label: 'name' }
        }
      }
    },
    {
      field: 'item',
      ifShow: data.record.production_item.is_split === 1
    },
    {
      field: 'duration',
      required: data.productiondata ? true : false,
      dynamicDisabled: true
    }
  ])
  init_split.value = data.record.production_item.is_split

  const [datePart, timePart] = data.record.created_at.split(' ')
  const [year, month, day] = datePart.split('-').map(Number)
  const [hours, minutes, seconds] = timePart.split(':').map(Number)
  const date = new Date(year, month - 1, day, hours, minutes, seconds)
  date.setHours(date.getHours() + Number(data.record.production_item.hours))
  const pad = (n) => n.toString().padStart(2, '0')
  const end = `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(
    date.getMinutes()
  )}:${pad(date.getSeconds())}`

  filesList.value = data.record?.files

  setFieldsValue({
    ...data.record,
    production_item_id: data.record.production_item_id,
    item: data.production_sale_item,
    start_at: data.record.created_at,
    end_at: end,
    new_production_item_id: data.record.new_production_item_id,
    duration: data.record.production_item.hours
  })
}
//拆分
function splitActions(record: any, cellRecord: any): ActionItem[] {
  return [
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record, cellRecord, 'edit')
    },
    {
      label: '删除',
      onClick: handledeletd.bind(null, record)
    }
  ]
}
function handleEdit(record, cellRecord, type) {
  opensplitModal(true, { record: { ...record, sname: cellRecord.name }, type, qty_request: cellRecord.quantity })
}

//子产品删除
async function handledeletd(record: any) {
  const tableAction = unref(expandedRowRefs)[record.request_id]?.tableAction
  const tableData = tableAction.getDataSource()
  const newData = tableData.filter((item) => item.key !== record.key)
  tableAction.setTableData(newData)
  totalSplittable.value = totalSplittable.value.filter((item) => {
    return item.key !== record.key
  })
}

function handleAdd(record, type) {
  opensplitModal(true, { record, type, qty_request: record.quantity })
}
function handlesplitadd(params) {
  console.log(params)
  const tableAction = unref(expandedRowRefs)[params.request_id]?.tableAction

  if (params.key) {
    tableAction.updateTableDataRecord(params.key, { ...params })
    const index = totalSplittable.value.findIndex((obj) => obj.key === params.key)
    totalSplittable.value.splice(index, 1, params)
  } else {
    const clonedDataSource = cloneDeep(tableAction.getDataSource())
    clonedDataSource.push(params)
    const dataSource = clonedDataSource
    tableAction.setTableData(dataSource)
    const tabledata = tableAction.getDataSource()
    totalSplittable.value.push(tabledata[tabledata.length - 1])
  }
}

//计算子产品占比
async function handleCalculate(data) {
  // 用于存储分组后的数据
  const groupedData = {}
  // 分组数据
  data.forEach((item) => {
    if (!groupedData[item.request_id]) {
      groupedData[item.request_id] = []
    }
    groupedData[item.request_id].push(item)
  })

  // 调整每个分组的 qroportion 总和不超过 100
  for (const requestId in groupedData) {
    let groups = groupedData[requestId]

    let group = groupedData[requestId].filter((item) => item.type !== 3)

    if (group.length > 0) {
      let totalQroportion = group.reduce((sum, item) => new Decimal(sum).add(item.proportion_org).toDecimalPlaces(2).toNumber(), 0)
      if (totalQroportion !== 100) {
        const datasource = await getDataSource()
        datasource.forEach((item) => {
          if (item.id == requestId) {
            message.error(`主产品'${item.name}' 下的子产品占比总和不为100%`)
            throw new Error(`主产品'${item.name}' 下的子产品占比总和必须为100`)
          }
        })
      }
    }

    // 更新分组后的数据
    groupedData[requestId] = groups
  }

  // 将分组后的数据重新合并为数组
  const result = ref<any>([])
  for (const key in groupedData) {
    result.value.push(...groupedData[key])
  }
  result.value.forEach((item) => {
    delete item.key
    delete item.sname
  })
  return result.value
}

//提交
async function handleSubmit() {
  try {
    changeOkLoading(true)
    const FormData = await validate()
    const itemsub = ref()
    if (init_split.value == 1) {
      itemsub.value = await handleCalculate(totalSplittable.value)
    }
    console.log(itemsub.value)

    delete FormData.item
    const params = init_split.value == 1 ? { ...FormData, items_sub: itemsub.value } : { ...FormData }
    await productionsaleProcesssetProcessIsFinish(params)
    setTimeout(() => {
      emit('success')
      changeOkLoading(false)
      closeModal()
    }, 1000)
  } catch (e) {
    console.log(e)
    changeOkLoading(false)
  } finally {
    changeOkLoading(false)
  }
}
//展示
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  console.log(prefix)
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>
