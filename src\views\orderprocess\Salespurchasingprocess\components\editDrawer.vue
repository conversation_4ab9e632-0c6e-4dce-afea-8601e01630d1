<template>
  <BasicDrawer @register="registerDrawer" @ok="handleSubmit">
    <!-- <Steps :current="0" direction="vertical">
      <Step>
        <template #icon>
          <loading-outlined />
        </template>
        <template #title>销售下单</template>
        <template #description
          ><div> <BasicForm @register="registerForm" /> </div
        ></template>
      </Step>
      <Step>
        <template #description><div>asdf</div></template>
      </Step>
      <Step>
        <template #description><div>asdf</div></template>
      </Step>
    </Steps> -->
    <BasicForm @register="registerForm" />
    <Tabs destroyInactiveTabPane>
      <TabPane key="1" tab="商品信息" type="card" forceRender>
        <BasicTable @register="registerTable" @selection-change="selectionchange">
          <template #headerCell="{ column }">
            <template v-if="searchConfig[column.dataIndex]">
              <Popover trigger="click" :title="searchConfig[column.dataIndex].title">
                <template #content>
                  <Input
                    allowClear
                    style="width: 70%"
                    v-model:value="searchValues[column.dataIndex]"
                    :placeholder="searchConfig[column.dataIndex].placeholder"
                    @change="handleallowClear"
                  />
                  <Button
                    style="margin-left: 5px"
                    type="primary"
                    @click="handleBatchEdit(searchValues[column.dataIndex], searchConfig[column.dataIndex].field)"
                  >
                    确定
                  </Button>
                </template>
                <span style="margin-right: 10px">
                  {{ column.customTitle }}
                  <EditOutlined />
                </span>
              </Popover>
            </template>
            <template v-else>{{ column.customTitle }}</template>
          </template>
          <template #bodyCell="{ text, column }">
            <template v-if="column.key === 'imgs'">
              <TableImg :imgList="text" :simpleShow="true" />
            </template>
          </template>
        </BasicTable>
      </TabPane>
      <TabPane key="2" tab="工序详情" forceRender>
        <BasicTable @register="registerprocessTable">
          <template #headerCell="{ column }">
            <div>{{ column.customTitle }}</div>
          </template>
        </BasicTable>
      </TabPane>
    </Tabs>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
// import { Steps, Step } from 'ant-design-vue'
// import { LoadingOutlined } from '@ant-design/icons-vue'
import { BasicForm, useForm } from '/@/components/Form'
import { columns, processcolumns, schemas, groupLeaderOptions } from '../datas/edit.datas'
import { BasicTable, useTable, TableImg } from '/@/components/Table'
import { message, Tabs, TabPane, Popover, Input, Button } from 'ant-design-vue'
import { productionsaleProcessupdate } from '/@/api/orderprocess/Salespurchasingprocess'
import { ref } from 'vue'
import { EditOutlined } from '@ant-design/icons-vue'
import { cloneDeep } from 'lodash-es'

const type = ref('')
const emit = defineEmits(['success'])

//在script部分添加搜索配置
const searchConfig = {
  name: {
    title: '搜索选择产品名称',
    placeholder: '请输入产品名称',
    field: 'name'
  },
  uniqid: {
    title: '搜索选择产品编码',
    placeholder: '请输入产品编码',
    field: 'uniqid'
  },
  desc: {
    title: '搜索选择产品描述',
    placeholder: '请输入产品描述',
    field: 'desc'
  },
  location_space: {
    title: '搜索选择产品位置',
    placeholder: '请输入产品位置',
    field: 'location_space'
  }
}

// 搜索值的统一管理
const searchValues = ref({
  name: '',
  uniqid: '',
  desc: '',
  location_space: ''
})

const [registerDrawer, { closeDrawer, changeOkLoading }] = useDrawerInner(async (data) => {
  console.log(data)
  resetFields()
  type.value = data.type
  await resetSchema(schemas(data.type, handleRequest))
  setTableData([])
  setprocessTableData([])
  if (data.type !== 'add') {
    data.record.production_sale_scheduling_item = data.record.production_sale_scheduling_item.map((item, index) => {
      return {
        ...item,
        inCharge_names: data.record.production.item[index].inCharge_name,
        participant_names: data.record.production.item[index].participant_name
      }
    })
    console.log(data.record.production_sale_scheduling_item)
    groupLeaderOptions.value = data.record.production.group_leader_name
      ? data.record.production.group_leader_name.map((item) => {
          return {
            label: item.name,
            value: item.id
          }
        })
      : []
    setFieldsValue({
      ...data.record,
      items: data.record.production_sale_item,
      production_name: data.record.production.name,
      group_leader: data.record.group_leader
        ? data.record.group_leader.map((item) => {
            return {
              label: item.name,
              value: item.id
            }
          })
        : []
    })
    handleRequest({ items: data.record.production_sale_item, type: 'work' })
    // await setSelectedRowKeys(data.record.production_sale_item.map((item) => item.id))
    setprocessTableData(data.record.production_sale_scheduling_item)
  }
})
const [registerTable, { getSelectRows, setTableData, getDataSource, setSelectedRowKeys }] = useTable({
  showIndexColumn: false,
  columns,
  canResize: false,
  pagination: false,
  rowKey: 'id',
  rowSelection: {}
})
const [registerprocessTable, { getDataSource: getprocessDataSource, setTableData: setprocessTableData }] = useTable({
  showIndexColumn: false,
  columns: processcolumns
})
const [registerForm, { validate, setFieldsValue, resetSchema, resetFields }] = useForm({
  baseColProps: { span: 8, style: 'padding: 0 10px' },
  actionColOptions: { span: 24 },
  showActionButtonGroup: false,
  labelWidth: 100,
  layout: 'vertical'

  // schemas: schemas()
})

async function handleSubmit() {
  try {
    changeOkLoading(true)
    // 1. 表单验证
    const formData = await validate()

    // 2. 获取选中的商品数据
    const selectedProducts = await getSelectRows()
    if (type.value !== 'add') {
      const data = await getDataSource()
      if (selectedProducts.length !== data.length) {
        message.error('请全选商品')
        return
      }
    } else {
      if (selectedProducts.length == 0) {
        message.error('请勾选商品')
        return
      }
    }
    // 3. 获取工序数据并验证
    const processData = await getprocessDataSource()
    const hasAnyInCharge = processData.some((item) => item.inCharge?.length > 0)
    if (!hasAnyInCharge) {
      message.error('请至少选择一个工序责任人')
      return
    }
    console.log(selectedProducts)

    // 4. 构建提交参数
    const params = {
      ...formData,
      items: selectedProducts.map((item) => ({
        request_id: type.value === 'add' ? item.id : item.request_id,
        quantity: item.quantity
      })),
      schedulingItems: processData.map((item) => ({
        production_item_id: type.value === 'detail' ? item.production_item_id : item.id,
        inCharge: item.inCharge?.length > 0 ? item.inCharge : null,
        participant: item.participant
      }))
    }
    delete params.production_sale_scheduling_item

    // 5. 提交数据
    await productionsaleProcessupdate(params)

    // 6. 成功处理
    message.success('保存成功')
    emit('success')
    closeDrawer()
  } catch (error) {
    message.error('保存失败')
    console.error(error)
  } finally {
    changeOkLoading(false)
  }
}

//原始table
const originaltable = ref([])
//全部勾选
const initallselectdata = ref<any[]>([])
function handleRequest({ items, type }) {
  if (type === 'work') {
    originaltable.value = cloneDeep(items)
  }
  type === 'work' ? setTableData(items) : setprocessTableData(items)
}

async function handleBatchEdit(data, field) {
  if (!data && !field) return

  const alldata = await getDataSource()
  const searchResult = fuzzySearchWithRegex(data, alldata, field)
  setTableData(searchResult)

  // 恢复当前搜索结果中已选中项的勾选状态
  const selectedIds = initallselectdata.value.map((item) => item.id)
  const currentIds = searchResult.filter((item) => selectedIds.includes(item.id)).map((item) => item.id)
  setSelectedRowKeys(currentIds)
}

function fuzzySearchWithRegex(query, items, searchField) {
  if (!query) {
    const selectedIds = initallselectdata.value.map((item) => item.id)
    setSelectedRowKeys(selectedIds)
    return originaltable.value
  }

  const regex = new RegExp(query, 'i')
  return items.filter((item) => {
    const fieldValue = item[searchField]
    return fieldValue ? regex.test(fieldValue.toString()) : false
  })
}

async function handleallowClear({ target }) {
  if (target.value === '') {
    setTableData(originaltable.value)
    const selectedIds = initallselectdata.value.map((item) => item.id)
    setSelectedRowKeys(selectedIds)
  }
}

function selectionchange({ keys, rows }) {
  // 获取当前表格数据的ID列表
  const currentTableIds = getDataSource().map((item) => item.id)
  // 1. 处理取消选中的情况
  const currentUnselectedIds = currentTableIds.filter((id) => !keys.includes(id))
  // 2. 从总选中数据中移除当前取消选中的项
  initallselectdata.value = initallselectdata.value.filter((item) => !currentUnselectedIds.includes(item.id))
  // 3. 添加新选中的项
  const existingIds = initallselectdata.value.map((item) => item.id)
  const newSelectedRows = rows.filter((row) => !existingIds.includes(row.id))
  // 4. 更新总选中数据
  initallselectdata.value = [...initallselectdata.value, ...newSelectedRows]
}
</script>
<!-- <style lang="less">
.hovers {
  transition: height 1s ease, width 1s ease, background-color 1s ease; /* 过渡效果 */
}
.hovers:hover {
  height: 200px !important;
  width: 100px !important;
  background-color: red;
}
</style> -->
