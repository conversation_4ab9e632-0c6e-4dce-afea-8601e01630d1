<template>
  <BasicModal @register="register" title="拆分" @ok="handleOk" width="800px">
    <BasicForm @register="registerForm">
      <template #files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          @change="change('files')"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
      <template #imgs>
        <Upload
          v-model:file-list="imgsList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          @change="change('imgs')"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { ref, watch } from 'vue'
import { UploadFile, Upload, message } from 'ant-design-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { PlusOutlined } from '@ant-design/icons-vue'
import { schemassplit } from '../datas/audit.datas'
import Decimal from 'decimal.js'

const init_record = ref()
const init_key = ref()
const type = ref()
const qty_request = ref()
const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
  console.log(data)

  resetFields()
  filesList.value = []
  imgsList.value = []
  init_key.value = ''
  init_record.value = data.record
  type.value = data.type
  qty_request.value = data.qty_request
  if (data.type == 'add') {
    setFieldsValue({
      sname: data.record.name
    })
  } else {
    init_key.value = data.record.key
    setFieldsValue({
      ...data.record
    })
    filesList.value = data.record.files?.map((item) => ({ url: item, name: item, uid: item })) ?? []
    imgsList.value = data.record.imgs?.map((item) => ({ url: item, name: item, uid: item })) ?? []
  }
})
const [registerForm, { setFieldsValue, validate, resetFields }] = useForm({
  schemas: schemassplit,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  labelCol: { span: 5 }
})

//附件
const filesList = ref<UploadFile[]>([])
//图片组
const imgsList = ref<UploadFile[]>([])

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val.map((item) => item.url) })
  }
)
watch(
  () => imgsList.value,
  async (val) => {
    await setFieldsValue({ imgs: val.map((item) => item.url) })
  }
)

const types = ref('')
function change(type) {
  types.value = type
}

async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    changeOkLoading(true)
    const result = await commonFileUpload(file, 'purchase')
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    types.value == 'files'
      ? (filesList.value = filesList.value!.map((item) => {
          return {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        }))
      : (imgsList.value = imgsList.value!.map((item) => {
          return {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        }))
    types.value == 'files'
      ? await setFieldsValue({
          files: filesList.value.map((item) => item.url)
        })
      : await setFieldsValue({
          imgs: imgsList.value.map((item) => item.url)
        })
    const isAllDone = filesList.value.every((item) => item.url)
    const isAllDone2 = imgsList.value.every((item) => item.url)
    if (isAllDone || isAllDone2) {
      changeOkLoading(false)
    }
  } catch (error) {
    changeOkLoading(false)
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    throw new Error(err)
  }
}

// 提交\
const emit = defineEmits(['register', 'handleAdd'])
async function handleOk() {
  const formdata = await validate()
  const once = new Decimal(qty_request.value).dividedBy(formdata.quantity).toDecimalPlaces(6)
  const twice = once.times(formdata.proportion_org).toDecimalPlaces(6)
  const thress = twice.dividedBy(100).toDecimalPlaces(6).toNumber()
  const params = {
    ...formdata,
    request_id: init_record.value.request_id,
    work_id: init_record.value.work_id,
    type: type.value == 'add' ? 1 : 2,
    key: init_key.value,
    id: type.value == 'add' ? undefined : init_record.value.id,
    // proportion: div(mul(div(qty_request.value, formdata.quantity, 6), formdata.proportion_org, 6), 100, 6)
    proportion: thress
  }
  emit('handleAdd', params)
  closeModal()
}
</script>
