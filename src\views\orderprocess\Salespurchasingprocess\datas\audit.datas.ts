import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'

export const schemas: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    show: false,
    component: 'Input'
  },
  {
    field: 'production_item_id',
    label: '当前工序',
    component: 'Select',
    dynamicDisabled: true,
    required: true
  },
  {
    field: 'start_at',
    label: '开工时间',
    component: 'DatePicker',
    required: true,
    dynamicDisabled: true,
    componentProps: ({ formModel }) => {
      return {
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        showTime: true,
        style: {
          width: '100%'
        },
        onChange(e) {
          const [datePart, timePart] = e.split(' ')
          const [year, month, day] = datePart.split('-').map(Number)
          const [hours, minutes, seconds] = timePart.split(':').map(Number)

          // 创建Date对象(注意月份从0开始)
          const date = new Date(year, month - 1, day, hours, minutes, seconds)

          // 添加天数
          date.setDate(date.getDate() + Number(formModel.duration))

          // 格式化输出(补零处理)
          const pad = (n) => n.toString().padStart(2, '0')
          const end = `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(
            date.getMinutes()
          )}:${pad(date.getSeconds())}`
          console.log(end)
          formModel.end_at = end
        }
      }
    }
  },
  {
    field: 'end_at',
    label: '计划完工时间',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: true,
      style: {
        width: '100%'
      }
    },
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'duration',
    label: '开工时长(小时)',
    component: 'Input',
    required: true
  },
  {
    field: 'new_production_item_id',
    label: '下一流程',
    component: 'Select',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'new_inCharge',
    label: '工序责任人',
    component: 'PagingApiSelect',
    required: true
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea'
  },
  {
    field: 'files',
    label: '附件',
    component: 'Upload',
    colProps: {
      span: 24
    },
    slot: 'Files',
    required: true
  },
  {
    field: 'item',
    label: '产品集合',
    component: 'Input',
    slot: 'Item',
    colProps: {
      span: 24
    },
    ifShow(renderCallbackParams) {
      return renderCallbackParams.model.is_split === 1
    }
  }
]

export const columns: BasicColumn[] = [
  {
    title: 'request_id',
    dataIndex: 'request_id',
    width: 150,
    defaultHidden: true,
    resizable: true
  },

  {
    title: '产品名称',
    dataIndex: 'name',
    width: 150,
    resizable: true
  },
  {
    title: '产品图片',
    dataIndex: 'imgs',
    width: 150,
    resizable: true
  },
  {
    title: '所在空间',
    dataIndex: 'location_space',
    width: 150,
    resizable: true
  },
  {
    title: '长',
    dataIndex: 'length',
    width: 50,
    resizable: true
  },
  {
    title: '高',
    dataIndex: 'height',
    width: 50,
    resizable: true
  },
  {
    title: '宽',
    dataIndex: 'width',
    width: 50,
    resizable: true
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 300,
    resizable: true
  },
  {
    title: '产品编号',
    dataIndex: 'uniqid',
    width: 150,
    resizable: true
  },
  {
    title: '产品数量',
    dataIndex: 'quantity',
    width: 100,
    resizable: true
  }
]

export const tablecolum: BasicColumn[] = [
  {
    title: 'request_id',
    dataIndex: 'request_id',
    width: 100,
    resizable: true,
    defaultHidden: true
  },
  {
    title: 'work_id',
    dataIndex: 'work_id',
    width: 100,
    resizable: true,
    defaultHidden: true
  },
  {
    title: '产品名称',
    dataIndex: 'name',
    width: 100,
    resizable: true
  },
  {
    title: '产品数量',
    dataIndex: 'quantity',
    width: 100,
    resizable: true
  },
  {
    title: '种类占比',
    dataIndex: 'proportion_org',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text + '%' : '-'
    }
  },
  {
    title: '单个sku占比',
    dataIndex: 'proportion',
    width: 100,
    resizable: true
    // customRender: ({ text }) => {
    //   return text ? text + '%' : '-'
    // }
    // defaultHidden: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 100,
    resizable: true
  },
  {
    title: '长度(CM)',
    dataIndex: 'length',
    width: 100,
    resizable: true
  },
  {
    title: '宽度(CM)',
    dataIndex: 'width',
    width: 100,
    resizable: true
  },
  {
    title: '高度(CM)',
    dataIndex: 'height',
    width: 100,
    resizable: true
  },
  {
    title: '供应商',
    dataIndex: 'remark',
    width: 100,
    resizable: true
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 100,
    resizable: true
  },
  {
    title: '图片',
    dataIndex: 'imgs',
    width: 100,
    resizable: true
  },
  {
    title: '附件',
    dataIndex: 'files',
    width: 250,
    resizable: true
  },
  {
    title: 'type',
    dataIndex: 'type',
    width: 100,
    resizable: true,
    defaultHidden: true
  }
]

//查看子产品
export const schemassplit: FormSchema[] = [
  {
    field: 'sname',
    label: '主产品名称',
    component: 'Input',
    colProps: {
      span: 12
    },
    componentProps: {
      disabled: true
    }
  },
  {
    field: 'name',
    label: '子产品名称',
    component: 'Input',
    colProps: {
      span: 12
    },
    required: true
  },
  {
    field: 'quantity',
    label: '产品数量',
    component: 'InputNumber',
    itemHelpMessage: '产品数量只能填整数',
    colProps: {
      span: 12
    },
    componentProps: {
      min: 0,
      precision: 0
    },
    required: true
  },
  {
    field: 'proportion_org',
    label: '产品占比',
    component: 'InputNumber',
    itemHelpMessage: '所有子产品占比总和为100%',
    componentProps: {
      min: 0,
      max: 100,
      precision: 2,
      addonAfter: '%'
    },
    colProps: {
      span: 12
    },
    required: true
  },
  {
    field: 'unit',
    label: '单位',
    required: true,
    component: 'Input',
    colProps: {
      span: 12
    }
  },
  {
    field: 'length',
    label: '长度(CM)',
    // required: true,
    component: 'Input',
    colProps: {
      span: 12
    }
  },
  {
    field: 'width',
    label: '宽度(CM)',
    // required: true,
    component: 'Input',
    colProps: {
      span: 12
    }
  },
  {
    field: 'height',
    label: '高度(CM)',
    // required: true,
    component: 'Input',
    colProps: {
      span: 12
    }
  },
  {
    field: 'remark',
    label: '供应商',
    component: 'InputTextArea',
    colProps: {
      span: 12
    }
  },
  {
    field: 'desc',
    label: '描述',
    component: 'InputTextArea',
    colProps: {
      span: 12
    }
  },
  {
    field: 'imgs',
    label: '图片组',
    component: 'Upload',
    slot: 'imgs',
    colProps: {
      span: 12
    }
    // required: true
  },
  {
    field: 'files',
    label: '附件',
    component: 'Upload',
    slot: 'files',
    colProps: {
      span: 12
    }
  }
]
