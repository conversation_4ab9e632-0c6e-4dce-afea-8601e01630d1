import { Tag } from 'ant-design-vue'
import { BasicColumn } from '/@/components/Table'
import { FormSchema } from '/@/components/Form'
import { isNullOrUnDef } from '/@/utils/is'
import { h } from 'vue'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { useRender } from '/@/components/Table/src/hooks/useRender'

const saleStore = useSaleOrderStore()
const saleType = {
  0: { text: '否', color: 'green' },
  1: { text: '是', color: 'red' },
  2: { text: '已回退', color: 'orange' }
}

export const columns: BasicColumn[] = [
  {
    title: '工单号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '单据单号',
    dataIndex: 'order_strid',
    width: 200,
    resizable: true
  },
  {
    title: '订单类型',
    dataIndex: 'type',
    width: 150,
    resizable: true,
    customRender: ({ value }) => (isNullOrUnDef(value) ? '-' : saleStore.saleType[value])
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 100,
    resizable: true
  },
  {
    title: '组长',
    dataIndex: 'group_leader',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      const newdata = text?.map((item) => item.name)
      return text ? useRender.renderTags(newdata) : '-'
    }
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 100,
    resizable: true
  },

  {
    title: '工单状态',
    dataIndex: 'status_name',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? h(Tag, { color: 'green' }, text) : '-'
    }
  },
  {
    title: '是否已结束',
    dataIndex: 'is_finish',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: text ? 'green' : 'red' }, text ? '是' : '否')
    }
  },
  // {
  //   title: '设计师',
  //   dataIndex: 'designer',
  //   width: 100,
  //   resizable: true,
  //   customRender: ({ text }) => {
  //     return text ? useRender.renderTags(text) : '-'
  //   }
  // },
  // {
  //   title: '主设计师',
  //   dataIndex: 'designer_header',
  //   width: 100,
  //   resizable: true,
  //   customRender: ({ text }) => {
  //     return text ? useRender.renderTags(text) : '-'
  //   }
  // },
  // {
  //   title: '采购人',
  //   dataIndex: 'purchaser',
  //   width: 100,
  //   resizable: true,
  //   customRender: ({ text }) => {
  //     return text ? useRender.renderTags(text) : '-'
  //   }
  // },
  // {
  //   title: '审单人',
  //   dataIndex: 'reviewer',
  //   width: 100,
  //   resizable: true,
  //   customRender: ({ text }) => {
  //     return text ? useRender.renderTags(text) : '-'
  //   }
  // },
  // {
  //   title: 'GB产品专员',
  //   dataIndex: 'gb_specialist',
  //   width: 100,
  //   resizable: true,
  //   customRender: ({ text }) => {
  //     return text ? useRender.renderTags(text) : '-'
  //   }
  // },
  {
    title: '回退人',
    dataIndex: 'return_inCharge_name',
    width: 100,
    resizable: true
  },

  {
    title: '回退备注',
    dataIndex: 'return_remark',
    width: 350,
    resizable: true
  },
  {
    title: '是否作废',
    dataIndex: 'is_cancel',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: text ? 'red' : 'green' }, text ? '是' : '否')
    }
  },
  {
    title: '作废人',
    dataIndex: 'cancel_inCharge_name',
    width: 100,
    resizable: true
  },
  // {
  //   title: '回退时间',
  //   dataIndex: 'return_at',
  //   width: 200,
  //   resizable: true
  // },
  {
    title: '作废时间',
    dataIndex: 'cancel_at',
    width: 200,
    resizable: true
  }
]
export const gbuilderColumns: (type, optiobs?, new_status?) => FormSchema[] = (type, options, new_status) => [
  {
    field: 'production_item_id',
    label: '工单状态',
    component: 'Select',
    required: true,
    componentProps: {
      options: [
        ...options
          .filter((item) => item.order < new_status)
          .map((item) => ({
            value: item.id,
            label: item.name
          }))
      ],
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' }
      }
    },
    show: type !== 'delay',
    ifShow: type !== 'delay'
  },
  {
    field: 'return_remark',
    label: '回退备注',
    component: 'InputTextArea',
    required: true,
    show: type !== 'delay',
    ifShow: type !== 'delay'
  },
  {
    field: 'hours',
    label: '延迟小时数',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 0,
      precision: 0
    },
    show: type === 'delay',
    ifShow: type === 'delay'
  },
  {
    field: 'remark',
    label: '延迟备注',
    component: 'InputTextArea',
    required: true,
    show: type === 'delay',
    ifShow: type === 'delay'
  }
]

export const childrenColumns: BasicColumn[] = [
  {
    title: '进度名称',
    dataIndex: 'name',
    width: 150,
    resizable: true
  },
  {
    title: '接单人',
    dataIndex: 'creator_name',
    width: 350,
    resizable: true
  },
  {
    title: '附件',
    dataIndex: 'files',
    resizable: true
  },
  {
    title: '接单时间',
    dataIndex: 'created_at',
    width: 150,
    resizable: true
  },

  // {
  //   title: '作废时间',
  //   dataIndex: 'cancel_at',
  //   resizable: true
  // },
  {
    title: '作废状态',
    dataIndex: 'is_cancel',
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: saleType[text].color }, saleType[text].text)
    }
  },
  {
    title: '是否完成',
    dataIndex: 'is_finish',
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: saleType[text].color }, saleType[text].text)
    }
  },
  {
    title: '完成时间',
    dataIndex: 'finish_at',
    resizable: true
  },
  {
    title: '审核备注',
    dataIndex: 'remark',
    resizable: true
  }
]
export const childrendelayColumns: BasicColumn[] = [
  {
    title: '延迟申请人',
    dataIndex: 'creator_name',
    width: 150,
    resizable: true
  },
  {
    title: '延迟时间(小时)',
    dataIndex: 'hours',
    width: 350,
    resizable: true
  },
  {
    title: '延迟备注',
    dataIndex: 'remark',
    resizable: true
  }
]

export const searchFormSchema: FormSchema[] = [
  {
    field: 'strid',
    label: '单号',
    component: 'Input',
    colProps: {
      span: 6
    }
  },
  {
    field: 'order_strid',
    label: '单据单号',
    component: 'Input',
    colProps: {
      span: 6
    }
  },
  {
    field: 'type',
    label: '订单类型',
    component: 'Select',
    colProps: {
      span: 6
    },
    componentProps: {
      options: Object.keys(saleStore.saleType).map((key) => {
        return {
          label: saleStore.saleType[key].text,
          value: key
        }
      })
    }
  },
  {
    field: 'creator',
    label: '创建人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getCreatorList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 6
    }
  },
  {
    field: 'group_leader',
    label: '组长',
    component: 'PagingApiSelect',
    componentProps: {
      api: getCreatorList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      mode: 'multiple',
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 6
    }
  },
  {
    field: 'cancel_inCharge',
    label: '作废人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getCreatorList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 6
    }
  },
  {
    field: 'is_cancel',
    label: '是否作废',
    component: 'Select',
    colProps: {
      span: 6
    },
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  },
  {
    field: 'created_at',
    label: '创建时间',
    component: 'SingleRangeDate',
    colProps: {
      span: 6
    },
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      startPickerProps: {
        showTime: true
      },
      endPickerProps: {
        showTime: true
      }
    }
  },
  {
    field: 'cancel_at',
    label: '作废时间',
    component: 'SingleRangeDate',
    colProps: {
      span: 6
    },
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      startPickerProps: {
        showTime: true
      },
      endPickerProps: {
        showTime: true
      }
    }
  }
]
