import { ref } from 'vue'
import { getWorkList } from '/@/api/commonUtils'
import { getSalesOrderListReq } from '/@/api/erp/sales'
import { productiongetList } from '/@/api/orderprocess/Salespurchasingprocess'
import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { useUserStoreWithOut } from '/@/store/modules/user'

const userStore = useUserStoreWithOut()
export const groupLeaderOptions = ref()
export const schemas = (type, hand: Function): FormSchema[] => [
  {
    field: 'id',
    label: 'id',
    component: 'Select',
    show: false
  },
  {
    field: 'type',
    label: '类型',
    defaultValue: 3,
    component: 'Select',
    show: false
  },
  {
    field: 'items',
    label: '类型',
    component: 'Select',
    show: false
  },
  {
    field: 'order_strid',
    label: '单据单号',
    component: 'PagingApiSelect',
    required: true,
    ifShow: true,
    show: true,
    componentProps: ({ formModel }) => {
      return {
        resultField: 'items',
        api: getWorkList,
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        searchParamField: 'source_uniqid',
        returnParamsField: 'source_uniqid',
        selectProps: {
          allowClear: true,
          fieldNames: { value: 'source_uniqid', label: 'source_uniqid' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'source_uniqid'
        },
        params: {
          type: formModel.type,
          status: [1, 2, 3, 4, 5]
        },
        onChange: async (val: number, shall) => {
          if (!shall) return
          formModel.work_id = shall.id
          const { items } = await getSalesOrderListReq({ PageSize: 99999, work_id: shall.id, is_finish_split: 0 })
          console.log(items)
          const datas = items
            .filter((item) => item.status < 15)
            .map((item) => {
              return {
                ...item,
                quantity: item.qty_request_left
              }
            })
          hand && hand({ items: datas, type: 'work' })
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    dynamicDisabled: type === 'detail'
  },
  {
    field: 'work_id',
    label: 'work_id',
    component: 'Input',
    show: false
  },
  {
    field: 'production_id',
    label: '生产模式',
    component: 'ApiSelect',
    show: false,
    ifShow: true
  },
  {
    field: 'production_name',
    label: '生产模式',
    required: true,
    component: 'PagingApiSelect',
    ifShow: true,
    componentProps: ({ formModel }) => {
      return {
        api: productiongetList,
        resultField: 'items',
        params: {
          pageSize: 9999,
          status: 1,
          is_disabled: 0,
          type: 3,
          dept_ids: [userStore.getUserInfo?.deptId]
        },
        selectProps: {
          allowClear: true,
          fieldNames: { key: 'id', value: 'name', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          returnParamsField: 'id',
          onChange: async (val: number, shall) => {
            if (!shall) return

            const items = shall.item.map((item) => {
              return {
                ...item,
                inCharge: [],
                inCharge_name: [],
                inCharge_names: item.inCharge.map((items, index) => {
                  return {
                    name: item.inCharge_name[index],
                    id: items
                  }
                }),
                participant_names: item.participant?.map((items, index) => {
                  return {
                    name: item.participant_name[index],
                    id: items
                  }
                }),
                production_item_id: item.id
              }
            })
            formModel.production_id = shall.id
            groupLeaderOptions.value = shall.group_leader
              ? shall.group_leader.map((item) => {
                  return {
                    label: item.name,
                    value: item.id
                  }
                })
              : []
            hand && hand({ items, type: 'production' })
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    dynamicDisabled: type === 'detail'
  },
  {
    field: 'group_leader',
    label: '组长',
    component: 'Select',
    componentProps: {
      options: groupLeaderOptions,
      mode: 'multiple'
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  }
]

export const columns: BasicColumn[] = [
  {
    title: 'request_id',
    dataIndex: 'request_id',
    width: 150,
    defaultHidden: true,
    resizable: true
  },

  {
    title: '产品名称',
    dataIndex: 'name',
    width: 150,
    resizable: true
  },
  {
    title: '产品图片',
    dataIndex: 'imgs',
    width: 150,
    resizable: true
  },
  {
    title: '所在空间',
    dataIndex: 'location_space',
    width: 150,
    resizable: true
  },
  {
    title: '长',
    dataIndex: 'length',
    width: 50,
    resizable: true
  },
  {
    title: '高',
    dataIndex: 'height',
    width: 50,
    resizable: true
  },
  {
    title: '宽',
    dataIndex: 'width',
    width: 50,
    resizable: true
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 300,
    resizable: true
  },
  {
    title: '产品编号',
    dataIndex: 'uniqid',
    width: 150,
    resizable: true
  },
  {
    title: '产品数量',
    // dataIndex: 'qty_request_left',
    dataIndex: 'quantity',
    width: 100,
    resizable: true
  }
]
export const processcolumns: BasicColumn[] = [
  {
    title: 'id',
    dataIndex: 'id',
    width: 150,
    defaultHidden: true,
    resizable: true
  },
  {
    title: 'production_item_id',
    dataIndex: 'production_item_id',
    width: 150,
    defaultHidden: true,
    resizable: true
  },
  {
    title: 'inCharge',
    dataIndex: 'inCharge',
    width: 150,
    ifShow: false,
    resizable: true
  },
  {
    title: 'participant',
    dataIndex: 'participant',
    width: 150,
    ifShow: false,
    resizable: true
  },
  {
    title: '工序名称',
    dataIndex: 'name',
    width: 150,
    resizable: true
  },
  {
    title: '工序责任人',
    dataIndex: 'inCharge_name',
    width: 150,
    resizable: true,
    edit: true,
    editIconShow: 'always',
    editComponent: 'PagingApiSelect',
    editComponentProps: ({ record }) => {
      console.log(record)
      return {
        options: record.inCharge_names,
        selectProps: {
          allowClear: true,
          mode: 'multiple',
          fieldNames: { key: 'id', value: 'name', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          onChange: (value: any, shall: any) => {
            console.log(value)
            record.inCharge = shall.map((item: any) => item.id)
          }
        }
      }
    },
    editRender({ text }) {
      return text ? useRender.renderTags(text) : '-'
    }
  },
  {
    title: '工序参与人',
    dataIndex: 'participant_name',
    width: 150,
    resizable: true,
    edit: true,
    editIconShow: 'always',
    editComponent: 'PagingApiSelect',
    editComponentProps: ({ record }) => {
      console.log(record)
      return {
        options: record.participant_names,
        selectProps: {
          allowClear: true,
          mode: 'multiple',
          fieldNames: { key: 'id', value: 'name', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          onChange: (value: any, shall: any) => {
            console.log(value)
            record.participant = shall.map((item: any) => item.id)
          }
        }
      }
    },
    editRender({ text }) {
      return text ? useRender.renderTags(text) : '-'
    }
  }
]
