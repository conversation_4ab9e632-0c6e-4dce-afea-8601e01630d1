<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handlecancel" v-if="hasPermission(635)" :loading="buttonlodaing">批量作废</Button>
        <Button type="primary" @click="handleAdd" v-if="hasPermission(636)" :loading="buttonlodaing">新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
      </template>
      <template #expandedRowRender="{ record: fatherrecord }">
        <BasicTable
          :columns="childrenColumns"
          :dataSource="fatherrecord.production_sale_process_item"
          :canResize="false"
          :showIndexColumn="false"
          :actionColumn="{
            title: '操作',
            dataIndex: 'action',
            width: 150,
            fixed: 'right'
          }"
        >
          <template #bodyCell="{ column, record: childRecord }">
            <template v-if="column.dataIndex === 'action'">
              <TableAction
                :actions="createChildActions(childRecord, fatherrecord)"
                :drop-down-actions="createChildDropDownActions(childRecord)"
              />
            </template>
            <template v-if="column.dataIndex === 'files'">
              <div v-for="(newVal, index) in childRecord.files" :key="index">
                <a :href="newVal.url" target="_blank" @click="handlePreview(newVal.url, $event)">{{ `${newVal.name}` }}</a></div
              >
            </template>
          </template>
          <template #expandedRowRender="{ record: fatherrecord }">
            <BasicTable
              :columns="childrendelayColumns"
              :dataSource="fatherrecord.production_sale_process_item_delay"
              :canResize="false"
              :showIndexColumn="false"
            />
          </template>
        </BasicTable>
      </template>
    </BasicTable>
    <editDrawer @register="registereditDrawer" @success="reload" />
    <auditModal @register="registerauditModal" @success="reload" />
    <PreviewFile @register="registerModal" />
  </div>
</template>
<script setup lang="tsx">
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import { Button, message } from 'ant-design-vue'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { columns, gbuilderColumns, childrenColumns, searchFormSchema, childrendelayColumns } from './datas/datas'
import {
  productionsaleProcessgetList,
  productionsaleProcessreturnStatus,
  productionsaleProcesssetDelay,
  productionsaleProcesssetIsCancel,
  productionsaleProcesssetStatus
} from '/@/api/orderprocess/Salespurchasingprocess'
import { ref } from 'vue'
import auditModal from './components/auditModal.vue'
import { useModal } from '/@/components/Modal'
import { usePermission } from '/@/hooks/web/usePermission'
import { BasicForm, useForm } from '/@/components/Form'
import { useMessage } from '/@/hooks/web/useMessage'
import { createImgPreview } from '/@/components/Preview'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { useUserStoreWithOut } from '/@/store/modules/user'

const userStore = useUserStoreWithOut()

const { hasPermission } = usePermission()
const { createMessage } = useMessage()
const buttonlodaing = ref(false)
const [registereditDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerauditModal, { openModal: openModalaudit, setModalProps: setModalPropsaudit }] = useModal()
const [registerModal, { openModal }] = useModal()

const [registerTable, { reload, getSelectRows, setLoading }] = useTable({
  useSearchForm: true,
  showIndexColumn: false,
  showTableSetting: true,
  columns,
  rowKey: 'id',
  rowSelection: {},
  afterFetch: (data) => {
    for (const item of data) {
      if (!item.production_sale_process_item) continue
      item.production_sale_process_item.forEach((newVal) => {
        if (!newVal.files || !Array.isArray(newVal.files)) return
        newVal.files = newVal.files.map((items) => {
          return {
            url: items,
            name: extractFileNamePart(items)
          }
        })
      })
    }
    return data
  },
  api: productionsaleProcessgetList,
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  formConfig: {
    schemas: searchFormSchema,
    fieldMapToTime: [
      ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['cancel_at', ['cancel_at_start', 'cancel_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  }
})

function handleAdd() {
  openDrawer(true, { type: 'add' })
  setDrawerProps({ title: '新增', showFooter: true, width: '90%' })
}
// function handleedit(record) {
//   openDrawer(true, { record, type: 'edit' })
//   setDrawerProps({ title: '编辑', showFooter: true, width: '90%' })
// }
function handleDetail(record) {
  openDrawer(true, { record, type: 'detail' })
  setDrawerProps({ title: '详情', showFooter: true, width: '90%' })
}

function createActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = [
    {
      label: '接单',
      tooltip: '设置后,订单进度将进行下一到工序',
      popConfirm: {
        title: '确定接单吗？',
        placement: 'left',
        confirm: completed.bind(null, record),
        disabled: record.is_cancel == 1 || (record.status_is_finish !== 1 && record.status !== 0) || !record.new_status
      },
      ifShow: hasPermission([640]) && record.new_status
    }
  ]

  return editButtonList
}

function createDropDownActions(record: EditRecordRow): ActionItem[] {
  return [
    // {
    //   label: '编辑',
    //   disabled: record.creator == userStore.getUserInfo?.userId ? record.is_cancel !== 1 && record.status !== 0 : true,
    //   onClick: handleedit.bind(null, record),
    //   ifShow: hasPermission([637])
    // },
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission([59])
    },
    {
      label: '退回进度状态',
      popConfirm: {
        title: (
          <div>
            <div>确认后将工单退回到特定阶段，是否确定执行？</div>
            <div>
              <BasicForm
                ref={(el: any) => (formRef.value = el?.formActionType)}
                register={useForm}
                showActionButtonGroup={false}
                schemas={gbuilderColumns('return', record.production.item, record.status)}
                baseColProps={{ span: 24 }}
              />
            </div>
          </div>
        ),
        confirm: handlegreturnStatus.bind(null, record, 'return'),
        disabled:
          record.status_inCharge == userStore.getUserInfo?.userId
            ? record.is_cancel == 1 || record.status < 2 || record.is_finish == 1
            : true
      },
      ifShow: hasPermission([638])
    }
  ]
}
function createChildDropDownActions(record: EditRecordRow): ActionItem[] {
  return [
    {
      label: '采购进度延迟',
      popConfirm: {
        title: (
          <div>
            <div>确认后将当前工序环节采购进度延迟特定时间，是否确定执行？</div>
            <div>
              <BasicForm
                ref={(el: any) => (formRef.value = el?.formActionType)}
                register={useForm}
                showActionButtonGroup={false}
                schemas={gbuilderColumns('delay', [])}
                baseColProps={{ span: 24 }}
              />
            </div>
          </div>
        ),
        disabled: record.creator == userStore.getUserInfo?.userId ? !(record.is_cancel == 0 && record.is_finish == 0) : true,
        confirm: handlegreturnStatus.bind(null, record, 'delay')
      },
      ifShow: hasPermission([638])
    }
  ]
}

function extractFileNamePart(url) {
  const regex = /\/([^\/]+)$/
  const match = url.match(regex)
  return match ? match[1] : null // 如果找到匹配项，则返回捕获组中的内容；否则返回 null
}

async function handlecancel() {
  try {
    buttonlodaing.value = true
    setLoading(true)
    const RowKeys = await getSelectRows()
    if (RowKeys.length == 0) {
      setLoading(false)
      return message.error('请选择数据')
    }
    const params = RowKeys.map((item) => {
      return {
        id: item.id,
        is_cancel: 1
      }
    })
    await productionsaleProcesssetIsCancel({ productionList: params })
    reload()
    buttonlodaing.value = false
    setLoading(false)
  } catch (e) {
    console.log(e)
    setLoading(false)
    buttonlodaing.value = false
  } finally {
    buttonlodaing.value = false
    setLoading(false)
  }
}

//审核
function handleconfirm(record, fatherrecord) {
  const productiondata = fatherrecord.production.item.find((item) => item.id == fatherrecord.new_production_item_id)

  openModalaudit(true, {
    record,
    productiondata,
    production_sale_item: fatherrecord.production_sale_item
  })
  setModalPropsaudit({ title: '审核', width: '70%', defaultFullscreen: true })
}

//退回
const formRef = ref<any>()
async function handlegreturnStatus(record, type) {
  const formdata = await formRef.value?.validate()
  type === 'return'
    ? await productionsaleProcessreturnStatus({ ...formdata, id: record.id })
    : await productionsaleProcesssetDelay({ id: record.id, ...formdata })
  await reload()
  formRef.value?.resetFields()
}

//展示
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}

function createChildActions(record: Recordable, fatherrecord: Recordable): ActionItem[] {
  return [
    {
      label: `${record.name}`,
      tooltip: '设置后,订单进度将进行下一到工序',
      onClick: handleconfirm.bind(null, record, fatherrecord),
      disabled: record.creator == userStore.getUserInfo?.userId ? !(record.is_cancel == 0 && record.is_finish == 0) : true,
      ifShow: hasPermission([639])
    }
  ]
}
async function completed(record) {
  try {
    await productionsaleProcesssetStatus({ production_sale_process_id: record.id, production_item_id: record.new_production_item_id })
  } catch (e) {
    console.log(e)
  } finally {
    await reload()
  }
}
</script>
