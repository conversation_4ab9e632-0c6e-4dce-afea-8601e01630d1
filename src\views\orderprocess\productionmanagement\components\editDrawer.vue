<template>
  <BasicDrawer @register="registerDrawer" width="90%" @ok="handleOK">
    <BasicForm @register="registerForm" />
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleAdd" :disabled="types === 'detail'">新增</Button>
      </template>
      <template #headerCell="{ column }">
        <template v-if="column.dataIndex === 'timeout_hours'">
          <Tooltip title="上一流程未被接单超时时长">
            <Popover trigger="click" title="批量填写超时时间">
              <template #content>
                <div style="display: flex">
                  <InputNumber v-model:value="timeouthours" />
                  <Button class="ml-2" type="primary" @click="handleSetAll('timeout_hours', timeouthours)"> 确定 </Button>
                </div>
              </template>
              <span style="margin-right: 10px">{{ column.customTitle }}</span>
              <EditOutlined />
            </Popover>
          </Tooltip>
        </template>
        <template v-else-if="column.dataIndex === 'participant_name'">
          <Popover trigger="click" title="批量填写工序参与人">
            <template #content>
              <PagingApiSelect
                :api="getStaffList"
                resultField="items"
                searchMode
                pagingMode
                :pagingSize="20"
                mode="multiple"
                v-model:value="participantarras"
                :selectProps="{
                  fieldNames: { key: 'id', value: 'name', label: 'name' },
                  showSearch: true,
                  placeholder: '请选择',
                  optionFilterProp: 'name',
                  allowClear: true,
                  style: { width: '200px' },
                  onChange: participantFn
                }"
              />
              <Button type="primary" @click="handleSetAll('participant_name', participantarras)"> 确定 </Button>
            </template>
            <span style="margin-right: 10px">{{ column.customTitle }}</span>
            <EditOutlined />
          </Popover>
        </template>
        <template v-else-if="column.dataIndex === 'is_finish_split'">
          <Tooltip title="工序环节中需存在一个环节产品拆分完成">
            <span style="margin-right: 10px">{{ column.customTitle }}</span>
          </Tooltip>
        </template>
        <template v-else>{{ column.customTitle }}</template>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import { cloums, schemas } from '../datas/edit.data'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm, PagingApiSelect } from '/@/components/Form'
import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table'
import type { EditRecordRow } from '/@/components/Table'
import { Button, message, Popover, InputNumber, Tooltip } from 'ant-design-vue'
import { EditOutlined } from '@ant-design/icons-vue'
import { ref } from 'vue'
import { productionupdate } from '/@/api/baseData/productionmanagement'
import { getStaffList } from '/@/api/erp/systemInfo'

const currentEditKeyRef = ref('')
const emit = defineEmits(['success'])
//删除的数据
const deletearr = ref<any>([])

const types = ref()
const typestatus = ref()
const timeouthours = ref(0)
const participantarras = ref([])
const participantids = ref([])

const [registerDrawer, { changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  console.log(data)
  resetFields()
  currentEditKeyRef.value = ''
  types.value = data.type
  setTableData([])
  resetSchema(schemas(data.type, data.record?.status))
  setColumns(cloums(data.type, data.record?.status))
  if (data.type !== 'add') {
    typestatus.value = data.record?.status
    await setFieldsValue({
      id: data.record?.id,
      name: data.record.name,
      type: data.record.type,
      dept_ids: data.record.dept_ids.map((item) => {
        return item.dept_id
      }),
      group_leader: data.record.group_leader
        ? data.record.group_leader.map((item) => {
            return item.id
          })
        : []
    })
    data.record.item.forEach((item) => {
      item['inCharge_names'] = item.inCharge_name
      item['inCharge'] = item.inCharge
      item['participant_name'] = item.participant_name
      item['participant'] = item.participant
    })
    setTableData(data.record.item)
  }
})

const [registerForm, { resetFields, validate, setFieldsValue, resetSchema }] = useForm({
  baseColProps: { span: 8 },
  showActionButtonGroup: false,
  labelCol: { span: 6 }
})

const [registerTable, { setTableData, getDataSource, deleteTableDataRecord, getColumns, updateTableDataRecord, setColumns }] = useTable({
  showIndexColumn: false,
  // columns: cloums(),
  actionColumn: {
    width: 250,
    title: '操作',
    dataIndex: 'action'
  }
})

function createActions(record: EditRecordRow): ActionItem[] {
  if (!record.editable) {
    return [
      {
        label: '编辑',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false || types.value === 'detail',
        onClick: handleEdit.bind(null, record)
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          placement: 'left',
          confirm: handleDelete.bind(null, record)
        },
        ifShow: typestatus.value !== 1,
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false || types.value === 'detail'
      },
      {
        label: '复制',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false || types.value === 'detail',
        onClick: handlecopylink.bind(null, record),
        ifShow: typestatus.value !== 1
      }
    ]
  }
  return [
    {
      label: '保存',
      onClick: handleSave.bind(null, record)
    },
    {
      label: '取消',
      popConfirm: {
        title: '是否取消编辑',
        placement: 'right',
        confirm: handleCancel.bind(null, record)
      }
    }
  ]
}

//格式化某一行的数据
function formatObject(record) {
  // getDataSource()有我们不需要的属性,所以需要清除
  let temporary = {}
  for (let colName of getColumns()) {
    if (colName.key !== 'action') {
      temporary[`${colName.dataIndex}`] = record[`${colName.dataIndex}`]
    }
  }
  return temporary
}
// 格式化提交的数据
function formatSubmit() {
  // getDataSource()有我们不需要的属性,所以需要清除
  const dataSource = getDataSource().map((item) => {
    let temporary = {}
    for (let colName of getColumns()) {
      if (colName.key !== 'action') {
        temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
      }
    }
    return temporary
  })
  return dataSource
}
//添加明细
async function handleAdd() {
  // 检查当前是否有未保存的编辑
  if (currentEditKeyRef.value !== '') {
    return message.error({ content: '请先保存数据', key: 'saving' })
  }
  const newRowDataItem = {
    name: '',
    order: getDataSource().length + 1,
    inCharge_names: [],
    participant_name: [],
    is_finish_split: 0
  }
  const newDataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))

  newDataSource.sort((a, b) => a.order + b.order)
  setTableData(newDataSource)
}

// 存储编辑前的record
const beforeRecord = ref()
//编辑
async function handleEdit(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  beforeRecord.value = cloneDeep(record)
  record.onEdit?.(true, false)
}

//删除tabel
function handleDelete(record) {
  deleteTableDataRecord(record.key)
  if (record.id) {
    const dataobj = {
      name: record.name,
      order: record.order
    }
    deletearr.value.push(dataobj)
  }
  const dataSource = getDataSource()
  dataSource.reverse().forEach((item, index) => {
    item.order = index + 1 // 从 1 开始编号
  })
  setTableData(dataSource.reverse())
}

//复制明细
async function handlecopylink(record) {
  const newrecord = formatObject(record)
  const datalength = await getDataSource()
  const newRowDataItem = {
    ...newrecord,
    order: datalength.length + 1
  }
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
}

//保存
async function handleSave(record: EditRecordRow) {
  console.log(record)

  // 校验
  const valid = await record.onValid?.()
  if (valid) {
    try {
      // 检查必填字段
      const requiredFields = {
        name: '流程节点名称',
        order: '流水节点顺序',
        // is_supplier: '是否需要供应商',
        is_split: '是否需要拆分',
        inCharge_names: '负责人',
        participant_name: '参与人',
        hours: '工序时长(小时)',
        is_finish_split: '是否已完成产品拆分'
      }

      // 检查所有必填字段
      for (const [field, label] of Object.entries(requiredFields)) {
        if (record[field] === undefined || record[field] === null || record[field] === '') {
          message.error({ content: `请填写${label}` })
          return
        }
      }

      const dataSource = getDataSource()
      const newdata = dataSource.filter((item) => item.key !== record.key)

      // 获取所有节点（包括当前编辑的节点）并按order排序
      const allNodes = [...newdata, record].sort((a, b) => a.order - b.order)
      const currentIndex = allNodes.findIndex((item) => item.order === record.order)

      // 检查产品拆分相关逻辑
      if (record.is_finish_split == 1) {
        // 检查是否已有其他节点标记为已完成拆分
        if (newdata.some((item) => item.is_finish_split === 1)) {
          message.error({ content: '只能有一个流程节点标记为已完成产品拆分' })
          return
        }

        // 检查后续节点是否存在产品拆分
        if (allNodes.slice(currentIndex + 1).some((item) => item.is_split === 1)) {
          message.error({ content: '已完成产品拆分后的节点不能再进行产品拆分' })
          return
        }
      }

      // 检查当前节点是否可以进行产品拆分
      if (record.is_split == 1) {
        // 检查之前的节点是否已有完成拆分的
        if (allNodes.slice(0, currentIndex).some((item) => item.is_finish_split === 1)) {
          message.error({ content: '已完成产品拆分的节点之后不能再进行产品拆分' })
          return
        }
      }

      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
      }
      message.success({ content: '数据已保存', key: 'saving' })
    } catch (error) {
      message.error({ content: '保存失败', key: 'saving' })
      throw new Error(`${error}`)
    }
  } else {
    message.error({ content: '请填写正确的数据', key: 'saving' })
  }
}

//取消
function handleCancel(record: EditRecordRow) {
  currentEditKeyRef.value = ''
  updateTableDataRecord(record.key, {
    name: beforeRecord.value.name,
    order: beforeRecord.value.order,
    // is_supplier: beforeRecord.value.is_supplier,
    days: beforeRecord.value.days,
    is_split: beforeRecord.value.is_split,
    inCharge: beforeRecord.value.inCharge,
    inCharge_name: beforeRecord.value.inCharge_name,
    inCharge_names: beforeRecord.value.inCharge_names,
    participant_name: beforeRecord.value.participant_name,
    participant: beforeRecord.value.participant,
    timeout_hours: beforeRecord.value.timeout_hours,
    is_finish_split: beforeRecord.value.is_finish_split
  })
  record.onEdit?.(false, false)
}

async function handleOK() {
  try {
    changeOkLoading(true)

    const formdata = await validate()
    const table = formatSubmit()

    if (currentEditKeyRef.value !== '') {
      changeOkLoading(false)
      return message.error({ content: '请先保存数据', key: 'saving' })
    }
    if (!table.length) {
      changeOkLoading(false)
      return message.error({ content: '请添加明细', key: 'saving' })
    }

    // 验证is_finish_split
    const finishSplitCount = table.filter((item) => item.is_finish_split === 1).length
    if (finishSplitCount === 0) {
      changeOkLoading(false)
      return message.error('必须有一个工序标记为已完成产品拆分')
    }
    if (finishSplitCount > 1) {
      changeOkLoading(false)
      return message.error('只能有一个工序标记为已完成产品拆分')
    }

    for (const item of table) {
      if (item.name === '' || item.name == null || item.name == undefined) {
        changeOkLoading(false)
        return message.error('请填写流程节点名称')
      } else if (item.order === '' || item.order == null || item.order == undefined) {
        changeOkLoading(false)
        return message.error('请填写流水节点顺序')
      }
    }
    table.forEach((item) => {
      delete item.inCharge_names
      delete item.participant_name
    })

    const params = {
      ...formdata,
      items: table
    }

    await productionupdate(params)
    setTimeout(() => {
      changeOkLoading(false)
      closeDrawer()
      emit('success')
    }, 1000)
  } catch (e) {
    console.log(e)
    changeOkLoading(false)
  } finally {
    timeouthours.value = 0
    participantids.value = []
    participantarras.value = []
    changeOkLoading(false)
  }
}

function participantFn(_, shall) {
  participantids.value = shall.map((item) => item.id)
}
function handleSetAll(type, value) {
  const table = getDataSource()
  if (type === 'timeout_hours') {
    table.forEach((item) => {
      item.timeout_hours = value
    })
  } else if (type === 'participant_name') {
    table.forEach((item) => {
      const uniqueParticipantNames = new Set(item.participant_name)
      const uniqueParticipants = new Set(item.participant)
      value.forEach((v) => {
        uniqueParticipantNames.add(v)
      })
      participantids.value.forEach((pid) => {
        uniqueParticipants.add(pid)
      })
      item.participant_name = Array.from(uniqueParticipantNames)
      item.participant = Array.from(uniqueParticipants)
    })
  }
  setTableData(table)
}
</script>
