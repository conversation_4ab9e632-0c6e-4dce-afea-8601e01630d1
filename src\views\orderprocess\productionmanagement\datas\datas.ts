import { Tag } from 'ant-design-vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { h } from 'vue'
import { isNullOrUnDef } from '/@/utils/is'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { getDeptSelectTree } from '/@/api/admin/dept'
import { DefaultOptionType } from 'ant-design-vue/lib/select'
import { GET_STATUS_SCHEMA } from '/@/const/status'

const saleStore = useSaleOrderStore()

const disabled_schema = GET_STATUS_SCHEMA(
  [
    { label: '启用', value: 0 },
    { label: '禁用', value: 1 }
  ],
  {
    field: 'is_disabled',
    colProps: {
      span: 12
    }
  }
)

const status_schema = GET_STATUS_SCHEMA(
  [
    { label: '未审核', value: 0 },
    { label: '已审核', value: 1 }
  ],
  {
    field: 'status',
    colProps: {
      span: 12
    }
  }
)

export const cloums: BasicColumn[] = [
  {
    title: '模式名称',
    dataIndex: 'name',
    width: 200,
    resizable: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 200,
    customRender({ text }) {
      return !isNullOrUnDef(text) ? h(Tag, { color: text == 0 ? '' : 'green' }, text == 0 ? '未审核' : '已审核') : ''
    },
    resizable: true
  },
  {
    title: '是否禁用',
    dataIndex: 'is_disabled',
    width: 200,
    customRender({ text }) {
      return !isNullOrUnDef(text) ? h(Tag, { color: text == 0 ? 'green' : 'red' }, text == 0 ? '否' : '是') : ''
    },
    resizable: true
  },
  {
    title: '订单类型',
    dataIndex: 'type',
    width: 200,
    customRender({ text }) {
      return !isNullOrUnDef(text) ? saleStore.saleType[text] : ''
    },
    resizable: true
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 200,
    resizable: true
  },
  {
    title: '组长',
    dataIndex: 'group_leader',
    width: 200,
    resizable: true,
    customRender({ text }) {
      const nameArr = text?.map((item) => item.name)
      return text ? useRender.renderTags(nameArr) : '-'
    }
  },
  {
    title: '关联部门',
    dataIndex: 'dept_ids',
    width: 300,
    customRender({ text }) {
      const nameArr = text.map((item) => item.name)
      return text ? useRender.renderTags(nameArr) : '-'
    },
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  status_schema,
  disabled_schema,
  { field: 'name', label: '名称', component: 'Input' },
  {
    field: 'type',
    label: '类型',
    component: 'Select',
    componentProps: {
      options: Object.keys(saleStore.saleType).map((key) => {
        return {
          label: saleStore.saleType[key],
          value: Number(key)
        }
      })
    }
  },
  {
    field: 'dept_ids',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptSelectTree,
      treeSelectProps: {
        multiple: true,
        treeDefaultExpandAll: true,
        fieldNames: { children: 'children', key: 'id', value: 'id', label: 'name' },
        placeholder: '请选择',
        showSearch: true,
        maxTagCount: 3,
        filterTreeNode: (search: string, item: DefaultOptionType) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  }
]
