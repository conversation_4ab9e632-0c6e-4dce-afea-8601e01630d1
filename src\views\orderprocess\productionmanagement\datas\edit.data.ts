import { getDeptSelectTree } from '/@/api/admin/dept'
import { getStaffList } from '/@/api/erp/systemInfo'
import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { DefaultOptionType } from 'ant-design-vue/lib/select'

const saleStore = useSaleOrderStore()

export function schemas(type: string, status): FormSchema[] {
  return [
    {
      field: 'id',
      label: 'id',
      component: 'Input',
      show: false
    },
    {
      field: 'name',
      label: '模式名称',
      component: 'Input',
      required: true,
      dynamicDisabled: type === 'detail' || status === 1
    },
    {
      field: 'type',
      label: '订单类型',
      component: 'Select',
      required: true,
      componentProps: {
        options: Object.keys(saleStore.saleType).map((key) => {
          return {
            label: saleStore.saleType[key],
            value: Number(key)
          }
        })
      },
      dynamicDisabled: type === 'detail' || status === 1
    },
    {
      field: 'dept_ids',
      label: '关联部门',
      required: true,
      component: 'ApiTreeSelect',
      componentProps: {
        api: getDeptSelectTree,
        treeSelectProps: {
          multiple: true,
          treeDefaultExpandAll: true,
          fieldNames: { children: 'children', key: 'id', value: 'id', label: 'name' },
          placeholder: '请选择',
          showSearch: true,
          maxTagCount: 3,
          filterTreeNode: (search: string, item: DefaultOptionType) => {
            if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
            return false
          }
        }
      },
      dynamicDisabled: type === 'detail'
    },
    // {
    //   field: 'group_leader',
    //   label: '组长',
    //   component: 'Input',
    //   show: false
    //   // ifShow: false
    //   // dynamicDisabled:type==='detail'
    // },
    {
      field: 'group_leader',
      label: '组长姓名',
      component: 'PagingApiSelect',
      componentProps: {
        api: getStaffList,
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        mode: 'multiple',
        returnParamsField: 'ids',
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true
          // onChange(_, shall) {
          //   formModel.group_leader = shall.map((item) => item.id)
          // }
        }
      }
    }
  ]
}

export function cloums(type: string, status): BasicColumn[] {
  return [
    {
      title: '流水节点顺序',
      dataIndex: 'order',
      width: 100,
      resizable: true
      // editRow: true,
      // editComponent: 'InputNumber',
      // editComponentProps: {
      //   min: 1
      // }
    },
    {
      title: '流程节点名称',
      dataIndex: 'name',
      width: 300,
      resizable: true,
      editRow: true,
      editComponent: 'Input',
      editDynamicDisabled: type === 'detail' || status === 1
    },
    {
      title: 'inCharge',
      dataIndex: 'inCharge',
      ifShow: false,
      resizable: true
      // defaultHidden: true
    },
    {
      title: 'participant',
      dataIndex: 'participant',
      ifShow: false,
      resizable: true
      // defaultHidden: true
    },
    {
      title: '工序负责人',
      dataIndex: 'inCharge_names',
      editRow: true,
      width: 300,
      resizable: true,
      editComponent: 'PagingApiSelect',
      editComponentProps: ({ record }) => {
        return {
          api: getStaffList,
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          pagingSize: 20,
          mode: 'multiple',
          returnParamsField: 'ids',
          selectProps: {
            fieldNames: { key: 'id', value: 'name', label: 'name' },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            onChange(_, shall) {
              record.inCharge = shall.map((item) => item.id)
            }
          }
        }
      },
      editRender({ text }) {
        return text ? useRender.renderTags(text) : '-'
      },
      editDynamicDisabled: type === 'detail'
    },
    {
      title: '工序参与人',
      dataIndex: 'participant_name',
      editRow: true,
      width: 300,
      resizable: true,
      editComponent: 'PagingApiSelect',
      editComponentProps: ({ record }) => {
        return {
          api: getStaffList,
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          pagingSize: 20,
          mode: 'multiple',
          returnParamsField: 'ids',
          selectProps: {
            fieldNames: { key: 'id', value: 'name', label: 'name' },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            onChange(_, shall) {
              record.participant = shall.map((item) => item.id)
            }
          }
        }
      },
      editRender({ text }) {
        return text ? useRender.renderTags(text) : '-'
      },
      editDynamicDisabled: type === 'detail'
    },
    // {
    //   title: '是否打款通知',
    //   dataIndex: 'is_finance',
    //   width: 150,
    //   resizable: true,
    //   editRow: true,
    //   editComponent: 'Select',
    //   editComponentProps: {
    //     options: [
    //       { label: '是', value: 1 },
    //       { label: '否', value: 0 }
    //     ]
    //   }
    // },
    // {
    //   title: '是否填写供应商',
    //   dataIndex: 'is_supplier',
    //   width: 150,
    //   resizable: true,
    //   editRow: true,
    //   editComponent: 'Select',
    //   editComponentProps: {
    //     options: [
    //       { label: '是', value: 1 },
    //       { label: '否', value: 0 }
    //     ]
    //   }
    // },
    {
      title: '开工周期(小时)',
      dataIndex: 'hours',
      width: 150,
      resizable: true,
      editRow: true,
      editComponent: 'InputNumber',
      editComponentProps: {
        min: 0,
        precision: 0
      },
      editDynamicDisabled: type === 'detail' || status === 1
    },
    {
      title: '上一流程超时时长(小时)',
      dataIndex: 'timeout_hours',
      width: 250,
      resizable: true,
      editRow: true,
      editComponent: 'InputNumber',
      editComponentProps: {
        min: 0,
        precision: 0
      },
      editDynamicDisabled: type === 'detail'
    },
    {
      title: '当前工序是否需要拆分产品',
      dataIndex: 'is_split',
      width: 200,
      resizable: true,
      editRow: true,
      editComponent: 'Select',
      editComponentProps: {
        options: [
          { label: '是', value: 1 },
          { label: '否', value: 0 }
        ]
      },
      editDynamicDisabled: type === 'detail' || status === 1
    },
    {
      title: '是否已完成产品拆分',
      dataIndex: 'is_finish_split',
      width: 200,
      resizable: true,
      editRow: true,
      editComponent: 'Select',
      editComponentProps: {
        options: [
          { label: '是', value: 1 },
          { label: '否', value: 0 }
        ]
      },
      editDynamicDisabled: type === 'detail' || status === 1
    }
  ]
}
