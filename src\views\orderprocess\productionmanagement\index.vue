<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleaudit('status')" :loading="lodaing" v-if="hasPermission([641])"> 批量审核 </Button>
        <Button type="primary" @click="handleaudit('is_disabled')" :loading="lodaing" v-if="hasPermission([642])"> 批量禁用 </Button>
        <Button type="primary" @click="handleaudit('disabled')" :loading="lodaing" v-if="hasPermission([643])"> 批量启用 </Button>
        <Button type="primary" @click="handleAdd" :loading="lodaing" v-if="hasPermission([644])"> 新增 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <editDrawer @register="registerDrawer" @success="reload" />
  </div>
</template>

<script setup lang="ts">
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import { Button, message } from 'ant-design-vue'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { productiongetList, productionsetIsDisabled, productionsetStatus } from '/@/api/baseData/productionmanagement'
import { cloums, schemas } from './datas/datas'
import { ref } from 'vue'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'
import { usePermission } from '/@/hooks/web/usePermission'

const { hasPermission } = usePermission()

const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const lodaing = ref(false)

const [registerTable, { reload, getSelectRows, clearSelectedRowKeys }] = useTable({
  showTableSetting: true,
  useSearchForm: true,
  api: productiongetList,
  showIndexColumn: false,
  rowKey: 'id',
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action'
  },
  columns: cloums,
  rowSelection: {},
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas,
    labelWidth: 50
  }
})

function createActions(record: EditRecordRow): Recordable[] {
  let editButtonList: ActionItem[] = [
    {
      label: '编辑',
      popConfirm: {
        title: '审核前可编辑全部数据,审核后可编辑部分数据',
        placement: 'left',
        confirm: handleEdit.bind(null, record),
        disabled: record.is_disabled == 1
      },
      ifShow: hasPermission([645])
    },
    {
      label: '详情',
      onClick: handledetail.bind(null, record)
    }
  ]

  return editButtonList
}

//新增
function handleAdd() {
  openDrawer(true, { type: 'add' })
  setDrawerProps({ title: '新增', showFooter: true })
}
//编辑
function handleEdit(record) {
  openDrawer(true, { record, type: 'edit' })
  setDrawerProps({ title: '编辑', showFooter: true })
}
//详情
function handledetail(record) {
  openDrawer(true, { record, type: 'detail' })
  setDrawerProps({ title: '详情', showFooter: false })
}

async function handleaudit(type) {
  try {
    lodaing.value = true
    const selectedRows = await getSelectRows()
    if (!selectedRows.length) return message.error('请选择数据')

    for (const item of selectedRows) {
      if (item.status == 1 && type == 'status') {
        lodaing.value = false
        return message.error('请选择未审核的数据')
      }
      if (item.is_disabled == 1 && type == 'is_disabled') {
        lodaing.value = false
        return message.error('请选择未禁用的数据')
      }
      if (item.is_disabled == 0 && type == 'disabled') {
        lodaing.value = false
        return message.error('请选择已禁用的数据')
      }
    }
    const productionList = selectedRows.map((item) => {
      return {
        id: item.id,
        status: type == 'status' ? 1 : undefined,
        is_disabled: type == 'is_disabled' ? 1 : type == 'disabled' ? 0 : undefined
      }
    })

    await (type == 'status' ? productionsetStatus({ productionList }) : productionsetIsDisabled({ productionList }))
    await clearSelectedRowKeys()
    await reload()

    lodaing.value = false
  } catch (e) {
    lodaing.value = false
    console.log(e)
  } finally {
    lodaing.value = false
    await reload()
  }
}
</script>
