import { h } from 'vue'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'
import { GET_STATUS_SCHEMA } from '/@/const/status'

const cancel_schema = GET_STATUS_SCHEMA(
  [
    { label: '未作废', value: 0 },
    { label: '已作废', value: 1 }
  ],
  {
    field: 'is_cancel'
  }
)

export const columns: BasicColumn[] = [
  {
    title: '采购单号',
    dataIndex: 'order_strid',
    width: 300,
    resizable: true
  },
  {
    title: '单号',
    dataIndex: 'strid',
    width: 300,
    resizable: true
  },
  {
    title: '产品名称',
    dataIndex: 'name',
    width: 300,
    resizable: true
  },
  {
    title: '创建人',
    resizable: true,
    dataIndex: 'creator_name',
    width: 200
  },
  {
    title: '作废人',
    resizable: true,
    dataIndex: 'cancel_inCharge_name',
    width: 200
  },
  {
    title: '是否作废',
    resizable: true,
    dataIndex: 'is_cancel',
    width: 200,
    customRender({ text }) {
      return !isNullOrUnDef(text) ? h(Tag, { color: text == 1 ? 'red' : '' }, text == 1 ? '已作废' : '未作废') : ''
    }
  },
  {
    title: '产品数量',
    dataIndex: 'quantity',
    width: 120,
    resizable: true
  },
  {
    title: '工序名称',
    dataIndex: 'production_item_name',
    width: 300,
    resizable: true
  },
  {
    title: '开工时间',
    dataIndex: 'start_at',
    resizable: true,
    width: 200
  },
  {
    title: '完工时间',
    resizable: true,
    dataIndex: 'end_at',
    width: 200
  },
  {
    title: '产品编码',
    resizable: true,
    dataIndex: 'puid',
    width: 200
  },
  {
    title: '产品图片',
    resizable: true,
    dataIndex: 'imgs',
    width: 200
  },
  {
    title: '创建时间',
    resizable: true,
    dataIndex: 'created_at',
    width: 200
  }
]

export const schemas: FormSchema[] = [
  cancel_schema,
  {
    field: 'order_strid',
    label: '采购订单',
    component: 'Input'
  },
  {
    field: 'strid',
    label: '单号',
    component: 'Input'
  },
  {
    field: 'cancel_inCharge',
    label: '作废人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getCreatorList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'name',
    label: '产品名称',
    component: 'Input'
  },
  {
    field: 'puid',
    label: '产品编码',
    component: 'Input'
  }
]
