<template>
  <div>
    <BasicTable @register="registerTable" />
  </div>
</template>
<script setup lang="ts">
import { columns, schemas } from './datas/data'
import { BasicTable, useTable } from '/@/components/Table'
import { productionrecordgetItemList } from '/@/api/erp/productionrecords'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'

const [registerTable] = useTable({
  showTableSetting: true,
  useSearchForm: true,
  columns,
  api: productionrecordgetItemList,
  formConfig: {
    schemas,
    ...NEW_STATUS_FORMCONFIG,
    labelWidth: 100
  }
})
</script>
