<template>
  <BasicDrawer @register="registerDrawer" width="90%" @ok="handleOk">
    <BasicForm @register="registerForm" />
    <BasicTable @register="registerTable">
      <template #toolbar>
        <div style="position: absolute; left: 0; width: 200px; display: flex; justify-content: space-around">
          <Button type="primary" @click="handledelect" :disabled="delectkeys.length == 0 || getDataSource().length == 1">批量删除</Button>
        </div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
      <template #headerCell="{ column }">
        <template v-if="column.dataIndex === 'production_item_id'">
          <Popover trigger="click" title="批量选择工序名称" @visible-change="handleHoverChange('itemID')" :visible="visible">
            <template #content>
              <Select :options="productiontypelist" v-model:value="productionItemId" style="width: 100%" />
              <div class="mt-1 justify-between flex">
                <a-button class="w-5/2" type="primary" size="small" @click="handleSetAllDept('itemID')"> 确定 </a-button>
                <a-button class="w-5/2 ml-2" size="small" @click="visible = false"> 取消 </a-button>
              </div>
            </template>
            <span style="margin-right: 10px">{{ column.customTitle }}</span>
            <EditOutlined />
          </Popover>
        </template>
        <template v-else-if="column.dataIndex === 'start_at'">
          <Popover trigger="click" title="批量选择工序名称" @visible-change="handleHoverChange('startAt')" :visible="startvisible">
            <template #content>
              <DatePicker v-model:value="startat" style="width: 100%" :valueFormat="'YYYY-MM-DD'" />
              <div class="mt-1 justify-between flex">
                <a-button class="w-5/2" type="primary" size="small" @click="handleSetAllDept('startAt')"> 确定 </a-button>
                <a-button class="w-5/2 ml-2" size="small" @click="startvisible = false"> 取消 </a-button>
              </div>
            </template>
            <span style="margin-right: 10px">{{ column.customTitle }}</span>
            <EditOutlined />
          </Popover>
        </template>
        <template v-else-if="column.dataIndex === 'end_at'">
          <Popover trigger="click" title="批量选择工序名称" @visible-change="handleHoverChange('endat')" :visible="endvisible">
            <template #content>
              <DatePicker v-model:value="endat" style="width: 100%" :valueFormat="'YYYY-MM-DD'" />
              <div class="mt-1 justify-between flex">
                <a-button class="w-5/2" type="primary" size="small" @click="handleSetAllDept('endat')"> 确定 </a-button>
                <a-button class="w-5/2 ml-2" size="small" @click="endvisible = false"> 取消 </a-button>
              </div>
            </template>
            <span style="margin-right: 10px">{{ column.customTitle }}</span>
            <EditOutlined />
          </Popover>
        </template>
        <template v-else>{{ column.customTitle }}</template>
      </template>
    </BasicTable>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table'
import type { EditRecordRow } from '/@/components/Table'
import { BasicForm, useForm } from '/@/components/Form'
import { cloums, schemas, productiontypelist } from '../datas/edit.data'
import { ref } from 'vue'
import { message, Button, Popover, Select, DatePicker } from 'ant-design-vue'
import { cloneDeep } from 'lodash-es'
import { getPurchaseDetail } from '/@/api/erp/purchaseOrder'
import { productionrecordupdate } from '/@/api/erp/productionrecords'
import { productiongetList } from '/@/api/baseData/productionmanagement'
import { EditOutlined } from '@ant-design/icons-vue'

const currentEditKeyRef = ref('')
const delectkeys = ref<any>([])
const SelectRow = ref<any>([])
const visible = ref(false)
const startvisible = ref(false)
const endvisible = ref(false)
const productionItemId = ref()
const startat = ref()
const endat = ref()
const emit = defineEmits(['success'])

const [registerDrawer, { closeDrawer, changeOkLoading }] = useDrawerInner(async (data) => {
  console.log(data)

  currentEditKeyRef.value = ''
  setTableData([])
  resetFields()
  await resetSchema(await schemas({ handcommodity, getDataSource, setTableData }))
  productiontypelist.value = []
  if (data.type !== 'add') {
    setFieldsValue(data.record)
    setTableData(data.record.item)
    const { items } = await productiongetList({ status: 1, is_disabled: 0, dept_ids: [data.record.dept_id] })
    const productionlist = items.find((item) => item.id == data.record.production_id)
    await setFieldsValue({
      production_id: Number(productionlist.id),
      production_name: productionlist.name
    })
    productiontypelist.value = productionlist.item.map((item) => {
      return {
        label: item.name,
        value: item.id
      }
    })
  }
})
const [registerForm, { resetFields, validate, setFieldsValue, resetSchema }] = useForm({
  baseColProps: { span: 8 },
  showActionButtonGroup: false,
  labelCol: { span: 6 }
  // schemas: schemas(handcommodity)
})
const [registerTable, { setTableData, getDataSource, deleteTableDataRecord, updateTableDataRecord, getColumns }] = useTable({
  showIndexColumn: false,
  columns: cloums,
  actionColumn: {
    width: 240,
    title: '操作',
    dataIndex: 'action'
  },
  rowSelection: {
    onChange: (key, rows) => {
      delectkeys.value = key
      SelectRow.value = rows
    }
  }
})

function createActions(record: EditRecordRow): ActionItem[] {
  if (!record.editable) {
    return [
      {
        label: '编辑',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        onClick: handleEdit.bind(null, record)
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          placement: 'left',
          confirm: handleDelete.bind(null, record)
        },
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false || getDataSource().length == 1
      }
    ]
  }
  return [
    {
      label: '保存',
      onClick: handleSave.bind(null, record)
    },
    {
      label: '取消',
      popConfirm: {
        title: '是否取消编辑',
        placement: 'right',
        confirm: handleCancel.bind(null, record)
      }
    }
  ]
}

async function handcommodity(record) {
  setTableData([])
  currentEditKeyRef.value = ''
  const { items } = await getPurchaseDetail({ doc_id: record.id, pageSize: 999 })
  const newitem = items.map((item) => {
    return {
      name: item.name,
      item_purchase_id: item.id
    }
  })
  const dataSource = [...newitem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
}

// 格式化提交的数据
function formatSubmit() {
  // getDataSource()有我们不需要的属性,所以需要清除
  const dataSource = getDataSource().map((item) => {
    let temporary = {}
    for (let colName of getColumns()) {
      if (colName.key !== 'action') {
        temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
      }
    }
    return temporary
  })
  return dataSource
}

// 存储编辑前的record
const beforeRecord = ref()
//编辑
async function handleEdit(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  beforeRecord.value = cloneDeep(record)
  record.onEdit?.(true, false)
}

//删除tabel
function handleDelete(record) {
  deleteTableDataRecord(record.key)
}

//保存b
async function handleSave(record: EditRecordRow) {
  // 校验
  const valid = await record.onValid?.()
  if (valid) {
    try {
      console.log(record)

      if (!record.quantity || !record.production_item_id || !record.start_at || !record.end_at) {
        message.error({ content: '请完整填写所有数据' })
        return
      }

      //TODO 此处将数据提交给服务器保存
      // ...
      // 保存之后提交编辑状态

      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
      }
      message.success({ content: '数据已保存', key: 'saving' })
    } catch (error) {
      message.error({ content: '保存失败', key: 'saving' })
      throw new Error(`${error}`)
    }
  } else {
    message.error({ content: '请填写正确的数据', key: 'saving' })
  }
}

//取消
function handleCancel(record: EditRecordRow) {
  currentEditKeyRef.value = ''
  updateTableDataRecord(record.key, {
    name: beforeRecord.value.name
  })

  record.onEdit?.(false, false)
}

//批量删除
function handledelect() {
  const tabledata = getDataSource()
  if (tabledata.length === delectkeys.value.length) {
    return message.error({ content: '不能删除全部数据' })
  }
  deleteTableDataRecord(delectkeys.value)
  delectkeys.value = []
}

async function handleOk() {
  try {
    changeOkLoading(true)
    const formdata = await validate()
    delete formdata.production_name
    const TableData = await formatSubmit()
    if (currentEditKeyRef.value !== '') {
      changeOkLoading(false)
      return message.error({ content: '请先保存数据', key: 'saving' })
    }

    const hasEmptyValue = (value) => value == null || String(value).trim() === ''
    for (let i = 0; i < TableData.length; i++) {
      const obj = TableData[i]
      if (typeof obj !== 'object' || obj === null || Array.isArray(obj)) {
        throw new Error(`Element at index ${i} is not a valid object.`)
      }

      for (const [key, value] of Object.entries(obj)) {
        if (hasEmptyValue(value)) {
          console.log(key)

          return message.error({ content: '请完整填写所有数据' })
        }
      }
    }
    const params = {
      ...formdata,
      items: TableData.map(({ name: _, ...rest }) => rest)
    }

    await productionrecordupdate(params)
    setTimeout(() => {
      closeDrawer()
      changeOkLoading(false)
      emit('success')
    })
  } catch (e) {
    changeOkLoading(false)
    console.log(e)
  } finally {
    changeOkLoading(false)
  }
}

//批量
function handleHoverChange(type) {
  if (delectkeys.value.length == 0) {
    return message.warning('请选择要编辑的行')
  }
  switch (type) {
    case 'itemID':
      visible.value = true
      break
    case 'startAt':
      startvisible.value = true
      break
    case 'endat':
      endvisible.value = true
      break
  }
}

function handleSetAllDept(type) {
  SelectRow.value.forEach((item) => {
    item.production_item_id = type == 'itemID' ? productionItemId.value : item.production_item_id
    item.start_at = type == 'startAt' ? startat.value : item.start_at
    item.end_at = type == 'endat' ? endat.value : item.end_at
  })
  switch (type) {
    case 'itemID':
      visible.value = false
      break
    case 'startAt':
      startvisible.value = false
      break
    case 'endat':
      endvisible.value = false
      break
  }
  productionItemId.value = ''
  startat.value = ''
  endat.value = ''
  updateTableDataRecord(delectkeys.value, SelectRow.value)
}
</script>
