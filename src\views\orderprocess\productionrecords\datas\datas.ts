import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { GET_STATUS_SCHEMA } from '/@/const/status'
import { getCreatorList } from '/@/api/financialDocuments/public'
const saleStore = useSaleOrderStore()

const cancel_schema = GET_STATUS_SCHEMA(
  [
    { label: '未作废', value: 0 },
    { label: '已作废', value: 1 }
  ],
  {
    field: 'status'
  }
)

export const columns: BasicColumn[] = [
  {
    title: '单号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '单据单号',
    dataIndex: 'order_strid',
    width: 200,
    resizable: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 120,
    resizable: true,
    customRender({ text }) {
      return !isNullOrUnDef(text) ? saleStore.saleType[text] : ''
    }
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 120,
    resizable: true
  },
  {
    title: '是否作废',
    dataIndex: 'is_cancel',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return !isNullOrUnDef(text) ? h(Tag, { color: text == 0 ? '' : 'red' }, text == 0 ? '未作废' : '已作废') : ''
    }
  },
  {
    title: '作废人',
    dataIndex: 'cancel_inCharge_name',
    width: 120,
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 120,
    resizable: true
  },
  {
    title: '作废时间',
    dataIndex: 'cancel_at',
    width: 120,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  cancel_schema,
  { field: 'strid', label: '单号', component: 'Input' },
  { field: 'order_strid', label: '单据单号', component: 'Input' },
  {
    field: 'type',
    label: '类型',
    component: 'Select',
    componentProps: {
      options: Object.keys(saleStore.saleType).map((key) => {
        return {
          label: saleStore.saleType[key],
          value: Number(key)
        }
      })
    }
  },
  {
    field: 'creator',
    label: '创建人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getCreatorList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'cancel_inCharge',
    label: '作废人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getCreatorList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'created_at',
    label: '创建日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  }
]

export const childRenColumns: BasicColumn[] = [
  {
    title: '产品名称',
    dataIndex: 'name',
    width: 300,
    resizable: true
  },
  {
    title: '产品数量',
    dataIndex: 'quantity',
    width: 120,
    resizable: true
  },
  {
    title: '工序名称',
    dataIndex: 'production_item_name',
    width: 300,
    resizable: true
  },
  {
    title: '开工时间',
    dataIndex: 'start_at',
    resizable: true,
    width: 200
  },
  {
    title: '完工时间',
    resizable: true,
    dataIndex: 'end_at',
    width: 200
  },
  {
    title: '产品编码',
    resizable: true,
    dataIndex: 'puid',
    width: 200
  },
  {
    title: '产品图片',
    resizable: true,
    dataIndex: 'imgs',
    width: 200
  }
]
