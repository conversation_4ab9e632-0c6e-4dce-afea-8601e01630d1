import { ref } from 'vue'
import { productiongetList } from '/@/api/baseData/productionmanagement'
import { getPurchaseSelectList } from '/@/api/erp/purchaseOrder'
import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { productionrecordgetPurchaseItem } from '/@/api/erp/productionrecords'

export const productiontypelist = ref()
export const schemas = (hand?: { handcommodity: Function; getDataSource: Function; setTableData: Function }): FormSchema[] => [
  {
    field: 'type',
    label: '类型',
    component: 'Select',
    required: true,
    defaultValue: 4,
    dynamicDisabled: true,
    componentProps: {
      options: [{ label: '采购订单', value: 4 }]
    }
  },
  {
    field: 'order_strid',
    label: '单据单号',
    required: true,
    component: 'PagingApiSelect',
    componentProps: ({ formModel }) => {
      return {
        resultField: 'items',
        api: getPurchaseSelectList,
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        searchParamField: 'strid',
        selectProps: {
          allowClear: true,
          fieldNames: { value: 'strid', label: 'strid' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'strid'
        },
        params: {
          statusList: [1, 15]
        },
        onChange(_, shall) {
          if (!shall) return
          formModel.work_id = shall.work_id
          formModel.dept_id = shall.dept_id
          hand?.handcommodity(shall)
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'production_name',
    label: '生产模式',
    required: true,
    component: 'PagingApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: (params) => {
          if (!formModel.dept_id) return
          // dept_ids: [formModel.dept_id]
          return productiongetList({ ...params, dept_ids: [formModel.dept_id] })
        },
        params: {
          status: 1,
          is_disabled: 0
        },
        searchMode: true,
        alwaysLoad: true,
        resultField: 'items',
        searchParamField: 'name',
        selectProps: {
          fieldNames: { key: 'id', value: 'name', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        },
        onChange(_, shall) {
          if (!shall) return
          formModel.production_id = shall.id
          productiontypelist.value = shall.item.map((item) => {
            return {
              label: item.name,
              value: item.id
            }
          })
          const dataSource = hand?.getDataSource()
          console.log(dataSource)
          dataSource.forEach((item) => {
            item.production_item_id = undefined
          })
          hand?.setTableData(dataSource)
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false
  },
  {
    field: 'work_id',
    label: 'work_id',
    component: 'Input',
    show: false
  },
  {
    field: 'production_id',
    label: 'production_name',
    component: 'Input',
    show: false
  },
  {
    field: 'dept_id',
    label: 'dept_id',
    component: 'Input',
    show: false,
    ifShow: false
  }
]

export const cloums: BasicColumn[] = [
  {
    title: '产品名称',
    dataIndex: 'name',
    width: 300,
    resizable: true
  },
  {
    title: 'item_purchase_id',
    dataIndex: 'item_purchase_id',
    defaultHidden: true,
    resizable: true
  },
  {
    title: '未生产数量',
    dataIndex: 'no_production_left',
    width: 120,
    resizable: true
  },
  {
    title: '产品数量',
    dataIndex: 'quantity',
    width: 120,
    editRow: true,
    resizable: true,
    editComponent: 'InputNumber',
    editComponentProps: ({ record }) => ({
      min: 0,
      precision: 2,
      max: record.no_production_left
    })
  },
  {
    title: '工序名称',
    dataIndex: 'production_item_id',
    width: 300,
    resizable: true,
    editRow: true,
    editComponent: 'Select',
    editComponentProps: ({ record }) => ({
      options: productiontypelist.value ?? [],
      onChange: async (value) => {
        console.log(value)
        console.log(record)
        const { items } = await productionrecordgetPurchaseItem({ production_item_id: value, item_purchase_id: record.item_purchase_id })
        console.log(items)

        record['no_production_left'] = items[0].no_production_left
      }
    })
  },
  {
    title: '开工时间',
    dataIndex: 'start_at',
    resizable: true,
    width: 200,
    editRow: true,
    editComponent: 'DatePicker',
    editComponentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    title: '完工时间',
    resizable: true,
    dataIndex: 'end_at',
    width: 200,
    editRow: true,
    editComponent: 'DatePicker',
    editComponentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  }
]
