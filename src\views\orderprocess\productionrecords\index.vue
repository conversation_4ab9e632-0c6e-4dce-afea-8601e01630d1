<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleaudit" :loading="lodaing" v-if="hasPermission([646])"> 批量作废 </Button>
        <!-- <Button type="primary" @click="handleaudit('is_disabled')" :loading="lodaing" v-if="hasPermission([600])"> 批量禁用 </Button> -->
        <Button type="primary" @click="handleAdd" :loading="lodaing" v-if="hasPermission([647])"> 新增 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <BasicTable
          class="p-4"
          :showIndexColumn="false"
          :canResize="false"
          :api="productionrecordgetItemList.bind(null, { strid: record.strid })"
          :columns="childRenColumns"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'imgs'">
              <TableImg :imgList="record.imgs" :simpleShow="true" />
            </template>
          </template>
        </BasicTable>
      </template>
    </BasicTable>
    <editDrawer @register="registerDrawer" @success="reload" />
  </div>
</template>

<script setup lang="ts">
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem, TableImg } from '/@/components/Table'
import { Button, message } from 'ant-design-vue'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { columns, schemas, childRenColumns } from './datas/datas'
import { ref } from 'vue'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'
import { usePermission } from '/@/hooks/web/usePermission'
import { productionrecordgetList, productionrecordsetIsCancel, productionrecordgetItemList } from '/@/api/erp/productionrecords'
const { hasPermission } = usePermission()

const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const lodaing = ref(false)

const [registerTable, { reload, getSelectRows, clearSelectedRowKeys }] = useTable({
  showTableSetting: true,
  useSearchForm: true,
  api: productionrecordgetList,
  showIndexColumn: false,
  rowKey: 'id',
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action'
  },
  columns,
  rowSelection: {},
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas,
    labelWidth: 100,
    fieldMapToTime: [['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  }
})

function createActions(record: EditRecordRow): Recordable[] {
  let editButtonList: ActionItem[] = [
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      disabled: record.is_cancel == 1,
      ifShow: hasPermission([648])
    }
    // {
    //   label: '详情',
    //   onClick: handledetail.bind(null, record),
    //   disabled: record.is_cancel == 1,
    //   ifShow: hasPermission([602])
    // }
  ]

  return editButtonList
}

//新增
function handleAdd() {
  openDrawer(true, { type: 'add' })
  setDrawerProps({ title: '新增', showFooter: true })
}
//编辑
function handleEdit(record) {
  openDrawer(true, { record, type: 'edit' })
  setDrawerProps({ title: '编辑', showFooter: true })
}
// //新增
// function handledetail(record) {
//   openDrawer(true, { record, type: 'detail' })
//   setDrawerProps({ title: '详情', showFooter: false })
// }

async function handleaudit() {
  try {
    lodaing.value = true
    const selectedRows = await getSelectRows()
    if (!selectedRows.length) {
      lodaing.value = false
      return message.error('请选择数据')
    }

    for (const item of selectedRows) {
      if (item.is_cancel == 1) {
        lodaing.value = false
        return message.error('请选择未作废的数据')
      }
    }
    const productionList = selectedRows.map((item) => {
      return {
        id: item.id,
        is_cancel: 1
      }
    })

    await productionrecordsetIsCancel({ productionList })
    await clearSelectedRowKeys()
    await reload()

    lodaing.value = false
  } catch (e) {
    lodaing.value = false
    console.log(e)
  }
}
</script>
