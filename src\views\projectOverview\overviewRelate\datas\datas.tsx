import { BasicColumn } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { mapReject, mapStatus } from '/@/views/erp/purchaseOrder/datas/datas'
import { mapStatus as inWarehouseMapStatus } from '/@/views/erp/inWarehouse/datas/datas'
import { mapStatus as mapOutWarehouseMapStatus } from '/@/views/erp/outWarehouse/datas/datas'
import { mapRetreatOrderType, mapStatus as mapRetreatMapStatus } from '/@/views/erp/retreat/datas/datas'
import { useI18n } from '/@/hooks/web/useI18n'
// import { commonMap } from '/@/views/erp/qualityDetection/datas/datas'
// import { TableImg } from '/@/components/Table'
// import { h } from 'vue'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { getPurchaseOrderList } from '/@/api/erp/purchaseOrder'
import { getInWarehouseTableList } from '/@/api/erp/inWarehouse'
import { getOutWarehouseList } from '/@/api/erp/outWarehouse'
import { getInventoryList, getInventoryListst } from '/@/api/erp/inventory'
import { getRetreatList } from '/@/api/erp/retreat'
// import { getQualityDetectionReport } from '/@/api/erp/qualityDetection'
import { getPaymentOrderList } from '/@/api/financialDocuments/paymentOrder'
import { getReceiptOrderList } from '/@/api/financialDocuments/receiptOrder'
import { getOthergetList } from '/@/api/financialDocuments/otherIncome'
import { getOtherExpendList } from '/@/api/financialDocuments/otherExpend'
import { getFundManage } from '/@/api/financialDocuments/refund'
import { usePermission } from '/@/hooks/web/usePermission'
import { ischecks, statusMaps } from '/@/views/financialDocuments/otherIncomeExpend/datas/datas'
import { getQcList } from '/@/api/erp/qc'
import { isNull, isUndefined } from 'lodash-es'
import { isNumber } from '/@/utils/is'
import { add } from '/@/utils/math'

const { hasPermission } = usePermission()
const { t } = useI18n()
const saleOrderStore = useSaleOrderStore()
export const purchaseColumns: BasicColumn[] = [
  {
    title: '采购单号',
    dataIndex: 'strid',
    width: 120,
    resizable: true
  },
  {
    title: '订单状态',
    dataIndex: 'status',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(mapStatus[value].label, mapStatus[value].color)
    }
  },
  {
    title: '财务审核',
    dataIndex: 'is_check',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(mapReject[value].label, mapReject[value].color)
    }
  },
  {
    title: '创建日期',
    dataIndex: 'created_at',
    width: 120,
    resizable: true
  },
  // {
  //   title: '采购商品数量',
  //   dataIndex: 'item_count',
  //   width: 120,
  //   resizable: true
  // },
  {
    title: '应付金额',
    dataIndex: 'cost',
    width: 120,
    resizable: true
  },
  {
    title: '已付金额',
    dataIndex: 'paid',
    width: 120,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'dept_name',
    width: 120,
    resizable: true
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    width: 120,
    resizable: true
  }
]

export const inWarehouseColumns: BasicColumn[] = [
  {
    title: '收货号',
    dataIndex: 'strid',
    width: 120,
    resizable: true
  },
  {
    title: '入库状态',
    dataIndex: 'status',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(inWarehouseMapStatus[value].value, inWarehouseMapStatus[value].color)
    }
  },
  {
    title: '创建日期',
    dataIndex: 'created_at',
    width: 120,
    resizable: true
  },
  // {
  //   title: '入库商品数量',
  //   dataIndex: 'item_count',
  //   width: 120,
  //   resizable: true
  // },
  {
    title: '部门',
    dataIndex: 'department_name',
    width: 120,
    resizable: true
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    width: 120,
    resizable: true
  }
]

export const outWarehouseColumns: BasicColumn[] = [
  {
    title: '收货号',
    dataIndex: 'strid',
    width: 120,
    resizable: true
  },
  {
    title: '出库状态',
    dataIndex: 'status',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(mapOutWarehouseMapStatus[value].label, inWarehouseMapStatus[value].color)
    }
  },
  {
    title: '创建日期',
    dataIndex: 'created_at',
    width: 120,
    resizable: true
  },
  // {
  //   title: '出库商品数量',
  //   dataIndex: 'quantity',
  //   width: 120,
  //   resizable: true
  // },
  {
    title: '出库日期',
    dataIndex: 'checkout_at',
    width: 120,
    resizable: true
  },
  {
    title: '确定时间',
    dataIndex: 'confirmed_at',
    width: 120,
    resizable: true
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name',
    width: 120,
    resizable: true
  }
]

export const transferColumns: BasicColumn[] = [
  {
    title: '转换单号',
    dataIndex: 'strid',
    width: 120,
    resizable: true
  },
  {
    title: '审核状态',
    dataIndex: 'status',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      //这状态都有什么
      return useRender.renderTag(value ? '已审核' : '未审核', value ? 'green' : 'red')
    }
  },
  {
    title: '创建人',
    dataIndex: 'creator_name'
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name'
  },
  {
    title: '申请人',
    dataIndex: 'applicant_name'
  },
  {
    title: '处理人',
    dataIndex: 'processor_name'
  },
  {
    title: '部门',
    dataIndex: 'dept_id'
  }
  // {
  //   title: '转换商品数量',
  //   dataIndex: 'quantity',
  //   width: 100,
  //   resizable: true
  // }
]

export const inventoryColumns: BasicColumn[] = [
  {
    title: '盘点单号',
    dataIndex: 'strid',
    width: 120,
    resizable: true
  },
  {
    title: '审核状态',
    dataIndex: 'status',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(value ? '已审核' : '未审核', value ? 'green' : 'red')
    }
  },
  {
    title: '创建人',
    dataIndex: 'creator_name'
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name'
  },
  {
    title: '申请人',
    dataIndex: 'applicant_name'
  },
  {
    title: '处理人',
    dataIndex: 'processor_name'
  },
  {
    title: '部门',
    dataIndex: 'dept_id'
  }
  // {
  //   title: '盘点商品数量',
  //   dataIndex: 'quantity',
  //   width: 100,
  //   resizable: true
  // }
]

export const retreatColumns: BasicColumn[] = [
  {
    title: '退货单号',
    dataIndex: 'strid',
    width: 120,
    resizable: true
  },
  {
    title: '名称',
    dataIndex: 'name',
    width: 120,
    resizable: true
  },
  {
    title: '订单状态',
    dataIndex: 'status',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(mapRetreatMapStatus[value].label, mapRetreatMapStatus[value].color)
    }
  },
  {
    title: '退货类型',
    dataIndex: 'type',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(mapRetreatOrderType[value].label, mapRetreatOrderType[value].color)
    }
  },
  // {
  //   title: '涉及库存商品',
  //   dataIndex: 'quantity',
  //   width: 120,
  //   resizable: true
  // },
  {
    title: '客户',
    dataIndex: 'client_name',
    width: 120,
    resizable: true
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    width: 120,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 120,
    resizable: true
  }
]

export const qualityColumns: BasicColumn[] = [
  {
    title: '质检单号',
    dataIndex: 'id',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'source_uniqid',
    title: '所属销售单',
    resizable: true,
    width: 200
  },
  {
    dataIndex: 'purchase_strid',
    title: '采购单号',
    resizable: true,
    width: 250
  },
  {
    dataIndex: 'status',
    title: '审核状态',
    resizable: true,
    width: 100,
    customRender: ({ text }) =>
      !isNull(text) && !isUndefined(text)
        ? useRender.renderTag(t(`tag.approveStatus.${text}.label`), t(`tag.approveStatus.${text}.color`))
        : '-'
  },
  {
    dataIndex: 'is_cancel',
    title: '是否作废',
    resizable: true,
    width: 100,
    customRender: ({ text }) =>
      !isNull(text) && !isUndefined(text) ? useRender.renderTag(t(`tag.tagColor.${text}.label`), t(`tag.tagColor.${text}.color`)) : '-'
  },
  {
    dataIndex: 'dept_name',
    title: '部门',
    resizable: true,
    width: 150
  },
  {
    dataIndex: 'inCharge_name',
    title: '质检人',
    resizable: true,
    width: 200
    // customRender: ({ record }) => commonMap.getMapPerson[record.user_id]
  }
  // {
  //   dataIndex: 'qr_type',
  //   title: '质检方式',
  //   resizable: true,
  //   width: 150,
  //   customRender: ({ text }) => (text ? t(`qc.mapQcType.${text}`) : '-')
  // },
  // {
  //   title: '质检内容',
  //   dataIndex: 'qc_rate',
  //   width: 400,
  //   resizable: true,
  //   customRender: ({ record }) =>
  //     h(
  //       'div',
  //       {},
  //       record.content.map((item) => useRender.renderTag(item))
  //     )
  // },
  // {
  //   dataIndex: 'qr_stage',
  //   title: '质检时期',
  //   resizable: true,
  //   width: 150,
  //   customRender: ({ text }) => (text ? t(`qc.mapQcStage.${text}`) : '-')
  // },
  // {
  //   dataIndex: 'double_check',
  //   title: '是否多次质检',
  //   resizable: true,
  //   width: 100,
  //   customRender: ({ value }) => t(`qc.mapQcDoubleCheck.${value}`)
  // },
  // {
  //   dataIndex: 'result',
  //   title: '质检结果',
  //   resizable: true,
  //   width: 100,
  //   customRender: ({ text }) => (!isNull(text) ? t(`qc.mapQcResult.${text}`) : '-')
  // },
  // {
  //   dataIndex: 'images',
  //   title: '图片',
  //   resizable: true,
  //   width: 100,
  //   customRender: ({ text }) => h(TableImg, { imgList: text, simpleShow: true })
  // },
  // {
  //   dataIndex: 'user_id',
  //   title: '质检人',
  //   resizable: true,
  //   width: 200,
  //   customRender: ({ record }) => commonMap.getMapPerson[record.user_id]
  // },
  // {
  //   dataIndex: 'created_at',
  //   title: '创建时间',
  //   resizable: true,
  //   width: 200
  // }
]

export const paymentColumns: BasicColumn[] = [
  {
    title: '付款单号',
    dataIndex: 'strid',
    width: 150
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ value }) => useRender.renderTag(t(`tag.payDetermineStatus.${value}.text`), t(`tag.payDetermineStatus.${value}.color`))
  },
  {
    title: '款单类型',
    dataIndex: 'clause',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return useRender.renderTag(saleOrderStore.orderType[text])
    }
  },
  {
    title: '付款日期',
    width: 120,
    dataIndex: 'collection_at',
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '审批',
    width: 100,
    dataIndex: 'is_check',
    customRender: ({ value }) => useRender.renderTag(t(`tag.financeExamine.${value}.text`), t(`tag.financeExamine.${value}.color`))
  },
  // {
  //   title: '是否作废',
  //   dataIndex: 'is_disuse'
  // },
  {
    title: '应付金额',
    width: 120,
    dataIndex: 'sale_amount',
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(Math.ceil(text * 100) / 100) : '0.00'
    }
  },
  {
    title: '本次应付金额',
    dataIndex: 'sale_amount_cost',
    width: 120,
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(text) : '0.00'
    }
  },
  {
    title: '明细已付金额',
    dataIndex: 'sale_amount_paid',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(text) : '0.00'
    }
  },
  {
    title: '客户',
    dataIndex: 'client_name',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    width: 150,
    resizable: true,
    customRender: ({ record }) => {
      return record.supplier_name ? record.supplier_name : '-'
    }
  },
  {
    title: '账号名称',
    dataIndex: 'account_name',
    width: 100,
    resizable: true,
    ifShow: false
  },
  {
    title: '账号',
    dataIndex: 'account',
    width: 100,
    resizable: true,
    ifShow: false
  },
  {
    title: '携带备注',
    dataIndex: 'g_remark',
    width: 200,
    resizable: true,
    helpMessage: '生成付款单时携带的备注！',
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '付款备注',
    dataIndex: 'remark',
    width: 200,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  }
]

export const collectionColumns: BasicColumn[] = [
  {
    title: '收款单号',
    dataIndex: 'strid'
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    resizable: true,
    customRender: ({ value }) =>
      [0, 1].includes(value)
        ? useRender.renderTag(t(`tag.collectDetermineStatus.${value}.text`), t(`tag.collectDetermineStatus.${value}.color`))
        : 'unknown'
  },
  {
    title: '收款日期',
    dataIndex: 'collection_at',
    width: 100,
    resizable: true
  },
  {
    title: '应收金额',
    dataIndex: 'amount',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(Number(text)) : '0.00'
    }
  },
  {
    title: '本次应收金额',
    dataIndex: 'amount_ans',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(text) : '0.00'
    }
  },
  {
    title: '本次已收金额',
    dataIndex: 'amount_rec',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(text) : '0.00'
    }
  },
  {
    title: '付款人',
    dataIndex: 'notes',
    width: 100,
    resizable: true
  },
  {
    title: '客户',
    dataIndex: 'client_name',
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    customRender: ({ record }) => {
      return record.supplier_name ? record.supplier_name : '-'
    }
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  }
]

export const otherIncomeColumns: BasicColumn[] = [
  {
    title: '单号',
    dataIndex: 'strid',
    width: 200
  },
  {
    title: '摘要',
    dataIndex: 'desc',
    width: 150,
    resizable: true
  },
  // {
  //   title: '总结算金额',
  //   dataIndex: 'amount',
  //   width: 100,
  //   customRender: ({ value }) => {
  //     return formatter.format(value)
  //   }
  // },
  {
    title: '应收金额',
    dataIndex: 'sale_amount',
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(Math.ceil(text * 100) / 100) : '0.00'
    }
  },
  {
    title: '本次应收金额',
    dataIndex: 'sale_amount_receivable',
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(text) : '0.00'
    }
  },
  {
    title: '明细已收金额',
    dataIndex: 'sale_amount_received',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(text) : '0.00'
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ record }) => {
      return useRender.renderTag(statusMaps[record.status]?.text, statusMaps[record.status]?.color)
    }
  },
  {
    title: '财务审核',
    dataIndex: 'is_check2',
    width: 100,
    customRender: ({ text }) => {
      return useRender.renderTag(ischecks[text]?.text, ischecks[text]?.color)
    }
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 100
  },
  // {
  //   title: '创建日期',
  //   dataIndex: 'created_at',
  //   width: 150
  // },
  // {
  //   title: '更新日期',
  //   dataIndex: 'updated_at',
  //   width: 150
  // },
  // {
  //   title: '审核日期',
  //   dataIndex: 'status_at',
  //   width: 150
  // },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 100
  },
  {
    title: '申请人',
    dataIndex: 'applicant_name',
    width: 100
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name',
    width: 100
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200
  },
  {
    title: '附件',
    dataIndex: 'files',
    width: 200
  }
]

export const otherExpensesColumns: BasicColumn[] = [
  {
    title: '单号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ value }) => useRender.renderTag(t(`tag.otherExpendStatus.${value}.text`), t(`tag.otherExpendStatus.${value}.color`))
  },
  {
    title: '财务审批',
    dataIndex: 'is_check',
    width: 100,
    customRender: ({ value }) =>
      useRender.renderTag(t(`tag.otherExpendCheckTag.${value}.text`), t(`tag.otherExpendCheckTag.${value}.color`))
  },
  {
    title: '订单类型',
    dataIndex: 'order',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      const orderMap = {
        1: { color: 'green', text: '销售订单' },
        2: { color: 'red', text: '采购订单' }
      }
      return useRender.renderTag(orderMap[value]?.text, orderMap[value]?.color)
    }
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 100,
    resizable: true,
    customRender: ({ value }) => useRender.renderTag(t(`tag.otherExpendTypeTag.${value}.text`), t(`tag.otherExpendTypeTag.${value}.color`))
  },
  {
    title: '结算货币',
    dataIndex: 'currency',
    width: 100,
    resizable: true
  },
  // {
  //   title: '总支出金额',
  //   dataIndex: 'amount',
  //   width: 120,
  //   customRender: ({ value }) => {
  //     return formatter.format(value)
  //   },
  //   resizable: true
  // },
  {
    title: '支出金额',
    dataIndex: 'sale_amount',
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(Math.ceil(text * 100) / 100) : '0.00'
    }
  },
  {
    title: '本次应付金额',
    dataIndex: 'sale_amount_cost',
    width: 100,
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(text) : '0.00'
    }
  },
  {
    title: '明细已付金额',
    dataIndex: 'sale_amount_paid',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(text) : '0.00'
    }
  },
  {
    title: '支付账号名称',
    dataIndex: 'account_name',
    width: 200,
    resizable: true
  },
  {
    title: '摘要',
    dataIndex: 'desc',
    width: 200
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 100,
    resizable: true
  },
  {
    title: '申请人',
    dataIndex: 'applicant_name',
    width: 100,
    resizable: true
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name',
    width: 100,
    resizable: true
  }
]

export const refundColumns: BasicColumn[] = [
  {
    title: '单号',
    dataIndex: 'strid'
  },
  {
    title: '订单状态',
    dataIndex: 'status',
    customRender: ({ value }) => useRender.renderTag(t(`tag.refundStatus.${value}.label`), t(`tag.refundStatus.${value}.color`))
  },
  {
    title: '款项类型',
    dataIndex: 'type',
    width: 120,
    customRender: ({ value }) => useRender.renderTag(t(`tag.refundType.${value}.label`), t(`tag.refundType.${value}.color`))
  },
  {
    title: '退款单类型',
    dataIndex: 'order',
    width: 120,
    customRender: ({ value }) => useRender.renderTag(t(`tag.refundOrderType.${value}.label`), t(`tag.refundOrderType.${value}.color`))
  },
  {
    title: '退款金额',
    dataIndex: 'amount',
    customRender: ({ value }) => formateerNotCurrency.format(value)
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    customRender: ({ record }) => {
      return record.supplier_name ? record.supplier_name : '-'
    }
  },
  {
    title: '客户名称',
    dataIndex: 'client_name'
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 120
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name'
  },
  {
    title: '申请人名称',
    dataIndex: 'applicant_name'
  }
]

export const mapErpComp = (id: number, is_auth = 1) => {
  return {
    Purchase: {
      title: '关联采购单',
      columns: purchaseColumns,
      api: (params) => getPurchaseOrderList({ ...params, basic_work_id: id, is_auth }),
      slotField: 'strid',
      ifShow: hasPermission(183),
      hasDataSource: true
    },
    InWarehouse: {
      title: '关联入库单',
      columns: inWarehouseColumns,
      api: (params) => getInWarehouseTableList({ ...params, basic_work_id: id, is_auth }),
      slotField: 'strid',
      ifShow: hasPermission([35, 67]),
      hasDataSource: true
    },
    OutWarehouse: {
      title: '关联出库单',
      columns: outWarehouseColumns,
      api: (params) => getOutWarehouseList({ ...params, basic_work_id: id, is_auth }),
      slotField: 'strid',
      ifShow: hasPermission(36),
      hasDataSource: true
    },
    WarehouseTransform: {
      title: '关联转换单',
      columns: transferColumns,
      api: (params) => getInventoryListst({ ...params, basic_work_id: id, is_auth }),
      slotField: 'strid',
      ifShow: hasPermission(37),
      hasDataSource: true
    },
    Inventory: {
      title: '关联盘点单',
      columns: inventoryColumns,
      api: (params) => getInventoryList({ ...params, basic_work_id: id, is_auth }),
      slotField: 'strid',
      ifShow: hasPermission(39),
      hasDataSource: true
    },
    Retreat: {
      title: '关联退货单',
      columns: retreatColumns,
      api: (params) => getRetreatList({ ...params, basic_work_id: id, is_auth }),
      slotField: 'strid',
      ifShow: hasPermission(142),
      hasDataSource: true
    },
    QualityDetection: {
      title: '关联质检单',
      columns: qualityColumns,
      api: (params) => getQcList({ ...params, basic_work_id: id, is_auth }),
      slotField: 'id',
      ifShow: hasPermission(149),
      hasDataSource: true
    }
  }
}

export const mapFinanceComp = (id: number, is_auth = 1) => {
  return {
    Payment: {
      title: '关联付款单',
      columns: paymentColumns,
      api: (params) => getPaymentOrderList({ ...params, basic_work_id: id, is_finance: 0, is_auth }),
      slotField: 'strid',
      ifShow: hasPermission([43]),
      hasDataSource: true,
      compSlots: {
        footer: (data) => {
          return (
            <div class="text-base font-semibold">
              <span>应付金额合计（当前页）：{handleFormatPrice(data, 'amount')}</span>
              <span class="ml-10">本次应付金额合计（当前页）：{handleFormatPrice(data, 'amount_cost')}</span>
              <span class="ml-10">本次已付金额合计（当前页）：{handleFormatPrice(data, 'amount_paid')}</span>
            </div>
          )
        }
      }
    },
    FinancePayment: {
      title: '关联付款单-财务',
      columns: paymentColumns,
      api: (params) => getPaymentOrderList({ ...params, basic_work_id: id, is_finance: 1, is_auth }),
      slotField: 'strid',
      ifShow: hasPermission([233]),
      hasDataSource: true,
      compSlots: {
        footer: (data) => {
          return (
            <div class="text-base font-semibold">
              <span>应付金额合计（当前页）：{handleFormatPrice(data, 'amount')}</span>
              <span class="ml-10">本次应付金额合计（当前页）：{handleFormatPrice(data, 'amount_cost')}</span>
              <span class="ml-10">本次已付金额合计（当前页）：{handleFormatPrice(data, 'amount_paid')}</span>
            </div>
          )
        }
      }
    },
    Receipt: {
      title: '关联收款单',
      columns: collectionColumns,
      api: (params) => getReceiptOrderList({ ...params, basic_work_id: id, is_auth }),
      slotField: 'strid',
      ifShow: hasPermission(42),
      hasDataSource: true,
      compSlots: {
        footer: (data) => {
          return (
            <div class="text-base font-semibold">
              <span>应收金额合计（当前页）：{handleFormatPrice(data, 'amount')}</span>
              <span class="ml-10">本次应收金额合计（当前页）：{handleFormatPrice(data, 'amount_ans')}</span>
              <span class="ml-10">本次已收金额合计（当前页）：{handleFormatPrice(data, 'amount_rec')}</span>
            </div>
          )
        }
      }
    },
    OtherIncome: {
      title: '关联其他收入单',
      columns: otherIncomeColumns,
      api: (params) => getOthergetList({ ...params, basic_work_id: id, is_auth }),
      slotField: 'strid',
      ifShow: hasPermission(44),
      hasDataSource: true
    },
    OtherExpend: {
      title: '关联其他支出单',
      columns: otherExpensesColumns,
      api: (params) => getOtherExpendList({ ...params, basic_work_id: id, is_finance: 0, is_auth }),
      slotField: 'strid',
      ifShow: hasPermission(45),
      hasDataSource: true
    },
    FinanceOtherExpend: {
      title: '关联其他支出单-财务',
      columns: otherExpensesColumns,
      api: (params) => getOtherExpendList({ ...params, basic_work_id: id, is_finance: 1, is_auth }),
      slotField: 'strid',
      ifShow: hasPermission(210),
      hasDataSource: true
    },
    Refund: {
      title: '关联退款单',
      columns: refundColumns,
      api: (params) => getFundManage({ ...params, basic_work_id: id, is_auth }),
      slotField: 'strid',
      ifShow: hasPermission(148),
      hasDataSource: true
    }
  }
}

function handleFormatPrice(data, field) {
  return data.reduce((acc, cur) => {
    return !isNaN(+cur[field]) && isNumber(+cur[field]) ? add(acc, +cur[field]) : acc
  }, 0)
}
