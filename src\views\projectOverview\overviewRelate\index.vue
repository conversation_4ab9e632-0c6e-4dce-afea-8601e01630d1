<template>
  <div>
    <PageWrapper contentBackground>
      <template v-if="type === 'erp'">
        <Row :gutter="[10, 10]">
          <template v-for="key in Object.keys(erpComp)" :key="key">
            <Col v-if="['Inventory', 'WarehouseTransform'].includes(key)" :span="12">
              <BasicTable
                v-if="erpComp[key].ifShow"
                :showTableSetting="true"
                :tableSetting="tableSetting"
                :title="erpComp[key].title"
                :scroll="{ y: 500 }"
                :showIndexColumn="false"
                :columns="erpComp[key].columns"
                :api="erpComp[key].api"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === erpComp[key].slotField">
                    <a-button type="link" @click="handleView(record, key)">{{ record[erpComp[key].slotField] }}</a-button>
                  </template>
                </template>
              </BasicTable>
            </Col>
            <Col v-else :span="24">
              <BasicTable
                v-if="erpComp[key].ifShow"
                :showTableSetting="true"
                :tableSetting="tableSetting"
                :title="erpComp[key].title"
                :scroll="{ y: 500 }"
                :showIndexColumn="false"
                :columns="erpComp[key].columns"
                :api="erpComp[key].api"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === erpComp[key].slotField">
                    <a-button type="link" @click="handleView(record, key)">{{ record[erpComp[key].slotField] }}</a-button>
                  </template>
                </template>
              </BasicTable>
            </Col>
          </template>
        </Row>
      </template>
      <template v-else-if="type === 'finance'">
        <template v-for="key in Object.keys(financeComp)" :key="key">
          <BasicTable
            v-if="financeComp[key].ifShow"
            :showTableSetting="true"
            :tableSetting="tableSetting"
            class="mb-8px"
            :title="financeComp[key].title"
            :columns="financeComp[key].columns"
            :api="financeComp[key].api"
            :scroll="{ y: 500 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === financeComp[key].slotField">
                <a-button type="link" @click="handleView(record, key)">{{ record[financeComp[key].slotField] }}</a-button>
              </template>
            </template>
            <template #footer="currentPageData">
              <div v-if="key === 'Payment'" class="footer">
                <!--                <span> 应付金额合计（当前页）： {{ formateerNotCurrency.format(totalAmount) }} </span>-->
                <span> 应付金额合计（当前页）： {{ handleFormatPrice(currentPageData, 'sale_amount') }} </span>
                <span>本次应付金额合计（当前页）：{{ handleFormatPrice(currentPageData, 'sale_amount_cost') }}</span>
                <span>本次已付金额合计（当前页）：{{ handleFormatPrice(currentPageData, 'sale_amount_paid') }}</span>
              </div>
            </template>
          </BasicTable>
        </template>
      </template>
    </PageWrapper>
    <template v-for="key in Object.keys(drawerComp)" :key="key">
      <component :is="drawerComp[key].comp" @register="drawerComp[key].register" />
    </template>
    <!--        <PurchaseDrawer @register="registerPurchaseDrawer" />-->
  </div>
</template>

<script setup lang="ts" name="/projectOverview/overviewRelate">
import { ref, nextTick, markRaw } from 'vue'
import { PageWrapper } from '/@/components/Page'
import { BasicTable, TableSetting } from '/@/components/Table'
import { useRoute } from 'vue-router'
import { decodeByBase64 } from '/@/utils/cipher'
import { Col, Row } from 'ant-design-vue'
import PurchaseDrawer from '/@/views/erp/purchaseOrder/components/purchaseDrawer.vue'
import InWarehouseDrawer from '/@/views/erp/inWarehouse/components/EditDrawer.vue'
import OutWarehouseDrawer from '/@/views/erp/outWarehouse/components/OutWarehouseDrawer.vue'
import WarehouseTransformDrawer from '/@/views/erp/warehouseTransfer/components/AddtransferDrawer.vue'
import InventoryDrawer from '/@/views/erp/inventoryTable/components/AddinventtorytableDrawer.vue'
import RetreatDrawer from '/@/views/erp/retreat/components/RetreatDrawer.vue'
import DetectionDrawer from '/@/views/erp/qc/components/QcDrawer.vue'
import ReceiptDrawer from '/@/views/financialDocuments/receiptOrder/components/DetailsDrawer.vue'
import PaymentDrawer from '/@/views/financialDocuments/paymentOrder/components/DetailsDrawer.vue'
import OtherIncomeDrawer from '/@/views/financialDocuments/otherIncomeExpend/components/DetailsDrawer.vue'
import OtherExpendDrawer from '/@/views/financialDocuments/otherExpend/components/DetailsDrawer.vue'
import RefundDrawer from '/@/views/financialDocuments/refund/components/refundDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { mapErpComp, mapFinanceComp } from './datas/datas'
import { isNumber } from '/@/utils/is'
import { add } from '/@/utils/math'

const route = useRoute()
const type = route.query.type
const id = decodeByBase64(route.query.id)
console.log(id)
const erpComp = mapErpComp(id)
const financeComp = mapFinanceComp(id)
const tableSetting: TableSetting = {
  redo: true,
  form: false,
  size: false,
  setting: false,
  fullScreen: false
}
const [registerPurchaseDrawer, purchaseEvent] = useDrawer()
const [registerInWarehouseDrawer, inWarehouseEvent] = useDrawer()
const [registerOutWarehouseDrawer, outWarehouseEvent] = useDrawer()
const [registerWarehouseTransformDrawer, warehouseTransformEvent] = useDrawer()
const [registerInventoryDrawer, inventoryEvent] = useDrawer()
const [registerRetreatDrawer, retreatEvent] = useDrawer()
const [registerDetectionDrawer, DetectionEvent] = useDrawer()
const [registerReceiptDrawer, receiptEvent] = useDrawer()
const [registerPaymentDrawer, paymentEvent] = useDrawer()
const [registerOtherIncomeDrawer, otherIncomeEvent] = useDrawer()
const [registerOtherExpendDrawer, otherExpendEvent] = useDrawer()
const [registerRefundDrawer, refundEvent] = useDrawer()
const curDrawer = ref('')
const drawerComp = ref({
  Purchase: { register: registerPurchaseDrawer, comp: markRaw(PurchaseDrawer), event: purchaseEvent },
  InWarehouse: { register: registerInWarehouseDrawer, comp: markRaw(InWarehouseDrawer), event: inWarehouseEvent },
  OutWarehouse: { register: registerOutWarehouseDrawer, comp: markRaw(OutWarehouseDrawer), event: outWarehouseEvent },
  WarehouseTransform: {
    register: registerWarehouseTransformDrawer,
    comp: markRaw(WarehouseTransformDrawer),
    event: warehouseTransformEvent
  },
  Inventory: { register: registerInventoryDrawer, comp: markRaw(InventoryDrawer), event: inventoryEvent },
  Retreat: { register: registerRetreatDrawer, comp: markRaw(RetreatDrawer), event: retreatEvent },
  QualityDetection: { register: registerDetectionDrawer, comp: markRaw(DetectionDrawer), event: DetectionEvent },
  Receipt: { register: registerReceiptDrawer, comp: markRaw(ReceiptDrawer), event: receiptEvent },
  FinancePayment: { register: registerPaymentDrawer, comp: markRaw(PaymentDrawer), event: paymentEvent },
  Payment: { register: registerPaymentDrawer, comp: markRaw(PaymentDrawer), event: paymentEvent },
  OtherIncome: { register: registerOtherIncomeDrawer, comp: markRaw(OtherIncomeDrawer), event: otherIncomeEvent },
  OtherExpend: { register: registerOtherExpendDrawer, comp: markRaw(OtherExpendDrawer), event: otherExpendEvent },
  FinanceOtherExpend: { register: registerOtherExpendDrawer, comp: markRaw(OtherExpendDrawer), event: otherExpendEvent },
  Refund: { register: registerRefundDrawer, comp: markRaw(RefundDrawer), event: refundEvent }
})

function handleView(record, comp) {
  curDrawer.value = comp
  nextTick(() => {
    drawerComp.value[curDrawer.value]?.event?.openDrawer(true, mapDrawerData(record))
    drawerComp.value[curDrawer.value]?.event?.setDrawerProps({ showFooter: false })
  })
}

function mapDrawerData(record) {
  const mapData = {
    Purchase: { record, isUpdate: false, type: 'detail' },
    InWarehouse: { isUpdate: false, type: 'detail', cardItem: record },
    OutWarehouse: { record, type: 'view' },
    WarehouseTransform: { record, isUpdate: false, type: 'detail' },
    Inventory: { record, isUpdate: false, type: 'detail' },
    Retreat: { record, type: 'detail' },
    QualityDetection: { record, type: 'detail' },
    Receipt: { id: record.id },
    Payment: { id: record.id },
    FinancePayment: { id: record.id },
    OtherIncome: { record },
    OtherExpend: { record, type: 'detail' },
    FinanceOtherExpend: { record, type: 'detail' },
    Refund: { type: 'detail', record }
  }
  return mapData[curDrawer.value]
}

function handleFormatPrice(data, field) {
  return data.reduce((acc, cur) => {
    return !isNaN(+cur[field]) && isNumber(+cur[field]) ? add(acc, +cur[field]) : acc
  }, 0)
}
</script>

<style scoped lang="less">
.footer {
  font-size: 15px;
  font-weight: bold;
  span:nth-of-type(2) {
    margin-left: 7%;
  }
  span:nth-of-type(3) {
    margin-left: 7%;
  }
}
</style>
