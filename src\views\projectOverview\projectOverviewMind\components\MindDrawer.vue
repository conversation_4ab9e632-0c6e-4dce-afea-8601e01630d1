<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="90%" destroyOnClose>
    <div>
      <a-button preIcon="ant-design:sync-outlined" @click="onChangeMode">切换{{ mode[modeIndex].text }}</a-button>
    </div>

    <div class="wrap" style="height: 500vh; overflow: auto; display: flex; flex-direction: column" v-show="modeIndex === 0">
      <div style="flex: 1; overflow: auto; position: relative">
        <vue3-tree-org
          :data="treeData"
          :horizontal="horizontal"
          :collapsable="collapsable"
          :label-style="labelStyle"
          :node-draggable="false"
          :scalable="false"
          @on-node-click="onNodeClick"
          :toolBar="false"
          :default-expand-level="2"
        >
          <template #default="{ node }">
            <div v-if="node.$$level === 0" class="p-4px bg-blue-700 text-white text-lg w-66">
              <div class="font-bold">
                <span class="mr-4">项目号: {{ node.$$data.project_number }}</span>
              </div>
              <Tag v-if="!isNullOrUnDef(node.$$data.p_status)">
                {{ mapProjectStatus[node.$$data.p_status] }}
              </Tag>
              <Tag :color="getCountColor(node.$$data.count)" @click.stop="onProjectNodeClick(node)"> 满意度:{{ node.$$data.count }} </Tag>
            </div>

            <div v-else-if="node.$$level === 1" class="p-4px bg-blue-500 w-100 rounded-md" @click="onClickSaleOrderClick(node)">
              <div class="mb-2">
                <span class="mr-4 text-white">
                  {{ node.$$data.source_uniqid }}
                </span>
                <Tag size="small" color="purple">{{ node.$$data.department }}</Tag>
                <Tooltip>
                  <template #title> 点击图标查看商品信息 </template>
                  <paper-clip-outlined
                    style="color: #fff; font-weight: 700; cursor: pointer; font-size: 16px"
                    @click.stop="onProductModal(node.$$data)"
                  />
                </Tooltip>
              </div>
              <Tooltip>
                <template #title> 销售单状态 </template>
                <Tag :color="isNullOrUnDef(node.$$data.status) ? '' : statusColor[node.$$data.status]">
                  {{ isNullOrUnDef(node.$$data.status) ? '' : saleStatus[node.$$data.status] }}
                </Tag>
              </Tooltip>
              <Tooltip>
                <template #title> 销售单结算状态 </template>
                <Tag>
                  {{ isNullOrUnDef(node.$$data.is_audit) ? '' : mapIsAudit[node.$$data.is_audit] }}
                </Tag>
              </Tooltip>

              <Tooltip>
                <template #title> 质检率 </template>
                <Tag> 质检率: {{ node.$$data.qc_rate }} </Tag>
              </Tooltip>

              <Tooltip>
                <template #title> 已采购率 </template>
                <Tag :color="getPurchaseRateColor(node.$$data.purchase_est_finish_at)"> 已采购率: {{ node.$$data.purchase_rate }} </Tag>
              </Tooltip>
            </div>

            <div v-else-if="node.$$level === 2" class="p-4px bg-blue-200 w-120 rounded-md" @click="onSupplierClick(node)">
              <div>
                {{ node.$$data.supplier_name }}
              </div>

              <div v-for="item in node.$$data.childrend" :key="item.id">
                <div> 采购单号: {{ item.strid }} </div>
                <div class="flex justify-center">
                  <Tooltip>
                    <template #title> 采购单状态 </template>
                    <Tag :color="getDeliverColor(item.status, item.deliver_at, 15)">
                      {{ isNullOrUnDef(item.status) ? '' : mapStatus[item.status].label }}
                    </Tag>
                  </Tooltip>

                  <Tooltip>
                    <template #title> 采购单结算状态 </template>
                    <Tag>
                      {{ isNullOrUnDef(item.is_audit) ? '' : mapAudit[item.is_audit].label }}
                    </Tag>
                  </Tooltip>

                  <Tooltip>
                    <template #title> 包裹率 </template>
                    <Tag :color="getDeliverColor(item.package_rate, item.deliver_at, 100)">
                      包裹率: {{ isNullOrUnDef(item.package_rate) ? '' : item.package_rate }}
                    </Tag>
                  </Tooltip>

                  <Tooltip>
                    <template #title> 入库率 </template>
                    <Tag :color="getDeliverColor(item.inWarehouse_rate, item.deliver_at, 100)">
                      入库率: {{ isNullOrUnDef(item.inWarehouse_rate) ? '' : item.inWarehouse_rate }}
                    </Tag>
                  </Tooltip>

                  <Tooltip>
                    <template #title> 质检率 </template>
                    <Tag :color="getDeliverColor(item.qc_rate, item.deliver_at, 100)">
                      质检率: {{ isNullOrUnDef(item.qc_rate) ? '' : item.qc_rate }}
                    </Tag>
                  </Tooltip>
                </div>
              </div>
            </div>
          </template>
        </vue3-tree-org>
      </div>
    </div>

    <Transition name="fade">
      <div v-show="modeIndex === 1">
        <BasicTable @register="registerTable">
          <template #expandedRowRender="{ record }">
            <BasicTable @register="registerChildTable" :data-source="record.work">
              <template #expandedRowRender="{ record: sale_record }">
                <BasicTable
                  :bordered="true"
                  :showIndexColumn="false"
                  :canResize="false"
                  :columns="grandChildColumns(sale_record.suppliers)"
                  :data-source="flattenSupplier(sale_record.suppliers)"
                />
              </template>
              <template #bodyCell="{ column, record: sale_record }">
                <template v-if="column.dataIndex === 'source_uniqid'">
                  <div>
                    <span class="mr-4"> {{ sale_record.source_uniqid }}</span>
                    <paper-clip-outlined style="color: #096dd9; cursor: pointer; font-size: 16px" @click="onProductModal(sale_record)" />
                  </div>
                </template>
              </template>
            </BasicTable>
          </template>
        </BasicTable>
      </div>
    </Transition>
    <ProductModal @register="registerProductModal" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { PaperClipOutlined } from '@ant-design/icons-vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { nextTick, reactive, ref } from 'vue'
import { Tag, Tooltip } from 'ant-design-vue'
import vue3TreeOrg2 from 'vue3-tree-org'
import 'vue3-tree-org/lib/vue3-tree-org.css'
import { isNullOrUnDef } from '/@/utils/is'
import { getCountColor, getDeliverColor, getPurchaseRateColor, flattenSupplier } from '../datas/fn'
import { collapsable, horizontal, labelStyle, mapIsAudit, mapProjectStatus, saleStatus, statusColor } from '../datas/const'
import { mapAudit, mapStatus } from '../../../erp/purchaseOrder/datas/datas'
import ProductModal from './ProductModal.vue'
import { useModal } from '/@/components/Modal'
import { BasicTable, useTable } from '/@/components/Table'
import { columns, childColumns, grandChildColumns } from '../datas/datas.new'

//文档 https://sangtian152.github.io/vue3-tree-org/demo/#%E5%9F%BA%E7%A1%80%E7%94%A8%E6%B3%95

const [registerProductModal, { openModal, setModalProps }] = useModal()
const vue3TreeOrg = vue3TreeOrg2.Vue3TreeOrg
// 定义树形数据类型
interface TreeNode {
  id: number
  label: string
  pid?: number
  isLeaf?: boolean
  $$level: number
}

const modeIndex = ref(0)
const mode = [
  {
    text: '为表格'
  },
  {
    text: '为思维图'
  }
]

const onChangeMode = () => {
  modeIndex.value = modeIndex.value === 0 ? 1 : 0
}

const items = ref([])

let treeData = reactive<TreeNode>({})

const [registerDrawer, { changeLoading }] = useDrawerInner(async (data) => {
  try {
    await changeLoading(true)

    items.value = data.record

    // 构建树形数据
    treeData = buildTreeData(data.record)

    setTableData([data.record])
    nextTick(async () => {
      await expandAll()
      expandAllChild()
    })
  } catch (err) {
    console.error(err, 'registerMindDrawer')
  } finally {
    changeLoading(false)
  }
})

const buildTreeData = (projectItems): TreeNode[] => {
  return {
    ...projectItems,
    label: projectItems.project_number, // 使用项目编号作为标签
    $$level: 0,
    children: projectItems.work?.map((work) => ({
      ...work,
      label: work.source_uniqid, // 使用销售订单号作为标签
      $$level: 1,
      children: work.suppliers?.map((supplier, index) => ({
        ...supplier,
        id: supplier.supplier_name + index,
        label: supplier.supplier_name, // 使用供应商名称作为标签
        $$level: 2,
        isLeaf: true
      }))
    }))
  }
}

// 节点点击事件处理
const onNodeClick = (_, data: TreeNode) => {
  console.info(data.label)
}

// 扩展和收缩树形节点的方法
// const expandChange = () => {
//   toggleExpand(treeData, expandAll.value)
// }

// 模拟展开/折叠操作
// const toggleExpand = (node: TreeNode, expand: boolean) => {
//   // 更新节点展开状态（这只是一个示例，根据实际需要调整）
//   node.$$level = expand ? 1 : 0
// }

function onProjectNodeClick(node: TreeNode) {
  console.log(node)
}
function onClickSaleOrderClick(node: TreeNode) {
  console.log(node, 'node')
}
const onSupplierClick = (node) => {
  // 处理供应商节点点击事件
  console.log('Supplier clicked:', node.$$data)
  // 这里可以添加您需要的功能，比如显示详情等
}

const onProductModal = (record) => {
  setModalProps({
    title: '销售单号: ' + record.source_uniqid
  })
  openModal(true, { id: record.id })
}

const [registerTable, { setTableData, expandAll }] = useTable({
  title: '项目总览',
  dataSource: [],
  bordered: true,
  showIndexColumn: false,
  rowKey: 'id',
  columns: columns,
  isTreeTable: true,
  useSearchForm: false,
  showTableSetting: true,
  pagination: false
})

const [registerChildTable, { expandAll: expandAllChild }] = useTable({
  showIndexColumn: false,
  // pagination: false,
  columns: childColumns,
  canResize: false,
  isTreeTable: true,
  defaultExpandAllRows: true
})
</script>

<style scoped lang="less">
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.5);
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
  transform: scale(1);
}

.fade-enter-active,
.fade-leave-active {
  transition: all 1s ease;
}
.wrap {
  ::v-deep(.tree-org-node__inner) {
    border-radius: 6px !important ;
    overflow: hidden;
  }
  ::v-deep(.tree-org-node) {
    &:not(:only-child),
    .tree-org-node__children,
    .tree-org-node {
      &::after,
      &::before {
        border-color: #000 !important;
      }
    }
  }
}
</style>
