<template>
  <BasicModal @register="registerModal" v-bind="$attrs" title="商品信息" width="90%" :showCancelBtn="false" @ok="closeModal">
    <BasicTable @register="registerChildTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'imgs'">
          <TableImg :imgList="record.imgs" :simpleShow="true" />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script setup lang="ts">
import { productColumns } from '../datas/datas.new'
import { getProjectSalesRequest } from '/@/api/projectOverview'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicTable, TableImg, useTable } from '/@/components/Table'

const [registerModal, { changeOkLoading, closeModal }] = useModalInner(async (data: Recordable) => {
  try {
    await changeOkLoading(true)
    await setProps({
      searchInfo: {
        id: data.id
      }
    })
    reload()
  } catch (err) {
    console.error(err, '注册商品抽屉')
  } finally {
    changeOkLoading(false)
  }
})

const [registerChildTable, { reload, setProps }] = useTable({
  columns: productColumns,
  showIndexColumn: false,
  // pagination: false,
  canResize: false,
  api: getProjectSalesRequest,
  immediate: false
})
</script>
