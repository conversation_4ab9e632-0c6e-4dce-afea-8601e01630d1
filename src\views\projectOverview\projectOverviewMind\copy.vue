<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="100%" destroyOnClose>
    <div class="wrap" style="height: 500vh; overflow: auto; display: flex; flex-direction: column">
      <div style="flex: 1; overflow: auto; position: relative">
        <vue3-tree-org
          :data="treeData"
          :horizontal="horizontal"
          :collapsable="collapsable"
          :label-style="labelStyle"
          :node-draggable="false"
          :scalable="false"
          @on-node-click="onNodeClick"
          :toolBar="false"
          :default-expand-level="2"
        >
          <template #default="{ node }">
            <div v-if="node.$$level === 0" class="p-4px bg-green-300 w-52">
              <div>
                {{ node.label }}
              </div>
              <!-- <Pagination
                simple
                size="small"
                v-model:current="currentPage"
                :total="total"
                show-less-items
                :defaultPageSize="pageSize"
                :hideOnSinglePage="true"
                :showSizeChanger="false"
                :showQuickJumper="true"
              /> -->
            </div>
            <div v-if="node.$$level === 1" class="p-4px bg-lime-400 w-64">
              <div>{{ node.$$data.project_number }}</div>
              <Tag>
                {{ isNullOrUnDef(node.$$data.status) ? '' : mapProjectStatus[node.$$data.status] }}
              </Tag>
              <Tag :color="getCountColor(node.$$data.count)" @click.stop="onProjectNodeClick(node)"> 满意度:{{ node.$$data.count }} </Tag>
            </div>

            <div v-else-if="node.$$level === 2" class="p-4px bg-orange-200 w-60" @click="onClickSaleOrderClick(node)">
              <div>
                {{ node.$$data.source_uniqid }}
              </div>
              <Tag :color="isNullOrUnDef(node.$$data.status) ? '' : statusColor[node.$$data.status]">
                {{ isNullOrUnDef(node.$$data.status) ? '' : saleStatus[node.$$data.status] }}
              </Tag>
              <Tag>
                {{ isNullOrUnDef(node.$$data.is_audit) ? '' : mapIsAudit[node.$$data.is_audit] }}
              </Tag>
              <Tag>
                {{ node.$$data.qc_rate }}
              </Tag>
              <Tag>
                {{ node.$$data.purchase_rate }}
              </Tag>
            </div>

            <div v-else-if="node.$$level === 3" class="p-4px bg-blue-200 w-80" @click="onSupplierClick(node)">
              <div>
                {{ node.$$data.doc_purchase.supplier_name }}
              </div>

              <div>
                <div> 采购单号: {{ node.$$data.doc_purchase.strid }} </div>
                <div class="flex justify-center">
                  <Tag :color="isNullOrUnDef(node.$$data.doc_purchase.status) ? '' : mapStatus[node.$$data.doc_purchase.status].color">
                    {{ isNullOrUnDef(node.$$data.doc_purchase.status) ? '' : mapStatus[node.$$data.doc_purchase.status].label }}
                  </Tag>
                  <Tag :color="isNullOrUnDef(node.$$data.doc_purchase.status) ? '' : mapAudit[node.$$data.is_audit].color">
                    {{ isNullOrUnDef(node.$$data.is_audit) ? '' : mapAudit[node.$$data.is_audit].label }}
                  </Tag>
                  <Tag>
                    {{ isNullOrUnDef(node.$$data.is_audit) ? '' : mapAudit[node.$$data.is_audit].label }}
                  </Tag>
                </div>
              </div>
            </div>
          </template>
        </vue3-tree-org>
      </div>
    </div>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'

import { onMounted, reactive, ref, watch } from 'vue'
import { message, Tag } from 'ant-design-vue'
import vue3TreeOrg2 from 'vue3-tree-org'
import 'vue3-tree-org/lib/vue3-tree-org.css'
import { getListByTree } from '/@/api/projectOverview'
import { isNullOrUnDef } from '/@/utils/is'
import { getCountColor } from './datas/fn'
import { collapsable, horizontal, labelStyle, mapIsAudit, mapProjectStatus, saleStatus, statusColor } from './datas/const'
import { mapAudit, mapStatus } from '../../erp/purchaseOrder/datas/datas'

//文档 https://sangtian152.github.io/vue3-tree-org/demo/#%E5%9F%BA%E7%A1%80%E7%94%A8%E6%B3%95

const vue3TreeOrg = vue3TreeOrg2.Vue3TreeOrg
// 定义树形数据类型
interface TreeNode {
  id: number
  label: string
  pid?: number
  isLeaf?: boolean
  $$level: number
}

const items = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(1)

const [registerDrawer, {}] = useDrawerInner(async () => {})

const treeData = reactive<TreeNode>({
  id: 0,
  label: '项目总览',
  $$level: 0,
  children: [] // 初始化 children 为空数组
})

const handleSearch = async () => {
  try {
    const { items: projectItems, total: totalItems } = await getListByTree({
      pageSize: pageSize.value,
      page: currentPage.value
    })

    items.value = projectItems
    total.value = totalItems

    // 构建树形数据
    treeData.children = buildTreeData(projectItems)
  } catch (error) {
    console.error('Failed to fetch data:', error)
  }
}

const buildTreeData = (projectItems: any[]): TreeNode[] => {
  return projectItems.map((project) => ({
    ...project,
    id: project.project_number,
    label: project.project_number, // 使用项目编号作为标签
    $$level: 1,
    children: project.work?.map((work) => ({
      ...work,
      id: work.source_uniqid,
      label: work.source_uniqid, // 使用销售订单号作为标签
      $$level: 2,
      children: work.type4_works?.map((supplier) => ({
        ...supplier,
        id: supplier.doc_purchase.strid,
        label: supplier.doc_purchase.supplier_name, // 使用供应商名称作为标签
        $$level: 3,
        isLeaf: true
      }))
    }))
  }))
}

watch([currentPage, pageSize], () => {
  handleSearch()
})

onMounted(() => {
  handleSearch()
})

// 节点点击事件处理
const onNodeClick = (e: Event, data: TreeNode) => {
  console.info(data.label)
}

// 扩展和收缩树形节点的方法
// const expandChange = () => {
//   toggleExpand(treeData, expandAll.value)
// }

// 模拟展开/折叠操作
// const toggleExpand = (node: TreeNode, expand: boolean) => {
//   // 更新节点展开状态（这只是一个示例，根据实际需要调整）
//   node.$$level = expand ? 1 : 0
// }

function onProjectNodeClick(node: TreeNode) {
  console.log(node)
  message.success('项目点击 ' + node.label)
}
function onClickSaleOrderClick(node: TreeNode) {
  console.log(node, 'node')
  message.success('销售单点击 ' + node.label)
}
const onSupplierClick = (node) => {
  // 处理供应商节点点击事件
  console.log('Supplier clicked:', node.$$data)
  // 这里可以添加您需要的功能，比如显示详情等
}
</script>
