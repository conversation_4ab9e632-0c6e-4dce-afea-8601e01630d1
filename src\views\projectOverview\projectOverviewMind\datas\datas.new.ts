import { mapIsAudit, mapProjectStatus, saleStatus } from './const'
import { getCountColor, getPurchaseRateColor, getDeliverColor, calculateRowSpan } from './fn'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { isNullOrUnDef } from '/@/utils/is'
import { mapAudit as mapPurchaseAudit, mapStatus as mapPurchaseStatus } from '../../../erp/purchaseOrder/datas/datas'
import { useI18n } from '/@/hooks/web/useI18n'
import { mul } from '/@/utils/math'

const { t } = useI18n()
export const searchFormSchema: FormSchema[] = [
  {
    field: 'project_number',
    label: '项目号',
    component: 'Input'
  },
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input'
  },
  {
    field: 'source_uniqid',
    label: '销售单号',
    component: 'Input'
  }
]

export const columns: BasicColumn[] = [
  {
    title: '项目号',
    dataIndex: 'project_number',
    resizable: true,
    width: 120
  },
  {
    title: '项目名称',
    dataIndex: 'project_name',
    resizable: true,
    width: 120
  },
  {
    title: '项目状态',
    dataIndex: 'p_status',
    resizable: true,
    width: 120,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : useRender.renderTag(mapProjectStatus[value])
    }
  },
  {
    title: '满意度',
    dataIndex: 'count',
    resizable: true,
    width: 120,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '' : useRender.renderTag(value, getCountColor(value))
    }
  }
]

export const childColumns: BasicColumn[] = [
  {
    title: '销售单号',
    dataIndex: 'source_uniqid',
    resizable: true,
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    resizable: true,
    width: 120,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '' : useRender.renderTag(saleStatus.value[value])
    }
  },
  {
    title: '结算状态',
    dataIndex: 'is_audit',
    resizable: true,
    width: 120,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '' : useRender.renderTag(mapIsAudit[value])
    }
  },
  {
    title: '质检率',
    dataIndex: 'qc_rate',
    resizable: true,
    width: 120,
    customRender: ({ value, record }) => {
      const { deliver_at } = record
      return isNullOrUnDef(value) ? '' : useRender.renderTag(value, getDeliverColor(value, deliver_at, 100))
    }
  },
  {
    title: '已采购率',
    dataIndex: 'purchase_rate',
    resizable: true,
    width: 120,
    customRender: ({ record, value }) => {
      // 未过采购需求日期：绿色，过了3天内黄色，超3天红色
      const color = getPurchaseRateColor(record.purchase_est_finish_at)
      return isNullOrUnDef(value) ? '' : useRender.renderTag(value, color)
    }
  }
]

export const grandChildColumns: (suppliers) => BasicColumn[] = (suppliers) => [
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    resizable: true,
    width: 120,
    // 需要合并单元格
    customCell: (_, index) => {
      const rowSpanData = calculateRowSpan(suppliers)
      return { rowSpan: rowSpanData[index].rowSpan }
    }
  },
  {
    title: '采购单号',
    dataIndex: 'strid',
    resizable: true,
    width: 120
  },
  {
    title: '采购状态',
    dataIndex: 'status',
    resizable: true,
    width: 120,
    customRender: ({ value, record }) => {
      const { deliver_at } = record
      if (Object.keys(mapPurchaseStatus).includes(String(value))) {
        return isNullOrUnDef(value) ? '' : useRender.renderTag(mapPurchaseStatus[value].label, getDeliverColor(value, deliver_at, 15))
      } else {
        return ''
      }
    }
  },
  {
    title: '结算状态',
    dataIndex: 'is_audit',
    resizable: true,
    width: 120,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '' : useRender.renderTag(mapPurchaseAudit[value].label)
    }
  },
  {
    title: '包裹率',
    dataIndex: 'package_rate',
    resizable: true,
    width: 120,
    customRender: ({ value, record }) => {
      const { deliver_at } = record
      return isNullOrUnDef(value) ? '' : useRender.renderTag(value, getDeliverColor(value, deliver_at, 100))
    }
  },
  {
    title: '入库率',
    dataIndex: 'inWarehouse_rate',
    resizable: true,
    width: 120,
    customRender: ({ value, record }) => {
      const { deliver_at } = record
      return isNullOrUnDef(value) ? '' : useRender.renderTag(value, getDeliverColor(value, deliver_at, 100))
    }
  },
  {
    title: '质检率',
    dataIndex: 'qc_rate',
    resizable: true,
    width: 120,
    customRender: ({ value, record }) => {
      const { deliver_at } = record
      return isNullOrUnDef(value) ? '' : useRender.renderTag(value, getDeliverColor(value, deliver_at, 100))
    }
  }
]
export const productColumns: BasicColumn[] = [
  {
    title: '商品名称',
    dataIndex: 'name',
    width: 120,
    resizable: true
  },
  {
    title: '商品图片',
    dataIndex: 'imgs',
    width: 80,
    resizable: true
  },
  {
    title: '产品编码',
    dataIndex: 'puid',
    width: 100,
    resizable: true
  },
  {
    title: '采购进度',
    dataIndex: 'purchaseSchedule',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(t(`tag.overviewTag.${value}.label`), t(`tag.overviewTag.${value}.color`))
    }
  },
  {
    title: '未采购数量',
    dataIndex: 'noPurchaseNum',
    width: 100,
    resizable: true
  },
  {
    title: '采购单位',
    dataIndex: 'unit',
    width: 80,
    resizable: true
  },
  {
    title: '入库进度',
    dataIndex: 'inWarehouseSchedule',
    width: 80,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(t(`tag.overviewTag.${value}.label`), t(`tag.overviewTag.${value}.color`))
    }
  },
  {
    title: '未入库数量',
    dataIndex: 'noInWarehouseNum',
    width: 80,
    resizable: true
  },
  {
    title: '出库进度',
    dataIndex: 'outWarehouseSchedule',
    width: 80,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(t(`tag.overviewTag.${value}.label`), t(`tag.overviewTag.${value}.color`))
    }
  },
  {
    title: '未出库数量',
    dataIndex: 'noOutWarehouseNum',
    width: 80,
    resizable: true
  },
  {
    title: '单价',
    dataIndex: 'unit_price',
    width: 80,
    resizable: true
  },
  {
    title: '实际需求数量',
    dataIndex: 'qty_request_actual',
    width: 80,
    resizable: true
  },
  {
    title: '销售单位',
    dataIndex: 'unit',
    width: 80,
    resizable: true
  },
  {
    title: '总价',
    dataIndex: 'unit_price',
    width: 80,
    resizable: true,
    customRender: ({ record }) => mul(record.qty_request_actual, record.unit_price)
  }
]
