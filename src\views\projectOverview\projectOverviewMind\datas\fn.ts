import dayjs from 'dayjs'
import { isNullOrUnDef } from '/@/utils/is'

export const getCountColor = (count) => {
  if (isNullOrUnDef(count)) return ''
  if (count <= 3) {
    return 'red'
  } else if (count <= 4.5) {
    return 'yellow'
  } else {
    return 'green'
  }
}

// 计算采购需求日期与当前日期的颜色逻辑
export const getPurchaseRateColor = (purchase_est_finish_at) => {
  if (isNullOrUnDef(purchase_est_finish_at)) return ''
  const now = dayjs() // 当前时间
  const estFinishDate = dayjs(purchase_est_finish_at) // 采购需求完成日期

  // 计算两个日期之间的天数差（正数表示已过期，负数表示未过期）
  const diffInDays = now.diff(estFinishDate, 'day')

  if (diffInDays < 0) {
    // 未过期
    return 'green'
  } else if (diffInDays <= 3) {
    // 过期3天内
    return 'yellow'
  } else {
    // 过期超过3天
    return 'red'
  }
}

/**
 * 导出一个函数，用于获取交付颜色
 * @param {number} value - 当前值
 * @param {string} deliver_at - 交付日期
 * @param {number} complete_value - 完成值
 */
export const getDeliverColor = (value, deliver_at, complete_value) => {
  // 如果交付日期为空，则返回空字符串
  if (isNullOrUnDef(deliver_at)) return ''
  // 获取当前日期
  const now = dayjs() // 当前日期
  // 获取交付日期
  const deliverDate = dayjs(deliver_at) // 交付日期
  // 计算交付日期与当前日期的差值（正数表示未来，负数表示过去）
  const diffInDays = deliverDate.diff(now, 'day') // 交付日期与当前日期的差值（正数表示未来，负数表示过去）
  // 判断是否完成
  const isComplete = value === complete_value

  // 交付日期前3天及更早（diffInDays > 3）
  if (diffInDays > 3) {
    return 'green'
  }

  // 交付日期3天内（0 < diffInDays <= 3）
  if (diffInDays > 0 && diffInDays <= 3) {
    return isComplete ? 'green' : 'yellow'
  }

  // 交付日期当天（diffInDays === 0）
  if (diffInDays === 0) {
    return isComplete ? 'green' : 'red'
  }

  // 交付日期已过（diffInDays < 0），延续100%时的颜色
  // 这里假设一旦达到100%，颜色不再变化，需根据业务逻辑确定初始颜色
  if (diffInDays < 0) {
    return isComplete ? 'green' : 'red' // 默认过期未完成是红色，完成是绿色
  }
}
export const flattenSupplier = (supplier) => {
  return supplier.reduce((acc, item) => {
    const children = item.childrend.map((child) => ({
      supplier_name: item.supplier_name,
      children_lenght: item.childrend.length,
      ...child
    }))
    return acc.concat(children)
  }, [])
}

export function calculateRowSpan(data) {
  const rowSpanArray: any[] = []
  let currentRow = 0

  data.forEach((item) => {
    const childrenLength = item.childrend.length

    // 记录首行的 rowSpan
    rowSpanArray.push({ rowSpan: childrenLength })

    // 后续的行不需要显示 rowSpan，设为 0
    for (let i = 1; i < childrenLength; i++) {
      rowSpanArray.push({ rowSpan: 0 })
    }

    currentRow = currentRow + childrenLength
  })

  return rowSpanArray
}
