<template>
  <div>
    <BasicTable @register="registerTable" @fetch-success="onFetchSuccess">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>

      <template #expandedRowRender="{ record }">
        <BasicTable @register="registerChildTable" :data-source="record.work">
          <template #expandedRowRender="{ record: sale_record }">
            <BasicTable
              :bordered="true"
              :showIndexColumn="false"
              :canResize="false"
              :columns="grandChildColumns(sale_record.suppliers)"
              :data-source="flattenSupplier(sale_record.suppliers)"
            />
          </template>
          <template #bodyCell="{ column, record: sale_record }">
            <template v-if="column.dataIndex === 'source_uniqid'">
              <div>
                <a-button type="link" size="small" class="mr-4 text-blue-800" @click="onMind({ ...record, work: [sale_record] })">
                  {{ sale_record.source_uniqid }}</a-button
                >

                <a-button type="link" size="small" @click.stop="onProductModal(sale_record)" postIcon="ant-design:paper-clip-outlined"
                  >相关产品
                </a-button>
              </div>
            </template>
          </template>
        </BasicTable>
      </template>
    </BasicTable>
    <MindDrawer @register="registerDrawer" />
    <ProductModal @register="registerProductModal" />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table'
import { childColumns, columns, grandChildColumns, searchFormSchema } from './datas/datas.new'
import { getListByTree } from '/@/api/projectOverview'
import MindDrawer from './components/MindDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { flattenSupplier } from './datas/fn'
import { useModal } from '/@/components/Modal'
import ProductModal from './components/ProductModal.vue'

const [registerDrawer, { openDrawer }] = useDrawer()
const [registerProductModal, { openModal, setModalProps }] = useModal()

const [registerTable, {}] = useTable({
  title: '项目总览',
  api: getListByTree,
  bordered: true,
  showIndexColumn: false,
  rowKey: 'id',
  columns: columns,
  isTreeTable: true,
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema,
    baseColProps: {
      span: 6
    },
    autoAdvancedLine: 3
  },
  useSearchForm: true,
  showTableSetting: true,
  actionColumn: {
    width: 130,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  }
})

const [registerChildTable, {}] = useTable({
  showIndexColumn: false,
  // pagination: false,
  columns: childColumns,
  canResize: false
})

const createActions: (record: any) => ActionItem[] = (record) => [
  {
    label: '以思维导图展示',
    icon: 'ant-design:retweet-outlined',
    onClick: onMind.bind(null, record)
  }
]

const onMind = async (record) => {
  openDrawer(true, { record })
}

const onFetchSuccess = () => {
  // nextTick(async () => {
  //   await expandAll()
  // })
}

const onProductModal = (record) => {
  setModalProps({
    title: '销售单号: ' + record.source_uniqid
  })
  openModal(true, { id: record.id })
}
</script>
