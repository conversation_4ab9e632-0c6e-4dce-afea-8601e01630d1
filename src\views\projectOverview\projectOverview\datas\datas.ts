import { h } from 'vue'
import { type BasicColumn, FormSchema } from '/@/components/Table'
import Icon from '@ant-design/icons-vue'
import { useRender } from '/@/components/Table/src/hooks/useRender'

import { useI18n } from '/@/hooks/web/useI18n'
import { mul } from '/@/utils/math'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { isUndefined, isNull } from 'lodash-es'
import Decimal from 'decimal.js'

const { t } = useI18n()

export const columns: BasicColumn[] = [
  {
    title: '开单时间',
    dataIndex: 'submited_at',
    width: 120,
    resizable: true
  },
  {
    title: '销售单ID',
    dataIndex: 'id',
    width: 80,
    resizable: true
  },
  {
    title: '销售单号',
    dataIndex: 'source_uniqid',
    width: 120,
    resizable: true
  },
  {
    title: '交付日期',
    dataIndex: 'deliver_at',
    width: 120,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 120,
    resizable: true
  },
  {
    title: '应收金额',
    dataIndex: 'receivable_left',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  {
    title: '订单尾款',
    dataIndex: 'receivable_lefts', //并没有这个字段,前端进行计算得出
    width: 120,
    resizable: true,
    customRender: ({ record }) => {
      if (record.is_audit !== 0) return 0

      // 检查值是否为null或undefined
      const receivableLeft = record.receivable_left ?? 0
      const receivedActual = record.received_actual ?? 0

      return new Decimal(receivableLeft).minus(receivedActual).toNumber()
    }
  },
  {
    title: '任务号',
    dataIndex: 'strid',
    width: 120,
    resizable: true
  },
  {
    title: '执行状态',
    dataIndex: 'status',
    width: 100,
    resizable: true,
    customRender({ value }) {
      return useRender.renderTag(t(`tag.salesStatus.${value}.label`), t(`tag.salesStatus.${value}.color`))
    }
  },
  {
    title: '采购进度',
    dataIndex: 'purchaseSchedule',
    width: 80,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(t(`tag.overviewTag.${value}.label`), t(`tag.overviewTag.${value}.color`))
    }
  },
  {
    title: '入库进度',
    dataIndex: 'inWarehouseSchedule',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(t(`tag.overviewTag.${value}.label`), t(`tag.overviewTag.${value}.color`))
    }
  },
  {
    title: '出库进度',
    dataIndex: 'outWarehouseSchedule',
    width: 80,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(t(`tag.overviewTag.${value}.label`), t(`tag.overviewTag.${value}.color`))
    }
  },
  {
    title: '质检率',
    dataIndex: 'qcRate',
    width: 80,
    resizable: true,
    customRender: ({ value }) => {
      return value + '%'
    }
  },
  {
    title: '收款次数',
    dataIndex: 'receivedNum',
    width: 80,
    resizable: true
  },
  // {
  //   title: '付款次数',
  //   dataIndex: 'pay_count',
  //   width: 80,
  //   resizable: true
  // },
  {
    title: '退货商品项',
    dataIndex: 'retreatNum',
    width: 80,
    resizable: true
  }
  // {
  //   title: '退货次数',
  //   dataIndex: 'retreatNum',
  //   width: 80,
  //   resizable: true
  // }
]
export const childColumns: BasicColumn[] = [
  {
    title: '商品名称',
    dataIndex: 'name',
    width: 120,
    resizable: true
  },
  {
    title: '商品图片',
    dataIndex: 'imgs',
    width: 80,
    resizable: true
  },
  {
    title: '产品编码',
    dataIndex: 'puid',
    width: 100,
    resizable: true
  },
  {
    title: '采购进度',
    dataIndex: 'purchaseSchedule',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(t(`tag.overviewTag.${value}.label`), t(`tag.overviewTag.${value}.color`))
    }
  },
  {
    title: '未采购数量',
    dataIndex: 'noPurchaseNum',
    width: 100,
    resizable: true
  },
  {
    title: '采购单位',
    dataIndex: 'unit',
    width: 80,
    resizable: true
  },
  {
    title: '入库进度',
    dataIndex: 'inWarehouseSchedule',
    width: 80,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(t(`tag.overviewTag.${value}.label`), t(`tag.overviewTag.${value}.color`))
    }
  },
  {
    title: '未入库数量',
    dataIndex: 'noInWarehouseNum',
    width: 80,
    resizable: true
  },
  {
    title: '出库进度',
    dataIndex: 'outWarehouseSchedule',
    width: 80,
    resizable: true,
    customRender: ({ value }) => {
      return useRender.renderTag(t(`tag.overviewTag.${value}.label`), t(`tag.overviewTag.${value}.color`))
    }
  },
  {
    title: '未出库数量',
    dataIndex: 'noOutWarehouseNum',
    width: 80,
    resizable: true
  },
  {
    title: '单价',
    dataIndex: 'unit_price',
    width: 80,
    resizable: true
  },
  {
    title: '实际需求数量',
    dataIndex: 'qty_request_actual',
    width: 80,
    resizable: true
  },
  {
    title: '销售单位',
    dataIndex: 'unit',
    width: 80,
    resizable: true
  },
  {
    title: '总价',
    dataIndex: 'unit_price',
    width: 80,
    resizable: true,
    customRender: ({ record }) => mul(record.qty_request_actual, record.unit_price)
  }
]

export function expandIcon(props) {
  let icon = 'ant-design:right-outlined'
  if (props.expanded) {
    icon = 'ant-design:down-outlined'
  } else {
    icon = 'ant-design:right-outlined'
  }
  return h(Icon, {
    icon: icon,
    onClick: function (e) {
      props.onExpand(props.record, e)
    },
    style: {
      fontSize: '22px',
      verticalAlign: 'middle',
      cursor: 'pointer',
      margin: '0 10px 3px 0'
    }
  })
}

export const searchSchemas: FormSchema[] = [
  {
    field: 'source_uniqid',
    label: '销售单号',
    component: 'Input'
  },
  {
    field: 'title',
    label: '项目名称',
    component: 'Input'
  },
  {
    field: 'customer',
    label: '客户',
    component: 'Input'
    // componentProps: {
    //   api: getClientList,
    //   selectProps: {
    //     fieldNames: { value: 'id', label: 'name' },
    //     showSearch: true,
    //     placeholder: '请选择',
    //     optionFilterProp: 'name'
    //   },
    //   resultField: 'items'
    // }
  },
  {
    field: 'deliver_at',
    label: '交付日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'id',
    label: '项目id',
    component: 'Input'
  }
]
