<!-- eslint-disable max-len -->
<template>
  <PageWrapper contentBackground>
    <template #headerContent>
      <div style="font-weight: bold; font-size: 28px" class="mb-4"
        >项目总览:
        <a
          style="color: red; font-size: 25px"
          href="https://img.gbuilderchina.com/erp/%E9%A1%B9%E7%9B%AE%E6%80%BB%E8%A7%88%E8%AF%B4%E6%98%8E%EF%BC%88%E5%8F%AF%E6%9F%A5%E9%A1%B9%E7%9B%AE%E8%AE%A2%E5%8D%95%E8%BF%9B%E5%BA%A6%EF%BC%89.docx"
          >项目总览教程说明（点击下载）</a
        >
      </div>
      <BasicForm @register="registerForm" :showAdvancedButton="true" :autoSubmitOnEnter="true" :autoAdvancedLine="1" />
    </template>
    <template v-if="projectList.length > 0">
      <Collapse v-loading="loading" :bordered="false" destroyInactivePanel>
        <template #expandIcon="{ isActive }">
          <caret-right-outlined :rotate="isActive ? 90 : 0" />
        </template>
        <CollapsePanel
          v-for="item in projectList"
          :key="item.id"
          style="background: #f7f7f7; border-radius: 4px; margin-bottom: 10px; border: 0; overflow: hidden"
        >
          <template #header>
            <div class="flex items-center">
              <div> 项目id：{{ item.id }} - {{ item.title }} - 客户：{{ item.customer_name }}</div>
              <!--<a-button class="ml-4" type="primary" size="small" @click.stop="handleCreateRevisit(item.id)">新增回访</a-button>-->
            </div>
          </template>
          <!--          <Descriptions :column="4" bordered size="middle" class="mb-4" :labelStyle="{ width: '140px' }">-->
          <!--            &lt;!&ndash; 没有回访记录就全显示 -,回访次数显示0  &ndash;&gt;-->
          <!--            <DescriptionsItem label="回访次数">{{ item.revisit ? item.revisit.count : 0 }}</DescriptionsItem>-->
          <!--            <DescriptionsItem label="回访日期">{{ item.revisit ? item.revisit.visit_date : '-' }}</DescriptionsItem>-->
          <!--<DescriptionsItem label="回访方式">{{ item.revisit ? mapWay[item.revisit.visit_way].label : '-' }}</DescriptionsItem>-->
          <!--            <DescriptionsItem label="群名称">{{ item.revisit ? item.revisit.group_name : '-' }}</DescriptionsItem>-->
          <!--          </Descriptions>-->
          <BasicTable
            :key="getFieldsValue().source_uniqid + getFieldsValue().title"
            :api="
              (params) =>
                getProjectSalesOrder({
                  ...params,
                  id: item.id,
                  source_uniqid: getFieldsValue().source_uniqid,
                  deliver_at_start: getFieldsValue().deliver_at_start,
                  deliver_at_end: getFieldsValue().deliver_at_end
                })
            "
            :canResize="false"
            :columns="columns"
            :showIndexColumn="false"
            :isTreeTable="true"
            rowKey="id"
            :actionColumn="{
              width: 300,
              title: '操作',
              dataIndex: 'action'
            }"
            :after-fetch="hanleafterfetch"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'action'">
                <TableAction :actions="createActions(record)" />
              </template>
            </template>
            <template #footer>
              <div class="footer">订单尾款合计：{{ totalreceivable }}元</div>
            </template>
            <template #expandedRowRender="{ record }">
              <BasicTable @register="registerChildTable" :api="(params) => getProjectSalesRequest({ ...params, id: record.id })">
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'imgs'">
                    <TableImg :imgList="record.imgs" :simpleShow="true" />
                  </template>
                </template>
              </BasicTable>
            </template>
          </BasicTable>
        </CollapsePanel>
      </Collapse>
    </template>
    <div class="p-4 text-right">
      <Pagination
        v-if="projectList.length > 0"
        v-model:current="paging.page"
        :total="pagingTotal"
        v-model:pageSize="paging.pageSize"
        show-size-changer
      />
    </div>
    <!--    <RelateDrawer @register="registerRelateDrawer" />-->
    <RevisitDrawer @register="registerRevisitDrawer" />
  </PageWrapper>
</template>

<script setup lang="ts" name="/projectOverview/projectOverview">
import { ref, onMounted, reactive, watch } from 'vue'
import { Collapse, CollapsePanel, Pagination } from 'ant-design-vue'
import { CaretRightOutlined } from '@ant-design/icons-vue'
// import { CollapseContainer } from '/@/components/Container'
import { PageWrapper } from '/@/components/Page'
import { BasicTable, useTable, TableAction, TableImg } from '/@/components/Table'
import { useDrawer } from '/@/components/Drawer'
import type { ActionItem } from '/@/components/Table'
import { getProjectList, getProjectSalesOrder, getProjectSalesRequest } from '/@/api/projectOverview'
// import RelateDrawer from './components/RelateDrawer.vue'
import RevisitDrawer from '/@/views/revisitManage/revisitLog/components/RevisitDrawer.vue'
// import { mapWay } from '/@/views/revisitManage/revisitLog/datas/datas'
import { columns, childColumns, searchSchemas } from './datas/datas'
import { useGo } from '/@/hooks/web/usePage'
import { encryptByBase64 } from '/@/utils/cipher'
import { BasicForm, useForm } from '/@/components/Form'
import { usePermission } from '/@/hooks/web/usePermission'
import Decimal from 'decimal.js'

const paging = reactive<{ page: number; pageSize: number }>({ page: 1, pageSize: 10 })
const { hasPermission } = usePermission()
const go = useGo()
const pagingTotal = ref<number>(0)
// const [registerRelateDrawer, { openDrawer }] = useDrawer()
// const [registerRevisitDrawer, { openDrawer: openRevisitDrawer, setDrawerProps: setRevisitDrawerProps }] = useDrawer()
const [registerRevisitDrawer] = useDrawer()
const loading = ref<boolean>(false)
const [registerForm, { getFieldsValue }] = useForm({
  labelWidth: 100,
  schemas: searchSchemas,
  baseColProps: { span: 8 },
  labelCol: { span: 4 },
  actionColOptions: { span: 24 },
  colon: true,
  submitButtonOptions: {
    loading
  },
  fieldMapToTime: [
    ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
    ['deliver_at', ['deliver_at_start', 'deliver_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
  ],
  submitFunc: async () => {
    paging.page = 1
    await initData()
  },
  submitOnReset: true
})

const projectList = ref<any[]>([])
onMounted(() => {
  initData()
})

const [registerChildTable] = useTable({
  columns: childColumns,
  showIndexColumn: false,
  // pagination: false,
  canResize: false
})

function createActions(record: any): ActionItem[] {
  return [
    {
      label: '查看关联业务单据',
      onClick: handleViewRelate.bind(null, { record, type: 'erp' }),
      ifShow: hasPermission(244)
    },
    {
      label: '查看关联财务单据',
      onClick: handleViewRelate.bind(null, { record, type: 'finance' }),
      ifShow: hasPermission(245)
    }
  ]
}

// function handleCreateRevisit(id) {
//   console.log(id)
//   setRevisitDrawerProps({ title: '新建回访' })
//   openRevisitDrawer(true, { id, isUpdate: false })
// }
function handleViewRelate({ record, type }: any) {
  // console.log('handleViewRelate', record)
  // const encryption = new AesEncryption({ key: 'abc@123', iv: '11111@4433' })
  const id = encryptByBase64(record.id)
  go({
    path: '/projectOverview/overviewRelate',
    query: {
      type,
      id
    }
  })
  // openDrawer(true, record)
}

async function initData() {
  try {
    loading.value = true
    const formData = getFieldsValue()
    const { items, total } = await getProjectList({ ...paging, ...formData })
    pagingTotal.value = total
    projectList.value = items
  } catch (e) {
    throw new Error(JSON.stringify(e))
  } finally {
    loading.value = false
  }
}

watch([() => paging.page, () => paging.pageSize], (data, oldData) => {
  const [page, pageSize] = data
  const [oldPage, oldPageSize] = oldData
  if (page !== oldPage || pageSize !== oldPageSize) {
    initData()
  }
})

const totalreceivable = ref()
function hanleafterfetch(data) {
  // 重置总和，避免累加问题
  totalreceivable.value = 0

  data.forEach((item) => {
    // 只有未完成结算(is_audit === 0)的项目才计算未收货款
    if (item.is_audit === 0) {
      // 检查值是否为null或undefined
      const receivableLeft = item.receivable_left ?? 0
      const receivedActual = item.received_actual ?? 0

      const receivable = new Decimal(receivableLeft).minus(receivedActual).toNumber()
      totalreceivable.value += receivable
    }
  })
  return data
}
</script>

<style scoped lang="less">
:deep(.ant-descriptions-view) {
  border-width: 2px;
}
.footer {
  font-size: 15px;
  font-weight: bold;
  span:nth-of-type(2) {
    margin-left: 7%;
  }
  span:nth-of-type(3) {
    margin-left: 7%;
  }
}
</style>
