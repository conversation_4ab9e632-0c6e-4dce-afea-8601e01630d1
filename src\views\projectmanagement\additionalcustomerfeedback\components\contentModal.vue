<template>
  <BasicModal
    @register="registerModal"
    title="客户反馈内容"
    :showCancelBtn="false"
    @ok="
      () => {
        closeModal()
      }
    "
  >
    <div>{{ initcontent }}</div>
  </BasicModal>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
const initcontent = ref('')

const [registerModal, { closeModal }] = useModalInner((data) => {
  initcontent.value = data
})
</script>
