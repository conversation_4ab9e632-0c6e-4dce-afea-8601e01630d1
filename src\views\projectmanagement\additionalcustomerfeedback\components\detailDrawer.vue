<template>
  <BasicDrawer @register="registerDrawer">
    <Descriptions :column="2" bordered>
      <DescriptionsItem label="绑定项目">
        <div v-if="records?.project_name">
          {{ records?.project_name }}
        </div>
        <div v-else>
          <Button type="link" @click="handleproject">绑定项目</Button>
        </div>
      </DescriptionsItem>
      <DescriptionsItem label="客户名称">{{ records?.client_name }}</DescriptionsItem>
      <DescriptionsItem label="客户邮箱">
        <Tag v-for="item in records?.from" :key="item.full">
          {{ item.full }}
        </Tag>
      </DescriptionsItem>
      <DescriptionsItem label="接收人邮箱">
        <Tag v-for="item in records?.to" :key="item.full">
          {{ item.full }}
        </Tag>
      </DescriptionsItem>
      <DescriptionsItem label="邮件主题">{{ records?.subject }} </DescriptionsItem>
      <DescriptionsItem label="客户反馈内容">{{ records?.text_body }} </DescriptionsItem>
      <DescriptionsItem label="反馈时间">{{ records?.date }} </DescriptionsItem>
      <DescriptionsItem label="邮箱附件">
        <ul>
          <li v-for="(item, index) in records?.files" :key="item">
            <a :href="item" target="_blank" @click="handlePreview(item, $event)">{{ `附件${index + 1}` }}</a>
          </li>
        </ul>
      </DescriptionsItem>
      <DescriptionsItem label="是否删除">
        <Tag :color="records?.is_cancel == 0 ? 'green' : 'red'">{{ records?.is_cancel ? '是' : '否' }}</Tag>
      </DescriptionsItem>
    </Descriptions>
    <PreviewFile @register="registerModal" />
    <projectModal @register="registerprojectModal" @success="handleclose" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { Descriptions, DescriptionsItem, message, Tag, Button } from 'ant-design-vue'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { createImgPreview } from '/@/components/Preview'
import { useModal } from '/@/components/Modal'
import projectModal from '../components/projectModal.vue'

const [registerModal, { openModal }] = useModal()
const [registerprojectModal, { openModal: openprojectModal }] = useModal()
const emit = defineEmits(['success'])
const records = ref()
const [registerDrawer, { closeDrawer }] = useDrawerInner((data) => {
  records.value = data
})

function handleproject() {
  openprojectModal(true, records.value)
}

function handleclose() {
  closeDrawer()
  emit('success')
}
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return message.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  console.log(prefix)
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>
