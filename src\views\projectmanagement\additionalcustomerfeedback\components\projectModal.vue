<template>
  <BasicModal @register="register" title="绑定项目" :minHeight="320" @ok="handleOk">
    <BasicForm @register="RegisterForm" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { emailmessagebindProject, emailmessagesetCancel } from '/@/api/projectmanagement/customercomplaint'
import { ref } from 'vue'
import { schemas } from '../datas/modal.data'

const emit = defineEmits(['success'])
const keys = ref()
const types = ref()
const [register, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  types.value = data.type
  resetSchema(schemas(data.type))
  if (data.type == 'reject') {
    setFieldsValue({ id: data.record.id })
  } else {
    keys.value = data.record
  }
})
const [RegisterForm, { setFieldsValue, validate, resetSchema }] = useForm({
  baseColProps: { span: 21 },
  showActionButtonGroup: false
  // schemas
})

async function handleOk() {
  try {
    changeOkLoading(true)
    const formdata = await validate()
    console.log(formdata)
    types.value == 'reject'
      ? await emailmessagebindProject({ id: formdata.id, project_number: formdata.project_number })
      : await emailmessagesetCancel({
          ids: keys.value.map((item) => {
            return {
              id: item
            }
          }),
          cancel_remark: formdata.cancel_remark
        })
    closeModal()
    emit('success')
    changeOkLoading(false)
  } catch (e) {
    console.log(e)
  } finally {
    changeOkLoading(false)
  }
}
</script>
