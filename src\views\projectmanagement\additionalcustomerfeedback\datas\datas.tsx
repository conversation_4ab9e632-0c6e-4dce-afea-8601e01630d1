import { BasicColumn, FormSchema } from '/@/components/Table'
import { Button, Tag } from 'ant-design-vue'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { isNullOrUnDef } from '/@/utils/is'
import { h } from 'vue'

export function columns(openModal): BasicColumn[] {
  return [
    {
      title: '项目名称',
      dataIndex: 'project_name',
      width: 300,
      resizable: true
    },
    {
      title: '项目ID',
      dataIndex: 'project_number',
      width: 300,
      resizable: true
    },
    {
      title: '客户名称',
      dataIndex: 'client_name',
      width: 300
    },
    {
      title: '客户反馈内容',
      dataIndex: 'content',
      width: 300,
      resizable: true,
      customRender({ text }) {
        return (
          <div style={{ display: 'flex' }}>
            <span style={{ width: '90%', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>{text}</span>
            <div>
              <Button type="link" onClick={() => openModal(true, text)}>
                展开
              </Button>
            </div>
          </div>
        )
      }
    },
    {
      title: '反馈时间',
      dataIndex: 'created_at',
      width: 200,
      resizable: true
    }
  ]
}

export function emailcolumns(openModal): BasicColumn[] {
  return [
    {
      title: '客户邮箱',
      dataIndex: 'from',
      width: 400,
      resizable: true,
      customRender({ text }) {
        console.log(text)
        const names = text?.map((item) => item?.full)
        return useRender.renderTags(names)
      }
    },
    {
      title: '邮件主题',
      dataIndex: 'subject',
      width: 300,
      resizable: true
    },
    {
      title: '客户反馈内容',
      dataIndex: 'text_body',
      width: 300,
      resizable: true,
      customRender({ text }) {
        return (
          <div style={{ display: 'flex' }}>
            <span style={{ width: '90%', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>{text}</span>
            <div>
              <Button type="link" onClick={() => openModal(true, text)}>
                展开
              </Button>
            </div>
          </div>
        )
      }
    },
    {
      title: '邮箱附件',
      dataIndex: 'files',
      width: 200,
      resizable: true
    },
    {
      title: '绑定项目',
      dataIndex: 'project_name',
      width: 300,
      resizable: true
    },
    {
      title: '项目ID',
      dataIndex: 'project_number',
      width: 300,
      resizable: true
    },
    {
      title: '客户名称',
      dataIndex: 'client_name',
      width: 300,
      resizable: true
    },

    {
      title: '接收人邮箱',
      dataIndex: 'to',
      width: 400,
      resizable: true,
      customRender({ text }) {
        console.log(text)
        const names = text?.map((item) => item?.full)
        return useRender.renderTags(names)
      }
    },

    {
      title: '反馈时间',
      dataIndex: 'date',
      width: 200,
      resizable: true
    },

    {
      title: '是否删除',
      dataIndex: 'is_cancel',
      width: 200,
      resizable: true,
      customRender({ text }) {
        return isNullOrUnDef(text) ? '' : h(Tag, { color: text == 0 ? 'green' : 'red' }, text == 0 ? '否' : '是')
      }
    }
  ]
}

export const searchFormSchema: FormSchema[] = [
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'project_number',
    label: '项目ID',
    component: 'Input',
    colProps: { span: 8 }
  }
]

export const emailcolumnsFormSchema: FormSchema[] = [
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'project_number',
    label: '项目ID',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'email',
    label: '客户邮箱',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'is_cancel',
    label: '是否删除',
    defaultValue: 0,
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    },
    colProps: { span: 8 }
  }
]
