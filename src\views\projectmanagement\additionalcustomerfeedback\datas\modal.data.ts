import { getProjectList } from '/@/api/projectOverview'
import { FormSchema } from '/@/components/Form'

export function schemas(type: string): FormSchema[] {
  return [
    {
      field: 'id',
      label: 'id',
      component: 'Input',
      show: false
    },
    {
      field: 'project_number',
      label: '项目ID',
      component: 'PagingApiSelect',
      required: ['reject'].includes(type),
      componentProps: ({ formModel }) => {
        return {
          api: getProjectList,
          resultField: 'items',
          labelField: 'title',
          valueField: 'id',
          searchMode: true,
          pagingMode: true,
          searchParamField: 'project_number',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'id',
              label: 'id'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'id',
            allowClear: true,
            onChange(_, shall) {
              formModel.project_name = shall?.project_name
              formModel.cline_name = shall?.customer_name
            }
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      },
      show: ['reject'].includes(type),
      ifShow: ['reject'].includes(type)
    },
    {
      field: 'project_name',
      label: '项目名称',
      component: 'Input',
      dynamicDisabled: true,
      show: ['reject'].includes(type),
      ifShow: ['reject'].includes(type)
    },
    {
      field: 'cline_name',
      label: '客户名称',
      component: 'Input',
      dynamicDisabled: true,
      show: ['reject'].includes(type),
      ifShow: ['reject'].includes(type)
    },
    {
      field: 'cancel_remark',
      label: '删除备注',
      component: 'InputTextArea',
      componentProps: {
        autosize: {
          minRows: 15
        }
      },
      show: ['delete'].includes(type),
      ifShow: ['delete'].includes(type)
    }
  ]
}
