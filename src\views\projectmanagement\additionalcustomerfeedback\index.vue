<template>
  <div>
    <PageWrapper>
      <Tabs default-active-key="1" type="card">
        <TabPane key="1" tab="评分链接反馈">
          <BasicTable @register="registerTable" />
        </TabPane>
        <TabPane key="2" tab="邮箱反馈">
          <BasicTable @register="registeremailTable">
            <template #toolbar>
              <Button type="primary" @click="handleclear">批量删除邮件</Button>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <TableAction :actions="createActions(record)" />
              </template>
              <template v-if="column.dataIndex == 'files'">
                <div v-for="(newVal, index) in record.files" :key="index">
                  <a :href="newVal.url" target="_blank" @click="handlePreview(newVal.url)">{{ newVal.name }}</a></div
                >
              </template>
            </template>
          </BasicTable>
        </TabPane>
      </Tabs>
      <contentModal @register="registercontentModal" />
      <projectModal @register="registerprojectModal" @success="reload" />
      <PreviewFile @register="registerModal" />
      <detailDrawer @register="registerDetailDrawer" @success="reload" />
    </PageWrapper>
  </div>
</template>
<script setup lang="ts">
import { emailmessagegetList, ratingsupplementgetList } from '/@/api/projectmanagement/customercomplaint'
import { BasicTable, useTable, TableAction, ActionItem, EditRecordRow } from '/@/components/Table'
import { columns, emailcolumns, emailcolumnsFormSchema, searchFormSchema } from './datas/datas'
import { PageWrapper } from '/@/components/Page'
import { Tabs, TabPane, message, Button } from 'ant-design-vue'
import contentModal from './components/contentModal.vue'
import { useModal } from '/@/components/Modal'
import projectModal from './components/projectModal.vue'
import { createImgPreview } from '/@/components/Preview/index'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import detailDrawer from './components/detailDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { usePermission } from '/@/hooks/web/usePermission'

const { hasPermission } = usePermission()
const [registercontentModal, { openModal: opencontentModal }] = useModal()
const [registerprojectModal, { openModal: openprojectModal, setModalProps: setprojectModalProps }] = useModal()
const [registerModal, { openModal }] = useModal()
const [registerDetailDrawer, { openDrawer, setDrawerProps }] = useDrawer()

const [registerTable] = useTable({
  api: ratingsupplementgetList,
  columns: columns(opencontentModal),
  showTableSetting: true,
  useSearchForm: true,
  bordered: true,
  showIndexColumn: false,
  formConfig: {
    name: 'searchForm',
    labelWidth: 120,
    schemas: searchFormSchema,
    alwaysShowLines: 1
  }
})

const [registeremailTable, { reload, getSelectRowKeys }] = useTable({
  api: emailmessagegetList,
  columns: emailcolumns(opencontentModal),
  showTableSetting: true,
  useSearchForm: true,
  bordered: true,
  showIndexColumn: false,
  formConfig: {
    name: 'searchForm',
    labelWidth: 120,
    schemas: emailcolumnsFormSchema,
    alwaysShowLines: 1
  },
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action'
  },
  rowKey: 'id',
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: (record) => {
      if (record.project_number) {
        return { disabled: true }
      } else {
        return { disabled: false }
      }
    }
  }
})

function createActions(record: EditRecordRow): Recordable[] {
  let editButtonList: ActionItem[] = [
    {
      label: '绑定项目',
      onClick: handleproject.bind(null, record),
      ifShow: hasPermission([692])
    },
    {
      label: '详情',
      onClick: handledatail.bind(null, record)
      // ifShow: hasPermission([691])
    }
  ]
  return editButtonList
}

function handleproject(record) {
  openprojectModal(true, { record, type: 'reject' })
  setprojectModalProps({ title: '绑定项目' })
}
function handledatail(record) {
  openDrawer(true, record)
  setDrawerProps({ title: '详情', width: '90%' })
}

async function handleclear() {
  try {
    const keys = await getSelectRowKeys()
    if (keys.length == 0) return message.error('请选择要删除的邮件')
    openprojectModal(true, { record: keys, type: 'delete' })
    setprojectModalProps({ title: '删除邮件' })
  } catch (error) {
    console.log(error)
  }
}
// 预览
async function handlePreview(val: string) {
  if (!val) return message.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>
