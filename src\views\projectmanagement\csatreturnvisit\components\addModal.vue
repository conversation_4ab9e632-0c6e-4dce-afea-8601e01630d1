<template>
  <BasicModal @register="registerModal" width="50%" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { ref } from 'vue'
import { schemas } from '../datas/add.data'
import dayjs from 'dayjs'
import { projectvistsetProjectVist } from '/@/api/projectmanagement/csatreturnvisit'
import { PROJECT_TYPE, PROJECT_TYPE_MAP } from '../datas/datas'

const emit = defineEmits(['success'])
// 类型定义
interface ModalData {
  type: string
  record: any
}

// 常量定义
const DATE_FORMAT = 'YYYY-MM-DD HH:mm:ss'

// 状态定义
const types = ref<string>()

const fromdata = ref()
// 模态框注册
const [registerModal, { changeOkLoading, closeModal }] = useModalInner((data: ModalData) => {
  types.value = data.type
  fromdata.value = data.record
  if (data.record.mapOrder) {
    fromdata.value.first_count = [7].includes(PROJECT_TYPE_MAP[data.record.mapOrder]) ? undefined : PROJECT_TYPE_MAP[data.record.mapOrder]

    fromdata.value.vist_status =
      data.record.mapOrder === PROJECT_TYPE.FINISHED
        ? 1
        : [PROJECT_TYPE.ONE, PROJECT_TYPE.TWO, PROJECT_TYPE.THREE].includes(data.record.mapOrder)
        ? 0
        : [PROJECT_TYPE.SEVEN].includes(data.record.mapOrder)
        ? 7
        : undefined
    delete fromdata.value.mapOrder
  }
  console.log(fromdata.value)

  resetFields()
})

// 表单注册
const [registerForm, { validate, resetFields }] = useForm({
  baseColProps: { span: 21 },
  labelWidth: 190,
  showActionButtonGroup: false,
  schemas
})

// 处理时间范围
function processDateRange(formData: Record<string, any>) {
  if (formData.profit_at && Array.isArray(formData.profit_at)) {
    const [profit_at_start, profit_at_end] = formData.profit_at
    formData.profit_at_start = dayjs(profit_at_start).startOf('day').format(DATE_FORMAT)
    formData.profit_at_end = dayjs(profit_at_end).endOf('day').format(DATE_FORMAT)
    delete formData.profit_at
  }
  return formData
}

// 提交处理
async function handleSubmit() {
  try {
    changeOkLoading(true)
    const formData = await validate()
    console.log(formData)

    const processedData = processDateRange({ ...formData, ...fromdata.value })
    await projectvistsetProjectVist(processedData)
    closeModal()
    emit('success')
    changeOkLoading(false)
  } catch (error) {
    console.error('提交失败:', error)
    changeOkLoading(false)
  }
}
</script>

<style scoped>
.basic-modal {
  padding: 16px;
}
</style>
