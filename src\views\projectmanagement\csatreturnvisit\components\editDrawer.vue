<template>
  <BasicDrawer @register="registerDrawer" width="90%">
    <template #footer>
      <Button @click="handleCancel" v-if="['manageSetStatus', 'submit'].includes(types || '')">返回</Button>
      <Button :loading="isSubmitLoading" type="primary" @click="handleSubmit('bc')" v-if="['filse', 'edit'].includes(types || '')">
        保存
      </Button>
      <Popconfirm
        title="请确认回访记录无误，提交后不可再次修改！！！"
        placement="topRight"
        @confirm="handleSubmit('tj')"
        :disabled="types !== 'edit'"
      >
        <Button
          :loading="isSubmitLoading"
          :type="types === 'manageSetStatus' ? 'success' : types == 'submit' || ischeck == 0 ? 'error' : 'primary'"
          @click="handleButtonClick"
          v-if="['manageSetStatus', 'submit', 'edit'].includes(types || '')"
        >
          {{ types === 'manageSetStatus' ? '通过' : types == 'submit' || ischeck == 0 ? '结束回访' : '提交' }}
        </Button>
      </Popconfirm>

      <Button :loading="isSubmitLoading" type="error" @click="handleReject" v-if="['manageSetStatus'].includes(types || '')"> 驳回 </Button>
    </template>

    <div v-if="types !== 'detail'">
      <BasicForm @register="registerForm" @field-value-change="handleFieldValueChange">
        <template #files>
          <Upload
            v-model:file-list="filesList"
            action="/api/oss/putImg"
            list-type="picture-card"
            :custom-request="handleFileRequest"
            :multiple="true"
            :disabled="!['filse', 'edit'].includes(types || '')"
          >
            <div>
              <plus-outlined />
              <div style="margin-top: 8px">Upload</div>
            </div>
          </Upload>
        </template>
      </BasicForm>
    </div>

    <div v-else>
      <BasicTable @register="registerTable">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'files'">
            <div v-for="(newVal, index) in record.files" :key="index">
              <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">
                {{ `附件${index + 1}` }}
              </a>
            </div>
          </template>
        </template>
      </BasicTable>
    </div>

    <rejectModal @register="registerrejectModal" @success="handleRejectSuccess" />
    <PreviewFile @register="registerModal" />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { columns, schemas } from '../datas/edite.data'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { PlusOutlined } from '@ant-design/icons-vue'
import { message, Upload, UploadFile, Button, Popconfirm } from 'ant-design-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { ref, watch, nextTick } from 'vue'
import rejectModal from './rejectModal.vue'
import { useModal } from '/@/components/Modal'
import { BasicTable, useTable } from '/@/components/Table'
import { useMessage } from '/@/hooks/web/useMessage'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { createImgPreview } from '/@/components/Preview/index'
import { projectvistsetVistStatus } from '/@/api/projectmanagement/csatreturnvisit'

// 类型定义
interface DrawerData {
  type: string
  record: {
    project_number: string
    vist_content: string
    vist_files: string | string[]
    vist_is_check: number
    project_vist_log: Array<{
      is_check: number
      check_status_remark: string
    }>
    vist_check_status_remark: string
  }
}

interface FileContent {
  vist_content: string
  vist_files: string[]
  is_check: number
}

interface SubmitContent extends FileContent {
  check_status_remark: string
}

// 常量定义
const VISIT_STATUS = {
  SAVED: 2,
  SUBMITTED: 3,
  COMPLETED: 15
} as const

const ALLOWED_IMAGE_TYPES = ['png', 'jpg', 'jpge', 'gif']
const ALLOWED_DOC_TYPES = ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf']

// 状态定义
const filesList = ref<UploadFile[]>([])
const isSubmitLoading = ref(false)
const types = ref<string>()
const newids = ref<string>()
const disabledbloon = ref(false)
const ischeck = ref(0)
const conents = ref<FileContent>()
const sunmint = ref<SubmitContent>()
const emit = defineEmits(['success', 'register'])

// 模态框注册
const [registerrejectModal, { openModal, setModalProps }] = useModal()
const [registerModal, { openModal: openMODAL }] = useModal()

// 抽屉注册
const [registerDrawer, { closeDrawer }] = useDrawerInner(async (data: DrawerData) => {
  try {
    filesList.value = []
    isSubmitLoading.value = false
    disabledbloon.value = false
    types.value = data.type
    newids.value = data.record.project_number

    const vistFiles = Array.isArray(data.record.vist_files) ? data.record.vist_files : [data.record.vist_files]

    conents.value = {
      vist_content: data.record.vist_content,
      vist_files: vistFiles,
      is_check: data.record.vist_is_check
    }

    sunmint.value = {
      vist_content: data.record.vist_content,
      vist_files: vistFiles,
      check_status_remark: data.record.project_vist_log[0]?.check_status_remark,
      is_check: data.record.vist_is_check
    }

    if (types.value !== 'detail') {
      await resetFields()
      disabledbloon.value = !['filse', 'edit'].includes(data.type)
      await resetSchema(schemas(data.type))

      const imgsdata = data.record.vist_files
      filesList.value = (
        typeof imgsdata === 'string'
          ? [{ url: imgsdata, uid: Date.now().toString(), name: imgsdata }]
          : (imgsdata as string[])?.map((item: string, idx: number) => ({
              url: item,
              uid: idx.toString(),
              name: item
            }))
      ) as UploadFile[]

      setFieldsValue({
        ...data.record,
        is_check: data.record.vist_is_check,
        check_status_remark: data.record.vist_check_status_remark
      })
    }

    if (data.type === 'detail') {
      await nextTick()
      setTableData(data.record.project_vist_log)
    }
  } catch (error) {
    console.error('初始化抽屉失败:', error)
  }
})

// 表单注册
const [registerForm, { setFieldsValue, resetSchema, resetFields, validate }] = useForm({
  baseColProps: { span: 21, md: 10 },
  labelWidth: 190,
  showActionButtonGroup: false,
  disabled: disabledbloon
})

// 表格注册
const [registerTable, { setTableData }] = useTable({
  columns,
  showIndexColumn: false
})

// 监听文件列表变化
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ vist_files: val?.map((item) => item.url) ?? [] })
  }
)

// 文件上传处理
async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    isSubmitLoading.value = true
    const curFile = filesList.value.find((item) => item.uid === (file as UploadFile).uid)
    const result = await commonFileUpload(file, 'purchase', curFile)

    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      return
    }

    onSuccess!(result.path)
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })

    await setFieldsValue({
      vist_files: filesList.value.map((item) => item.url)
    })
  } catch (err: any) {
    if (err.code === 'ERR_NETWORK') {
      filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    } else {
      filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    }
    throw new Error(err)
  } finally {
    isSubmitLoading.value = false
  }
}

// 取消处理
function handleCancel() {
  filesList.value = []
  closeDrawer()
}

// 驳回处理
function handleReject() {
  if (!newids.value || !conents.value) return
  openModal(true, { id: newids.value, type: 'reject', conents: conents.value })
  setModalProps({ title: '驳回' })
}

// 驳回成功处理
function handleRejectSuccess() {
  closeDrawer()
  emit('success')
}

// 提交处理
async function handleSubmit(_type: string) {
  try {
    isSubmitLoading.value = true
    const formData = await validate()

    switch (types.value) {
      case 'filse':
        await projectvistsetVistStatus({ ...formData, vist_status: VISIT_STATUS.SAVED })
        break
      case 'edit':
        let vist_status: number
        if (_type === 'tj' && formData.is_check == 1) {
          vist_status = VISIT_STATUS.SUBMITTED // 3
        } else if (_type === 'bc') {
          vist_status = VISIT_STATUS.SAVED // 2
        } else if (_type === 'tj' && formData.is_check == 0) {
          vist_status = VISIT_STATUS.COMPLETED // 15
        } else {
          vist_status = VISIT_STATUS.SAVED // 2
        }
        await projectvistsetVistStatus({
          ...formData,
          vist_status: vist_status
        })
        break
      case 'manageSetStatus':
        if (!newids.value || !conents.value) return
        openModal(true, { id: newids.value, type: 'submit', conents: conents.value })
        setModalProps({ title: '通过' })
        return
      case 'submit':
        if (!formData.project_number || !sunmint.value) return
        await projectvistsetVistStatus({
          project_number: formData.project_number,
          ...sunmint.value,
          vist_status: VISIT_STATUS.COMPLETED
        })
        break
    }

    message.success('提交成功')
    closeDrawer()
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    isSubmitLoading.value = false
  }
}

// 预览处理
const { createMessage } = useMessage()
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')

  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]

  if (prefix && ALLOWED_IMAGE_TYPES.includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ALLOWED_DOC_TYPES.includes(prefix)) {
    openMODAL(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}

// 按钮点击处理
function handleButtonClick(e: Event) {
  if (types.value === 'edit') {
    e.stopPropagation()
  } else {
    handleSubmit('tj')
  }
}

// 字段值变化处理
function handleFieldValueChange(key: string, val: any) {
  console.log(key, val)
  if (key === 'is_check') {
    ischeck.value = val
  }
}
</script>

<style scoped>
.basic-drawer {
  padding: 16px;
}
</style>
