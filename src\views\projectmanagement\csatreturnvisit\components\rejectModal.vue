<template>
  <BasicModal @register="register" title="驳回" width="500px" @ok="handleOk">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { ref } from 'vue'
import { projectvistsetVistStatus } from '/@/api/projectmanagement/csatreturnvisit'

// 类型定义
interface ModalData {
  id: string
  type: string
  conents: Record<string, any>
}

// 常量定义
const VISIT_STATUS = {
  REJECTED: 5,
  FINISHED: 4
} as const

// 状态定义
const ids = ref<string>()
const types = ref<string>()
const conents = ref<Record<string, any>>()

// 事件定义
const emit = defineEmits(['success', 'register'])

// 模态框注册
const [register, { changeLoading, closeModal }] = useModalInner((data: ModalData) => {
  ids.value = data.id
  types.value = data.type
  conents.value = data.conents
  resetFields()
  setProps({
    schemas: [
      {
        field: 'check_status_remark',
        label: data.type === 'reject' ? '驳回原因' : '主管审批意见',
        required: true,
        component: 'InputTextArea'
      }
    ]
  })
})

// 表单注册
const [registerForm, { validate, resetFields, setProps }] = useForm({
  showActionButtonGroup: false,
  layout: 'vertical',
  baseColProps: { span: 21 }
})

// 提交处理
async function handleOk() {
  try {
    changeLoading(true)
    const formData = await validate()
    const params = {
      project_number: ids.value,
      check_status_remark: formData.check_status_remark,
      ...conents.value
    }

    if (types.value === 'reject') {
      await projectvistsetVistStatus({
        ...params,
        vist_status: VISIT_STATUS.REJECTED
      })
    } else {
      await projectvistsetVistStatus({
        ...params,
        vist_status: VISIT_STATUS.FINISHED
      })
    }

    closeModal()
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    changeLoading(false)
  }
}
</script>

<style scoped>
.basic-modal {
  padding: 16px;
}
</style>
