import { FormSchema } from '/@/components/Form'

// 常量定义
const DATE_FORMAT = 'YYYY-MM-DD HH:mm:ss'

// 表单配置
export const schemas: FormSchema[] = [
  {
    field: 'profit_at',
    label: '项目结束时间',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: DATE_FORMAT
    },
    rules: [
      {
        type: 'array',
        message: '项目结束时间选择不完整',
        trigger: 'change',
        validator(_rule, value) {
          // 如果不是数组，直接通过验证（允许为空）
          if (!Array.isArray(value)) {
            return Promise.resolve()
          }

          // 如果数组为空，直接通过验证（允许为空）
          if (value.length === 0) {
            return Promise.resolve()
          }

          // 如果数组中有值，检查是否都填写了
          const hasStartDate = value[0] !== null && value[0] !== undefined && value[0] !== ''
          const hasEndDate = value[1] !== null && value[1] !== undefined && value[1] !== ''

          // 如果只填写了其中一个，则报错
          if (hasStartDate && !hasEndDate) {
            return Promise.reject('请选择结束时间')
          }

          if (!hasStartDate && hasEndDate) {
            return Promise.reject('请选择开始时间')
          }

          // 如果都没填写或都填写了，则通过验证
          return Promise.resolve()
        }
      }
    ]
  },
  {
    field: 'proportion',
    label: '抽样比例(%)',
    required: true,
    component: 'InputNumber',
    componentProps: {
      min: 0,
      max: 100,
      precision: 0
    }
  }
]
