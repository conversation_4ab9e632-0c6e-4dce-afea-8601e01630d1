import { h, nextTick } from 'vue'
import { getStaffList } from '/@/api/erp/systemInfo'
import { getClientList } from '/@/api/financialDocuments/public'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Badge, Tag } from 'ant-design-vue'
import { useRender } from '/@/components/Table/src/hooks/useRender'

// 类型定义
interface StatusConfig {
  color: string
  text: string
}

interface StatusMap {
  [key: number]: StatusConfig
}

interface ProjectStatusOption {
  value: string
  label: string
}

// 常量定义
const VISIT_STATUS_MAP: StatusMap = {
  0: { color: '#2563EB', text: '暂缓回访' },
  1: { color: '#16A34A', text: '回访中' },
  2: { color: '#16A34A', text: '回访中' },
  3: { color: '#16A34A', text: '回访中' },
  4: { color: '#16A34A', text: '回访中' },
  5: { color: '#16A34A', text: '回访中' },
  6: { color: '#16A34A', text: '待回访' },
  7: { color: '#16A34A', text: '可回访' },
  15: { color: '#EA580C', text: '已结束' }
}

const APPROVAL_STATUS_MAP: StatusMap = {
  0: { color: '', text: '-' },
  1: { color: '#2563EB', text: '待审批' },
  2: { color: '#16A34A', text: '已审批' },
  3: { color: 'red', text: '驳回' }
}

export const PROJECT_TYPE = {
  ALL: 'all',
  ONE: 'one',
  TWO: 'two',
  THREE: 'threen',
  FINISHED: 'finished',
  SEVEN: 'seven'
} as const

export const PROJECT_TYPE_MAP = {
  [PROJECT_TYPE.ALL]: undefined,
  [PROJECT_TYPE.ONE]: 1,
  [PROJECT_TYPE.TWO]: 2,
  [PROJECT_TYPE.THREE]: 3,
  [PROJECT_TYPE.FINISHED]: 4,
  [PROJECT_TYPE.SEVEN]: 7
} as const

const PROJECT_STATUS_OPTIONS: ProjectStatusOption[] = [
  { value: PROJECT_TYPE.ALL, label: '全部' },
  { value: PROJECT_TYPE.ONE, label: '首评4星以下' },
  { value: PROJECT_TYPE.TWO, label: '首评4星-4.5星' },
  { value: PROJECT_TYPE.THREE, label: '首评5星' },
  { value: PROJECT_TYPE.FINISHED, label: '回访中' },
  { value: PROJECT_TYPE.SEVEN, label: '可回访' }
]

const FINAL_RATING_OPTIONS = [
  { label: '终评5星', value: 2 },
  { label: '终评5星以下', value: 1 }
]

const VISIT_STATUS_OPTIONS = [
  { label: '待回访', value: 6 },
  { label: '回访中', value: 1 },
  { label: '已结束', value: 15 },
  { label: '可回访', value: 7 },
  { label: '暂缓回访', value: 0 }
]

export const CHANNEL = {
  1: { text: 'WA手机版', color: '#191970' },
  2: { text: 'WA电脑版', color: '#1E90FF' },
  3: { text: '专员微信', color: '#3CB371' },
  4: { text: '专员邮箱', color: '#EEDD82' },
  5: { text: '专员电话', color: '#CD5C5C' },
  6: { text: '部门邮箱', color: '#FA8072' }
}

function isWithinWorkingHours(timestamp) {
  // 处理空值或无效输入
  if (!timestamp || typeof timestamp !== 'string') {
    return true // 可根据需求改为返回默认值或抛出错误
  }
  // 解析时间字符串
  const [_, timePart] = timestamp.split(' ')
  const [hours, minutes, seconds] = timePart.split(':').map(Number)

  // 验证时间有效性
  if (isNaN(hours) || isNaN(minutes) || isNaN(seconds)) {
    throw new Error('无效的时间格式，应为YYYY-MM-DD HH:MM:SS')
  }

  // 计算总分钟数（忽略秒）
  const totalMinutes = hours * 60 + minutes
  const startMinutes = 9 * 60 // 9:00
  const endMinutes = 17 * 60 // 17:00

  // 判断是否在工作时间范围内（包含9:00和17:00）
  return totalMinutes >= startMinutes && totalMinutes <= endMinutes
}

// 表格列配置
export const columns: BasicColumn[] = [
  {
    title: '项目ID',
    dataIndex: 'project_number',
    width: 150,
    resizable: true
  },
  {
    title: '项目经理',
    dataIndex: 'inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '项目名称',
    dataIndex: 'project_name',
    width: 150,
    resizable: true
  },
  {
    title: '客户联系方式',
    dataIndex: 'contact',
    width: 150,
    resizable: true,
    customRender({ record }) {
      return record.customer_manage.contact
    }
  },
  {
    title: '客户国家当前时间',
    dataIndex: 'country_time_now',
    width: 150,
    resizable: true,
    customRender({ record }) {
      return isNullOrUnDef(record.customer_manage.country_time_now)
        ? '-'
        : h(
            'div',
            { style: { color: !isWithinWorkingHours(record.customer_manage.country_time_now) ? 'red' : 'green' } },
            record.customer_manage.country_time_now
          )
    }
  },
  {
    title: '订单结束时间',
    dataIndex: 'est_finished_at',
    width: 150,
    resizable: true
  },

  {
    title: '回访状态',
    dataIndex: 'vist_status',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text)
        ? ''
        : h(
            Tag,
            {
              color: VISIT_STATUS_MAP[text]?.color
            },
            () => VISIT_STATUS_MAP[text]?.text
          )
    }
  },
  {
    title: '审批状态',
    dataIndex: 'vist_dept_status',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text)
        ? ''
        : h(
            Tag,
            {
              color: APPROVAL_STATUS_MAP[text]?.color
            },
            () => APPROVAL_STATUS_MAP[text]?.text
          )
    }
  },
  {
    title: '国家',
    dataIndex: 'country',
    width: 150,
    resizable: true
  },
  {
    title: '客户名称',
    dataIndex: 'customer_name',
    width: 150,
    resizable: true,
    customRender({ text, record }) {
      return isNullOrUnDef(text) ? '-' : h(Badge, { offset: [10, -8], count: record.project_count }, text)
    }
  },
  {
    title: '客户属性',
    dataIndex: 'customer_attributes_text',
    width: 150,
    resizable: true,
    customRender({ record }) {
      return record.customer_manage.customer_attributes_text
    }
  },
  {
    title: '交付经理',
    dataIndex: 'delivery_incharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '方案经理',
    dataIndex: 'program_incharges_name',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return text ? useRender.renderTags(text) : '-'
    }
  },

  {
    title: '客户首评',
    dataIndex: 'first_count',
    width: 150,
    resizable: true
  },
  {
    title: '客户终评',
    dataIndex: 'count',
    width: 150,
    resizable: true
  },
  {
    title: '首评时间',
    dataIndex: 'first_rating_at',
    width: 150,
    resizable: true
  },
  {
    title: '终评时间',
    dataIndex: 'last_rating_at',
    width: 150,
    resizable: true
  },
  {
    title: '回访渠道',
    dataIndex: 'channel',
    width: 150,
    resizable: true,
    customRender({ text }) {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: CHANNEL[text]?.color }, () => CHANNEL[text]?.text)
    }
  },
  {
    title: '客户反馈',
    dataIndex: 'vist_content',
    width: 150,
    resizable: true
  },
  {
    title: '回复内容',
    dataIndex: 'reply_content',
    width: 150,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 150,
    resizable: true
  }
]

// 搜索表单配置
export function searchFormSchema(tableAction?): FormSchema[] {
  return [
    {
      field: 'mapOrder',
      label: '',
      defaultValue: PROJECT_TYPE.ALL,
      component: 'RadioButtonGroup',
      componentProps: ({ formActionType }) => ({
        options: PROJECT_STATUS_OPTIONS,
        onChange: (value) => {
          console.log(PROJECT_TYPE.SEVEN, PROJECT_TYPE_MAP[value])

          tableAction?.setProps({
            searchInfo: {
              first_count: [7].includes(PROJECT_TYPE_MAP[value]) ? undefined : PROJECT_TYPE_MAP[value],
              vist_status:
                value === PROJECT_TYPE.FINISHED
                  ? 1
                  : [PROJECT_TYPE.ONE, PROJECT_TYPE.TWO, PROJECT_TYPE.THREE].includes(value)
                  ? undefined
                  : [PROJECT_TYPE.SEVEN].includes(value)
                  ? 7
                  : undefined
            }
          })

          nextTick(() => formActionType.submit())
        }
      }),
      colProps: {
        span: 21
      }
    },
    {
      field: 'project_number',
      label: '项目ID',
      component: 'Input',
      colProps: {
        span: 6
      }
    },
    {
      field: 'vist_status',
      label: '回访状态',
      component: 'Select',
      componentProps: {
        options: VISIT_STATUS_OPTIONS
      },
      colProps: {
        span: 6
      }
    },

    {
      field: 'contact',
      label: '客户联系方式',
      component: 'Input',
      colProps: {
        span: 6
      }
    },
    {
      field: 'est_finished',
      label: '订单结束时间',
      component: 'SingleRangeDate',
      componentProps: {
        valueFormat: 'YYYY-MM-DD'
      },
      colProps: { span: 6 }
    },
    {
      field: 'country',
      label: '国家',
      component: 'Input',
      colProps: {
        span: 6
      }
    },
    {
      label: '客户属性',
      field: 'customer_attributes',
      component: 'Select',
      componentProps: {
        options: [
          { label: '房地产开发商', value: 1 },
          { label: '建筑师', value: 2 },
          { label: '工程师', value: 3 },
          { label: '业主', value: 4 },
          { label: '业主中间商（贸易公司、翻译、货代等）', value: 5 },
          { label: 'B端客户(批发商和当地开店)', value: 6 }
        ]
      },
      colProps: {
        span: 6
      }
    },
    {
      field: 'client_id',
      label: '客户',
      component: 'PagingApiSelect',
      componentProps: {
        api: getClientList,
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        },
        resultField: 'items'
      },
      itemProps: {
        validateTrigger: 'blur'
      },
      colProps: { span: 6 }
    },

    {
      field: 'count',
      label: '终评',
      component: 'Select',
      componentProps: {
        options: FINAL_RATING_OPTIONS
      },
      colProps: {
        span: 6
      }
    },
    {
      field: 'inCharges',
      label: '项目经理',
      component: 'PagingApiSelect',
      componentProps: {
        api: getStaffList,
        resultField: 'items',
        immediate: true,
        lazyLoad: true,
        selectProps: {
          mode: 'multiple',
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      },
      colProps: { span: 6 }
    },
    {
      field: 'delivery_incharges',
      label: '交付经理',
      component: 'PagingApiSelect',
      componentProps: {
        api: getStaffList,
        resultField: 'items',
        immediate: true,
        lazyLoad: true,
        selectProps: {
          mode: 'multiple',
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      },
      colProps: { span: 6 }
    },
    {
      field: 'program_incharges',
      label: '方案经理',
      component: 'PagingApiSelect',
      componentProps: {
        api: getStaffList,
        resultField: 'items',
        immediate: true,
        lazyLoad: true,
        selectProps: {
          mode: 'multiple',
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      },
      colProps: { span: 6 }
    }
  ]
}

export const excelHeader = ['项目ID', '客户反馈', '回复内容', '备注', '回访渠道']
export const IMP_EXCEL_END = 20000
