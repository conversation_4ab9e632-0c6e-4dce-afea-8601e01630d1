import { h } from 'vue'
import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'
import { CHANNEL } from './datas'

// 类型定义
interface StatusConfig {
  color: string
  text: string
}

interface StatusMap {
  [key: number]: StatusConfig
}

// 常量定义
const OPERATION_TYPE_MAP: StatusMap = {
  1: { color: '', text: '开始回访' },
  2: { color: 'orange', text: '保存回访记录' },
  3: { color: 'green', text: '提交回访记录' },
  4: { color: 'red', text: '审批通过' },
  5: { color: 'pink', text: '驳回' },
  15: { color: 'blue', text: '已结束' }
}

const AUDIT_OPTIONS = [
  { label: '是', value: 1 },
  { label: '否', value: 0 }
]

// 表单配置
export function schemas(type: string): FormSchema[] {
  const isEditOrFile = ['edit', 'filse'].includes(type)
  const isSubmit = ['submit'].includes(type)

  return [
    {
      field: 'project_number',
      label: '回访ID',
      component: 'Input',
      show: false
    },
    {
      field: 'channel',
      label: '回访渠道',
      component: 'Select',
      componentProps: {
        options: [
          {
            label: 'WA手机版',
            value: 1
          },
          {
            label: 'WA电脑版',
            value: 2
          },
          {
            label: '专员微信',
            value: 3
          },
          {
            label: '专员邮箱',
            value: 4
          },
          {
            label: '专员电话',
            value: 5
          },
          {
            label: '部门邮箱',
            value: 6
          }
        ]
      },
      required: true
    },
    {
      field: 'vist_content',
      label: '客户反馈',
      component: 'InputTextArea'
    },
    {
      field: 'reply_content',
      label: '回复内容',
      component: 'InputTextArea'
    },
    {
      field: 'remark',
      label: '备注',
      component: 'InputTextArea'
    },

    {
      field: 'vist_files',
      label: '回访沟通附件',
      component: 'Upload',
      slot: 'files'
    },
    {
      field: 'is_check',
      label: '是否需要CSAT主官审批',
      component: 'RadioButtonGroup',
      required: true,
      show: isEditOrFile,
      ifShow: isEditOrFile,
      componentProps: {
        options: AUDIT_OPTIONS
      },
      defaultValue: 0
    },
    {
      label: '主管审批意见',
      field: 'check_status_remark',
      component: 'InputTextArea',
      show: isSubmit,
      ifShow: isSubmit
    }
  ]
}

// 表格列配置
export const columns: BasicColumn[] = [
  {
    title: '操作人',
    dataIndex: 'creator_name',
    width: 100,
    resizable: true
  },
  {
    title: '操作步骤',
    dataIndex: 'type',
    width: 100,
    resizable: true,
    customRender({ text }) {
      return isNullOrUnDef(text) ? '' : h(Tag, { color: OPERATION_TYPE_MAP[text]?.color }, () => OPERATION_TYPE_MAP[text]?.text)
    }
  },
  {
    title: '操作时间',
    dataIndex: 'created_at',
    width: 100,
    resizable: true
  },
  {
    title: '回访渠道',
    dataIndex: 'channel',
    width: 150,
    resizable: true,
    customRender({ text }) {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: CHANNEL[text]?.color }, () => CHANNEL[text]?.text)
    }
  },
  {
    title: '客户反馈',
    dataIndex: 'content',
    width: 150,
    resizable: true
  },
  {
    title: '回复内容',
    dataIndex: 'reply_content',
    width: 150,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 150,
    resizable: true
  },
  {
    title: '回访沟通附件',
    dataIndex: 'files',
    width: 100,
    resizable: true
  },
  {
    title: '主管审批意见',
    dataIndex: 'check_status_remark',
    width: 100,
    resizable: true
  }
]
