<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleAdd" :loading="isBtnLoading" v-if="hasPermission(685)"> 抽样回访 </Button>
        <Button type="primary" @click="handleMenuClick" :loading="isBtnLoading" v-if="hasPermission(686)">
          <upload-outlined /> 导入
        </Button>
        <Button type="primary" @click="handleExcel" :loading="isBtnLoading" v-if="hasPermission(686)">
          <cloud-download-outlined />导出
        </Button>
        <Button
          type="primary"
          @click="onExpExcelTemplate(excelHeader, false, '回访记录模板')"
          :loading="isBtnLoading"
          v-if="hasPermission(686)"
        >
          <download-outlined />模板
        </Button>
      </template>

      <template #form-resetBefore>
        <Button class="mr-2" type="primary" @click="handleSearch" :loading="isBtnLoading" v-if="hasPermission(684)"> 待审批 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <addModal @register="registereditModal" @success="reload" />
    <editDrawer @register="registereditDrawer" @success="reload" />
    <merchandiseDrawer @register="registerMerchandiseDrawer" @success="reload" />
    <ImpExcelModal @register="registerUploadModal" :dataCallBackFn="handleUploadData" ref="ImpExcelModalRef" />
  </div>
</template>

<script setup lang="ts">
import { BasicTable, useTable, TableAction, ActionItem, EditRecordRow } from '/@/components/Table'
import addModal from './components/addModal.vue'
import editDrawer from './components/editDrawer.vue'
import { Button, message } from 'ant-design-vue'
import { columns, excelHeader, IMP_EXCEL_END, PROJECT_TYPE, PROJECT_TYPE_MAP, searchFormSchema } from './datas/datas'
import { usePermission } from '/@/hooks/web/usePermission'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'
import { onMounted, ref } from 'vue'
import { useModal } from '/@/components/Modal'
import { CloudDownloadOutlined } from '@ant-design/icons-vue'
import { useDrawer } from '/@/components/Drawer'
import {
  projectvistgetListByCast,
  projectvistgetListByCastexcel,
  projectvistimplodeProjectVist,
  projectvistsetVistStatus
} from '/@/api/projectmanagement/csatreturnvisit'
import { UploadOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import { onExpExcelTemplate } from '/@/utils/exportTemplate'
import { ImpExcelModal } from '/@/components/Excel'

import merchandiseDrawer from './components/merchandiseDrawer.vue'
import { transformData2Import } from './datas/importModal'

//execl上传
const [registerUploadModal, { openModal: openAddModal }] = useModal()

// 类型定义
interface VisitRecord extends EditRecordRow {
  project_number: string
  vist_status: number
}

// 状态常量
const VISIT_STATUS = {
  PENDING: 0,
  IN_PROGRESS: 1,
  COMPLETED: 2,
  MANAGER_REVIEW: 3,
  FINISHED: 4,
  REJECTED: 5,
  CANCELED: 6,
  MANAGER: 7
} as const

// 权限常量
const PERMISSIONS = {
  SEARCH: 684,
  ADD: 685,
  EXPORT: 686,
  SET_STATUS: 687,
  UPLOAD: 688,
  EDIT: 689,
  REVIEW: 690,
  VIEW: 691
} as const

const isBtnLoading = ref(false)
const { hasPermission } = usePermission()

const [registereditModal, { openModal, setModalProps }] = useModal()
const [registereditDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registerMerchandiseDrawer, { openDrawer: openMerchandiseDrawer, setDrawerProps: setMerchandiseDrawerProps }] = useDrawer()
const [registerTable, { setProps, reload, setLoading, getForm }] = useTable({
  columns,
  showTableSetting: true,
  showIndexColumn: false,
  api: projectvistgetListByCast,
  useSearchForm: true,
  actionColumn: {
    width: 300,
    title: '操作',
    dataIndex: 'action'
  },
  beforeFetch: (params) => {
    return { ...params, mapOrder: undefined }
  },
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas: searchFormSchema(),
    fieldMapToTime: [['est_finished', ['est_finished_at_start', 'est_finished_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  }
})

onMounted(() => {
  setProps({
    formConfig: {
      ...NEW_STATUS_FORMCONFIG,
      schemas: searchFormSchema({ setProps }),
      labelWidth: 120,
      resetFunc: (): any => {
        setProps({
          api: projectvistgetListByCast,
          searchInfo: {}
        })
      },
      fieldMapToTime: [['est_finished', ['est_finished_at_start', 'est_finished_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
    }
  })
})

function createActions(record: VisitRecord): ActionItem[] {
  return [
    {
      label: '确认回访',
      onClick: () => handleSetStatus(record, 'SetStatus'),
      ifShow:
        hasPermission([PERMISSIONS.SET_STATUS]) &&
        ([VISIT_STATUS.PENDING, VISIT_STATUS.CANCELED, VISIT_STATUS.MANAGER].includes(record.vist_status) || !record.vist_status)
    },
    {
      label: '进行回访',
      onClick: () => handleSetStatus(record, 'filse'),
      ifShow: hasPermission([PERMISSIONS.UPLOAD]) && record.vist_status === VISIT_STATUS.IN_PROGRESS
    },
    {
      label: '编辑',
      onClick: () => handleSetStatus(record, 'edit'),
      ifShow:
        hasPermission([PERMISSIONS.EDIT]) && (record.vist_status === VISIT_STATUS.COMPLETED || record.vist_status === VISIT_STATUS.REJECTED)
    },
    {
      label: 'CSAT主管审批',
      onClick: () => handleSetStatus(record, 'manageSetStatus'),
      ifShow: hasPermission([PERMISSIONS.REVIEW]) && record.vist_status === VISIT_STATUS.MANAGER_REVIEW
    },
    {
      label: '结束回访',
      onClick: () => handleSetStatus(record, 'submit'),
      ifShow: hasPermission([PERMISSIONS.REVIEW]) && record.vist_status === VISIT_STATUS.FINISHED
    },
    {
      label: '查看商品信息',
      onClick: () => handleProductInfo(record)
    },
    {
      label: '详情',
      onClick: () => handleSetStatus(record, 'detail'),
      ifShow: hasPermission([PERMISSIONS.VIEW])
    }
  ]
}

function handleAdd() {
  const form = getForm().getFieldsValue()
  openModal(true, { type: 'add', record: form })
  setModalProps({ title: '开始抽样' })
}

async function handleSetStatus(record: VisitRecord, type: string) {
  if (type === 'SetStatus') {
    try {
      await projectvistsetVistStatus({
        project_number: record.project_number,
        vist_status: VISIT_STATUS.IN_PROGRESS
      })
      reload()
    } catch (error) {
      message.error('状态更新失败')
    }
  } else {
    openDrawer(true, { record, type })
    setDrawerProps({
      title: ['filse', 'edit'].includes(type) ? '回访记录' : type === 'detail' ? '回访详情' : 'CSAT主管审批',
      showFooter: true
    })
  }
}

async function handleExcel() {
  try {
    isBtnLoading.value = true
    setLoading(true)
    const params = getForm()?.getFieldsValue()
    // 检查是否存在 mapOrder 参数
    if (params.mapOrder) {
      // 根据 mapOrder 的值替换参数
      const first_count = [7].includes(PROJECT_TYPE_MAP[params.mapOrder]) ? undefined : PROJECT_TYPE_MAP[params.mapOrder]
      const vist_status =
        params.mapOrder === PROJECT_TYPE.FINISHED ? 1 : [PROJECT_TYPE.SEVEN].includes(params.mapOrder) ? 7 : params.vist_status

      // 更新参数
      params.first_count = first_count
      params.vist_status = vist_status
      delete params.mapOrder
    }
    const response = await projectvistgetListByCastexcel({
      is_excel: 1,
      ...params,
      pageSize: 10000
    })

    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    downloadLink.download = `回访记录-${Date.now()}.xlsx`
    downloadLink.click()
    URL.revokeObjectURL(downloadLink.href)

    message.success('导出成功')
  } catch (error) {
    message.error('导出失败')
    console.error('导出失败:', error)
  } finally {
    setLoading(false)
    isBtnLoading.value = false
  }
}

function handleSearch() {
  setLoading(true)
  isBtnLoading.value = true
  setProps({
    api: projectvistgetListByCast,
    searchInfo: {
      vist_status: 3
    }
  })
  reload()
  setTimeout(() => {
    setLoading(false)
    isBtnLoading.value = false
  }, 500)
}

function handleProductInfo(record) {
  openMerchandiseDrawer(true, { record })
  setMerchandiseDrawerProps({
    title: '商品信息',
    showFooter: false
  })
}

function handleMenuClick() {
  openAddModal(true, {
    sheetName: 'Sheet1',
    headerRow: 1,
    startCell: 'A2',
    endCell: `E${IMP_EXCEL_END}`
  })
}

const ImpExcelModalRef = ref<InstanceType<typeof ImpExcelModal>>()

async function handleUploadData(data) {
  ImpExcelModalRef.value?.changeLoading(true)
  const hide = message.loading('正在导入数据，请稍后...', 0)
  try {
    const newData = await transformData2Import(data)
    console.log(newData)
    await projectvistimplodeProjectVist({ project_lists: newData })
    hide()
    ImpExcelModalRef.value?.changeLoading(false)
    await reload()
    return Promise.resolve('OK')
  } catch (err) {
    console.log(err)
    hide()
    ImpExcelModalRef.value?.changeLoading(false)
    // 接口失败直接抛出错误，不再返回任何值
    throw err
  }
}
</script>
