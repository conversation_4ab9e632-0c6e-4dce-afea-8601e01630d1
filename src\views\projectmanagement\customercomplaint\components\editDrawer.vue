<template>
  <BasicDrawer @register="registerDrawer">
    <template #footer>
      <Button @click="handleCancel">返回</Button>
      <Button :loading="isSubmitLoading" type="success" @click="handleSubmit">{{
        ['SetStatus', 'propsSetStatus', 'add', 'edit'].includes(types) ? '提交' : '通过'
      }}</Button>
      <Button
        :loading="isSubmitLoading"
        type="danger"
        @click="handlereject"
        v-if="['mengSetStatus', 'manageSetStatus', 'propsSetStatus'].includes(types)"
        >{{ ['propsSetStatus'].includes(types) ? '客户不满意退回' : '驳回' }}</Button
      >
    </template>
    <BasicForm @register="registerForm">
      <template #filess>
        <Upload
          v-model:file-list="filesLists"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequests"
          :multiple="true"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
      <template #files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest"
          :multiple="true"
          :disabled="!['add', 'edit'].includes(types)"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
    </BasicForm>
    <BasicTable @register="registerTable" v-if="!['add', 'edit'].includes(types)">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'files'">
          <div v-for="(newVal, index) in record.files" :key="index">
            <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
          >
        </template>
      </template>
    </BasicTable>
    <PreviewFile @register="registerModal" />
    <rejectModal @register="handleReject" @success="handleRejectSuccess" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { schemas, columns } from '../datas/edit.data'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { ratingcomplaintupdate, ratingcomplaintsetStatus } from '/@/api/projectmanagement/customercomplaint'
import { ref } from 'vue'
import { message, Upload, UploadFile, Button } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { watch } from 'vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { BasicTable, useTable } from '/@/components/Table'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { useMessage } from '/@/hooks/web/useMessage'
import { useModal } from '/@/components/Modal'
import { createImgPreview } from '/@/components/Preview/index'
import rejectModal from './rejectModal.vue'

const emit = defineEmits(['success', 'register'])
const disabledbloon = ref()
const types = ref()
//附件
const filesList = ref<UploadFile[]>([])
const filesLists = ref<UploadFile[]>([])
const newstatus = ref()
const newids = ref()
const isSubmitLoading = ref(false)

const [handleReject, { openModal, setModalProps }] = useModal()

const [registerDrawer, { closeDrawer }] = useDrawerInner(async (data) => {
  resetFields()
  console.log(data)
  filesList.value = []
  disabledbloon.value = !['add', 'edit'].includes(data.type)
  types.value = data.type
  resetSchema(schemas(data.type))
  if (data.type !== 'add') {
    setFieldsValue({
      ...data.record,
      processor: data.record.customer_manage_complaint_item[data.record.customer_manage_complaint_item.length - 1]?.processor || undefined,
      inCharge_status_remark: data.record.customer_manage_complaint_item[data.record.customer_manage_complaint_item.length - 1]?.remark
    })
    newstatus.value = data.record.status
    newids.value = data.record.id
    const imgsdata = data.record?.files
    filesList.value = (
      typeof imgsdata === 'string'
        ? [{ url: imgsdata, uid: +new Date().toString(), name: imgsdata }]
        : (imgsdata as string[])?.map((item: string, idx: number) => ({ url: item, uid: idx.toString(), name: item }))
    ) as UploadFile[]
    setTableData(data.record?.customer_manage_complaint_item || [])
  } else {
    setFieldsValue({ id: undefined, processor: undefined })
  }
})
const [registerForm, { setFieldsValue, validate, resetFields, resetSchema }] = useForm({
  baseColProps: { span: 21 },
  labelWidth: 190,
  showActionButtonGroup: false,
  // schemas:schemas(),
  disabled: disabledbloon
})

const [registerTable, { setTableData }] = useTable({
  columns,
  title: '审核明细',
  showIndexColumn: false
})

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val?.map((item) => item.url) ?? [] })
  }
)
watch(
  () => filesLists.value,
  async (val) => {
    await setFieldsValue({ filess: val?.map((item) => item.url) ?? [] })
  }
)
//附件上传
async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    console.log(file)
    isSubmitLoading.value = true
    const curFile = filesList.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      isSubmitLoading.value = false
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({
      files: filesList.value.map((item) => item.url)
    })
    isSubmitLoading.value = false
  } catch (err) {
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    isSubmitLoading.value = false
    throw new Error(err)
  }
}
//附件上传
async function handleFileRequests({ file, onSuccess }: UploadRequestOption) {
  try {
    console.log(file)
    isSubmitLoading.value = true
    const curFile = filesLists.value.find((item) => item.uid === file.uid)
    const result = await commonFileUpload(file, 'purchase', curFile)
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesLists.value = filesLists.value!.filter((item) => item.url)
      isSubmitLoading.value = false
      return
    }
    filesLists.value = filesLists.value!.map((item) => {
      return item.url || (item.status === 'done' && item.response)
        ? {
            url: (item.url as string) || (item.response as string),
            uid: item.uid,
            name: item.name
          }
        : item
    })
    await setFieldsValue({
      filess: filesLists.value.map((item) => item.url)
    })
    isSubmitLoading.value = false
  } catch (err) {
    if (err.code === 'ERR_NETWORK') filesLists.value = filesLists.value!.filter((item) => item.status === 'done' || item.url)
    else filesLists.value = filesLists.value!.filter((item) => item.status !== 'error' || item.url)
    isSubmitLoading.value = false
    throw new Error(err)
  }
}

async function handleSubmit() {
  try {
    isSubmitLoading.value = true

    const formdata = await validate()
    console.log(formdata)
    switch (types.value) {
      case 'add':
        await ratingcomplaintupdate({ ...formdata })
        break
      case 'edit':
        await ratingcomplaintupdate({ ...formdata })
        break
      case 'SetStatus':
        const params = {
          id: formdata.id,
          is_check: formdata.is_check,
          type: 1,
          amount: formdata.amount,
          files: formdata.filess,
          status: 1,
          is_legal_risk: formdata.is_legal_risk,
          processor: formdata.processor,
          remark: formdata.inCharge_status_remark
        }
        await ratingcomplaintsetStatus(params)
        break
      case 'mengSetStatus':
        const params1 = {
          id: formdata.id,
          type: 1,
          status: 2,
          remark: formdata.meng_status_remark
        }
        await ratingcomplaintsetStatus(params1)
        break
      case 'propsSetStatus':
        const params2 = {
          id: formdata.id,
          type: 1,
          status: 4
        }
        await ratingcomplaintsetStatus(params2)
        break
      case 'manageSetStatus':
        const params3 = {
          id: formdata.id,
          type: 1,
          status: 3,
          remark: formdata.manager_status_remark
        }
        await ratingcomplaintsetStatus(params3)
        break
    }
    closeDrawer()
    emit('success')
    isSubmitLoading.value = false
  } catch (e) {
    console.log(e)
    isSubmitLoading.value = false
  }
}
async function handlereject() {
  openModal(true, { id: newids.value, status: newstatus.value, type: types.value })
  setModalProps({ title: types.value == 'propsSetStatus' ? '客户不满意退回' : '驳回' })
}
async function handleRejectSuccess() {
  closeDrawer()
  emit('success')
}

function handleCancel() {
  closeDrawer()
}
// 预览
const { createMessage } = useMessage()
const [registerModal, { openModal: openMODAL }] = useModal()
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openMODAL(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>
