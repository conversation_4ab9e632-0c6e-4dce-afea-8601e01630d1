<template>
  <BasicModal @register="register" title="驳回" width="500px" @ok="handleOk">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { ref } from 'vue'
import { ratingcomplaintsetStatus } from '/@/api/projectmanagement/customercomplaint'
const ids = ref()
const types = ref()
const statuss = ref()
const emit = defineEmits(['success', 'register'])
const [register, { changeLoading, closeModal }] = useModalInner((data) => {
  console.log(data)
  ids.value = data.id
  types.value = data.type
  statuss.value = data.status
  resetFields()
  setProps({
    schemas: [
      {
        field: 'remark',
        label: data.type == 'propsSetStatus' ? '客户不满意退回原因' : '驳回原因',
        required: true,
        component: 'InputTextArea'
      }
    ]
  })
})

const [registerForm, { validate, resetFields, setProps }] = useForm({
  showActionButtonGroup: false,
  layout: 'vertical',
  baseColProps: { span: 21 }
})

async function handleOk() {
  try {
    changeLoading(true)
    const formdata = await validate()
    const params3 = {
      id: ids.value,
      type: 2,
      status: types.value == 'propsSetStatus' ? 0 : statuss.value - 1,
      remark: formdata.remark
    }
    await ratingcomplaintsetStatus(params3)
    changeLoading(false)
    closeModal()
    emit('success')
  } catch (e) {
    console.log(e)
  } finally {
    changeLoading(false)
  }
}
</script>
