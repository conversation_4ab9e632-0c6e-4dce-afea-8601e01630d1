<template>
  <BasicDrawer @register="registerDrawer" @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #Files>
        <Upload
          v-model:file-list="filesList"
          action="/api/oss/putImg2Stocking"
          :custom-request="handleFileRequest"
          :multiple="true"
          :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
        >
          <a-button type="primary">
            <upload-outlined />
            Upload
          </a-button>
        </Upload>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { UploadFile, Upload, message } from 'ant-design-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { schemas } from '../datas/tailafter.datas'
import { UploadOutlined } from '@ant-design/icons-vue'
import { pointsgetList } from '/@/api/baseData/pointmanagement'
import { ratingcomplaintaddCommissionerFollowLog, ratingcomplaintaddFollowLog } from '/@/api/projectmanagement/customercomplaint'
import { useUserStore } from '/@/store/modules/user'
import { storeToRefs } from 'pinia'

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
console.log(userInfo)
//附件
const filesList = ref<UploadFile[]>([])
const emit = defineEmits(['success'])
const types = ref()
const [registerDrawer, { changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  console.log(data)

  resetFields()
  resetSchema(schemas)
  types.value = data.type
  filesList.value = []
  const { items } = await pointsgetList({ id: data.type == 'normal' ? 14 : 16 })
  await updateSchema([
    {
      field: 'matter_strid',
      required: items[0].is_strid == 1 ? true : false
    },
    {
      field: 'files',
      required: items[0].is_file == 1 ? true : false
    },
    {
      field: 'content',
      required: items[0].is_content == 1 ? true : false
    }
  ])
  setFieldsValue({
    ...data.record,
    points_manage_id: data.type == 'normal' ? 14 : 16,
    creator: userInfo.value!.userId,
    creator_name: userInfo.value!.realName
  })
})
const [registerForm, { setFieldsValue, validate, resetFields, resetSchema, updateSchema }] = useForm({
  baseColProps: { span: 12 },
  colon: true,
  showActionButtonGroup: false,
  labelWidth: 140
  // schemas
})

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val?.map((item) => item.url) ?? [] })
  }
)
//附件上传
async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  try {
    const result = await commonFileUpload(file, 'purchase')
    onSuccess!(result.path)
    if (!result.path) {
      message.error('上传失败')
      filesList.value = filesList.value!.filter((item) => item.url)
      changeOkLoading(false)
      return
    }
    filesList.value = filesList.value!.map((item) => {
      return {
        url: (item.url as string) || (item.response as string),
        uid: item.uid,
        name: item.name
      }
    })
    await setFieldsValue({
      files: filesList.value.map((item) => item.url)
    })
  } catch (err: any) {
    if (err.code === 'ERR_NETWORK') filesList.value = filesList.value!.filter((item) => item.status === 'done' || item.url)
    else filesList.value = filesList.value!.filter((item) => item.status !== 'error' || item.url)
    changeOkLoading(false)
    throw new Error(err)
  }
}

async function handleSubmit() {
  try {
    changeOkLoading(true)
    const formData = await validate()
    delete formData.creator_name
    types.value == 'normal' ? await ratingcomplaintaddFollowLog(formData) : await ratingcomplaintaddCommissionerFollowLog(formData)
    changeOkLoading(false)
    closeDrawer()
    emit('success')
  } catch (e) {
    console.log(e)
    changeOkLoading(false)
  }
}
</script>
