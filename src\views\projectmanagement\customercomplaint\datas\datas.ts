import { Tag } from 'ant-design-vue'
import { getStaffList } from '/@/api/baseData/staff'
import { getClientList } from '/@/api/financialDocuments/public'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { h, nextTick } from 'vue'
import { isNullOrUnDef } from '/@/utils/is'
import { useRender } from '/@/components/Table/src/hooks/useRender'

export const status = {
  0: { color: '', text: '待输出方案' },
  1: { color: 'orange', text: '待审核' }, //项目经理
  2: { color: 'pink', text: '待审核' }, //项目主管
  3: { color: 'skyblue', text: '待确认' }, //总经理
  // 4: { color: 'blue', text: '已确认执行' },
  15: { color: 'green', text: '已通过' }
}

export const statusdetail = {
  0: { color: '', text: '待输出方案' },
  1: { color: 'orange', text: '项目经理' },
  2: { color: 'pink', text: '部门主管' },
  3: { color: 'skyblue', text: '总经理' },
  // 4: { color: 'blue', text: '已确认执行' },
  15: { color: 'green', text: '已结束' }
}

export const timeout = {
  0: { color: '', text: '未开始' },
  1: { color: 'orange', text: '进行中' },
  2: { color: 'red', text: '已超时' },
  15: { color: 'green', text: '已结束' }
}

export const columns: BasicColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'project_name',
    width: 250,
    resizable: true
  },
  {
    title: '项目号',
    dataIndex: 'project_number',
    width: 100,
    resizable: true
  },
  {
    title: '客诉单号',
    dataIndex: 'strid',
    width: 250,
    resizable: true
  },
  {
    title: '申请时间',
    dataIndex: 'created_at',
    width: 200,
    resizable: true
  },
  {
    title: '客诉状态',
    dataIndex: 'status',
    width: 150,
    resizable: true,
    customRender: ({ text, record }) => {
      return !isNullOrUnDef(text)
        ? h(
            Tag,
            { color: status[text].color },
            (text == 1 && record.is_check_dept == 0) || (text == 2 && record.is_check == 0) ? '待确认' : status[text].text
          )
        : ''
    }
  },
  {
    title: '项目经理响应状态',
    dataIndex: 'status_timeout_incharge',
    width: 170,
    resizable: true,
    customRender: ({ text }) => {
      return !isNullOrUnDef(text) ? h(Tag, { color: timeout[text].color }, timeout[text].text) : ''
    }
  },
  {
    title: '是否作废',
    dataIndex: 'is_cancel',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: text == 0 ? 'green' : 'red' }, text == 0 ? '否' : '是')
    }
  },

  {
    title: '方案预估产生费用金额',
    dataIndex: 'amount',
    width: 150,
    resizable: true
  },
  {
    title: '问题描述',
    dataIndex: 'complaint_content',
    width: 400,
    resizable: true
  },
  {
    title: '客户需求',
    dataIndex: 'appeal_content',
    width: 400,
    resizable: true
  },
  {
    title: '客户体验中心是否需要跟进',
    dataIndex: 'is_need_follow_commissioner',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: text == 0 ? 'green' : 'red' }, text == 0 ? '否' : '是')
    }
  },
  {
    title: '客户体验中心是否已跟进',
    dataIndex: 'is_follow_commissioner',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: text == 0 ? 'green' : 'red' }, text == 0 ? '否' : '是')
    }
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 150,
    resizable: true
  },
  {
    title: '抄送人',
    dataIndex: 'processor_name',
    width: 200,
    resizable: true,
    customRender: ({ text }) => {
      return text ? useRender.renderTags(text) : '-'
    }
  },
  {
    title: '项目经理',
    dataIndex: 'inCharge_name',
    width: 150,
    resizable: true
  },

  {
    title: '交付经理',
    dataIndex: 'delivery_incharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '客户名称',
    dataIndex: 'client_name',
    width: 250,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 300
  }
]

export const ALL = 'all'
export const ONE = 'one'
// export const TWO = 'two'
export const THREEN = 'threen'
export const FINMISHED = 'oa'

export const mapTypeMenu = {
  [ALL]: null,
  [ONE]: 1,
  // [TWO]: 2,
  [THREEN]: 3,
  [FINMISHED]: 4
}
export const projectstatus = [
  { value: ALL, label: '全部' },
  // { value: STARTED, label: '交付未开始' },
  { value: ONE, label: '进行中' },
  // { value: TWO, label: '已超时未结束' },
  { value: FINMISHED, label: '已结束' }
]

export function searchFormSchema(tableAction?): FormSchema[] {
  return [
    {
      field: 'mapOrder',
      label: '',
      defaultValue: ALL,
      component: 'RadioButtonGroup',
      componentProps: ({ formActionType }) => ({
        options: projectstatus,
        onChange: (value) => {
          tableAction?.setProps({
            searchInfo: {
              project_status: mapTypeMenu[value]
            }
          })

          nextTick(() => formActionType.submit())
        }
      }),
      colProps: {
        span: 21
      }
    },
    {
      field: 'project_number',
      label: '项目号',
      component: 'Input',
      colProps: {
        span: 6
      }
    },
    {
      field: 'strid',
      label: '客诉单号',
      component: 'Input',
      colProps: {
        span: 6
      }
    },
    {
      field: 'status',
      label: '客诉审核状态',
      component: 'Select',
      componentProps: {
        options: Object.keys(status).map((key) => {
          let label = status[key].text
          if (key === '1') {
            label = '部门主管待审核'
          } else if (key === '2') {
            label = '总经理待审核'
          }
          return {
            label,
            value: key
          }
        })
      },
      colProps: {
        span: 6
      }
    },
    {
      field: 'client_id',
      label: '客户',
      component: 'ApiSelect',
      componentProps: ({}) => ({
        api: getClientList,
        selectProps: {
          fieldNames: { value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          labelInValue: true
        },
        resultField: 'items'
      }),
      itemProps: {
        validateTrigger: 'blur'
      },
      colProps: { span: 6 }
    },
    {
      field: 'inCharge',
      label: '项目经理',
      component: 'PagingApiSelect',
      componentProps: {
        api: getStaffList,
        resultField: 'items',
        immediate: true,
        lazyLoad: true,
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      },
      colProps: { span: 6 }
    },
    {
      field: 'delivery_incharge',
      label: '交付经理',
      component: 'PagingApiSelect',
      componentProps: {
        api: getStaffList,
        resultField: 'items',
        immediate: true,
        lazyLoad: true,
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      },
      colProps: { span: 6 }
    },
    {
      field: 'creator',
      label: '创建人',
      component: 'PagingApiSelect',
      componentProps: {
        api: getStaffList,
        resultField: 'items',
        immediate: true,
        lazyLoad: true,
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      },
      colProps: { span: 6 }
    },
    {
      field: 'is_cancel',
      label: '是否作废',
      component: 'Select',
      defaultValue: 0,
      componentProps: {
        options: [
          {
            label: '是',
            value: 1
          },
          {
            label: '否',
            value: 0
          }
        ]
      },
      colProps: {
        span: 6
      }
    },
    {
      field: 'created_at',
      label: '创建日期',
      component: 'SingleRangeDate',
      componentProps: {
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        startPickerProps: {
          showTime: true
        },
        endPickerProps: {
          showTime: true
        }
      },
      colProps: { span: 6 }
    },
    // {
    //   field: 'remark',
    //   label: '备注',
    //   component: 'Input',
    //   colProps: {
    //     span: 6
    //   }
    // },
    // {
    //   field: 'complaint_content',
    //   label: '问题描述',
    //   component: 'Input',
    //   colProps: {
    //     span: 6
    //   }
    // },
    // {
    //   field: 'appeal_content',
    //   label: '客户需求',
    //   component: 'Input',
    //   colProps: {
    //     span: 6
    //   }
    // },
    {
      field: 'is_follow_commissioner',
      label: '专员是否已跟进',
      component: 'Select',
      componentProps: {
        options: [
          {
            label: '是',
            value: 1
          },
          {
            label: '否',
            value: 0
          }
        ]
      },
      colProps: {
        span: 6
      }
    },
    {
      field: 'is_need_follow_commissioner',
      label: '专员是否需要跟进',
      component: 'Select',
      componentProps: {
        options: [
          {
            label: '是',
            value: 1
          },
          {
            label: '否',
            value: 0
          }
        ]
      },
      colProps: {
        span: 6
      }
    }
  ]
}
export const childercolumns: BasicColumn[] = [
  {
    title: '项目ID',
    dataIndex: 'project_number',
    width: 150,
    resizable: true
  },
  {
    title: '项目名称',
    dataIndex: 'project_name',
    width: 150,
    resizable: true
  },
  {
    title: '负责人',
    dataIndex: 'creator_name',
    width: 150,
    resizable: true
  },
  {
    title: '抄送人',
    dataIndex: 'cc_recipient_name',
    width: 150,
    resizable: true
  },
  {
    title: '工作指标',
    dataIndex: 'target',
    width: 150,
    resizable: true
  },
  {
    title: '得分权重',
    dataIndex: 'points',
    width: 150,
    resizable: true
  },
  {
    title: '交付标准描述',
    dataIndex: 'desc',
    width: 150,
    resizable: true
  },
  {
    title: '是否作废',
    dataIndex: 'is_cancel',
    width: 150,
    customRender: ({ value }) => {
      return !isNullOrUnDef(value) ? h(Tag, { color: value == 1 ? 'red' : 'green' }, () => (value == 1 ? '是' : '否')) : ''
    },
    resizable: true
  },
  {
    title: '作废人',
    dataIndex: 'cancel_inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '事项单号',
    dataIndex: 'matter_strid',
    width: 150,
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 150,
    resizable: true
  },
  {
    title: '作废时间',
    dataIndex: 'cancel_at',
    width: 150,
    resizable: true
  },
  {
    title: '回访描述',
    dataIndex: 'content',
    width: 350,
    resizable: true
  },
  {
    title: '作废备注',
    dataIndex: 'cancel_remark',
    width: 150,
    resizable: true
  },
  {
    title: '附件',
    dataIndex: 'files',
    width: 150,
    resizable: true
  }
]
