import { h } from 'vue'
import { statusdetail } from './datas'
import { getProjectList } from '/@/api/projectOverview'
import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'
import { getCreatorList } from '/@/api/financialDocuments/public'

export const schemas = (type: string): FormSchema[] => [
  {
    field: 'id',
    label: 'id',
    defaultValue: null,
    component: 'Input',
    show: false,
    colProps: {
      span: 7
    }
  },
  {
    field: 'project_number',
    label: '项目号',
    component: 'PagingApiSelect',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        api: getProjectList,
        resultField: 'items',
        labelField: 'title',
        valueField: 'id',
        searchMode: true,
        pagingMode: true,
        searchParamField: 'id',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'id',
            label: 'id'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'id',
          allowClear: true,
          onChange(_, shall) {
            formModel.project_name = shall?.title
            formModel.client_name = shall?.customer_manage.name
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: !['add', 'edit', 'detail'].includes(type) ? 7 : 21
    }
  },
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input',
    required: true,
    dynamicDisabled: true,
    colProps: {
      span: !['add', 'edit', 'detail'].includes(type) ? 7 : 21
    }
  },
  {
    field: 'client_name',
    label: '客户名称',
    component: 'Input',
    required: true,
    dynamicDisabled: true,
    colProps: {
      span: !['add', 'edit', 'detail'].includes(type) ? 7 : 21
    }
  },
  {
    field: 'complaint_content',
    label: '问题描述',
    component: 'InputTextArea',
    componentProps: {
      rows: 4
    },
    required: true,
    colProps: {
      span: !['add', 'edit', 'detail'].includes(type) ? 7 : 21
    }
  },
  {
    field: 'appeal_content',
    label: '客户诉求',
    component: 'InputTextArea',
    componentProps: {
      rows: 4
    },
    required: true,
    colProps: {
      span: !['add', 'edit', 'detail'].includes(type) ? 7 : 21
    }
  },
  {
    field: 'files',
    label: '客诉相关资料附件',
    component: 'InputTextArea',
    componentProps: {
      rows: 4
    },
    slot: 'files',
    colProps: {
      span: !['add', 'edit', 'detail'].includes(type) ? 7 : 21
    }
  },
  {
    field: 'inCharge_status_remark',
    label: '项目经理处理意见与方案',
    component: 'InputTextArea',
    required: ['SetStatus'].includes(type),
    dynamicDisabled: type !== 'SetStatus',
    show: ['SetStatus'].includes(type),
    ifShow: ['SetStatus'].includes(type),
    colProps: {
      span: 7
    }
  },
  {
    field: 'processor',
    label: '抄送人',
    required: ['SetStatus'].includes(type),
    component: 'PagingApiSelect',
    componentProps: {
      api: getCreatorList,
      searchMode: true,
      pagingMode: true,
      resultField: 'items',
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        mode: 'multiple',
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 7
    },
    dynamicDisabled: type !== 'SetStatus',
    show: ['SetStatus'].includes(type),
    ifShow: ['SetStatus'].includes(type)
  },
  {
    field: 'meng_status_remark',
    label: '部门主管处理意见与方案',
    component: 'InputTextArea',
    required: ['mengSetStatus'].includes(type),
    dynamicDisabled: ['manageSetStatus'].includes(type),
    show: ['mengSetStatus'].includes(type),
    ifShow: ['mengSetStatus'].includes(type),
    colProps: {
      span: 7
    }
  },
  {
    field: 'amount',
    label: '方案预估产生费用金额',
    required: type === 'SetStatus',
    component: 'InputNumber',
    show: type === 'SetStatus',
    ifShow: type === 'SetStatus',
    componentProps: {
      precision: 2,
      min: 0
    },
    dynamicDisabled: type !== 'SetStatus',
    colProps: {
      span: 7
    }
  },
  {
    field: 'is_legal_risk',
    label: '是否存在法律风险',
    component: 'Select',
    defaultValue: 0,
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    },
    required: ['SetStatus'].includes(type),
    dynamicDisabled: type !== 'SetStatus',
    show: ['SetStatus', 'mengSetStatus', 'manageSetStatus'].includes(type),
    ifShow: ['SetStatus', 'mengSetStatus', 'manageSetStatus'].includes(type),
    colProps: {
      span: 7
    }
  },
  {
    field: 'filess',
    itemHelpMessage: '上传客户沟通记录与解决方案截图或附件',
    label: '上传客户沟通记录与解决方案',
    component: 'RadioButtonGroup',
    show: type === 'SetStatus',
    ifShow: type === 'SetStatus',
    slot: 'filess',
    colProps: {
      span: 7
    }
  },
  {
    field: 'manager_status_remark',
    label: '总经理审核意见',
    component: 'InputTextArea',
    required: ['manageSetStatus'].includes(type),
    dynamicDisabled: type !== 'manageSetStatus',
    show: ['manageSetStatus'].includes(type),
    ifShow: ['manageSetStatus'].includes(type),
    colProps: {
      span: 7
    }
  }
]

export const columns: BasicColumn[] = [
  {
    title: '审核人',
    dataIndex: 'creator_name',
    width: 100
  },
  // {
  //   title: '上一状态',
  //   dataIndex: 'last_status',
  //   width: 100,
  //   customRender: ({ text }) => {
  //     return !isNullOrUnDef(text) ? h(Tag, { color: status[text].color }, status[text].text) : ''
  //   }
  // },
  {
    title: '节点',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => {
      return !isNullOrUnDef(text) ? h(Tag, { color: statusdetail[text].color }, statusdetail[text].text) : ''
    }
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 100,
    customRender: ({ text }) => {
      return text === 1 ? '审核通过' : '驳回'
    }
  },
  {
    title: '备注与说明',
    dataIndex: 'remark',
    width: 100
  },
  // {
  //   title: '方案预估产生费用金额',
  //   dataIndex: 'amount',
  //   width: 100
  // },
  // {
  //   title: '是否存在法律风险',
  //   dataIndex: 'is_legal_risk',
  //   width: 100,
  //   customRender: ({ text }) => {
  //     return text == 1 ? '是' : '否'
  //   }
  // },
  {
    title: '沟通或方案附件',
    dataIndex: 'files',
    width: 100
  },
  {
    title: '审核时间',
    dataIndex: 'created_at',
    width: 100
  }
]
