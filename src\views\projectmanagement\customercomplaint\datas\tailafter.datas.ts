import { getCreatorList } from '/@/api/financialDocuments/public'
import { FormSchema } from '/@/components/Form'

export const schemas: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false
  },
  {
    field: 'project_number',
    label: '项目ID',
    required: true,
    component: 'Input',
    dynamicDisabled: true
  },
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input',
    dynamicDisabled: true
  },
  {
    field: 'client_name',
    label: '客户名称',
    component: 'Input',
    dynamicDisabled: true
  },
  {
    field: 'client_id',
    label: '客户',
    component: 'Input',
    dynamicDisabled: true,
    show: false
  },
  {
    field: 'creator',
    label: 'creator',
    component: 'Input',
    dynamicDisabled: true,
    show: false,
    ifShow: true
  },
  {
    field: 'creator_name',
    label: '负责人',
    component: 'PagingApiSelect',
    dynamicDisabled: true,
    required: true,
    componentProps: ({ formModel }) => {
      return {
        api: getCreatorList,
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        returnParamsField: 'id',
        selectProps: {
          fieldNames: { value: 'name', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          onChange(_, shall) {
            if (shall) {
              return true
            }
            formModel.creator = shall.id
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'cc_recipient',
    label: '抄送人',
    component: 'PagingApiSelect',
    required: true,
    componentProps: {
      api: getCreatorList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'points_manage_id',
    label: '工作指标',
    component: 'Select',
    required: true,
    dynamicDisabled: true,
    componentProps: {
      options: [
        {
          value: 14,
          label: '客诉跟踪'
        },
        {
          value: 16,
          label: '客诉超时跟踪'
        }
      ]
    }
  },
  {
    field: 'matter_strid',
    label: '事项单号',
    component: 'Input',
    required: true
  },
  {
    field: 'content',
    label: '回访描述',
    component: 'InputTextArea',
    required: true
  },
  {
    field: 'files',
    label: '附件',
    required: true,
    component: 'Upload',
    slot: 'Files'
  }
]
