<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleAdd" v-if="hasPermission(650)">新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
      </template>
      <template #expandedRowRender="{ record: fatherrecord }">
        <BasicTable
          :columns="childercolumns"
          :dataSource="fatherrecord.project_follow_log"
          :pagination="false"
          :canResize="false"
          :showIndexColumn="false"
        >
          <template #bodyCell="{ column: childcolumn, record: childrecord }">
            <template v-if="childcolumn.key === 'files'">
              <div v-for="(newVal, index) in childrecord.files" :key="index">
                <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
              >
            </template>
          </template>
        </BasicTable>
      </template>
    </BasicTable>
    <editDrawer @register="registereditDrawer" @success="reload" />
    <tailafterDrawer @register="registeretailDrawer" @success="reload" />
    <PreviewFile @register="registerModal" />
  </div>
</template>
<script setup lang="tsx">
import { columns, searchFormSchema, childercolumns } from './datas/datas'
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import { Button, message, Textarea } from 'ant-design-vue'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { useMessage } from '/@/hooks/web/useMessage'
import { usePermission } from '/@/hooks/web/usePermission'
import { ratingcomplaintgetList, ratingcomplaintsetCancel } from '/@/api/projectmanagement/customercomplaint'
import tailafterDrawer from './components/tailafterDrawer.vue'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { createImgPreview } from '/@/components/Preview/index'
import { useModal } from '/@/components/Modal'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'
import { onMounted, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useUserStore } from '/@/store/modules/user'
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
console.log(userInfo)

const [registereditDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const [registeretailDrawer, { openDrawer: openetailDrawer, setDrawerProps: setetailDrawerProps }] = useDrawer()
const { createMessage } = useMessage()
const { hasPermission } = usePermission()
const [registerModal, { openModal }] = useModal()
const clearRemark = ref()

const [registerTable, { reload, setProps }] = useTable({
  showTableSetting: true,
  showIndexColumn: false,
  api: ratingcomplaintgetList,
  useSearchForm: true,
  columns,
  actionColumn: {
    width: 250,
    title: '操作',
    dataIndex: 'action'
  },
  beforeFetch: (params) => {
    return { ...params, mapOrder: undefined }
  },
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas: searchFormSchema(),
    resetFunc: async () => {
      setProps({
        beforeFetch: () => {
          return { page: 1, pageSize: 10 }
        }
      })

      setTimeout(() => {
        setProps({
          beforeFetch: (params) => {
            return { ...params, mapOrder: undefined }
          }
        })
      }, 0)
    },
    fieldMapToTime: [['check_at', ['check_at_start', 'check_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  }
})

onMounted(() => {
  setProps({
    formConfig: {
      ...NEW_STATUS_FORMCONFIG,
      schemas: searchFormSchema({ setProps }),
      labelWidth: 120,
      //自动展开行
      resetFunc: async () => {
        setProps({
          beforeFetch: () => {
            return { page: 1, pageSize: 10 }
          }
        })

        setTimeout(() => {
          setProps({
            beforeFetch: (params) => {
              return { ...params, mapOrder: undefined }
            }
          })
        }, 0)
      },
      fieldMapToTime: [['check_at', ['check_at_start', 'check_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
    }
  })
})

function handleAdd() {
  openDrawer(true, { type: 'add' })
  setDrawerProps({ title: '新增', showFooter: true, width: '50%' })
}

function createActions(record: EditRecordRow): Recordable[] {
  let editButtonList: ActionItem[] = [
    {
      label: '客诉处理意见与方案',
      onClick: handleSetStatus.bind(null, record, 'SetStatus', '客诉处理意见与方案'),
      ifShow:
        hasPermission([651]) &&
        record.status === 0 &&
        record.is_cancel !== 1 &&
        (record.inCharge === userInfo.value!.userId || ['developer', 'super_admin'].includes(userInfo.value!.roleValue))
    },
    {
      label: '部门主管审核',
      onClick: handleSetStatus.bind(null, record, 'mengSetStatus', '部门主管审核'),
      ifShow:
        (hasPermission([651]) && record.status == 1 && record.is_check_dept == 1 && record.is_cancel !== 1) ||
        (record.status == 1 && record.is_legal_risk == 1 && record.is_cancel !== 1)
    },
    {
      label: '总经理审核',
      onClick: handleSetStatus.bind(null, record, 'manageSetStatus', '总经理审核'),
      ifShow:
        (hasPermission([652]) && record.status == 2 && record.is_check == 1 && record.is_cancel !== 1) ||
        (record.status == 2 && record.is_legal_risk == 1 && record.is_cancel !== 1)
    },
    {
      label: '确认执行',
      onClick: handleSetStatus.bind(null, record, 'propsSetStatus', '确认执行'),
      ifShow:
        hasPermission([651]) &&
        record.is_cancel !== 1 &&
        (record.delivery_incharge === userInfo.value!.userId || ['developer', 'super_admin'].includes(userInfo.value!.roleValue)) &&
        (record.status === 3 ||
          (record.status === 2 && record.is_check === 0) ||
          (record.status === 1 && record.is_check_dept === 0 && record.is_check === 0 && record.is_legal_risk === 0))
    }
  ]

  return editButtonList
}

function createDropDownActions(record: EditRecordRow): ActionItem[] {
  return [
    {
      label: '详情',
      onClick: handledetail.bind(null, record)
    },
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      disabled: record.status !== 0 || record.is_cancel === 1,
      ifShow: hasPermission([653])
    },
    {
      label: '客诉跟踪',
      onClick: handletailafter.bind(null, record, 'normal'),
      disabled: record.is_cancel === 1,
      ifShow: hasPermission(654)
    },
    {
      label: '客诉超时跟踪',
      onClick: handletailafter.bind(null, record, 'timeout'),
      disabled: record.is_need_follow_commissioner !== 1 || record.is_cancel === 1,
      ifShow: hasPermission(671)
    },
    {
      label: '客诉作废',
      popConfirm: {
        title: (
          <div class="w-100">
            <div>作废原因</div>
            <Textarea autoSize={false} v-model:value={clearRemark.value} placeholder="请输入作废备注" allow-clear />
          </div>
        ),
        placement: 'left',
        confirm: handleSetclearreamrk.bind(null, record),
        disabled: record.status !== 0 || record.is_cancel === 1
      },
      ifShow: hasPermission(671)
    }
  ]
}

async function handleSetStatus(record, type, title) {
  openDrawer(true, { type, record })
  setDrawerProps({ title, showFooter: true, width: '90%' })
}
// async function handlemanageSetStatus(record) {
//   openDrawer(true, { type: 'manageSetStatus', record })
//   setDrawerProps({ title: '总经理审核', showFooter: true, width: '50%' })
// }

function handleEdit(record) {
  openDrawer(true, { type: 'edit', record })
  setDrawerProps({ title: '编辑', showFooter: true, width: '50%' })
}

function handledetail(record) {
  openDrawer(true, { record, type: 'detail' })
  setDrawerProps({ title: '详情', showFooter: false, width: '90%' })
}

function handletailafter(record, type) {
  openetailDrawer(true, { record, type })
  setetailDrawerProps({ title: type == 'normal' ? '客诉跟踪' : '客诉超时跟踪', showFooter: true, width: '50%' })
}

async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}

async function handleSetclearreamrk(record) {
  try {
    if (!clearRemark.value) return message.error('请先填写作废备注')
    const { msg } = await ratingcomplaintsetCancel({ id: record.id, cancel_remark: clearRemark.value })

    if (msg === 'success') {
      message.success('作废成功')
      reload()
    }
  } catch (err) {
    throw new Error(err)
  }
}
</script>
