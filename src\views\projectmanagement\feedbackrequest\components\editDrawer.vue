<template>
  <BasicDrawer @register="registerDrawer" @ok="handleSubmit">
    <template #footer>
      <Button @click="handleCancel" :loading="buttonloading" v-if="['add', 'SetStatus', 'manageSetStatus', 'edit'].includes(types || '')"
        >返回</Button
      >
      <Button
        type="success"
        :loading="buttonloading"
        @click="handleSubmit"
        v-if="['add', 'SetStatus', 'manageSetStatus', 'edit'].includes(types || '')"
        >确认</Button
      >
      <Button type="danger" :loading="buttonloading" @click="handlereject" v-if="['SetStatus', 'manageSetStatus'].includes(types || '')"
        >驳回</Button
      >
    </template>
    <BasicForm @register="registerForm">
      <template #files1>
        <Upload
          v-model:file-list="filesLists.files1.value"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest1"
          :multiple="true"
          :disabled="disabledbloon"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
      <template #files2>
        <Upload
          v-model:file-list="filesLists.files2.value"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest2"
          :multiple="true"
          :disabled="disabledbloon"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
      <template #files3>
        <Upload
          v-model:file-list="filesLists.files3.value"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest3"
          :multiple="true"
          :disabled="disabledbloon"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
      <template #files4>
        <Upload
          v-model:file-list="filesLists.files4.value"
          action="/api/oss/putImg"
          list-type="picture-card"
          :custom-request="handleFileRequest4"
          :multiple="true"
          :disabled="disabledbloon"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
    </BasicForm>
    <rejectModal @register="registerrejectModal" @success="handleRejectSuccess" />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { schemas } from '../datas/edit.data'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { UploadFile, Upload, message, Button } from 'ant-design-vue'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { PlusOutlined } from '@ant-design/icons-vue'
import { ratingfeedbacksetManageStatus, ratingfeedbacksetStatus, ratingfeedbackupdate } from '/@/api/projectmanagement/feedbackrequest'
import rejectModal from './rejectModal.vue'
import { useModal } from '/@/components/Modal'

const emit = defineEmits(['success', 'register'])
const [registerrejectModal, { openModal, setModalProps }] = useModal()

const buttonloading = ref(false)
// 统一管理文件列表
const filesLists = {
  files1: ref<UploadFile[]>([]),
  files2: ref<UploadFile[]>([]),
  files3: ref<UploadFile[]>([]),
  files4: ref<UploadFile[]>([])
}

const disabledbloon = ref(false)
const types = ref('')
const records = ref('')

// 统一的文件处理函数
const handleFileRequest = async (fileKey: keyof typeof filesLists, options: UploadRequestOption) => {
  const { file, onSuccess } = options
  const fileList = filesLists[fileKey]

  try {
    buttonloading.value = true
    const result = await commonFileUpload(file, 'purchase')

    if (!result?.path) {
      message.error('上传失败')
      fileList.value = fileList.value.filter((item) => item.url)
      return
    }

    onSuccess?.(result.path)

    fileList.value = fileList.value.map((item) => ({
      url: item.url || item.response,
      uid: item.uid,
      name: item.name
    }))

    await setFieldsValue({
      [fileKey]: fileList.value.map((item) => item.url)
    })

    if (fileList.value.every((item) => item.url)) {
      buttonloading.value = false
    }
  } catch (error: any) {
    console.error('File upload error:', error)
    buttonloading.value = false

    if (error?.code === 'ERR_NETWORK') {
      fileList.value = fileList.value.filter((item) => item.status === 'done' || item.url)
    } else {
      fileList.value = fileList.value.filter((item) => item.status !== 'error' || item.url)
    }

    throw error
  }
}

// 创建处理函数
const createFileHandler = (fileKey: keyof typeof filesLists) => {
  return (options: UploadRequestOption) => handleFileRequest(fileKey, options)
}

const handleFileRequest1 = createFileHandler('files1')
const handleFileRequest2 = createFileHandler('files2')
const handleFileRequest3 = createFileHandler('files3')
const handleFileRequest4 = createFileHandler('files4')

const [registerDrawer, { closeDrawer }] = useDrawerInner(async (data) => {
  const { type, record = {} } = data
  disabledbloon.value = !['add', 'edit'].includes(type)
  types.value = type
  records.value = record

  resetSchema(schemas(type))
  resetFields()

  // 重置所有文件列表
  Object.values(filesLists).forEach((list) => (list.value = []))

  if (type !== 'add') {
    setFieldsValue(record)
    // 设置文件列表
    Object.entries(filesLists).forEach(([key, list]) => {
      list.value =
        record[key]?.map((file: string) => ({
          name: file,
          url: file,
          uid: Math.random() * 100000
        })) ?? []
    })
  } else {
    setFieldsValue({ id: undefined })
  }
})

const [registerForm, { setFieldsValue, validate, resetFields, resetSchema }] = useForm({
  baseColProps: { span: 21 },
  labelWidth: 150,
  showActionButtonGroup: false,
  disabled: disabledbloon
})

// 统一的文件监听
Object.entries(filesLists).forEach(([key, list]) => {
  watch(
    () => list.value,
    async (val) => {
      await setFieldsValue({ [key]: val?.map((item) => item.url) ?? [] })
    }
  )
})

async function handleSubmit() {
  try {
    buttonloading.value = true
    const formdata = await validate()

    const actions = {
      add: () => ratingfeedbackupdate({ ...formdata }),
      edit: () => ratingfeedbackupdate({ ...formdata }),
      SetStatus: () =>
        ratingfeedbacksetStatus({
          id: formdata.id,
          is_check: formdata.is_check,
          count: formdata.count,
          status_remark: formdata.status_remark
        }),
      manageSetStatus: () =>
        ratingfeedbacksetManageStatus({
          id: formdata.id,
          count: formdata.counts,
          manager_status_remark: formdata.manager_status_remark
        })
    }

    await actions[types.value]?.()

    closeDrawer()
    emit('success')
  } catch (error) {
    console.error('Submit error:', error)
    // message.error('提交失败')
  } finally {
    buttonloading.value = false
  }
}

function handlereject() {
  openModal(true, {
    records: records.value,
    type: records.value.status == 0 ? 1 : records.value.manager_status == 0 && records.value.is_check == 1 ? 2 : undefined
  })
  setModalProps({
    title:
      records.value.status == 0
        ? '客户体验中心驳回'
        : records.value.manager_status == 0 && records.value.is_check == 1
        ? '总经理驳回'
        : undefined
  })
}

async function handleRejectSuccess() {
  closeDrawer()
  emit('success')
}

function handleCancel() {
  filesLists.files1.value = []
  filesLists.files2.value = []
  filesLists.files3.value = []
  filesLists.files4.value = []
  closeDrawer()
}
</script>
