import { h } from 'vue'
import { getStaffList } from '/@/api/baseData/staff'
import { getClientList } from '/@/api/financialDocuments/public'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'
const mstatus = {
  0: { color: '', text: '待审核' },
  1: { color: 'green', text: '已审核' },
  2: { color: 'red', text: '驳回' }
}
const status = {
  0: { color: '', text: '待审核' },
  1: { color: 'green', text: '已审核' },
  2: { color: 'red', text: '驳回' }
}

export const columns: BasicColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'project_name',
    width: 250,
    resizable: true
  },
  {
    title: '项目号',
    dataIndex: 'project_number',
    width: 100,
    resizable: true
  },
  {
    title: '单号',
    dataIndex: 'strid',
    width: 250,
    resizable: true
  },
  {
    title: '申请说明',
    dataIndex: 'content',
    width: 400,
    resizable: true
  },
  {
    title: '申请时间',
    dataIndex: 'created_at',
    width: 200,
    resizable: true
  },
  {
    title: '客户体验中心是否已审核',
    dataIndex: 'status',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: status[text].color }, () => status[text].text)
    }
  },
  {
    title: '是否需要总经理审核',
    dataIndex: 'is_check',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return !isNullOrUnDef(text) ? h(Tag, { color: text == 1 ? 'green' : 'red' }, text == 1 ? '是' : '否') : ''
    }
  },
  {
    title: '总经理审核状态',
    dataIndex: 'manager_status',
    width: 150,
    resizable: true,
    customRender: ({ text, record }) => {
      return !isNullOrUnDef(text) ? (record.is_check == 1 ? h(Tag, { color: mstatus[text].color }, mstatus[text].text) : '-') : ''
    }
  },
  {
    title: '审核人',
    dataIndex: 'status_inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '审核时间',
    dataIndex: 'status_at',
    width: 200,
    resizable: true
  },
  {
    title: '审核备注',
    dataIndex: 'status_remark',
    width: 150,
    resizable: true
  },
  {
    title: '总经理审核备注',
    dataIndex: 'manager_status_remark',
    width: 150,
    resizable: true
  },
  {
    title: '分数',
    dataIndex: 'count',
    width: 100,
    resizable: true
  },
  {
    title: '申请人',
    dataIndex: 'creator_name',
    width: 150,
    resizable: true
  },
  {
    title: '操作人',
    dataIndex: 'status_inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '项目经理',
    dataIndex: 'inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '交付经理',
    dataIndex: 'delivery_incharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '客户名称',
    dataIndex: 'client_name',
    width: 250,
    resizable: true
  },
  {
    title: '第一步附件',
    dataIndex: 'files1',
    width: 250,
    resizable: true
  },
  {
    title: '第二步附件',
    dataIndex: 'files2',
    width: 150,
    resizable: true
  },
  {
    title: '第三步附件',
    dataIndex: 'files3',
    width: 150,
    resizable: true
  },
  {
    title: '第四步附件',
    dataIndex: 'files4',
    width: 150,
    resizable: true
  }
]

export const searchFormSchema: FormSchema[] = [
  {
    field: 'project_number',
    label: '项目号',
    component: 'Input',
    colProps: {
      span: 6
    }
  },
  {
    field: 'strid',
    label: '单号',
    component: 'Input',
    colProps: {
      span: 6
    }
  },
  {
    field: 'client_id',
    label: '客户',
    component: 'ApiSelect',
    componentProps: ({}) => ({
      api: getClientList,
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        labelInValue: true
      },
      resultField: 'items'
    }),
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: { span: 6 }
  },
  {
    field: 'status_inCharge',
    label: '审核人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: { span: 6 }
  },
  {
    field: 'created_at',
    label: '创建日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      startPickerProps: {
        showTime: true
      },
      endPickerProps: {
        showTime: true
      }
    },
    colProps: { span: 6 }
  },
  {
    field: 'status_remark',
    label: '审核备注',
    component: 'Input',
    colProps: {
      span: 6
    }
  },
  {
    field: 'content',
    label: '申请说明',
    component: 'Input',
    colProps: {
      span: 6
    }
  }
]
