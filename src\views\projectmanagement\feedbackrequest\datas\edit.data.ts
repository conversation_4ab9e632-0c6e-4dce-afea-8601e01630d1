import { getProjectList } from '/@/api/projectOverview'
import { FormSchema } from '/@/components/Form'

export const schemas = (type: string): FormSchema[] => [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false
  },
  {
    field: 'project_number',
    label: '项目号',
    component: 'PagingApiSelect',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        api: getProjectList,
        resultField: 'items',
        labelField: 'title',
        valueField: 'id',
        searchMode: true,
        pagingMode: true,
        searchParamField: 'id',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'id',
            label: 'id'
          },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'id',
          allowClear: true,
          onChange(_, shall) {
            formModel.project_name = shall?.title
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'content',
    label: '申请说明',
    component: 'InputTextArea',
    required: true
  },
  {
    field: 'files1',
    label: '第一步附件',
    component: 'Upload',
    required: true,
    slot: 'files1'
  },
  {
    field: 'files2',
    label: '第二步附件',
    component: 'Upload',
    required: true,
    slot: 'files2'
  },
  {
    field: 'files3',
    label: '第三步附件',
    component: 'Upload',
    required: true,
    slot: 'files3'
  },
  {
    field: 'files4',
    label: '第四步附件',
    component: 'Upload',
    required: true,
    slot: 'files4'
  },

  {
    field: 'count',
    label: '申述内容是否属实',
    component: 'RadioButtonGroup',
    componentProps: ({ formModel }) => {
      return {
        options: ['manageSetStatus', 'detail'].includes(type)
          ? [
              {
                label: formModel.count === 1 ? '否' : '是',
                value: formModel.count
              }
            ]
          : [
              {
                label: '是',
                value: 4
              },
              {
                label: '否',
                value: 1
              }
            ]
      }
    },
    required: ['SetStatus'].includes(type),
    dynamicDisabled: !(type === 'SetStatus'),
    show: ['manageSetStatus', 'SetStatus', 'detail'].includes(type),
    ifShow: ['manageSetStatus', 'SetStatus', 'detail'].includes(type)
  },
  {
    field: 'status_remark',
    label: '审核备注',
    component: 'InputTextArea',
    required: ['SetStatus'].includes(type),
    dynamicDisabled: type !== 'SetStatus',
    show: ['manageSetStatus', 'SetStatus', 'detail'].includes(type),
    ifShow: ['manageSetStatus', 'SetStatus', 'detail'].includes(type)
  },
  {
    field: 'is_check',
    label: '是否需要总经理审核',
    required: type === 'SetStatus',
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    },
    dynamicDisabled: !(type === 'SetStatus'),
    show: ['SetStatus', 'detail'].includes(type),
    ifShow: ['SetStatus', 'detail'].includes(type)
  },
  {
    field: 'counts',
    label: '总经理最终评分',
    component: 'RadioButtonGroup',
    dynamicDisabled: !(type === 'manageSetStatus'),
    required: ['manageSetStatus'].includes(type),
    show: ['manageSetStatus', 'detail'].includes(type),
    ifShow: ['manageSetStatus', 'detail'].includes(type),
    componentProps: {
      options: [
        {
          label: '1星',
          value: 1
        },
        {
          label: '4星',
          value: 4
        }
      ]
    }
  },
  {
    field: 'manager_status_remark',
    label: '总经理审核备注',
    component: 'InputTextArea',
    required: ['manageSetStatus'].includes(type),
    dynamicDisabled: type !== 'manageSetStatus',
    show: ['manageSetStatus', 'detail'].includes(type),
    ifShow: ['manageSetStatus', 'detail'].includes(type)
  }
]
