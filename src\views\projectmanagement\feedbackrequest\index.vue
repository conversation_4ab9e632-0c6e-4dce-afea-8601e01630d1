<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleAdd" v-if="hasPermission(632)">新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'files1'">
          <div v-for="(newVal, index) in record.files1" :key="index">
            <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
          >
        </template>
        <template v-if="column.dataIndex == 'files2'">
          <div v-for="(newVal, index) in record.files2" :key="index">
            <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
          >
        </template>
        <template v-if="column.dataIndex == 'files3'">
          <div v-for="(newVal, index) in record.files4" :key="index">
            <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
          >
        </template>
        <template v-if="column.dataIndex == 'files4'">
          <div v-for="(newVal, index) in record.files4" :key="index">
            <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
          >
        </template>
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
      </template>
    </BasicTable>
    <PreviewFile @register="registerModal" />
    <editDrawer @register="registereditDrawer" @success="reload" />
  </div>
</template>
<script setup lang="tsx">
import { columns, searchFormSchema } from './datas/datas'
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import { Button } from 'ant-design-vue'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { ratingfeedbackgetList } from '/@/api/projectmanagement/feedbackrequest'
import { useMessage } from '/@/hooks/web/useMessage'
import { createImgPreview } from '/@/components/Preview/index'
import { useModal } from '/@/components/Modal'
import { usePermission } from '/@/hooks/web/usePermission'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'

const [registereditDrawer, { openDrawer, setDrawerProps }] = useDrawer()
const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()
const { hasPermission } = usePermission()

const [registerTable, { reload }] = useTable({
  showTableSetting: true,
  showIndexColumn: false,
  api: ratingfeedbackgetList,
  useSearchForm: true,
  columns,
  actionColumn: {
    width: 250,
    title: '操作',
    dataIndex: 'action'
  },
  formConfig: {
    schemas: searchFormSchema,
    fieldMapToTime: [['check_at', ['check_at_start', 'check_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  }
})

function handleAdd() {
  openDrawer(true, { type: 'add' })
  setDrawerProps({ title: '新增', showFooter: true, width: '50%' })
}

function createActions(record: EditRecordRow): Recordable[] {
  let editButtonList: ActionItem[] = [
    {
      label: '审核',
      onClick: handleSetStatus.bind(null, record, 'SetStatus'),
      ifShow: hasPermission([633]),
      disabled: record.status !== 0
    },
    {
      label: '总经理审核',
      onClick: handleSetStatus.bind(null, record, 'manageSetStatus'),
      ifShow: hasPermission([668]),
      disabled: !(record.manager_status == 0 && record.is_check == 1)
    }
  ]

  return editButtonList
}

function createDropDownActions(record: EditRecordRow): ActionItem[] {
  return [
    {
      label: '详情',
      onClick: handledetail.bind(null, record)
    },
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      disabled: !([0, 2].includes(record.status) || (record.status === 1 && record.manager_status === 2)),
      ifShow: hasPermission([653])
    }
  ]
}

async function handleSetStatus(record, type) {
  // await ratingfeedbacksetStatus({
  openDrawer(true, { type, record })
  setDrawerProps({ title: type === 'manageSetStatus' ? '总经理审核' : '审核', showFooter: true, width: '50%' })
}

function handledetail(record) {
  openDrawer(true, { type: 'detail', record })
  setDrawerProps({ title: '详情', showFooter: false, width: '50%' })
}
function handleEdit(record) {
  openDrawer(true, { type: 'edit', record })
  setDrawerProps({ title: '编辑', showFooter: true, width: '50%' })
}

// 预览
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>
