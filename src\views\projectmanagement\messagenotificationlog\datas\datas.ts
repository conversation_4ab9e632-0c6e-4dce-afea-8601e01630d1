import { h } from 'vue'
import { getAccountList } from '/@/api/commonUtils'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { Tag } from 'ant-design-vue'

const type = [
  { label: '满意度通知  ', value: 1 },
  { label: '流水通知', value: 2 },
  { label: '销售单通知', value: 3 },
  { label: '订单流程通知', value: 4 },
  { label: '付款单', value: 5 },
  { label: '收款单', value: 6 },
  { label: '装箱单', value: 7 },
  { label: '项目', value: 8 },
  { label: '退货单', value: 9 },
  { label: '采购单', value: 10 },
  { label: '质检单', value: 11 },
  { label: '其他支出单', value: 12 },
  { label: '出库单', value: 13 },
  { label: '评价', value: 14 },
  { label: '客诉', value: 15 },
  { label: '预约质检单', value: 16 },
  { label: '扣费通知', value: 17 }
]

export const columns: BasicColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'project_name',
    width: 200,
    resizable: true
  },
  {
    title: '项目ID',
    dataIndex: 'project_number',
    width: 100,
    resizable: true
  },
  {
    title: '单据单号',
    dataIndex: 'order_strid',
    width: 200,
    resizable: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? type.find((item) => item.value == text)?.label : '-'
    }
  },
  {
    title: '推送时间',
    dataIndex: 'created_at',
    width: 200,
    resizable: true
  },
  {
    title: '推送内容',
    dataIndex: 'content',
    width: 300,
    resizable: true
  },
  {
    title: '发送类型',
    dataIndex: 'send_type',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? h(Tag, { color: text == 1 ? 'green' : 'orange' }, text == 1 ? '个人' : '群聊') : '-'
    }
  },
  {
    title: '接收人',
    dataIndex: 'recipient_name',
    width: 100,
    resizable: true
  },
  {
    title: '群聊ID',
    dataIndex: 'chatid',
    width: 100,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 100,
    resizable: true
  }
]

export const searchFormSchema: FormSchema[] = [
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'project_number',
    label: '项目ID',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'type',
    label: '单据类型',
    component: 'ApiSelect',
    componentProps: {
      api: () => {
        return type
      },
      labelField: 'label',
      valueField: 'value'
    },
    colProps: { span: 8 }
  },
  {
    field: 'order_strid',
    label: '单据单号',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'recipient',
    label: '接收人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: { span: 8 }
  }
]
