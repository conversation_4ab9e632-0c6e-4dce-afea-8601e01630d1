<template>
  <div>
    <BasicTable @register="registerTable" />
  </div>
</template>
<script setup lang="ts">
import { messageerpsmlgetList } from '/@/api/admin/messagenotificationlog'
import { BasicTable, useTable } from '/@/components/Table'
import { columns, searchFormSchema } from './datas/datas'
const [registerTable] = useTable({
  api: messageerpsmlgetList,
  columns,
  showTableSetting: false,
  showIndexColumn: false,
  useSearchForm: true,
  formConfig: {
    name: 'searchForm',
    labelWidth: 120,
    schemas: searchFormSchema
  }
})
</script>
