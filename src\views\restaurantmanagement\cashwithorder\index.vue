<template>
  <div>
    <BasicTable @register="registerTable">
      <template #form-advanceBefore>
        <a-button type="primary" class="mr-8px" @click="handleBeforeExport" :loading="disableding" :disabled="disableding"
          >条件导出EXCEL</a-button
        >
      </template>
    </BasicTable>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { columns, schemas } from './datas/datas'
import { mfgetPayInfo } from '/@/api/restaurantmanagement/cashwithorder'
import { BasicTable, useTable } from '/@/components/Table'
import { message } from 'ant-design-vue'
import { mfexport } from '/@/api/restaurantmanagement/storeorders'
const disableding = ref(false)
const [registerTable, { getForm }] = useTable({
  showTableSetting: true,
  showIndexColumn: false,
  api: mfgetPayInfo,
  columns,
  useSearchForm: true,
  formConfig: {
    schemas,
    labelWidth: 120,
    alwaysShowLines: 1,
    fieldMapToTime: [['checkout_at', ['begin_date', 'end_date'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  }
})

//导出
async function handleBeforeExport() {
  try {
    disableding.value = true
    const fordata = await getForm().getFieldsValue()
    const response = await mfexport({ ...fordata, type: 3 })
    // const blob = new Blob([response.data], { type: response.headers['content-type'] })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    downloadLink.download = `订单信息-${+new Date()}.xlsx`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    message.success('导出成功')
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    disableding.value = false
  }
}
</script>
