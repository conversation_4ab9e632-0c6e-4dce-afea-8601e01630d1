<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleAdd">凭证生成</Button>
      </template>
      <template #form-advanceBefore>
        <a-button type="primary" class="mr-8px" @click="handleBeforeExport" :loading="disableding" :disabled="disableding"
          >条件导出EXCEL</a-button
        >
      </template>
    </BasicTable>
    <voucherModal @register="registerModal" />
  </div>
</template>
<script setup lang="ts">
import { columns, schemas } from './datas/datas'
import { meituanmfgetOrderList } from '/@/api/restaurantmanagement/orderinformation'
import { BasicTable, useTable } from '/@/components/Table'
import { Button, message } from 'ant-design-vue'
import voucherModal from './components/voucherModal.vue'
import { useModal } from '/@/components/Modal'
import { ref } from 'vue'
import { mfexport } from '/@/api/restaurantmanagement/storeorders'

const [registerModal, { openModal }] = useModal()

const disableding = ref(false)
const [registerTable, { getForm }] = useTable({
  showIndexColumn: false,
  showTableSetting: true,
  columns,
  api: meituanmfgetOrderList,
  useSearchForm: true,
  formConfig: {
    schemas,
    name: 'searchForm',
    labelWidth: 120,
    alwaysShowLines: 1,
    fieldMapToTime: [['data', ['begin_date', 'end_date'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  }
})

function handleAdd() {
  openModal(true)
}

//导出
async function handleBeforeExport() {
  try {
    disableding.value = true
    const fordata = await getForm().getFieldsValue()
    const response = await mfexport({ ...fordata, type: 1 })
    // const blob = new Blob([response.data], { type: response.headers['content-type'] })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    downloadLink.download = `订单信息-${+new Date()}.xlsx`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    message.success('导出成功')
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    disableding.value = false
  }
}
</script>
