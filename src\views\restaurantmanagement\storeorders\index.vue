<template>
  <div>
    <BasicTable @register="registerTable" :searchInfo="{ no_cache: 0 }">
      <template #toolbar>
        <Button
          type="primary"
          @click="
            () => {
              reload()
            }
          "
          >更新数据</Button
        >
      </template>
      <template #form-advanceBefore>
        <a-button type="primary" class="mr-8px" @click="handleBeforeExport" :loading="disableding" :disabled="disableding"
          >条件导出EXCEL</a-button
        >
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'imgs'">
          <TableImg :imgList="record.imgs" :simpleShow="true" />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>

    <detailDrawer @register="registerDetailDrawer" />
  </div>
</template>
<script lang="ts" setup>
import { columns, schemas } from './datas/datas'
import { mfexport, mfgetOrderList } from '/@/api/restaurantmanagement/storeorders'
import { BasicTable, useTable, TableImg, EditRecordRow, ActionItem, TableAction } from '/@/components/Table'
import detailDrawer from './components/detailDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { Button, message } from 'ant-design-vue'
import { ref } from 'vue'

const [registerDetailDrawer, { openDrawer }] = useDrawer()

const disableding = ref(false)

const [registerTable, { reload, getForm }] = useTable({
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  columns,
  api: mfgetOrderList,
  formConfig: {
    schemas,
    name: 'searchForm',
    labelWidth: 120,
    alwaysShowLines: 1,
    fieldMapToTime: [['checkout_at', ['begin_date', 'end_date'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  },
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action'
  },
  pagination: {
    pageSize: 10
  }
})

function createActions(record: EditRecordRow): Recordable[] {
  let editButtonList: ActionItem[] = [
    {
      label: '详情',
      onClick: handledetail.bind(null, record)
    }
  ]

  return editButtonList
}
function handledetail(record: any) {
  openDrawer(true, record)
}

//导出
async function handleBeforeExport() {
  try {
    disableding.value = true
    const fordata = await getForm().getFieldsValue()
    const response = await mfexport({ ...fordata, type: 2 })
    // const blob = new Blob([response.data], { type: response.headers['content-type'] })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    message.success('导出成功')
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    disableding.value = false
  }
}
</script>
