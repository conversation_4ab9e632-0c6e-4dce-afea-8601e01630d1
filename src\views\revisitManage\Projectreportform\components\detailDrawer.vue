<template>
  <BasicDrawer @register="registerDrawer" title="详情" width="90%">
    <BasicTable @register="registerTable">
      <template #expandedRowRender="{ record: fatherrecord }">
        <BasicTable
          :columns="childercolumns"
          :dataSource="fatherrecord.project_follow_log"
          :pagination="false"
          :canResize="false"
          :showIndexColumn="false"
        >
          <template #bodyCell="{ column: childcolumn, record: childrecord }">
            <template v-if="childcolumn.key === 'files'">
              <div v-for="(newVal, index) in childrecord.files" :key="index">
                <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
              >
            </template>
          </template>
        </BasicTable>
      </template>
    </BasicTable>
    <PreviewFile @register="registerModal" />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable } from '/@/components/Table'
import { useMessage } from '/@/hooks/web/useMessage'
import { columns, childercolumns } from '/@/views/projectmanagement/customercomplaint/datas/datas'
import { createImgPreview } from '/@/components/Preview/index'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { useModal } from '/@/components/Modal'

const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

const [registerDrawer] = useDrawerInner(async (data) => {
  console.log(data)
  setLoading(true)
  setTableData(data.customer_manage_complaint)
  setLoading(false)
})

const [registerTable, { setTableData, setLoading }] = useTable({
  showTableSetting: false,
  showIndexColumn: false,
  useSearchForm: false,
  columns
})

async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}
</script>
