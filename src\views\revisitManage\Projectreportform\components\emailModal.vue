<template>
  <BasicModal @register="register" title="客户联系方式">
    <div class="info-label">客户邮箱</div>
    <div class="info-value" @click="copyToClipboard(initrecord?.customer_manage?.email)">
      {{ initrecord?.customer_manage?.email }}
      <span class="copy-icon"><Icon icon="ant-design:copy-outlined" /></span>
    </div>
    <div class="info-label">联系方式</div>
    <div class="info-value" @click="copyToClipboard(initrecord?.customer_manage?.contact)">
      {{ initrecord?.customer_manage?.contact }}
      <span class="copy-icon"><Icon icon="ant-design:copy-outlined" /></span>
    </div>
    <div class="info-label">抄送邮箱</div>
    <div class="info-value" @click="copyToClipboard(initrecord?.customer_manage?.contact)">
      {{ initrecord?.customer_manage?.cc_email }}
      <span class="copy-icon"><Icon icon="ant-design:copy-outlined" /></span>
    </div>
  </BasicModal>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { Icon } from '/@/components/Icon'
import { useMessage } from '/@/hooks/web/useMessage'

const { createMessage } = useMessage()

const initrecord = ref()
const [register] = useModalInner((data) => {
  initrecord.value = data
})

// 复制到剪贴板函数
const copyToClipboard = (text) => {
  if (!text) return

  navigator.clipboard
    .writeText(text)
    .then(() => {
      createMessage.success('已复制到剪贴板')
    })
    .catch((err) => {
      console.error('复制失败:', err)
      createMessage.error('复制失败')
    })
}
</script>

<style lang="less" scoped>
.info-label {
  font-weight: bold;
  margin-top: 12px;
  color: #666;
}

.info-value {
  margin-bottom: 12px;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s;

  &:hover {
    background-color: #e6f7ff;

    .copy-icon {
      opacity: 1;
    }
  }
}

.copy-icon {
  opacity: 0.3;
  transition: opacity 0.3s;
  margin-left: 8px;
  color: #1890ff;
}
</style>
