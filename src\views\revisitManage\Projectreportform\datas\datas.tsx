import { h, nextTick } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { getDept } from '/@/api/baseData/accountDept'

const pstatus = [
  { label: '未结束进行中', value: 0, color: 'red' },
  { label: '已发运待回访 ', value: 1, color: 'orange' },
  { label: '已收货待评价', value: 2, color: 'green' },
  { label: '客诉处理中', value: 3, color: 'blue' },
  { label: '已结束', value: 15, color: 'purple' }
]

export const ALL = 'all'
export const ONE = 'one'
export const TWO = 'two'
export const THREEN = 'threen'
export const FINMISHED = 'oa'

export const mapTypeMenu = {
  [ALL]: null,
  [ONE]: 1,
  [TWO]: 2,
  [THREEN]: 3,
  [FINMISHED]: 15
}
export const projectstatus = [
  { value: ALL, label: '全部' },
  { value: TWO, label: '进行中' },
  { value: THREEN, label: '已超时' },
  { value: FINMISHED, label: '已结束' }
]

export const timeout = {
  0: { color: '', text: '未开始' },
  1: { color: 'orange', text: '进行中' },
  2: { color: 'red', text: '已超时' },
  15: { color: 'green', text: '已结束' }
}

export const columns: BasicColumn[] = [
  {
    title: '项目ID',
    dataIndex: 'project_number',
    width: 100,
    resizable: true
  },
  {
    title: '项目名称',
    dataIndex: 'project_name',
    width: 200,
    resizable: true
  },
  {
    title: '客户名称',
    dataIndex: 'customer_name',
    width: 200,
    resizable: true
  },
  {
    title: '业务部门',
    dataIndex: 'operation_name',
    width: 200,
    resizable: true
  },
  {
    title: '项目状态',
    dataIndex: 'p_status',
    width: 200,
    resizable: true,
    customRender: ({ text }) => {
      return text
        ? h(Tag, { color: pstatus.find((item) => item.value === text)?.color }, pstatus.find((item) => item.value === text)?.label)
        : '-'
    }
  },
  {
    title: '交付响应状态',
    dataIndex: 'status_timeout_delivery_incharge',
    width: 150,
    resizable: true,
    customRender: ({ text }) => {
      return !isNullOrUnDef(text) ? h(Tag, { color: timeout[text].color }, timeout[text].text) : ''
    }
  },
  {
    title: '项目经理响应状态',
    dataIndex: 'status_timeout_incharge',
    width: 170,
    resizable: true,
    customRender: ({ text }) => {
      return !isNullOrUnDef(text) ? h(Tag, { color: timeout[text].color }, timeout[text].text) : ''
    }
  },
  {
    title: '项目经理',
    dataIndex: 'inCharge_name',
    width: 140,
    resizable: true
  },
  {
    title: '交付经理',
    dataIndex: 'delivery_incharge_name',
    width: 140,
    resizable: true
  },
  {
    title: '方案经理',
    dataIndex: 'program_incharges_name',
    width: 200,
    resizable: true,
    customRender: ({ text }) => {
      return text ? useRender.renderTags(text) : '-'
    }
  },
  {
    title: '收货后天数',
    dataIndex: 'arrived_days',
    width: 120,
    resizable: true
  },
  {
    title: '客户首评后天数',
    dataIndex: 'first_rating_days',
    width: 120,
    resizable: true
  },
  {
    title: '第一次评分',
    dataIndex: 'first_rating_count',
    width: 120,
    resizable: true
  },
  {
    title: '最新评分',
    dataIndex: 'count',
    width: 120,
    resizable: true
  },
  // {
  //   title: '不满意内容',
  //   dataIndex: 'first_rating_count',
  //   width: 100,
  //   resizable: true
  // },
  // {
  //   title: '客户建议',
  //   dataIndex: 'program_incharge',
  //   width: 100,
  //   resizable: true
  // },
  {
    title: '最新评分时间',
    dataIndex: 'last_rating_at',
    width: 150,
    resizable: true
  },
  {
    title: '评分次数',
    dataIndex: 'rating_num',
    width: 120,
    resizable: true
  },
  {
    title: '是否已反馈申请',
    dataIndex: 'is_have_feedback',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: text == 0 ? 'green' : 'red' }, text == 0 ? '否' : '是')
    }
  },
  {
    title: '客户体验中心是否已跟进',
    dataIndex: 'is_follow_commissioner',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: text == 0 ? 'green' : 'red' }, text == 0 ? '否' : '是')
    }
  }
  // {
  //   title: '客诉链接',
  //   dataIndex: 'lianj',
  //   width: 120,
  //   resizable: true,
  //   customRender: ({ record }) => {
  //     return <Button type="primary">客诉详情</Button>
  //   }
  // }
]

export function searchFormSchema(tableAction?): FormSchema[] {
  return [
    {
      field: 'mapOrder',
      label: '',
      defaultValue: ALL,
      component: 'RadioButtonGroup',
      componentProps: ({ formActionType }) => ({
        options: projectstatus,
        onChange: (value) => {
          tableAction?.setProps({
            searchInfo: {
              project_status: mapTypeMenu[value]
            }
          })

          nextTick(() => formActionType.submit())
        }
      }),

      colProps: {
        span: 21
      }
    },
    {
      field: 'id',
      label: '项目ID',
      component: 'Input',
      colProps: {
        span: 6
      }
    },
    {
      field: 'title',
      label: '项目名称',
      component: 'Input',
      colProps: {
        span: 6
      }
    },
    {
      field: 'customer',
      label: '客户名称',
      component: 'Input',
      colProps: {
        span: 6
      }
    },
    {
      field: 'operation',
      label: '业务部门',
      component: 'PagingApiSelect',
      componentProps: {
        api: getDept,
        // params: { status: 1, is_audit: 1, is_operate: 1 },
        params: { status: 1, is_audit: 1 },
        resultField: 'items',
        labelField: 'name',
        valueField: 'id',
        searchMode: true,
        pagingMode: true,
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'id',
            label: 'name'
          },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          style: {
            width: '100%'
          }
        }
      },
      colProps: {
        span: 6
      }
    },
    {
      field: 'is_follow_commissioner',
      label: '客户体验中心是否已跟进',
      component: 'Select',
      componentProps: {
        options: [
          {
            label: '是',
            value: 1
          },
          {
            label: '否',
            value: 0
          }
        ]
      },
      colProps: {
        span: 6
      }
    },
    {
      field: 'p_status',
      label: '项目状态',
      component: 'Select',
      componentProps: {
        options: pstatus
      },
      colProps: {
        span: 6
      }
    }
  ]
}
