<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleexcel" v-if="hasPermission(663)">导出</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <div class="flex items-center">
            <Badge :count="record.deliver_num" :numberStyle="{}">
              <a-button size="small" type="link" class="ml-2" @click.stop="handldetild(record)"> 查看客诉 </a-button>
            </Badge>
            <TableAction class="ml-2" :drop-down-actions="createDropDownActions(record)" v-if="hasPermission(672)" />
          </div>
        </template>
      </template>
      <template #expandedRowRender="{ record: fatherrecord }">
        <BasicTable
          :columns="childercolumns"
          :dataSource="fatherrecord.doc_satisfaction_rating"
          :pagination="false"
          :canResize="false"
          :showIndexColumn="false"
        >
          <template #bodyCell="{ column: childcolumn, record: childrecord }">
            <template v-if="childcolumn.key === 'files'">
              <div v-for="(newVal, index) in childrecord.files" :key="index">
                <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
              >
            </template>
          </template>
        </BasicTable>
      </template>
    </BasicTable>
    <detailDrawer @register="registerDrawer" />
    <tailafterDrawer @register="registeretailDrawer" />
    <PreviewFile @register="registerModal" />
    <emailModal @register="registeremailModal" />
  </div>
</template>
<script setup lang="ts">
import { getProjectList, GetProjectListExcel } from '/@/api/projectOverview'
import { ActionItem, BasicTable, useTable, EditRecordRow, TableAction } from '/@/components/Table'
import { columns, searchFormSchema } from './datas/datas'
import detailDrawer from './components/detailDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { usePermission } from '/@/hooks/web/usePermission'
import { message, Button, Badge } from 'ant-design-vue'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'
import { onMounted } from 'vue'
import tailafterDrawer from '../../projectmanagement/customercomplaint/components/tailafterDrawer.vue'
import { columns as childercolumns } from '../evaluate/datas/data'
import { useMessage } from '/@/hooks/web/useMessage'
import { createImgPreview } from '/@/components/Preview/index'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { useModal } from '/@/components/Modal'
import emailModal from './components/emailModal.vue'

const [registerDrawer, { openDrawer }] = useDrawer()
const { hasPermission } = usePermission()
const [registeretailDrawer, { openDrawer: openetailDrawer, setDrawerProps: setetailDrawerProps }] = useDrawer()
const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()
const [registeremailModal, { openModal: openemailModal }] = useModal()

const [registerTable, { setLoading, getForm, setProps }] = useTable({
  api: getProjectList,
  columns,
  actionColumn: {
    title: '详情',
    width: 150,
    dataIndex: 'action',
    fixed: 'right'
  },
  beforeFetch: (params) => {
    return { ...params, mapOrder: undefined }
  },
  showTableSetting: true,
  useSearchForm: true,
  showIndexColumn: false,
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas: searchFormSchema(),
    labelWidth: 170,
    resetFunc: async () => {
      setProps({
        beforeFetch: () => {
          return { page: 1, pageSize: 10 }
        }
      })

      setTimeout(() => {
        setProps({
          beforeFetch: (params) => {
            return { ...params, mapOrder: undefined }
          }
        })
      }, 0)
    },
    showAdvancedButton: false,
    fieldMapToTime: [['check_at', ['check_at_start', 'check_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  }
})
onMounted(() => {
  setProps({
    formConfig: {
      ...NEW_STATUS_FORMCONFIG,
      schemas: searchFormSchema({ setProps }),
      labelWidth: 120,
      resetFunc: async () => {
        setProps({
          beforeFetch: () => {
            return { page: 1, pageSize: 10 }
          }
        })

        setTimeout(() => {
          setProps({
            beforeFetch: (params) => {
              return { ...params, mapOrder: undefined }
            }
          })
        }, 0)
      },
      showAdvancedButton: false,
      fieldMapToTime: [['check_at', ['check_at_start', 'check_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
    }
  })
})

function createDropDownActions(record: EditRecordRow): ActionItem[] {
  return [
    {
      label: '客诉超时跟踪',
      onClick: handletailafter.bind(null, record, 'timeout'),
      disabled: record.status_timeout_delivery_incharge !== 2,
      ifShow: hasPermission(672)
    },
    {
      label: '客户联系方式',
      onClick: handleemil.bind(null, record),
      ifShow: hasPermission(694)
    }
  ]
}

function handletailafter(record, type) {
  openetailDrawer(true, { record, type })
  setetailDrawerProps({ title: type == 'normal' ? '客诉跟踪' : '客诉超时跟踪', showFooter: true, width: '50%' })
}

function handldetild(record) {
  openDrawer(true, record)
}

async function handleexcel() {
  try {
    setLoading(true)
    const params = getForm()?.getFieldsValue()
    const response = await GetProjectListExcel({ ...params, is_excel: 1, pageSize: 50000 })
    // const blob = new Blob([response.data], { type: response.headers['content-type'] })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    downloadLink.download = `项目报表-${+new Date()}.xlsx`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    message.success('导出成功')
    setLoading(false)
  } catch (err: any) {
    message.error('导出失败')
    setLoading(false)

    throw new Error(err)
  } finally {
    setLoading(false)
    // exporting.value = false
  }
}

async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}

function handleemil(record) {
  openemailModal(true, record)
}
</script>
