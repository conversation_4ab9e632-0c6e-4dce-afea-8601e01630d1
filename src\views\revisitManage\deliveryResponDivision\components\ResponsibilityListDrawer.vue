<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="90%" destroyOnClose title="订单责任列表" :showFooter="false">
    <BasicTable @register="registerTable" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { getResponList } from '/@/api/revisit/deliveryResponDivision'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable } from '/@/components/Table'
import { columns, firstColumns } from '../datas/drawer'

const [registerTable, { reload, setProps }] = useTable({
  title: '订单责任列表',
  api: getResponList,
  columns,
  showIndexColumn: false,
  immediate: false,
  canResize: false,
  pagination: false,
  afterFetch: (data) =>
    data.map((item, index) => {
      return { ...item, firstColumns: firstColumns[index] }
    })
})

const [registerDrawer, { changeLoading, changeOkLoading }] = useDrawerInner(async ({ searchInfo }) => {
  try {
    await changeLoading(true)
    await setProps({ searchInfo })
    await reload()

    changeOkLoading(false)
  } catch (err) {
    console.error(err)
  } finally {
    changeLoading(false)
  }
})
</script>
