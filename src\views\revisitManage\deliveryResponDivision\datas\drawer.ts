export const firstColumns = {
  0: '实际完成日期',
  1: '最晚完成时间',
  2: '是否逾期'
}

import { h } from 'vue'
import type { BasicColumn } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'

const customRenderFn = ({ record: { firstColumns }, value }) => {
  if (firstColumns === '是否逾期') {
    return h(
      'div',
      { style: { color: isNullOrUnDef(value) ? '' : value ? 'red' : 'green' } },
      isNullOrUnDef(value) ? '-' : value ? '是' : '否'
    )
  } else {
    return isNullOrUnDef(value) ? '-' : value
  }
}

export const columns: BasicColumn[] = [
  {
    dataIndex: 'firstColumns',
    title: ''
  },
  {
    dataIndex: 'type1',
    title: '可备货日期',
    customRender: customRenderFn
  },
  {
    dataIndex: 'type2',
    title: '拆单完成日期',
    customRender: customRenderFn
  },
  {
    dataIndex: 'type3',
    title: '采购日期',
    customRender: customRenderFn
  },
  {
    dataIndex: 'type4',
    title: '生产完成日期',
    customRender: customRenderFn
  },
  {
    dataIndex: 'type5',
    title: '质检完成日期',
    customRender: customRenderFn
  },
  {
    dataIndex: 'type6',
    title: '装箱完成日期',
    customRender: customRenderFn
  }
]
