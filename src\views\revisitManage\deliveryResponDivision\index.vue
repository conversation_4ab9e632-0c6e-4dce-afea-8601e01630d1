<template>
  <div>
    <BasicTable :data-cachekey="routePath" @register="registerTable" class="p-4" @fetch-success="onFetchSuccess">
      <template #toolbar>
        <a-button type="primary" @click.stop="handleExpenOrCollect">一键{{ isExpendAll ? '收起' : '展开' }}项目</a-button>
      </template>
      <template #expandedRowRender="{ record: { work } }">
        <BasicTable
          :dataSource="work"
          :columns="saleColumns"
          :="{
            canResize: false,
            rowKey: 'id',
            pagination: false,
            showIndexColumn: false,
            actionColumn: {
              width: 120,
              title: '操作',
              dataIndex: 'action',
              fixed: 'right'
            }
          }"
          class="p-4"
        >
          <template #bodyCell="{ column: saleColumn, record: saleRecord }">
            <template v-if="saleColumn.dataIndex === 'action'">
              <div class="flex items-center py-3">
                <a-button type="primary" size="small" @click.stop="onViewResponsibilityList(saleRecord)" v-if="hasPermission([409])"
                  >订单责任列表
                </a-button>
              </div>
            </template>
          </template>
          <template #expandedRowRender="{ record: saleRecord }">
            <BasicTable
              v-bind="{
                api: getSalesOrderListReq,
                rowKey: 'id',
                pagination: { size: 'small' },
                showIndexColumn: false,
                canResize: false,
                scroll: { y: 500 },
                columns: productColumns,
                isTreeTable: true,
                searchInfo: { work_id: saleRecord.id }
              }"
            >
              <template #toolbar>
                <div class="progress-bar">
                  <div class="left">
                    <div class="status">
                      <a-button
                        type="link"
                        @click.stop="onViewSaleDetail(saleRecord)"
                        class="status"
                        :style="{ color: saleRecord.status === 15 ? '#999' : '', margin: 0, padding: 0 }"
                        >{{ saleRecord.source_uniqid }}</a-button
                      >
                      <span :style="{ color: allStatus[saleRecord.status].color, marginLeft: '10px' }">
                        {{ allStatus[saleRecord.status].label }}</span
                      ></div
                    >
                    <div class="time">预计交货日期: {{ saleRecord.delivery_at }}</div>
                  </div>
                  <div class="right" v-if="![15, 16].includes(saleRecord.status)">
                    <Steps :current="mapStatus[saleRecord.status].index" :status="mapStatus[saleRecord.status].status" progressDot>
                      <template v-for="(toolItem, index) in baseStatusArray" :key="toolItem">
                        <Step :title="toolItem">
                          <template #description v-if="mapStatus[saleRecord.status].index >= index">
                            {{ saleRecord[mapStatus[index].description] }}
                          </template>
                        </Step>
                      </template>
                    </Steps>
                  </div>
                </div>
              </template>
              <template #bodyCell="{ column: productColumn, record: productRecord }">
                <template v-if="productColumn.dataIndex === 'product'">
                  <div class="product">
                    <TableImg :size="60" :simpleShow="true" :imgList="productRecord.imgs" :margin="1" />
                    <div class="product-info">
                      <div class="name">{{ productRecord.name }}</div>
                      <div class="puid">产品编号: {{ productRecord.puid }}</div>
                    </div>
                  </div>
                </template>
              </template>
              <template #expandedRowRender="{ record: productRecord }">
                <BasicTable
                  v-bind="{
                    api: getProgressTracking,
                    columns: productStatusColumns,
                    showIndexColumn: false,
                    pagination: false,
                    canResize: false,
                    searchInfo: { request_id: productRecord.id }
                  }"
                />
              </template>
            </BasicTable>
          </template>
        </BasicTable>
      </template>
      <template #form-InCharges="{ model }">
        <div style="display: flex; align-items: center">
          <PagingApiSelect
            :api="getStaffList"
            v-model:value="model.inCharges"
            resultField="items"
            :select-props="{
              fieldNames: { key: 'key', value: 'id', label: 'name' },
              showSearch: true,
              placeholder: '请选择',
              optionFilterProp: 'name',
              mode: 'multiple'
            }"
          />
        </div>
      </template>
    </BasicTable>

    <VerifyDrawer @register="registerSaleDetailDrawer" />
    <ResponsibilityListDrawer @register="registerResponsibilityListDrawer" />
  </div>
</template>
<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { Steps, Step } from 'ant-design-vue'

import { BasicTable, useTable, TableImg } from '/@/components/Table'
import { PagingApiSelect } from '/@/components/Form'
import { useDrawer } from '/@/components/Drawer'
import { getProjectList } from '/@/api/revisit/index'
import { getSalesOrderListReq } from '/@/api/erp/sales'
import { getProgressTracking } from '/@/api/erp/progressTracking'
import { usePermission } from '/@/hooks/web/usePermission'
import VerifyDrawer from '/@/views/erp/saleOrder/components/VerifyDrawer.vue'
import ResponsibilityListDrawer from './components/ResponsibilityListDrawer.vue'
import { useRoute } from 'vue-router'
import { getStaffList } from '/@/api/baseData/staff'
import {
  columns,
  schemas,
  productStatusColumns,
  productColumns,
  mapStatus,
  allStatus,
  baseStatusArray,
  saleColumns
} from '../revisitLog/datas/datas'

const route = useRoute()
const { name: routeName, path: routePath } = route
console.log(route)

const { hasPermission } = usePermission()
const activeKey = ref([])

const isExpendAll = ref(true)

const [registerSaleDetailDrawer, { openDrawer: openSaleDetailDrawer, setDrawerProps: setSaleDetailDrawerProps }] = useDrawer()
const [registerResponsibilityListDrawer, { openDrawer: openResponsibilityListDrawer }] = useDrawer()

const [registerTable, { expandAll, collapseAll }] = useTable({
  title: '交付责任划分',
  api: getProjectList,
  columns,
  showIndexColumn: false,
  useSearchForm: true,
  rowKey: 'id',
  formConfig: {
    labelWidth: 120,
    schemas: schemas(routeName),
    autoSubmitOnEnter: true,
    baseColProps: { span: 8 },
    autoAdvancedLine: 1,
    fieldMapToTime: [
      ['follow_up_at', ['follow_up_at_start', 'follow_up_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['est_finished_at', ['est_finished_at_start', 'est_finished_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['submited_at', ['submited_at_start', 'submited_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  },
  beforeFetch: (params) => {
    if (params.iability_type) {
      params.sales_status = 15
    }
    params['from'] = 2

    return params
  },
  isTreeTable: true,
  showTableSetting: true
})

function onFetchSuccess() {
  activeKey.value = []
  nextTick(() => (isExpendAll.value ? expandAll() : collapseAll()))
}

function handleExpenOrCollect() {
  isExpendAll.value = !isExpendAll.value
  isExpendAll.value ? expandAll() : collapseAll()
}

function onViewSaleDetail(item) {
  setSaleDetailDrawerProps({ title: '销售详情' })
  openSaleDetailDrawer(true, item)
}

function onViewResponsibilityList(record) {
  openResponsibilityListDrawer(true, { searchInfo: { id: record.id } })
}
</script>

<style lang="less" scoped>
.progress-bar {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  border: 1px solid #eee;
  border-radius: 10px;
  padding: 20px;
  .left {
    .status {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 6px;
    }
    .time {
      font-weight: 600;
      font-size: 18px;
      margin-bottom: 6px;
    }
  }
  .right {
    margin-left: 200px;
  }
}
.product {
  display: flex;
  align-items: center;
  .vben-basic-table-img.flex.items-center.mx-auto {
    margin-left: 0;
    margin-right: 8px;
  }
  .name {
    font-weight: 700;
    margin-bottom: 6px;
  }
  .puid {
    color: #999;
    font-size: 13px;
  }
}

.sale-list-right {
  //width: 365px;
  box-sizing: border-box;
}
</style>
