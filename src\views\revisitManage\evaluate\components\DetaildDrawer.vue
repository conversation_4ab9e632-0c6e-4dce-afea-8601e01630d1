<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="详情" width="90%">
    <Descriptions title="详情" :column="2">
      <DescriptionsItem label="评价客户">{{ init_record.client_name }}</DescriptionsItem>
      <DescriptionsItem label="评价内容">{{ init_record.content }}</DescriptionsItem>
      <DescriptionsItem label="评价时间">{{ init_record.created_at }}</DescriptionsItem>
      <DescriptionsItem label="在线签名">
        <Image :width="100" :src="init_record.autograph_image" />
      </DescriptionsItem>
    </Descriptions>
    <Descriptions title="项目列表" :column="2" />
    <BasicTable @register="registerTable" :columns="tabcolumns" :dataSource="dataSource">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'count'">
          <Rate v-model:value="record.count" disabled allow-half style="margin-left: 10px" />
        </template>
      </template>
    </BasicTable>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { getevaluatedetail } from '/@/api/revisit'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { Image, Descriptions, DescriptionsItem, Rate } from 'ant-design-vue'
import { BasicTable, useTable } from '/@/components/Table'
import { tabcolumns } from '../datas/data'

const init_record = ref<any>({})
const dataSource = ref([])
const [registerDrawer] = useDrawerInner(async (data) => {
  init_record.value = data.record
  const { items } = await getevaluatedetail({ id: data.id })
  dataSource.value = items
})
const [registerTable] = useTable()
</script>
