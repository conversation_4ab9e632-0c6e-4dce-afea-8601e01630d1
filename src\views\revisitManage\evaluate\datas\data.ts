import { getClientList } from '/@/api/commonUtils'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { h } from 'vue'
const dutytype = {
  1: { label: '项目经理' },
  2: { label: '方案经理' },
  3: { label: '交付经理' }
}

export const columns: BasicColumn[] = [
  {
    dataIndex: 'project_name',
    title: '项目名称',
    width: 300,
    resizable: true
  },
  {
    dataIndex: 'project_number',
    title: '项目ID',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'from',
    title: '评价来源',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return text == 1 ? '小程序' : 'pc'
    }
  },
  {
    dataIndex: 'client_name',
    title: '评价客户',
    width: 200,
    resizable: true
  },

  {
    dataIndex: 'bad_reason',
    title: '评价内容',
    width: 300,
    resizable: true,
    customRender: ({ text }) => {
      if (!text) return '--'

      // 判断是否为数组
      const textArray = typeof text === 'string' ? [text] : text

      // 使用h函数创建一个带有样式的div元素
      return h(
        'div',
        {
          style: {
            maxWidth: '100%',
            wordBreak: 'break-word', // 允许在单词内部换行
            whiteSpace: 'normal', // 允许文本换行
            lineHeight: '1.5' // 设置行高
          }
        },
        // 创建带序号的文本内容
        Array.isArray(textArray)
          ? textArray.map((item, index) => {
              return h('div', { style: { marginBottom: '5px' } }, `${index + 1}. ${item}`)
            })
          : text
      )
    }
  },
  {
    dataIndex: 'created_at',
    title: '评价时间',
    width: 200,
    resizable: true
  },
  {
    dataIndex: 'count',
    title: '分数',
    width: 200,
    resizable: true
  }
]
export const schemas: FormSchema[] = [
  {
    field: 'client_id',
    label: '客户',
    component: 'PagingApiSelect',
    componentProps: {
      api: getClientList,
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      },
      resultField: 'items'
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'source_uniqid',
    label: '销售订单',
    component: 'Input'
  },
  {
    field: 'content',
    label: '评价内容',
    component: 'Input'
  },
  {
    field: 'created_at',
    label: '评价时间',
    component: 'SingleRangeDate'
  },
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input'
  },
  {
    field: 'project_number',
    label: '项目ID',
    component: 'Input'
  }
]
//详情表格
export const tabcolumns: BasicColumn[] = [
  {
    dataIndex: 'project_number',
    title: '项目ID',
    width: 200,
    resizable: true
  },

  {
    dataIndex: 'project_name',
    title: '项目名称',
    width: 300,
    resizable: true
  },
  {
    dataIndex: 'inCharge_name',
    title: '负责人',
    width: 200,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '--'
    }
  },
  {
    dataIndex: 'type',
    title: '职务',
    width: 200,
    resizable: true,
    customRender: ({ text }) => {
      return text ? dutytype[text].label : '-'
    }
  },
  {
    dataIndex: 'count',
    title: '服务星级',
    width: 200,
    resizable: true
  }
]
