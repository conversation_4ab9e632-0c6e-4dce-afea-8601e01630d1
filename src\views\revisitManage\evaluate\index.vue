<template>
  <div>
    <BasicTable :data-cachekey="routePath" @register="register">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'autograph_image'">
          <Image :width="100" :src="record.autograph_image" :preview="record.autograph_image ? true : false" />
        </template>
      </template>
    </BasicTable> </div
></template>
<script lang="ts" setup>
import { BasicTable, useTable } from '/@/components/Table'
import { columns, schemas } from './datas/data'
import { getevaluateList } from '/@/api/revisit'
import { Image } from 'ant-design-vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const { path: routePath } = route
const [register] = useTable({
  api: getevaluateList,
  columns,
  showIndexColumn: false,
  useSearchForm: true,
  formConfig: {
    schemas,
    labelWidth: 120,
    actionColOptions: {
      span: 24
    },
    baseColProps: {
      span: 6
    },
    fieldMapToTime: [['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  },
  showTableSetting: true
})
</script>
