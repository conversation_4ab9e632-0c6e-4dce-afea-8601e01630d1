<template>
  <BasicModal @register="registerModal" width="70%" destroyOnClose :showOkBtn="false" cancelText="关闭">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button @click="handleBatchRead" v-if="!isAllRead">一键已读</a-button>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script setup lang="ts">
import { h, ref } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicTable, useTable } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { setIsRead } from '/@/api/revisit/index'
import { useMessage } from '/@/hooks/web/useMessage'

const emit = defineEmits(['register', 'reload'])
const propsData = ref<{ items: Recordable[]; type?: string; code?: number }>({ items: [] })

const isAllRead = ref(false)

const [registerModal, { changeLoading }] = useModalInner(async (data) => {
  try {
    await changeLoading(true)
    propsData.value = data
    isAllRead.value = data.items.every((item) => item.is_read)
    await setTableData(data.items)
    changeLoading(false)
  } catch (err) {
    console.error(err)
    changeLoading(false)
  }
})

const [registerTable, { setTableData, updateTableDataRecord }] = useTable({
  title: '',
  dataSource: [],
  rowKey: 'id',
  columns: [
    {
      title: '销售订单',
      dataIndex: 'source_uniqid'
    },
    {
      title: '申请人',
      dataIndex: 'creator_name'
    },
    {
      title: '延期原因',
      dataIndex: 'remark',
      customRender: ({ value }) => {
        return h('span', null, value ?? '')
      }
    },
    {
      title: '交付日期',
      dataIndex: 'deliver_at',
      customRender: ({ value }) => {
        return h('span', null, value ?? '')
      }
    },
    {
      title: '需求生产完成日期',
      dataIndex: 'request_status_at',
      customRender: ({ value }) => {
        return h('span', null, value ?? '')
      }
    },
    {
      title: '日志创建日期',
      dataIndex: 'created_at'
    },
    {
      title: '是否已读',
      dataIndex: 'is_read',
      customRender: ({ value }) => {
        return useRender.renderBadge(value ? '已读' : '未读', value ? 'green' : 'red')
      }
    }
  ],
  pagination: false,
  canResize: false
})
const { createConfirm } = useMessage()
async function handleBatchRead() {
  createConfirm({
    title: '确认已读',
    content: '确认后,将更新回访日志页面的视图,确认继续吗？',
    iconType: 'warning',
    onOk: async () => {
      try {
        await changeLoading
        const { code, type, items } = propsData.value
        await setIsRead(type === 'project' ? { project_number: code } : { sale_work_id: code })
        isAllRead.value = true
        emit('reload')
        const noneReadItems = items.filter((item) => !item.is_read)
        noneReadItems.forEach((item) => {
          updateTableDataRecord(item.id, { is_read: true })
        })

        changeLoading(false)
      } catch (err) {
        changeLoading(false)
        console.error(err)
      }
    }
  })
}
</script>
