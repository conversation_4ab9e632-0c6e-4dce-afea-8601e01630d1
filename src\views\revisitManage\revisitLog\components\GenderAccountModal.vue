<template>
  <BasicModal @register="registerModal" title="查看生成账号" :destroy-on-close="true">
    <Alert message="账号初始密码生成为:123456;" description=" 当修改密码后密码不会显示,请记住修改后的密码;" />
    <br />
    <BasicForm @register="registerForm" />
    <template #footer>
      <template v-if="!isUpdate">
        <a-button :loading="btnStatus" type="primary" @click="handleChangeStatus(false)">修改</a-button>
        <a-button :loading="btnStatus" type="primary" @click="handleGenderAccount({ id: propsData?.record?.client_id })"
          >一键生成客户账号</a-button
        >
      </template>
      <template v-else>
        <a-button :loading="btnStatus" @click="handleCancel">取消</a-button>
        <a-button :loading="btnStatus" type="primary" @click="handleSubmit">确定</a-button>
      </template>
    </template>
  </BasicModal>
</template>
<script setup lang="ts">
import { useModalInner, BasicModal } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { transformFieldDisabledStatus } from '/@/utils/formSchema'
import { schemas } from '/@/views/revisitManage/revisitLog/datas/createAccount'
import { ref } from 'vue'
import { getProjectAccount, setProjectAccount } from '/@/api/revisit'
import { useMessage } from '/@/hooks/web/useMessage'
import { Alert } from 'ant-design-vue'

const { createMessage } = useMessage()
const btnStatus = ref<boolean>(false)
const isUpdate = ref<boolean>(false)
const propsData = ref({})
const [registerModal, { changeLoading }] = useModalInner((data) => {
  console.log(data)
  propsData.value = data
  isUpdate.value = false
  const defaultSchemas = transformFieldDisabledStatus(schemas, true)
  updateSchema(defaultSchemas)

  getAccount(data.record)
})

const [registerForm, { updateSchema, setFieldsValue, validate }] = useForm({
  baseColProps: { span: 24 },
  showActionButtonGroup: false,
  colon: true,
  labelAlign: 'left',
  labelCol: { style: { width: '100px' } },
  schemas
})

async function getAccount(record) {
  try {
    changeLoading(true)
    btnStatus.value = true
    const { items } = await getProjectAccount({ id: record.client_id })
    await setFieldsValue({ username: items.username })
    console.log(items)
  } catch (err) {
    throw new Error(err)
  } finally {
    changeLoading(false)
    btnStatus.value = false
  }
}

function handleChangeStatus(status) {
  isUpdate.value = !status
  const defaultSchemas = transformFieldDisabledStatus(schemas, status)
  updateSchema(defaultSchemas)
}

async function handleGenderAccount(data) {
  try {
    if (!data.id) return createMessage.error('没有找到项目的客户ID，请联系管理员')
    btnStatus.value = true
    const { msg } = await setProjectAccount(data)
    if (msg === 'success') {
      createMessage.success('密码修改成功')
      // isUpdate.value = false
      getAccount(propsData.value.record)
      return
    }
    createMessage.error('失败')
    // await setFieldsValue({ items })
  } catch (e) {
    throw new Error(e)
  } finally {
    btnStatus.value = false
  }
}

function handleCancel() {
  handleChangeStatus(true)
  getAccount(propsData.value.record)
}

async function handleSubmit() {
  try {
    const values = await validate()
    const { client_id } = propsData.value.record
    await handleGenderAccount({ ...values, id: client_id })
    isUpdate.value = false
    btnStatus.value = false
    handleChangeStatus(true)
  } catch (err) {
    throw new Error(err)
  }
}
</script>
