<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" showFooter width="50%" @ok="handleSubmit" destroyOnClose>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'

import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { UpdateRevisitRecord } from '/@/api/revisit'
import { schemas } from '../datas/drawer'
const emit = defineEmits(['success', 'register'])
const propsData = ref<{ type: 'add' | 'edit' }>()

const [registerDrawer, { changeLoading, changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  try {
    changeLoading(true)
    await resetFields()

    propsData.value = data
    if (data.type === 'edit') {
      await resetSchema(schemas)
      data.record && setFieldsValue(data.record)
    }
  } catch (err) {
    console.error(err)
  } finally {
    changeLoading(false)
  }
})

const [registerForm, { resetFields, setFieldsValue, resetSchema, validate }] = useForm({
  schemas,
  baseColProps: { span: 12 },
  colon: true,
  showActionButtonGroup: false,
  labelWidth: 120
})

async function handleSubmit() {
  try {
    await changeOkLoading(true)
    const formData = await validate()
    await UpdateRevisitRecord({ id: propsData.value?.record?.id, ...formData })
    await closeDrawer()
    emit('success')
    await changeOkLoading(false)
  } catch (err) {
    await changeOkLoading(false)
    throw new Error(`${err}`)
  } finally {
    changeLoading(false)
  }
}
</script>
