<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    width="60%"
    destroyOnClose
    title="回访日志"
    :showFooter="isCreate"
    @ok="handleSubmit"
  >
    <template v-if="isCreate">
      <BasicForm @register="registerForm" @field-value-change="handleFiledValueChange">
        <template #Files>
          <Upload
            v-model:file-list="filesList"
            action="/api/oss/putImg2Stocking"
            :custom-request="handleFileRequest"
            :multiple="true"
            :showUploadList="{ showDownloadIcon: true, showRemoveIcon: true }"
          >
            <a-button type="primary">
              <upload-outlined />
              Upload
            </a-button>
          </Upload>
        </template>
      </BasicForm>
    </template>

    <template v-else>
      <BasicTable @register="registerTable">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex == 'files'">
            <div v-for="(newVal, index) in record.files" :key="index">
              <a :href="newVal" target="_blank" @click="handlePreview(newVal, $event)">{{ `附件${index + 1}` }}</a></div
            >
          </template>
        </template>
      </BasicTable>
      <PreviewFile @register="registerModal" />
    </template>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { BasicTable, useTable } from '/@/components/Table'
import { createRevisitLog, getRevisitLog } from '/@/api/revisit'
import { schemas, columns } from '../datas/RevisitLogDrawer'
import { UploadFile, Upload } from 'ant-design-vue'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { UploadOutlined } from '@ant-design/icons-vue'
import { useMessage } from '/@/hooks/web/useMessage'
import { createImgPreview } from '/@/components/Preview/index'
import PreviewFile from '/@/components/PreviewFile/xlsxFile.vue'
import { useModal } from '/@/components/Modal'
import { getListByDate } from '/@/api/erp/outWarehouse'

const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

const propsData = ref<{ type: 'create'; record?: Recordable }>({
  type: 'create'
})
const isCreate = computed(() => propsData.value.type === 'create')
//附件
const filesList = ref<UploadFile[]>([])

const [registerDrawer, { changeLoading, changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  try {
    await changeOkLoading(false)
    changeLoading(true)
    console.log(data)

    await resetFields()
    filesList.value = []
    propsData.value = data
    if (data.type === 'view') {
      nextTick(async () => {
        await setProps({
          searchInfo: { project_number: Number(data.record.project_number) }
        })
        await reload()
      })
    }
  } catch (err) {
    console.error(err)
  } finally {
    changeLoading(false)
  }
})

const [registerForm, { resetFields, validate, setFieldsValue, updateSchema }] = useForm({
  schemas,
  baseColProps: { span: 12 },
  colon: true,
  showActionButtonGroup: false,
  labelWidth: 140
})

const [registerTable, { reload, setProps }] = useTable({
  title: '回访日志',
  api: getRevisitLog,
  columns,
  immediate: false
})

//附件
watch(
  () => filesList.value,
  async (val) => {
    await setFieldsValue({ files: val?.map((item) => item.url) ?? [] })
  }
)
async function handleSubmit() {
  try {
    changeOkLoading(true)
    const values = await validate()
    const { creator, cc_recipient, content, points_manage_id, matter_strid, files } = values
    await createRevisitLog({
      project_number: propsData.value.record?.project_number,
      project_name: propsData.value.record?.project_name,
      client_id: propsData.value.record?.client_id,
      creator,
      cc_recipient,
      content,
      points_manage_id,
      matter_strid: points_manage_id == 12 ? matter_strid?.join(',') : matter_strid,
      files
    })
    closeDrawer()
  } catch (err) {
    changeOkLoading(false)
    console.error(err)
  }
}

//附件上传
async function handleFileRequest({ file, onSuccess }: UploadRequestOption) {
  const result = await commonFileUpload(file, 'purchase')
  onSuccess!(result.path)
  filesList.value = filesList.value!.map((item) => {
    return {
      url: (item.url as string) || (item.response as string),
      uid: item.uid,
      name: item.name
    }
  })
  await setFieldsValue({
    files: filesList.value.map((item) => item.url)
  })
}

// 预览
async function handlePreview(val: string, e: Event) {
  e.preventDefault()
  if (!val) return createMessage.error('没有找到文件路径，请联系管理员')
  const reg = /\.([0-9a-z]+)(?:[\?#]|$)/i
  const prefix = val.match(reg)?.[1]
  if (prefix && ['png', 'jpg', 'jpge', 'gif'].includes(prefix)) {
    createImgPreview({ imageList: [val], maskClosable: true, defaultWidth: 500 })
  } else if (prefix && ['doc', 'docx', 'ppt', 'pptx', 'xlsx', 'xls', 'pdf'].includes(prefix)) {
    openModal(true, { url: val, fileType: prefix })
  } else {
    window.open(val, '_block')
  }
}

function handleFiledValueChange(key, value) {
  console.log(key, value)
  if (key == 'points_manage_id' && value == 12) {
    setFieldsValue({ matter_strid: [] })
    updateSchema({
      field: 'matter_strid',
      label: '事项单号',
      component: 'PagingApiSelect',
      componentProps: {
        api: getListByDate,
        resultField: 'items',
        params: {
          is_cancel: 0,
          project_number: propsData.value.record?.project_number
        },
        searchMode: true,
        pagingMode: true,
        returnParamsField: 'strid',
        selectProps: {
          mode: 'multiple',
          fieldNames: { value: 'strid', label: 'strid' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        }
      }
    })
  }
  if (key == 'points_manage_id' && value != 12) {
    setFieldsValue({ matter_strid: '' })
    updateSchema({
      field: 'matter_strid',
      label: '事项单号',
      component: 'Input'
    })
  }
}
</script>
