<template>
  <BasicModal @register="registerModal" width="40%" @ok="handleSubmit" destroyOnClose :bodyStyle="{ height: '400px' }">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import dayjs from 'dayjs'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { updateDeliveryAt, UpdateRevisitRecord } from '/@/api/revisit'

import { deliverSchemas } from '../datas/modal'
import * as propertyConst from '../datas/const'
import { message } from 'ant-design-vue'

const emit = defineEmits(['success', 'register'])
const propsData = ref()

const [registerModal, { closeModal, changeOkLoading }] = useModalInner(async (data) => {
  await resetFields()
  await updateSchema(deliverSchemas(data.type))

  //如果是下次回访日期,时间为data.follow_up_at+14天
  if (data.type === propertyConst.REVISITATLABLE) {
    const followUpAt = data.follow_up_at ? dayjs(data.follow_up_at).add(14, 'days') : dayjs().add(14, 'days')
    setFieldsValue({
      follow_up_at: followUpAt.format('YYYY-MM-DD 00:00:00'),
      last_follow_up_at: dayjs().format('YYYY-MM-DD 00:00:00') //上次回访日期,默认今天
    })
  }
  propsData.value = data
})

/** 注册Form */
const [registerForm, { resetFields, validate, updateSchema, setFieldsValue }] = useForm({
  baseColProps: { span: 12 },
  labelWidth: 130,
  showActionButtonGroup: false,
  schemas: deliverSchemas('交付日期'),
  colon: true
})

async function handleSubmit() {
  try {
    await changeOkLoading(true)
    const formData = await validate()
    const { id, type, work_id } = propsData.value

    //处理request_status_at和deliver_at
    if ([propertyConst.DELIVERATLABLE, propertyConst.QCAPPLYDELAYLABLE].includes(type)) {
      formData.request_status_at = dayjs(formData.deliver_at).subtract(5, 'day').format('YYYY-MM-DD 00:00:00')
    } else if ([propertyConst.PURCHASEAPPLYDELAYLABLE].includes(type)) {
      formData.deliver_at = dayjs(formData.request_status_at).add(5, 'day').format('YYYY-MM-DD 23:59:59')
    } else if ([propertyConst.REVISITATLABLE].includes(type)) {
      // 判断下次回访日期是否大于上次回访日期
      const { follow_up_at, last_follow_up_at } = formData
      if (dayjs(follow_up_at).isBefore(dayjs(last_follow_up_at))) {
        const errMsg = '下次回访日期不能小于上次回访日期'
        message.error(errMsg)
        throw new Error(errMsg)
      }
    }

    const params = {
      ...formData,
      is_deliver: propertyConst.DELIVERATLABLE === type ? 1 : undefined,
      work_id: [propertyConst.QCAPPLYDELAYLABLE, propertyConst.PURCHASEAPPLYDELAYLABLE, propertyConst.DELIVERATLABLE].includes(type)
        ? work_id
        : undefined,
      id
    }

    if (type === propertyConst.REVISITATLABLE) {
      await UpdateRevisitRecord(params)
    } else {
      await updateDeliveryAt(params)
    }

    await closeModal()
    emit('success', { order_id: propsData.value.order_id ?? null })
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
  }
}
</script>
