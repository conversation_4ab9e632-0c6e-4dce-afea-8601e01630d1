<template>
  <BasicModal @register="registerModal" v-bind="$attrs" width="70%" title="查看出库预约" :showOkBtn="false">
    <BasicTable
      v-if="projectId"
      :key="projectId"
      :api="(params) => getBookingList({ ...params, project_number: projectId })"
      @register="registerTable"
    />
  </BasicModal>
</template>

<script setup lang="ts">
import BasicModal from '/@/components/Modal/src/BasicModal.vue'
import { useModalInner } from '/@/components/Modal'
import { BasicTable, useTable } from '/@/components/Table'
import { getBookingList } from '/@/api/erp/bookOutWarehouse'
import { columns } from '/@/views/erp/bookOutWarehouse/datas/datas'
import { ref } from 'vue'

const projectId = ref(null)

const [registerModal] = useModalInner((data) => {
  console.log(data)
  projectId.value = data.record.project_number
})

const [registerTable] = useTable({
  // api: getBookingList,
  showIndexColumn: false,
  columns,
  showTableSetting: false,
  rowKey: 'id',
  canResize: false
})
</script>
