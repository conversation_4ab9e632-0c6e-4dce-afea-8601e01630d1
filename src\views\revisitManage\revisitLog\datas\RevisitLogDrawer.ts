import { h } from 'vue'
import { pointsgetList } from '/@/api/baseData/pointmanagement'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { types } from '/@/views/baseData/pointmanagement/datas/data'
import { Tag } from 'ant-design-vue'

const general_options = [
  {
    label: '是',
    value: 1
  },
  {
    label: '否',
    value: 0
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'creator',
    label: '负责人',
    component: 'PagingApiSelect',
    required: true,
    componentProps: {
      api: getCreatorList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'cc_recipient',
    label: '抄送人',
    component: 'PagingApiSelect',
    required: true,
    componentProps: {
      api: getCreatorList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'points_manage_id',
    label: '工作指标',
    component: 'PagingApiSelect',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        api: pointsgetList,
        resultField: 'items',
        searchMode: true,
        params: {
          status: 1,
          is_disabled: 0
        },
        pagingMode: true,
        returnParamsField: 'id',
        selectProps: {
          fieldNames: { value: 'id', label: 'target' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          onChange(_, shall) {
            if (!shall) return
            // points_manage_shall.value = shall
            formModel.type = shall.type
            formModel.points = shall.points
            formModel.is_strid = shall.is_strid
            formModel.is_file = shall.is_file
            formModel.is_send = shall.is_send
          }
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'type',
    label: '积分类型',
    component: 'Select',
    componentProps: {
      options: Object.keys(types).map((item) => ({
        label: types[item].label,
        value: parseInt(item, 10)
      }))
    },
    ifShow(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    },
    dynamicDisabled(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    }
  },
  {
    field: 'points',
    label: '积分权重',
    component: 'Input',
    ifShow(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    },
    dynamicDisabled(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    }
  },
  {
    field: 'is_strid',
    label: '是否填写系统单号',
    component: 'Select',
    componentProps: {
      options: general_options
    },
    ifShow(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    },
    dynamicDisabled(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    }
  },
  {
    field: 'is_file',
    label: '是否需要上传附件',
    component: 'Select',
    componentProps: {
      options: general_options
    },
    ifShow(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    },
    dynamicDisabled(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    }
  },
  {
    field: 'is_send',
    label: '是否项目通知推送',
    component: 'Select',
    componentProps: {
      options: general_options
    },
    ifShow(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    },
    dynamicDisabled(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    }
  },
  {
    field: 'is_content',
    label: '是否填写备注',
    component: 'Select',
    componentProps: {
      options: general_options
    },
    ifShow(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    },
    dynamicDisabled(renderCallbackParams) {
      return !isNullOrUnDef(renderCallbackParams.values.points_manage_id)
    }
  },
  {
    field: 'matter_strid',
    label: '事项单号',
    component: 'Input',
    required(renderCallbackParams) {
      return renderCallbackParams.values.is_strid === 1
    }
  },
  {
    field: 'content',
    label: '回访描述',
    component: 'InputTextArea',
    required(renderCallbackParams) {
      return renderCallbackParams.values.is_content === 1
    }
  },
  {
    field: 'files',
    label: '附件',
    component: 'Upload',
    slot: 'Files',
    required(renderCallbackParams) {
      return renderCallbackParams.values.is_file === 1
    }
  }
]

export const columns: BasicColumn[] = [
  {
    title: '项目ID',
    dataIndex: 'project_number',
    width: 150,
    resizable: true
  },
  {
    title: '项目名称',
    dataIndex: 'project_name',
    width: 150,
    resizable: true
  },
  {
    title: '负责人',
    dataIndex: 'creator_name',
    width: 150,
    resizable: true
  },
  {
    title: '抄送人',
    dataIndex: 'cc_recipient_name',
    width: 150,
    resizable: true
  },
  {
    title: '工作指标',
    dataIndex: 'target',
    width: 150,
    resizable: true
  },
  {
    title: '积分权重',
    dataIndex: 'points',
    width: 150,
    resizable: true
  },
  {
    title: '工作指标解释',
    dataIndex: 'desc',
    width: 150,
    resizable: true
  },
  {
    title: '是否作废',
    dataIndex: 'is_cancel',
    width: 150,
    customRender: ({ value }) => {
      return !isNullOrUnDef(value) ? h(Tag, { color: value == 1 ? 'red' : 'green' }, () => (value == 1 ? '是' : '否')) : ''
    },
    resizable: true
  },
  {
    title: '作废人',
    dataIndex: 'cancel_inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '事项单号',
    dataIndex: 'matter_strid',
    width: 150,
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 150,
    resizable: true
  },
  {
    title: '作废时间',
    dataIndex: 'cancel_at',
    width: 150,
    resizable: true
  },
  {
    title: '回访描述',
    dataIndex: 'content',
    width: 150,
    resizable: true
  },
  {
    title: '作废备注',
    dataIndex: 'cancel_remark',
    width: 150,
    resizable: true
  },
  {
    title: '附件',
    dataIndex: 'files',
    width: 150,
    resizable: true
  }
]
