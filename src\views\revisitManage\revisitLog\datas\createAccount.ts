import { FormSchema } from '/@/components/Form'

export const schemas: FormSchema[] = [
  {
    label: '客户账号',
    field: 'username',
    component: 'Input',
    required: true
  },
  {
    label: '密码',
    field: 'password',
    component: 'Input',
    required: true,
    rules: [
      {
        required: true,
        message: '请记住修改的密码, 下次登录时使用,修改成功后不再显示密码',
        trigger: 'blur'
      }
    ]
  }
]
