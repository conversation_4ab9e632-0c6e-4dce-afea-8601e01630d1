import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { isNull, isNullOrUnDef, isUnDef } from '/@/utils/is'
import { getStaffList } from '/@/api/baseData/staff'
import { cloneDeep, isEqual } from 'lodash-es'
import * as propertyConst from './const'
import dayjs from 'dayjs'
import { renderCell } from './fn'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { getDeptTree } from '/@/api/admin/dept'
import { getClientList } from '/@/api/commonUtils'
const saleStore = useSaleOrderStore()

const commonSelectProp: FormSchema = {
  label: '',
  field: '',
  component: 'Select',
  componentProps: {
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 0 }
    ]
  }
}

export const mapStatus = {
  0: {
    label: 'Waiting Comfirmed', //待执行
    color: '#08979c',
    status: 'process',
    index: 0,
    description: 'waiting_comfirmed_num'
  },
  1: {
    label: 'Comfirmed', //执行中
    color: '#b7eb8f',
    status: 'process',
    index: 1,
    description: 'comfirmed_num'
  },
  2: {
    label: 'Available', //可备货
    color: '#f50',
    status: 'process',
    index: 2,
    description: 'available_num'
  },
  3: {
    label: 'Process', //备货中
    color: '#f50',
    status: 'process',
    index: 3,
    description: 'process_num'
  },
  4: {
    label: 'Received Warehouse', //已入库
    color: '#f50',
    status: 'process',
    index: 4,
    description: 'received_warehouse_num'
  },
  5: {
    label: 'Delivery', //出库中
    color: '#b7eb8f',
    status: 'finish',
    index: 5,
    description: 'delivery_num'
  }
}

export const cancelStatus = {
  15: { label: 'Finished', color: '#b7eb8f', status: 'finish' }, //已结束
  16: { label: 'Cancelled', color: 'red', status: 'error' } //已取消
}

export const baseStatusArray = ['Waiting Comfirmed', 'Comfirmed', 'Available', 'Process', 'Received Warehouse', 'Delivery']

export const allStatus = { ...cloneDeep(mapStatus), ...cloneDeep(cancelStatus) }

export const mapProductStatus = {
  0: { label: 'Waiting Comfirmed', color: 'green' },
  1: { label: 'Order Comfirming', color: 'green' },
  2: { label: 'In production', color: 'green' },
  3: { label: 'Production and stocking in progress', color: 'green' },
  4: {
    label: 'Production completed and waiting for QC',
    color: 'green'
  },
  5: { label: 'QC finish and wait for loading', color: 'green' },
  6: { label: 'Delivery', color: '#b7eb8f' }
}

export const mapType = {
  1: { label: 'Comfimrd', color: '#b7eb8f' },
  2: { label: 'Process', color: '#08979c' },
  3: { label: 'Finished', color: '#b7eb8f' },
  4: { label: 'Warehouse', color: '#08979c' },
  5: { label: 'QC', color: '#08979c' },
  6: { label: 'Warehouse', color: '#08979c' },
  7: { label: 'Process', color: '#08979c' },
  8: { label: 'Comfirmed', color: '#b7eb8f' },
  9: { label: 'Warehouse', color: '#b7eb8f' },
  10: { label: 'Delivery', color: '#b7eb8f' }
}

export const mapWay = {
  1: { label: 'whatsapp', icon: 'whats-app-outlined' },
  2: { label: '微信', icon: 'wechat-outlined' }
}

const iabilitytype = {
  1: { label: '销售', color: '#b7eb8f' },
  2: { label: '拆单员', color: '#b7eb8f' },
  3: { label: '采购员', color: '#b7eb8f' },
  4: { label: '采购跟进', color: '#b7eb8f' },
  5: { label: '质检', color: '#b7eb8f' },
  6: { label: '装箱员', color: '#b7eb8f' },
  7: { label: '采购主管审核', color: '#b7eb8f' },
  8: { label: '质检审核', color: '#b7eb8f' }
}

export const columns: BasicColumn[] = [
  {
    dataIndex: propertyConst.PROJECTNUMBER,
    title: propertyConst.PROJECTNUMBERLABEL,
    width: 120,
    resizable: true
  },
  {
    dataIndex: propertyConst.PROJECTNAME,
    title: propertyConst.PROJECTNAMELABEL,
    width: 120,
    resizable: true,
    customRender: renderCell
  },
  {
    dataIndex: propertyConst.CLIENTNAME,
    title: propertyConst.CLIENTNAMEELABEL,
    width: 120,
    resizable: true,
    customRender: renderCell
  },
  {
    dataIndex: propertyConst.GROUPTYPE,
    title: propertyConst.GROUPTYPELABEL,
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      if (isNull(value) || isUnDef(value)) return ''
      return h('span', null, mapWay[value].label)
    }
  },
  {
    dataIndex: propertyConst.GROUPNAME,
    title: propertyConst.GROUPNAMELABEL,
    width: 120,
    resizable: true,
    customRender: renderCell
  },
  {
    dataIndex: propertyConst.GROUPAT,
    title: propertyConst.GROUPATLABLE,
    width: 120,
    resizable: true,
    customRender: ({ value }) => (isNull(value) || isUnDef(value) ? '' : h('span', null, dayjs(value).format('YYYY-MM-DD HH:mm:ss')))
  },
  {
    dataIndex: 'delivery_incharge_name',
    title: '交付经理',
    width: 120,
    resizable: true,
    customRender: renderCell
  },
  {
    dataIndex: propertyConst.DELIVERYATSTART,
    title: propertyConst.DELIVERYATSTARTLABLE,
    width: 120,
    resizable: true,
    customRender: ({ value }) => (isNull(value) || isUnDef(value) ? '' : h('span', null, dayjs(value).format('YYYY-MM-DD HH:mm:ss')))
  },
  {
    dataIndex: propertyConst.DELIVERYATEND,
    title: propertyConst.DELIVERYATENDLABLE,
    width: 120,
    resizable: true,
    customRender: ({ value }) => (isNull(value) || isUnDef(value) ? '' : h('span', null, dayjs(value).format('YYYY-MM-DD HH:mm:ss')))
  },
  {
    dataIndex: 'qc_num',
    title: '质检报告数',
    width: 80,
    resizable: true,
    customRender: renderCell
  },
  {
    dataIndex: 'submited_at',
    title: '开单日期',
    width: 110,
    customRender: renderCell
  },
  {
    dataIndex: 'belong_department',
    title: '所属部门',
    width: 110,
    resizable: true,
    customRender: renderCell
  },
  {
    title: '运营中心',
    dataIndex: 'operation_department',
    width: 120,
    resizable: true,
    customRender: renderCell
  },
  {
    dataIndex: propertyConst.FOLLOWUPAT,
    title: propertyConst.FOLLOWUPATLABLE,
    width: 120,
    fixed: 'right',
    resizable: true,
    customRender: ({ value }) => {
      if (isNull(value) || isUnDef(value)) return ''
      //是否已经过了回访日期
      const isLater = new Date(value).getTime() < new Date().getTime()
      // return useRender.renderTag(`${isLater ? '已逾期' : ''} ${value}`, isLater ? '#cd201f' : '#87d068')
      return h('div', { style: { color: '#fff', background: isLater ? '#cd201f' : '#87d068' } }, [
        h('div', null, isLater ? '已逾期' : ''),
        h('div', null, value)
      ])
    }
  },
  {
    dataIndex: propertyConst.LASTFOLLOWUPAT,
    title: propertyConst.LASTFOLLOWUPATLABLE,
    width: 120,
    fixed: 'right',
    resizable: true,
    customRender: ({ value }) => (isNull(value) || isUnDef(value) ? '' : h('span', null, dayjs(value).format('YYYY-MM-DD HH:mm:ss')))
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'submited_at',
    label: '开单日期',
    component: 'SingleRangeDate',
    // 默认值是今年的一月一日到今天
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'status',
    label: '项目状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '进行中', value: 1 },
        { label: '已发运', value: 2 }
      ]
    }
  },
  {
    field: propertyConst.PROJECTNUMBER,
    label: propertyConst.PROJECTNUMBERLABEL,
    component: 'Input'
  },
  {
    field: propertyConst.PROJECTNAME,
    label: propertyConst.PROJECTNAMELABEL,
    component: 'Input'
  },
  {
    field: propertyConst.GROUPTYPE,
    label: propertyConst.GROUPTYPELABEL,
    component: 'Select',
    componentProps: {
      options: Object.keys(mapWay).map((key) => ({ label: mapWay[key].label, value: Number(key) }))
    }
  },
  {
    field: propertyConst.GROUPNAME,
    label: propertyConst.GROUPNAMELABEL,
    component: 'Input'
  },
  {
    field: propertyConst.DELIVERYINCHARGE,
    label: propertyConst.DELIVERYINCHARGELABEL,
    component: 'ApiSelect',
    componentProps({}) {
      return {
        api: getStaffList,
        resultField: 'items',
        selectProps: {
          allowClear: true,
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        },
        params: {
          pageSize: 9999
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'source_uniqid',
    label: '销售单号',
    component: 'Input'
  },
  {
    field: propertyConst.FOLLOWUPAT,
    label: propertyConst.FOLLOWUPATLABLE,
    component: 'SingleRangeDate'
  },
  {
    field: propertyConst.DELIVERYATSTART,
    label: propertyConst.DELIVERYATSTARTLABLE,
    component: 'DatePicker',
    componentProps: {
      style: 'width:100%',
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  {
    field: propertyConst.DELIVERYATEND,
    label: propertyConst.DELIVERYATENDLABLE,
    component: 'DatePicker',
    componentProps: {
      style: 'width:100%',
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  { ...commonSelectProp, field: 'group_name_is_null', label: '群名称为空' },
  {
    field: 'belong_dept_id',
    label: '归属部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        placeholder: '请选择',
        showSearch: true,
        optionFilterProp: 'name',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  {
    ...commonSelectProp,
    field: 'belong_dept_id_is_null',
    label: '归属部门为空'
  },
  {
    ...commonSelectProp,
    field: 'delivery_incharge_is_null',
    label: '交付经理为空'
  },
  {
    field: 'is_overdue',
    label: '是否逾期',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    field: 'inCharges',
    label: '项目负责人',
    component: 'ApiSelect',
    // componentProps: ({ formModel }) => {
    //   return {
    //     api: getStaffList,
    //     resultField: 'items',
    //     params: { dept_id: formModel.belong_dept_id },
    //     selectProps: {
    //       fieldNames: { key: 'key', value: 'id', label: 'name' },
    //       showSearch: true,
    //       placeholder: '请选择',
    //       optionFilterProp: 'name',
    //       mode: 'multiple'
    //     }
    //   }
    // },

    slot: 'InCharges'
  },
  {
    field: 'iability_type',
    label: '责任人搜索',
    component: 'Select',
    // show: url == '/erpFlow/deliveryResponDivision' ? true : false,
    componentProps: {
      options: Object.keys(iabilitytype).map((key) => ({ label: iabilitytype[key].label, value: Number(key) }))
    }
  },
  {
    field: 'client_id',
    label: '客户',
    component: 'PagingApiSelect',
    componentProps: {
      api: getClientList,
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      },
      resultField: 'items'
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'est_finished_at',
    label: '任务结束日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  }
]

export const saleColumns: BasicColumn[] = [
  {
    title: '销售单号',
    dataIndex: 'source_uniqid',
    width: 120,
    resizable: true,
    customRender: renderCell
  },
  {
    title: '订单状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : useRender.renderTag(saleStore.saleStatus[value], saleStore.statusColor[value])
    }
  },
  {
    title: '开单时间',
    dataIndex: 'created_at',
    width: 120,
    resizable: true,
    customRender: renderCell
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 120,
    resizable: true,
    customRender: renderCell
  },
  // {
  //   title: '客户',
  //   dataIndex: 'client_name',
  //   width: 120,
  //   resizable: true,
  //   customRender: renderCell
  // },
  {
    title: '项目负责人',
    dataIndex: 'inCharge_name',
    width: 120,
    resizable: true,
    customRender: renderCell
  },
  {
    title: '方案经理',
    dataIndex: 'creator_name',
    width: 120,
    resizable: true,
    customRender: renderCell
  },
  {
    title: '交付经理',
    dataIndex: 'delivery_incharge_name',
    width: 120,
    resizable: true,
    customRender: renderCell
  },
  {
    title: '责任人',
    dataIndex: 'iability_type',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return text ? iabilitytype[text].label : '-'
    }
  },
  {
    title: '交付日期',
    dataIndex: 'deliver_at',
    width: 120,
    resizable: true,
    customRender: renderCell
  },
  {
    title: '任务结束日期',
    dataIndex: 'est_finished_at',
    width: 120,
    resizable: true,
    customRender: renderCell
  },
  {
    title: '交货日期',
    dataIndex: 'delivery_at',
    width: 120,
    resizable: true,
    customRender: renderCell
  },
  {
    title: '质检报告数',
    dataIndex: 'qc_num',
    width: 120,
    resizable: true,
    helpMessage: '上次回访日期到当前日期的质检报告数',
    customRender: renderCell
  },
  {
    dataIndex: 'qc_rate',
    title: '质检率',
    width: 80,
    resizable: true,
    customRender: renderCell
  }
]

export const productColumns: BasicColumn[] = [
  {
    title: '商品',
    dataIndex: 'product',
    width: 180,
    align: 'left',
    resizable: true
  },
  {
    title: '描述',
    dataIndex: 'desc',
    width: 180,
    customRender: ({ value }) => (isNull(value) || isUnDef(value) ? '' : h('span', null, value)),
    resizable: true
  },
  {
    title: '订单实际需求数量',
    dataIndex: 'qty_request_actual',
    width: 180,
    resizable: true
  },
  {
    //商品状态
    title: '状态',
    dataIndex: 'status',
    width: 200,
    resizable: true,
    customRender: ({ value }) =>
      isNull(value) || isUnDef(value) ? '' : useRender.renderTag(mapProductStatus[value].label, mapProductStatus[value].color)
  },
  {
    title: 'Comfimrd',
    dataIndex: 'comfimrd',
    width: 120,
    resizable: true,
    customRender: ({ value, record }) =>
      useRender.renderTag(value ?? '', isEqual(Number(record.qty_request_actual), Number(value)) ? 'green' : 'red')
  },
  {
    title: 'Process',
    dataIndex: 'process',
    width: 120,
    resizable: true,
    customRender: ({ value, record }) =>
      useRender.renderTag(value ?? '', isEqual(Number(record.qty_request_actual), Number(value)) ? 'green' : 'red')
  },
  {
    title: 'Finished',
    dataIndex: 'finished',
    width: 120,
    resizable: true,
    customRender: ({ value, record }) =>
      useRender.renderTag(value ?? '', isEqual(Number(record.qty_request_actual), Number(value)) ? 'green' : 'red')
  },
  {
    title: 'QC',
    dataIndex: 'qc',
    width: 120,
    resizable: true,
    customRender: ({ value, record }) =>
      useRender.renderTag(value ?? '', isEqual(Number(record.qty_request_actual), Number(value)) ? 'green' : 'red')
  },
  {
    title: 'Warehouse',
    dataIndex: 'warehouse',
    width: 120,
    resizable: true,
    customRender: ({ value, record }) =>
      useRender.renderTag(value ?? '', isEqual(Number(record.qty_request_actual), Number(value)) ? 'green' : 'red')
  },
  {
    title: 'Delivery',
    dataIndex: 'delivery',
    width: 120,
    resizable: true,
    customRender: ({ value, record }) =>
      useRender.renderTag(value ?? '', isEqual(Number(record.qty_request_actual), Number(value)) ? 'green' : 'red')
  }
]

export const productStatusColumns: BasicColumn[] = [
  {
    title: '类型描述',
    dataIndex: 'type',
    width: 200,
    customRender: ({ value }) => (isNull(value) || isUnDef(value) ? '' : useRender.renderTag(mapType[value].label, mapType[value].color))
  },
  {
    title: '状态描述-英文',
    dataIndex: 'type_desc_english',
    width: 150
  },
  {
    title: '状态描述-中文',
    dataIndex: 'type_desc_chinese',
    width: 150
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    width: 100,
    customRender: ({ value }) => (isNull(value) || isUnDef(value) ? '-' : h('span', null, value))
  },
  {
    title: '日期',
    dataIndex: 'created_at',
    width: 180
  }
]
