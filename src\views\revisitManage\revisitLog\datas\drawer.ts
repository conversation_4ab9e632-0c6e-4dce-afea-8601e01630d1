import { FormSchema } from '/@/components/Form'
import { mapWay } from './datas'
import { getStaffList } from '/@/api/baseData/staff'
import * as propertyConst from './const'

export const schemas: FormSchema[] = [
  {
    field: propertyConst.GROUPTYPE,
    label: propertyConst.GROUPTYPELABEL,
    component: 'Select',
    componentProps: {
      options: Object.keys(mapWay).map((key) => ({ label: mapWay[key].label, value: Number(key) }))
    }
  },
  {
    field: propertyConst.GROUPNAME,
    label: propertyConst.GROUPNAMELABEL,
    component: 'Input'
  },
  {
    label: propertyConst.GROUPATLABLE,
    field: propertyConst.GROUPAT,
    component: 'DatePicker',
    componentProps: {
      style: { width: '100%' }
    }
  },
  {
    field: propertyConst.DELIVERYINCHARGE,
    label: propertyConst.DELIVERYINCHARGELABEL,
    required: true,
    component: 'ApiSelect',
    componentProps({}) {
      return {
        api: getStaffList,
        resultField: 'items',
        selectProps: {
          allowClear: true,
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        },
        params: {
          pageSize: 9999
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  // {
  //   label: propertyConst.LASTFOLLOWUPATLABLE,
  //   field: propertyConst.LASTFOLLOWUPAT,
  //   component: 'DatePicker',
  //   required: true,
  //   componentProps: {
  //     style: { width: '100%' }
  //   }
  // },
  {
    label: propertyConst.FOLLOWUPATLABLE,
    field: propertyConst.FOLLOWUPAT,
    component: 'DatePicker',
    componentProps: {
      style: { width: '100%' }
    }
  }
]
