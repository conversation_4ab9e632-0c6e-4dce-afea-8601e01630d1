import { FormSchema } from '/@/components/Form'
import * as propertyConst from './const'
import dayjs from 'dayjs'
import type { Dayjs } from 'dayjs'

export const deliverSchemas: (type: '回访日期' | '交付日期' | '延期日期') => FormSchema[] = (type) => [
  {
    field: 'deliver_at',
    label: propertyConst.DELIVERATLABLE,
    component: 'DatePicker',
    componentProps: {
      style: { width: '100%' },
      valueFormat: 'YYYY-MM-DD 23:59:59',
      showToday: false,
      disabledDate: (current: Dayjs) => {
        return current && current < dayjs().endOf('day')
      }
    },
    required: true,
    ifShow: [propertyConst.DELIVERATLABLE, propertyConst.QCAPPLYDELAYLABLE].includes(type)
  },
  {
    field: 'request_status_at',
    label: '需求生产完成日期',
    component: 'DatePicker',
    componentProps: {
      style: { width: '100%' },
      valueFormat: 'YYYY-MM-DD 00:00:00',
      showToday: false,
      disabledDate: (current: Dayjs) => {
        return current && current < dayjs().endOf('day')
      }
    },
    required: true,
    ifShow: [propertyConst.PURCHASEAPPLYDELAYLABLE].includes(type)
  },
  {
    field: 'remark',
    label: '延期原因',
    component: 'InputTextArea',
    ifShow: [propertyConst.DELIVERATLABLE, propertyConst.QCAPPLYDELAYLABLE, propertyConst.PURCHASEAPPLYDELAYLABLE].includes(type),
    required: true
  },
  {
    label: propertyConst.LASTFOLLOWUPATLABLE,
    field: propertyConst.LASTFOLLOWUPAT,
    component: 'DatePicker',
    required: true,
    componentProps: {
      style: { width: '100%' },
      valueFormat: 'YYYY-MM-DD 00:00:00',
      disabledDate: (current: Dayjs) => {
        return current && current > dayjs().endOf('day')
      }
    },
    ifShow: [propertyConst.REVISITATLABLE].includes(type)
  },
  {
    label: propertyConst.FOLLOWUPATLABLE,
    field: propertyConst.FOLLOWUPAT,
    component: 'DatePicker',
    required: true,
    componentProps: {
      valueFormat: 'YYYY-MM-DD 00:00:00',
      style: { width: '100%' }
    },
    ifShow: [propertyConst.REVISITATLABLE].includes(type)
  }
]
