import dayjs from 'dayjs'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { getcancel, getFundType, getStatus } from '/@/views/financialDocuments/capitalFlow/datas/fn'
import { h } from 'vue'
import { Tag } from 'ant-design-vue'
import { isNullAndUnDef, isNullOrUnDef } from '/@/utils/is'

export const columns: BasicColumn[] = [
  {
    title: '单号',
    dataIndex: 'strid',
    width: 200,
    resizable: true
  },
  {
    title: '资金调拨单号',
    dataIndex: 'allot_strid',
    width: 200,
    resizable: true
  },
  {
    title: '创建日期',
    dataIndex: 'created_at',
    width: 100,
    resizable: true
  },
  {
    title: '审批日期',
    dataIndex: 'status_at',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? dayjs(text).format('YYYY-MM-DD') : '-'
    }
  },
  {
    title: '收/付款日期',
    dataIndex: 'occurrence_at',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? dayjs(text).format('YYYY-MM-DD') : '-'
    }
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 120,
    customRender: ({ value }) => {
      return value ? value : '-'
    }
  },
  {
    title: '创建人',
    dataIndex: 'creator_name',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '负责人',
    dataIndex: 'inCharge_name',
    width: 100,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '项目负责人',
    dataIndex: 'project_inCharge_name',
    width: 100,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '客户',
    dataIndex: 'client_name',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '付款资金资料',
    dataIndex: 'from_plaform',
    width: 100,
    resizable: true
  },
  {
    title: '付款资金货币',
    dataIndex: 'from_currency',
    width: 100,
    resizable: true
  },
  {
    title: '收款资金资料',
    dataIndex: 'to_plaform',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '收款资金货币',
    dataIndex: 'to_currency',
    width: 100,
    resizable: true
  },
  {
    title: '汇率',
    dataIndex: 'rate',
    width: 100,
    resizable: true,
    customRender: ({ record }) => {
      return record.rate ? `${record.from_currency}-${formateerNotCurrency.format(record.rate)}` : '0.00'
    }
  },
  {
    title: '外汇手续费',
    dataIndex: 'fgfee_amount',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(text) : '0.00'
    }
  },
  {
    title: '手续费',
    dataIndex: 'fee',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(text) : '0.00'
    }
  },
  {
    title: '外汇金额',
    dataIndex: 'fg_amount',
    width: 100,
    resizable: true,
    customRender: ({ record, text }) => {
      if (record.type == 2) {
        return text ? `-${formateerNotCurrency.format(text)}` : '0.00'
      } else {
        return text ? formateerNotCurrency.format(text) : '0.00'
      }
    }
  },
  {
    title: '金额',
    dataIndex: 'amount',
    width: 100,
    resizable: true,
    customRender: ({ record, text }) => {
      if (record.type == 2) {
        return text ? `-${formateerNotCurrency.format(text)}` : '0.00'
      } else {
        return text ? formateerNotCurrency.format(text) : '0.00'
      }
    }
  },
  {
    title: '剩余金额',
    dataIndex: 'amount_left',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(text) : '0.00'
    }
  },
  {
    title: '信保单号',
    dataIndex: 'insurance_strid',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '信保外汇金额',
    dataIndex: 'fgins_amount',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(text) : '0.00'
    }
  },
  {
    title: '信保金额',
    dataIndex: 'insurance_amount',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? formateerNotCurrency.format(text) : '0.00'
    }
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '核对备注',
    dataIndex: 'check_remark',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return text ? text : '-'
    }
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 100,
    resizable: true,
    customRender: ({ record }) => {
      return getFundType(record)
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return getStatus(text)
    }
  },
  {
    title: '是否作废',
    dataIndex: 'is_cancel',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return getcancel(text)
    }
  },
  {
    title: '是否提现完成',
    dataIndex: 'carry_finish',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return h(Tag, { color: isNullOrUnDef(text) ? 'red' : text ? 'green' : 'red' }, isNullOrUnDef(text) ? '否' : text ? '是' : '否')
    }
  },
  {
    title: '提现手续费',
    dataIndex: 'c_fee',
    width: 100,
    resizable: true
  },
  {
    title: '汇兑损益',
    dataIndex: 'pl_amount',
    width: 100,
    resizable: true
  },
  {
    title: '是否提现流水',
    dataIndex: 'is_carry',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return isNullAndUnDef(text) ? '否' : text ? '是' : '否'
    }
  }
]

export const childRenColumns: BasicColumn[] = [
  {
    title: '销售单号',
    dataIndex: 'source_uniqid',
    width: 120,
    resizable: true
  },
  {
    title: '开单日期',
    dataIndex: 'submited_at',
    width: 120,
    resizable: true
  },
  //   {
  //     title: '当前应收金额',
  //     dataIndex: 'receivable_left',
  //     width: 120,
  //     resizable: true
  //   },
  {
    title: '应付金额',
    dataIndex: 'receivable_left',
    width: 120,
    resizable: true
  },
  {
    title: '实收金额',
    dataIndex: 'received_actual',
    width: 120,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'is_cus',
    label: '客户是否为空',
    component: 'Select',
    defaultValue: 1,
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  },
  {
    field: 'is_sup',
    label: '供应商是否为空',
    component: 'Select',
    defaultValue: 1,
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  },
  {
    field: 'is_residue',
    label: '剩余金额是否大于0',
    component: 'Select',
    defaultValue: 1,
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  }
]
