<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <!-- <Button type="primary" @click="handleup">更新报表</Button> -->
      </template>
      <!-- <template #expandedRowRender="{ record }">
        <div class="extend-table-container">
          <BasicTable
            :key="record.status"
            :ref="(el) => (expandedRowRefs[record.id] = el)"
            class="p-4"
            @register="registerChildrenTable"
            :api="clientgetPurList.bind(null, { client_id: record.client_id })"
          />
        </div>
      </template> -->
    </BasicTable>
  </div>
</template>
<script setup lang="ts">
// import { ref } from 'vue'
import { columns, schemas } from './datas/datas'
import { BasicTable, useTable } from '/@/components/Table'
// import { Button } from 'ant-design-vue'
// import { clientgetPurList } from '/@/api/statement/Customerreport'
import { getFundIsNullList } from '/@/api/statement/Currentstatement'

// const expandedRowRefs = ref<{ [key: string]: TableActionType | Nullable<any> }>({})

const [registerTable] = useTable({
  title: '流水报表',
  showTableSetting: true,
  columns,
  api: getFundIsNullList,
  useSearchForm: true,
  formConfig: {
    labelWidth: 120,
    schemas: schemas,
    autoSubmitOnEnter: true,
    baseColProps: { span: 6 },
    labelCol: { span: 8 }
  }
})

// /** 注册子表格 */
// const [registerChildrenTable, {}] = useTable({
//   showIndexColumn: false,
//   columns: childRenColumns,
//   showTableSetting: false
// })

// function handleup() {
//   setLoading(true)
//   setProps({
//     api: getFundIsNullList,
//     searchInfo: {
//       no_cache: 1
//     }
//   })
//   reload()
//   setTimeout(() => {
//     setProps({
//       api: getFundIsNullList,
//       searchInfo: {}
//     })
//     setLoading(false)
//   }, 500)
// }
</script>
