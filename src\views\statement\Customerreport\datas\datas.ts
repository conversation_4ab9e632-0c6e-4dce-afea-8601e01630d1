import { getClientList } from '/@/api/commonUtils'
import { BasicColumn, FormSchema } from '/@/components/Table'

export const columns: BasicColumn[] = [
  {
    title: '客户名称',
    dataIndex: 'client_name',
    resizable: true,
    width: 120
  },
  {
    title: '总应收金额',
    dataIndex: 'receivable_lefts',
    width: 120
  },
  {
    title: '预收金额',
    dataIndex: 'amount_exp',
    width: 120
  },
  {
    title: '当前应收金额',
    dataIndex: 'amount_curve',
    width: 120,
    resizable: true
  },
  {
    title: '总实收金额',
    dataIndex: 'received_actuals',
    width: 120,
    resizable: true
  },

  {
    title: '出库未收金额',
    dataIndex: 'amount_out',
    width: 120,
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 120,
    resizable: true
  }
]

export const childRenColumns: BasicColumn[] = [
  {
    title: '销售单号',
    dataIndex: 'source_uniqid',
    width: 120,
    resizable: true
  },
  {
    title: '开单日期',
    dataIndex: 'submited_at',
    width: 120,
    resizable: true
  },
  //   {
  //     title: '当前应收金额',
  //     dataIndex: 'receivable_left',
  //     width: 120,
  //     resizable: true
  //   },
  {
    title: '应付金额',
    dataIndex: 'receivable_left',
    width: 120,
    resizable: true
  },
  {
    title: '实收金额',
    dataIndex: 'received_actual',
    width: 120,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'is_curve',
    label: '当前应收金额是否大于0',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '等于',
          value: 0
        },
        {
          label: '大于',
          value: 1
        }
      ]
    }
  },
  {
    field: 'is_exp',
    label: '预收金额是否大于0',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '等于',
          value: 0
        },
        {
          label: '大于',
          value: 1
        }
      ]
    }
  },
  {
    field: 'client_id',
    label: '客户',
    component: 'ApiSelect',
    componentProps: {
      api: getClientList,
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      },
      resultField: 'items'
    }
  }
]
