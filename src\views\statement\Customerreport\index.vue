<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleup">更新报表</Button>
      </template>
      <template #expandedRowRender="{ record }">
        <div class="extend-table-container">
          <BasicTable
            :key="record.status"
            :ref="(el) => (expandedRowRefs[record.id] = el)"
            class="p-4"
            @register="registerChildrenTable"
            :searchInfo="{ client_id: record.client_id }"
          />
        </div>
      </template>
    </BasicTable>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { childRenColumns, columns, schemas } from './datas/datas'
import { BasicTable, TableActionType, useTable } from '/@/components/Table'
import { Button } from 'ant-design-vue'
import { clientgetList, clientgetPurList } from '/@/api/statement/Customerreport'

const expandedRowRefs = ref<{ [key: string]: TableActionType | Nullable<any> }>({})

const [registerTable, { setLoading, setProps, reload }] = useTable({
  title: '客户报表',
  showTableSetting: true,
  columns,
  api: clientgetList,
  useSearchForm: true,
  formConfig: {
    labelWidth: 120,
    schemas: schemas,
    autoSubmitOnEnter: true,
    baseColProps: { span: 6 },
    labelCol: { span: 8 }
  }
})

/** 注册子表格 */
const [registerChildrenTable, {}] = useTable({
  showIndexColumn: false,
  columns: childRenColumns,
  showTableSetting: false,
  api: clientgetPurList
})

function handleup() {
  setLoading(true)
  setProps({
    api: clientgetList,
    searchInfo: {
      no_cache: 1
    }
  })
  reload()
  setTimeout(() => {
    setProps({
      api: clientgetList
    })
    setLoading(false)
  }, 500)
}
</script>
