import { getErpSupplier } from '/@/api/commonUtils'
import { BasicColumn, FormSchema } from '/@/components/Table'

export const columns: BasicColumn[] = [
  {
    title: '供应商名称',
    dataIndex: 'supplier_name',
    resizable: true,
    width: 120
  },

  {
    title: '总应付金额',
    dataIndex: 'cost_lefts',
    width: 120
  },
  {
    title: '预付金额',
    dataIndex: 'amount_exp',
    width: 120
  },
  {
    title: '当前应付金额',
    dataIndex: 'amount_curve',
    width: 120,
    resizable: true
  },
  {
    title: '总实付金额',
    dataIndex: 'paid_actuals',
    width: 120,
    resizable: true
  },
  {
    title: '入库未付金额',
    dataIndex: 'amount_inno',
    width: 120,
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 120,
    resizable: true
  }
]

export const childRenColumns: BasicColumn[] = [
  {
    title: '采购单号',
    dataIndex: 'strid',
    width: 120,
    resizable: true
  },
  {
    title: '采购日期',
    dataIndex: 'status_at',
    width: 120,
    resizable: true
  },
  {
    title: '当前应付金额',
    dataIndex: 'cost_curve',
    width: 120,
    resizable: true
  },
  {
    title: '应付金额',
    dataIndex: 'cost_left',
    width: 120,
    resizable: true
  },
  {
    title: '实付金额',
    dataIndex: 'paid_actual',
    width: 120,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  {
    field: 'is_curve',
    label: '当前应付金额是否大于0',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '大于',
          value: 1
        },
        {
          label: '等于',
          value: 0
        }
      ]
    }
  },
  {
    field: 'is_exp',
    label: '预付金额是否大于0',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '大于',
          value: 1
        },
        {
          label: '等于',
          value: 0
        }
      ]
    }
  },
  {
    field: 'supplier_id',
    label: '供应商',
    component: 'PagingApiSelect',
    componentProps: () => {
      return {
        api: getErpSupplier,
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        },
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        resultField: 'items'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  }
]
