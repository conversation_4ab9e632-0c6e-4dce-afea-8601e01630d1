<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleup">更新报表</Button>
      </template>
      <template #expandedRowRender="{ record }">
        <div>
          <BasicTable
            :key="record.status"
            :ref="(el) => (expandedRowRefs[record.id] = el)"
            class="p-4"
            @register="registerChildrenTable"
            :searchInfo="{ supplier_id: record.supplier_id }"
          />
        </div>
      </template>
    </BasicTable>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { childRenColumns, columns, schemas } from './datas/datas'
import { BasicTable, TableActionType, useTable } from '/@/components/Table'
import { Button } from 'ant-design-vue'
import { supgetList, supgetPurList } from '/@/api/statement/supplierstatement'

const expandedRowRefs = ref<{ [key: string]: TableActionType | Nullable<any> }>({})
const gesize = ref(10)
const pages = ref(1)

const [registerTable, { setLoading, setProps, reload }] = useTable({
  title: '供应商报表',
  showTableSetting: true,
  columns,
  api: supgetList,
  useSearchForm: true,
  formConfig: {
    labelWidth: 120,
    schemas: schemas,
    autoSubmitOnEnter: true,
    baseColProps: { span: 6 },
    labelCol: { span: 8 }
  }
})

/** 注册子表格 */
const [registerChildrenTable, {}] = useTable({
  showIndexColumn: false,
  columns: childRenColumns,
  showTableSetting: false,
  api: supgetPurList,
  pagination: {
    onChange(page, pageSize) {
      gesize.value = pageSize
      pages.value = page
      console.log(page, pageSize)
    }
  }
})

function handleup() {
  setLoading(true)
  setProps({
    api: supgetList,
    searchInfo: {
      no_cache: 1
    }
  })
  reload()
  setTimeout(() => {
    setProps({
      api: supgetList,
      searchInfo: {}
    })
    setLoading(false)
  }, 500)
}
</script>
