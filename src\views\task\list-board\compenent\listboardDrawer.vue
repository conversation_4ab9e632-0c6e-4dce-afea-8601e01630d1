<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" show-footer title="查看详情" width="90%" @ok="handleSubmit">
    <div v-if="type === 'detail'">
      <Description @register="registerDesc1" :data="record" :column="detailsschemas1" title="订单基本信息" />
      <Description @register="registerDesc2" :data="record" :column="detailsschemas2" title="订单金额信息" />
      <Description @register="registerDesc3" :data="record" :column="detailsschemas3" title="订单时间信息" />
    </div>
    <div v-else>
      <BasicForm @register="registerForm">
        <!-- <template #exchange_rate>
          <div style="display: flex; align-items: contents">
            <Select v-model:value="exchange_rate" @change="changeselect" :options="exchangedata" style="width: 88%" />
            <div style="width: 12%; height: 32px; line-height: 32px; text-align: center; border: 1px solid #ccc">{{
              exchange_rate_lable
            }}</div>
          </div>
        </template> -->
      </BasicForm>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref, unref } from 'vue'

import { message } from 'ant-design-vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { Description, useDescription } from '/@/components/Description'
// import { getcustomerList } from '/@/api/financialDocuments/otherIncome'
import { getTaskadd, getTaskupdate } from '/@/api/task/task'
// import { Select } from 'ant-design-vue'
// import { getRmbquot } from '/@/api/erp/sales'
import { detailsschemas1, detailsschemas2, detailsschemas3, updateschemasFn } from '../datas/Drawer'

//先置条件
const type = ref()
// const width = ref()
const record = ref()
// const id = ref()
const propData = ref()

const [registerDrawer, { changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  resetFields()
  // getRmbquotFn()
  const { type } = data
  propData.value = data
  if (type == 'detail') {
    // width.value = '70%'
    record.value = data.record
  } else if (type == 'edit') {
    const datavisa = ref()
    if (data.record.receivable) {
      datavisa.value = '1'
    } else {
      datavisa.value = '2'
    }
    // width.value = '40%'
    // id.value = data.record.id
    await resetSchema(await updateschemasFn(datavisa.value))
    await setFieldsValue({ currency: '人民币' })
    console.log(data.record, 'ata.record')
    await setFieldsValue(data.record)
  } else if (type == 'add') {
    // width.value = '40%'
    await resetSchema(await updateschemasFn())
    await setFieldsValue({ currency: '人民币' })
  }
})
//form
const [registerForm, { setFieldsValue, validateFields, resetFields, resetSchema }] = useForm({
  // schemas: updateschemasFn,
  showActionButtonGroup: false,
  labelWidth: 100,
  baseColProps: { span: 12 },
  actionColOptions: { span: 21 }
})
//Description
const [registerDesc1] = useDescription({
  schema: detailsschemas1,
  column: 2,
  data: record
})
const [registerDesc2, {}] = useDescription({
  schema: detailsschemas2,
  column: 2,
  data: record
})
const [registerDesc3, {}] = useDescription({
  schema: detailsschemas3,
  column: 2,
  data: record
})
//提交
const emit = defineEmits(['success', 'register'])
async function handleSubmit() {
  await changeOkLoading(true)
  try {
    const formData = await validateFields()
    console.log(formData, 'formData')
    const { client_id: client } = formData
    const commonParams = { ...formData, client_id: client.value, client_name: client.label }
    const params = unref(propData).type === 'edit' ? { ...commonParams, id: unref(propData).record.id } : commonParams
    unref(propData).type === 'edit' ? await getTaskupdate(params) : await getTaskadd(params)

    message.success('success')
    emit('success')
    await closeDrawer()
    changeOkLoading(false)
  } catch (err) {
    changeOkLoading(false)
    throw new Error(`${err}`)
  }
  // const fromdata = await getFieldsValue()
  // if (type.value == 'edit') {
  //   Reflect.set(fromdata, 'id', id.value)
  // }
  // const client_name = ref()
  // const { items } = await getcustomerList()
  // items.forEach((item) => {
  //   if (item.id == fromdata.client_id) {
  //     return (client_name.value = item.name)
  //   }
  // })
  // Reflect.set(fromdata, 'client_name', client_name.value)
  // Reflect.set(fromdata, 'exchange_rate', exchange_rate.value)
  // const res = ref()
  // if (type.value == 'edit') {
  //   res.value = await getTaskupdate(fromdata)
  // } else if (type.value === 'detail') {
  //   closeDrawer()
  // } else {
  //   res.value = await getTaskadd(fromdata)
  // }
  // if (res.value.news == 'success') {
  //   closeDrawer()
  //   resetFields()
  //   emit('success', 1)
  // }
}
// //关闭
// function close() {
//   resetFields()
//   datasouce.value = ''
//   exchange_rate.value = ''
// }
//应收
// async function changevalue(e) {
//   const a = ref()
//   if (e == 'receivable') {
//     a.value = '1'
//   } else if (e == 'cost') {
//     a.value = '2'
//   } else {
//     return
//   }
//   updateSchema(await updateschemasFn(a.value))
// }
// const exchange_rate = ref()
// const exchangedata = ref()
// const exchange_rate_lable = ref()
// function changeselect(_, shall) {
//   exchange_rate_lable.value = shall.value
// }
// async function getRmbquotFn() {
//   const { items } = await getRmbquot()
//   exchangedata.value = items.map((obj: any) => ({
//     value: obj.fBuyPri,
//     label: obj.name
//   }))
// }
</script>

<style lang="less" scoped></style>
