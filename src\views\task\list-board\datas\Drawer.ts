import { FormSchema } from '/@/components/Form'
import { DescItem } from '/@/components/Description'

import { getcustomerList } from '/@/api/financialDocuments/otherIncome'
import { getStaffList } from '/@/api/baseData/staff'
import { getDeptSelectTree } from '/@/api/admin/dept'
import { DefaultOptionType } from 'ant-design-vue/lib/vc-tree-select/TreeSelect'
import { getRmbquot } from '/@/api/erp/sales'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
// import { getRmbquot } from '/@/api/erp/sales'
const saleStore = useSaleOrderStore()

export async function updateschemasFn(type?: any): Promise<FormSchema[]> {
  const updateschemas: FormSchema[] = [
    {
      field: 'name',
      component: 'Input',
      label: '任务名称',
      colProps: {
        span: 12
      },

      required: true
    },
    {
      field: 'type',
      component: 'Select',
      label: '任务类型',
      colProps: {
        span: 12
      },
      required: true,
      componentProps: {
        options: saleStore.mapOrderTypeOptions
      }
    },
    {
      field: 'client_id',
      component: 'ApiSelect',
      label: '客户',
      colProps: {
        span: 12
      },
      required: true,
      componentProps: () => {
        return {
          api: getcustomerList,
          resultField: 'items',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'id',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            labelInValue: true
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'status',
      component: 'Select',
      label: '状态',
      ifShow: type === '1',
      colProps: {
        span: 12
      },
      required: true,
      componentProps: {
        options: saleStore.mapOrderStatusOptions
      }
    },

    {
      field: 'inCharge',
      label: '负责人',
      required: true,
      component: 'ApiSelect',
      componentProps: {
        api: getStaffList,
        resultField: 'items',
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        }
      },
      colProps: {
        span: 12
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'dept_id',
      required: true,
      label: '部门',
      component: 'ApiTreeSelect',
      componentProps: {
        api: getDeptSelectTree,
        treeSelectProps: {
          fieldNames: { children: 'children', key: 'id', value: 'id', label: 'name' },
          placeholder: '请选择',
          treeDefaultExpandAll: true,
          filterTreeNode: (search: string, item: DefaultOptionType) => {
            if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
            return false
          }
        }
      },
      colProps: {
        span: 12
      }
      // required: true
    },
    {
      field: 'currency',
      component: 'Input',
      label: '货币',
      colProps: {
        span: 12
      },
      required: true,
      componentProps: {
        disabled: true
      }
    },
    // {
    //   field: 'total_price',
    //   component: 'InputNumber',
    //   label: '总价格',
    //   colProps: {
    //     span: 12
    //   },
    //   required: true,
    //   componentProps: { min: 0 }
    // },
    {
      field: 'receivable',
      component: 'InputNumber',
      label: '应收金额',
      colProps: {
        span: 12
      },
      // required: type == '1',
      required: true,
      componentProps: { min: 0, disabled: type == '2', precision: 4 }
    },
    {
      field: 'cost',
      component: 'InputNumber',
      label: '应付金额',
      colProps: {
        span: 12
      },
      // required: type == '2',
      required: true,
      componentProps: { min: 0, disabled: type == '1', precision: 4 }
    },
    {
      field: 'exchange_rate',
      component: 'ApiSelect',
      required: true,
      label: '汇率',
      colProps: {
        span: 12
      },
      componentProps: {
        api: getRmbquot,
        resultField: 'items',
        selectProps: {
          fieldNames: { value: 'fBuyPri', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    // {
    //   field: 'exchange_rate',
    //   component: 'ApiSelect',
    //   label: '汇率',
    //   colProps: {
    //     span: 12
    //   },
    //   // slot: 'exchange_rate',
    //   required: true
    // },
    // {
    //   field: 'exchange_rate',
    //   required: true,
    //   component: 'InputNumber',
    //   label: '汇率',
    //   colProps: {
    //     span: 12
    //   },
    //   labelWidth: 100
    // },
    // {
    //   field: 'project_number',
    //   component: 'Input',
    //   label: '项目进度号',
    //   colProps: {
    //     span: 12
    //   }
    // },
    {
      field: 'remark',
      component: 'Input',
      label: '备注',
      colProps: {
        span: 12
      }
    }
  ]
  return updateschemas
}

export const detailsschemas1: DescItem[] = [
  {
    field: 'name',
    label: '订单名称'
  },
  {
    field: 'strid',
    label: '订单号'
  },
  {
    field: 'type',
    label: '订单类型',
    render: (val) => {
      return saleStore.saleType[val]
    }
  },
  {
    field: 'source',
    label: '渠道'
  },
  {
    field: 'source_uniqid',
    label: '渠道单号'
  },
  {
    field: 'client_name',
    label: '客户'
  },
  {
    field: 'status',
    label: '状态',
    render: (val) => {
      return saleStore.saleStatus[val]
    }
  },
  {
    field: 'department',
    label: '部门'
  }
]
export const detailsschemas2: DescItem[] = [
  {
    field: 'creator_name',
    label: '创建人'
  },
  {
    field: 'auditor_name',
    label: '审核人'
  },
  {
    field: 'inCharge_name',
    label: '负责人'
  },
  {
    field: 'currency',
    label: '货币'
  },
  {
    field: 'total_price',
    label: '总价格'
  },
  {
    field: 'receivable',
    label: '应收金额'
  },
  {
    field: 'received',
    label: '已收金额'
  },
  {
    field: 'exchange_rate',
    label: '汇率'
  },
  {
    field: 'received_actual',
    label: '实收金额'
  },
  {
    field: 'cost',
    label: '应付金额'
  },
  {
    field: 'paid',
    label: '已付金额'
  },
  {
    field: 'paid_actual',
    label: '实际付金额'
  }
]
export const detailsschemas3: DescItem[] = [
  {
    field: 'created_at',
    label: '创建时间'
  },
  {
    field: 'updated_at',
    label: '更新时间'
  },
  {
    field: 'submited_at',
    label: '提交时间'
  },
  {
    field: 'remark',
    label: '备注'
  }
]
