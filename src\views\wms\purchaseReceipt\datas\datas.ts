import type { BasicColumn, FormSchema } from '/@/components/Table'

export const columns: BasicColumn[] = [
  {
    title: 'id',
    dataIndex: 'ERPID',
    width: 100,
    resizable: true
  },
  {
    title: '任务单号',
    dataIndex: 'work_strid',
    width: 100,
    resizable: true
  },
  {
    title: '单据编号',
    dataIndex: 'FBillNo',
    width: 100,
    resizable: true
  },
  {
    title: '物料代码',
    dataIndex: 'FItemNum',
    width: 100,
    resizable: true
  },
  {
    title: '批号',
    dataIndex: 'FBatchNo',
    width: 100,
    resizable: true
  },
  {
    title: '入库时间',
    dataIndex: 'FInsertDate',
    width: 100,
    resizable: true
  },
  {
    title: '实发数量',
    dataIndex: 'FAuxQty',
    width: 100,
    resizable: true
  },

  {
    title: '摘要',
    dataIndex: 'FExplanation',
    width: 100,
    resizable: true,
    customRender: ({ record }) => record.FExplanation || '-'
  },

  {
    title: '备注',
    dataIndex: 'FNote',
    width: 100,
    resizable: true,
    customRender: ({ record }) => record.FNote || '-'
  }
]

// 筛选
export const searchFromSchemas: FormSchema[] = [
  {
    field: 'work_strid',
    label: '任务单号',
    component: 'Input'
  },
  {
    field: 'FItemNum',
    label: '物料代码',
    component: 'Input'
  },
  {
    field: 'FBatchNo',
    label: '批号',
    component: 'Input'
  },
  {
    field: 'FInsertDate',
    label: '入库时间',
    component: 'RangePicker'
  }
]
