<template>
  <BasicTable class="p-4" @register="registerTable" />
</template>

<script setup lang="ts" name="/wms/purchaseReceipt">
import { getPurchaseReceiptList } from '/@/api/wms/purchaseReceipt'
import { columns, searchFromSchemas } from './datas/datas'

import { BasicTable, useTable } from '/@/components/Table'

const [registerTable] = useTable({
  title: '外购入库',
  api: getPurchaseReceiptList,
  showIndexColumn: false,
  columns,
  showTableSetting: true,
  useSearchForm: true,
  rowKey: 'id',
  formConfig: {
    labelWidth: 120,
    actionColOptions: {
      span: 24
    },
    baseColProps: {
      span: 6
    },
    schemas: searchFromSchemas,
    fieldMapToTime: [['FInsertDate', ['FInsertDate_start', 'FInsertDate_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]]
  },
  pagination: {
    // size: 'small',
    pageSize: 20,
    pageSizeOptions: ['10', '20', '100']
  }
})
</script>
