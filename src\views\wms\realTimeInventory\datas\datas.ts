import type { BasicColumn, FormSchema } from '/@/components/Table'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { isNullOrUnDef } from '/@/utils/is'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { isNull, isUndefined } from 'lodash-es'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { h } from 'vue'
import { Tag } from 'ant-design-vue'
import { getDeptTree } from '/@/api/admin/dept'
import { changeMapToOptions, GET_STATUS_SCHEMA } from '/@/const/status'

const saleStore = useSaleOrderStore()

export const mapAudit = {
  0: { label: '未结算', color: '' },
  1: { label: '已结算', color: 'green' },
  2: { label: '待结算', color: 'skyblue' }
}

const status_schema = GET_STATUS_SCHEMA(saleStore.mapOrderStatusOptions)
const mapAudit_schema = GET_STATUS_SCHEMA(changeMapToOptions(mapAudit))

export const columns: BasicColumn[] = [
  {
    title: '项目id',
    dataIndex: 'project_number',
    width: 120,
    resizable: true
  },
  {
    title: '项目名称',
    dataIndex: 'project_name',
    width: 120,
    resizable: true
  },
  {
    title: '订单类型',
    dataIndex: 'type',
    width: 100,
    resizable: true,
    customRender: ({ value }) => (isNullOrUnDef(value) ? '-' : saleStore.saleType[value])
  },
  {
    title: '销售单号',
    dataIndex: 'source_uniqid',
    width: 300,
    resizable: true
  },
  {
    title: '所属部门',
    dataIndex: 'department',
    width: 250
  },

  {
    title: '客户名称',
    dataIndex: 'client_name',
    width: 100,
    resizable: true
  },

  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : useRender.renderTag(saleStore.saleStatus[value], saleStore.statusColor[value])
    }
  },
  {
    title: '结算状态',
    dataIndex: 'is_audit',
    width: 150,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '-' : h(Tag, { color: mapAudit[value]?.color }, () => mapAudit[value]?.label)
      // return mapStatus[record.status]?.label
    }
  },
  {
    title: '结算时间',
    dataIndex: 'audit_at',
    width: 120,
    resizable: true
  },
  {
    title: '项目负责人',
    dataIndex: 'inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '订单金额',
    dataIndex: 'total_price',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  {
    title: '已收金额',
    dataIndex: 'received',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value ? value : 0)
    }
  },
  {
    title: '实收金额',
    dataIndex: 'received_actual',
    width: 100,
    resizable: true,
    helpMessage: '实收金额与已收金额不对等,应是本张销售单存在退货或退款金额',
    customRender: ({ value }) => {
      return isUndefined(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  {
    title: '开单时间',
    dataIndex: 'submited_at',
    width: 200,
    resizable: true
  }
]

// 筛选
export const searchFromSchemas: FormSchema[] = [
  { ...status_schema, colProps: { span: 12 } },
  { ...mapAudit_schema, field: 'is_audit', colProps: { span: 12 } },
  {
    field: 'dept_ids',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        placeholder: '请选择',
        showSearch: true,
        multiple: true,
        maxTagCount: 3,
        optionFilterProp: 'name',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  {
    field: 'submited_at',
    label: '开单日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'audit_at',
    label: '结算日期',
    component: 'SingleRangeDate',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'project_number',
    label: '项目id',
    component: 'Input'
  }
]
