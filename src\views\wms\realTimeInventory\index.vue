<template>
  <div>
    <BasicTable class="p-4" @register="registerTable">
      <template #expandedRowRender="{ record }">
        <BasicTable class="p-4" :columns="childrencolumns" :data-source="record.item_stocking" :showIndexColumn="false" :canResize="false">
          <template #bodyCell="{ column: childColumn, record: childRecord }">
            <template v-if="childColumn.key === 'warehouse_id'">
              {{ childRecord.warehouse_name }}
            </template>
            <template v-if="childColumn.key === 'src'"> {{ options[childRecord.src].label }}</template>
          </template>
          <template #footer>
            <div class="footer">
              <span> 毛利合计： {{ record.item_stocking.reduce((sum, item) => sum + item.profit, 0) }} </span>
            </div>
          </template>
        </BasicTable>
      </template>
    </BasicTable>
  </div>
</template>

<script setup lang="ts" name="/wms/realTimeInventory">
import { projectwkgetOrderList } from '/@/api/wms/realTimeInventory'
import { columns, searchFromSchemas } from './datas/datas'
import { BasicTable, useTable } from '/@/components/Table'
import { columns as childrencolumns, options } from '/@/views/erp/Inventory/datas/data'
import { onMounted } from 'vue'

const [registerTable] = useTable({
  title: '即时库存',
  api: projectwkgetOrderList,
  showIndexColumn: false,
  columns,
  showTableSetting: true,
  useSearchForm: true,
  rowKey: 'id',
  formConfig: {
    labelWidth: 120,
    actionColOptions: {
      span: 24
    },
    baseColProps: {
      span: 6
    },
    schemas: searchFromSchemas,
    fieldMapToTime: [
      ['submited_at', ['submited_at_start', 'submited_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['audit_at', ['audit_at_start', 'audit_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  },
  pagination: {
    // size: 'small',
    pageSize: 20,
    pageSizeOptions: ['10', '20', '100']
  }
})

onMounted(() => {
  childrencolumns.forEach((item: any, index, array) => {
    if (['source_uniqid', 'department', 'deliver_at'].includes(item.dataIndex)) {
      item.defaultHidden = true
    }
    if (['warehouse_id'].includes(item.dataIndex)) {
      const newItem = {
        title: '仓位',
        dataIndex: 'warehouse_item_name',
        width: 300
      }
      array.splice(index + 1, 0, newItem)
    }
    if (['unit_price_right'].includes(item.dataIndex)) {
      const newItem = {
        title: '库存毛利',
        dataIndex: 'profit',
        width: 100
      }
      array.splice(index + 1, 0, newItem)
    }
    if (['profit'].includes(item.dataIndex)) {
      const newItem = {
        title: '销售单价',
        dataIndex: 'sale_unit_price',
        width: 100
      }
      array.splice(index + 1, 0, newItem)
    }
  })
})
</script>
<style lang="scss" scoped>
.footer {
  color: red;
  font-size: 18px;
}
</style>
