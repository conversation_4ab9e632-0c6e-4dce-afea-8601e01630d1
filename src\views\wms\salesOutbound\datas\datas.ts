import type { BasicColumn, FormSchema } from '/@/components/Table'

export const columns: BasicColumn[] = [
  {
    title: 'id',
    dataIndex: 'ERPID',
    width: 100,
    resizable: true
  },
  {
    title: '单据编号',
    dataIndex: 'FBillNo',
    width: 100,
    resizable: true
  },
  {
    title: '物料代码',
    dataIndex: 'FItemNum',
    width: 100,
    resizable: true
  },

  {
    title: '实发数量',
    dataIndex: 'FAuxQty',
    width: 100,
    resizable: true
  },
  {
    title: '批号',
    dataIndex: 'FBatchNo',
    width: 100,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'FNote',
    width: 100,
    resizable: true,
    customRender: ({ record }) => record.FNote || '-'
  },
  {
    title: '摘要',
    dataIndex: 'FExplanation',
    width: 100,
    resizable: true,
    customRender: ({ record }) => record.FExplanation || '-'
  }
]

// 筛选
export const searchFromSchemas: FormSchema[] = [
  {
    field: 'strid',
    label: '单号',
    component: 'Input'
  }
]
