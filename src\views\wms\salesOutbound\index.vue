<template>
  <BasicTable class="p-4" @register="registerTable" />
</template>

<script setup lang="ts" name="/wms/salesOutbound">
import { getSalesOutboundList } from '/@/api/wms/salesOutbound'
import { columns, searchFromSchemas } from './datas/datas'

import { BasicTable, useTable } from '/@/components/Table'

const [registerTable] = useTable({
  title: '销售出库',
  api: getSalesOutboundList,
  showIndexColumn: false,
  columns,
  showTableSetting: true,
  useSearchForm: false,
  rowKey: 'id',
  formConfig: {
    labelWidth: 120,
    actionColOptions: {
      span: 24
    },
    baseColProps: {
      span: 6
    },
    schemas: searchFromSchemas
  },
  pagination: {
    // size: 'small',
    pageSize: 20,
    pageSizeOptions: ['10', '20', '100']
  }
})
</script>
